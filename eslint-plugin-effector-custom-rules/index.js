//no-undefined-store: Deprecate undefined as magical value to skip store updates. It is forbidden to have undefined in Store
//no-second-argument-for-watch: Deprecate .watch with second argument in favor of sample
//no-side-effects: Error when unit called from a pure function

module.exports = {
    rules: {
        'no-undefined-store': require('./rules/no-undefined-store'),
        'no-second-argument-for-watch': require('./rules/no-second-argument-for-watch'),
        'no-side-effects': require('./rules/no-side-effects')
    },
    configs: {
        recommended: require('./config/recommended')
    }
};
