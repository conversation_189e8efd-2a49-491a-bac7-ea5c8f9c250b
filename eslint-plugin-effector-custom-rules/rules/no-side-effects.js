//This ESLint rule prohibits the use of Effector units (createEvent, createEffect, createStore) inside pure functions (map, on, prepend, filter, filterMap).
// It tracks calls to these units and reports an error if they are inside a pure function or passed to sample.
// If side effects are found in the function body, the rule recommends moving them outside pure functions.

// AST handlers:
// FunctionDeclaration(node), FunctionExpression(node), ArrowFunctionExpression(node) – save function declarations for later analysis.
// VariableDeclarator(node) – adds Effector units to the list of monitored ones.
// CallExpression(node) – tracks function calls and checks them for prohibited constructs.
// CallExpression:exit(node) – removes processed calls to Effector methods from the stack.

//example the Error
//const someStore = effectFx.doneData.map(value => {
//     effect2Fx();                     //error
//     functionFirstLevelSomeEffect();  //error
//     functionSecondLevelSomeEffect(); //error
//     sameFuncWithoutSideEffect(1, 2); // no error
//     return value === 'asd' ? 'value1' : 'value2';
// });

//sample({
//     clock: event1,
//     filter: () => {
//         effectFx();                       //error
//         functionFirstLevelSomeEffect();   //error

//         sameFuncWithoutSideEffect(1, 2);  // no error
//         return true;
//     },
//     fn: () => {
//         effectFx();
//         functionFirstLevelSomeEffect();   //error
//         functionSecondLevelSomeEffect();  //error
//         functionThirdLevelSomeEffect();   //error
//
//         sameFuncWithoutSideEffect(1, 2);  // no error
//         return null;
//     },
//     target: store1
// });

module.exports = {
    meta: {
        type: 'problem',
        docs: {
            description: 'Disallow Effector units inside pure functions',
            recommended: true
        },
        messages: {
            noSideEffects: "Do not use '{{ name }}' inside '{{ context }}'. Move it outside."
        },
        schema: []
    },
    create(context) {
        const functionStack = [];
        const PURE_METHODS = ['map', 'on', 'prepend', 'filter', 'filterMap'];
        const SIDE_EFFECT_CREATORS = ['createEvent', 'createEffect', 'createStore'];
        const EFFECTOR_UNITS = new Set();
        const FUNCTION_DECLARATIONS = new Map();

        //retrieves the root object from the MemberExpression chain.
        function getRootObject(node) {
            let current = node;
            while (current.type === 'MemberExpression') {
                current = current.object;
            }
            return current.type === 'Identifier' ? current : null;
        }
        //checks if the method call is a pure Effector function call PURE_METHODS
        function isEffectorMethodCall(node) {
            if (node.callee.type === 'MemberExpression' && PURE_METHODS.includes(node.callee.property?.name)) {
                const rootObject = getRootObject(node.callee.object);
                return rootObject && EFFECTOR_UNITS.has(rootObject.name);
            }
            return false;
        }
        //analyzes the function body for side effects.
        function analyzeFunctionBody(node) {
            if (!node || !node.body) return false;

            const statements = Array.isArray(node.body.body) ? node.body.body : [node.body];
            let isHaveSideEffect = false;

            function checkStatement(statement) {
                const expression = statement?.expression;
                const type = expression?.type;

                let callee;
                if (type === 'CallExpression') {
                    callee = expression.callee;
                } else if (type === 'AwaitExpression' && expression.argument?.type === 'CallExpression') {
                    callee = expression.argument.callee;
                }

                if (callee?.type === 'Identifier') {
                    const functionName = callee.name;

                    if (EFFECTOR_UNITS.has(functionName)) {
                        isHaveSideEffect = true;
                        return;
                    }

                    if (!!FUNCTION_DECLARATIONS.size && FUNCTION_DECLARATIONS.has(functionName)) {
                        const functionNode = FUNCTION_DECLARATIONS.get(functionName);
                        if (analyzeFunctionBody(functionNode)) {
                            isHaveSideEffect = true;
                        }
                    }
                }
            }

            statements.forEach(checkStatement);
            return isHaveSideEffect;
        }
        //checks function calls for using Effector units inside pure functions.
        function checkForSideEffects(node) {
            if (!node || !node.callee) return;

            const isInsideEffectorMethod = functionStack.length > 0;
            if (!isInsideEffectorMethod) return;

            if (node.callee.type === 'Identifier') {
                const functionName = node.callee.name;

                if (EFFECTOR_UNITS.has(functionName) || SIDE_EFFECT_CREATORS.includes(functionName)) {
                    context.report({
                        node,
                        messageId: 'noSideEffects',
                        data: {
                            name: functionName,
                            context: functionStack[functionStack.length - 1]
                        }
                    });
                }

                if (!!FUNCTION_DECLARATIONS.size && FUNCTION_DECLARATIONS.has(functionName)) {
                    const functionNode = FUNCTION_DECLARATIONS.get(functionName);
                    if (analyzeFunctionBody(functionNode)) {
                        context.report({
                            node,
                            messageId: 'noSideEffects',
                            data: {
                                name: functionName,
                                context: functionStack[functionStack.length - 1]
                            }
                        });
                    }
                }
            }
        }

        function checkIfIfStatementBody(node) {
            //check body if (alternate)
            if (node.consequent) {
                if (Array.isArray(node.consequent.body)) {
                    for (const stmt of node.consequent.body) {
                        if (checkSampleBody(stmt)) {
                            return true;
                        }
                    }
                } else if (checkSampleBody(node.consequent)) {
                    return true;
                }
            }
            // check body else / else if (alternate)
            if (node.alternate) {
                if (node.alternate.type === 'BlockStatement') {
                    for (const stmt of node.alternate.body) {
                        if (checkSampleBody(stmt)) {
                            return true;
                        }
                    }
                } else if (checkSampleBody(node.alternate)) {
                    return true;
                }
            }
            return false;
        }

        //checks the sample body for side effects.
        function checkSampleBody(node) {
            // check IfStatement
            if (node.type === 'IfStatement') {
                return checkIfIfStatementBody(node);
            }

            // check ExpressionStatement
            if (node.type === 'ExpressionStatement') {
                const expression = node?.expression;
                const type = expression?.type;
                let callee;
                if (type === 'CallExpression') {
                    callee = expression.callee;
                } else if (type === 'AwaitExpression' && expression.argument?.type === 'CallExpression') {
                    callee = expression.argument.callee;
                }
                if (callee?.type === 'Identifier') {
                    const functionName = callee.name;

                    if (EFFECTOR_UNITS.has(functionName)) {
                        return true;
                    }
                    if (!!FUNCTION_DECLARATIONS.size && FUNCTION_DECLARATIONS.has(functionName)) {
                        const functionNode = FUNCTION_DECLARATIONS.get(functionName);
                        return analyzeFunctionBody(functionNode);
                    }
                }
            }

            return false;
        }

        //analyzes the sample arguments and looks for illegal calls in them
        function checkSample(node) {
            const options = node.arguments[0];
            if (options.type === 'ObjectExpression') {
                options.properties.forEach(prop => {
                    if (
                        (prop.key.name === 'filter' || prop.key.name === 'fn') &&
                        (prop.value.type === 'ArrowFunctionExpression' || prop.value.type === 'FunctionExpression')
                    ) {
                        prop.value.body?.body?.forEach(item => {
                            if (checkSampleBody(item)) {
                                context.report({
                                    node: item,
                                    messageId: 'noSideEffects',
                                    data: {
                                        name: item.expression?.callee?.name || 'side effects',
                                        context: 'sample'
                                    }
                                });
                            }
                        });
                    }
                });
            }
        }

        return {
            FunctionDeclaration(node) {
                FUNCTION_DECLARATIONS.set(node.id.name, node);
            },

            FunctionExpression(node) {
                if (node.parent?.type === 'VariableDeclarator' && node.parent.id.type === 'Identifier') {
                    FUNCTION_DECLARATIONS.set(node.parent.id.name, node);
                }
            },

            ArrowFunctionExpression(node) {
                if (node.parent?.type === 'VariableDeclarator' && node.parent.id.type === 'Identifier') {
                    FUNCTION_DECLARATIONS.set(node.parent.id.name, node);
                }
            },
            VariableDeclarator(node) {
                if (
                    node.init &&
                    node.init?.type === 'CallExpression' &&
                    node.init.callee.type === 'Identifier' &&
                    SIDE_EFFECT_CREATORS.includes(node.init.callee.name)
                ) {
                    EFFECTOR_UNITS.add(node.id.name);
                }
            },
            CallExpression(node) {
                if (!node) return;

                if (isEffectorMethodCall(node)) {
                    functionStack.push(node.callee.property.name);
                }

                if (node.callee.type === 'Identifier' && node.callee.name === 'sample') {
                    checkSample(node);
                }

                checkForSideEffects(node);
            },

            'CallExpression:exit'(node) {
                if (isEffectorMethodCall(node)) {
                    functionStack.pop();
                }
            }
        };
    }
};
