import { extractOCRFromContract } from './extractOCRFromContract';

describe('extractOCRFromContract', () => {
    it('Common', () => {
        const data = {
            customTemplateData: {
                entry: [
                    { key: 'VINResult', value: { type: 'xs:string', _text: 'some' } },
                    { key: 'jsonLabourRates', value: { type: 'xs:string', _text: 'some' } },
                    { key: 'vehicleTypeOCR', value: { type: 'xs:string', _text: 'some' } },
                    { key: 'vehicleModelOCR', value: { type: 'xs:string', _text: 'some' } },
                    { key: 'vehicleColorOCR', value: { type: 'xs:string', _text: 'some' } }
                ]
            }
        };

        expect(extractOCRFromContract(data)).toStrictEqual([
            { key: 'vehicleModelOCR', value: 'some' },
            { key: 'vehicleColorOCR', value: 'some' }
        ]);
    });
});
