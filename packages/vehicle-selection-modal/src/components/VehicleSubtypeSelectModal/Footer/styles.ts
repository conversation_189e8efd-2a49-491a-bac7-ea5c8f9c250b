import styled from 'styled-components/macro';
import { media } from '@wedat/ui-kit/mediaQueries';

export const ButtonContainer = styled.div`
    width: 100%;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid ${({ theme }) => theme.colors.deepBlue['100']};
    padding: 24px 20px;

    ${media.phoneBig`
        justify-content: center;
    `}
`;

export const ChooseButtonWrapper = styled.div`
    button {
        max-width: 122px;

        background-color: ${({ theme }) => theme.colors.deepBlue['800']};
        font: ${({ theme }) => theme.typography.note.font};
        color: ${({ theme }) => theme.colors.white};
        border: none;
        transition: opacity 150ms;

        &:hover {
            background-color: ${({ theme }) => theme.colors.deepBlue['800']};
            color: ${({ theme }) => theme.colors.white};
            border: none;
            opacity: 0.8;
        }

        &:disabled {
            background-color: ${({ theme }) => theme.colors.deepBlue['50']};
            color: ${({ theme }) => theme.colors.dustBlue['200']};
            border: none;
        }

        ${media.phoneBig`
        max-width: 50%;
    `}
    }
`;

export const CancelButtonWrapper = styled.div`
    button {
        max-width: 136px;

        background-color: ${({ theme }) => theme.colors.white};
        font: ${({ theme }) => theme.typography.note.font};
        color: ${({ theme }) => theme.colors.black};
        border-color: ${({ theme }) => theme.colors.black};
        transition: opacity 150ms;

        margin-right: 10px;

        &:hover {
            background-color: ${({ theme }) => theme.colors.white};
            color: ${({ theme }) => theme.colors.black};
            border-color: ${({ theme }) => theme.colors.black};
            opacity: 0.6;
        }

        ${media.phoneBig`
        max-width: 50%;
        margin-right: 16px;
    `}
    }
`;
