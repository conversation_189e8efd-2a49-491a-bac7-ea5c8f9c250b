import { useEffect, FocusEvent, SyntheticEvent, useCallback, useRef, FC } from 'react';
import { useField } from 'formik';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';

import { NumberInputField } from '@wedat/ui-kit/Formik';
import { contractEffects, contractStores } from '@dat/shared-models/contract';
import { createPayloadForCreateOrUpdateContract } from '@dat/shared-models/contract/utils';

import { getTemplateDataValuesFromContract } from '../../utils/getTemplateDataValuesFromContract';
import { HideBlock } from '@dat/smart-components/HideBlock';

const { createOrUpdateContractFx } = contractEffects;

interface Props {
    disabled?: boolean;
}

export const TiresPercentInputField: FC<Props> = ({ disabled }) => {
    const [, , { setValue }] = useField('tiresPercent');
    const setValueRef = useRef(setValue);

    const { t } = useTranslation();

    const currentContract = useUnit(contractStores.contract);
    const contractId = useUnit(contractStores.contractId);

    const tiresPercent = currentContract ? getTemplateDataValuesFromContract(currentContract, 'tiresPercent') : '';

    const handleCreateOrUpdateContract = useCallback(
        async (e: SyntheticEvent<HTMLInputElement>) => {
            // join and split in e.target.value for removing dots in value
            const tiresPercent = e.currentTarget.value;

            //filled value should be less than 100%
            const filledValue = (tiresPercent && +tiresPercent < 100) || !tiresPercent;

            if (!contractId && filledValue) {
                const newContractId = await contractEffects.createContractFx({
                    ...createPayloadForCreateOrUpdateContract({ tiresPercent: tiresPercent ? tiresPercent : '0' })
                });

                await contractEffects.getContractFx(newContractId);
            } else if (filledValue) {
                await createOrUpdateContractFx({
                    contractId,
                    ...createPayloadForCreateOrUpdateContract({ tiresPercent: tiresPercent ? tiresPercent : '0' })
                });
            }
        },
        [contractId]
    );

    const handleBlurTiresPercentField = useCallback(
        (e: FocusEvent<HTMLInputElement>) => {
            handleCreateOrUpdateContract(e);
        },
        [handleCreateOrUpdateContract]
    );

    const handleEnter = useCallback(
        (e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === 'Enter') {
                handleCreateOrUpdateContract(e);
            }
        },
        [handleCreateOrUpdateContract]
    );

    useEffect(() => {
        tiresPercent && setValueRef.current(tiresPercent);
    }, [tiresPercent]);

    return (
        <HideBlock sidebarBlock="tyres-input">
            <NumberInputField
                fixedDecimalScale={false}
                type="tel"
                name="tiresPercent"
                label={t('inputsPlaceholders.tiresPercent')}
                onBlur={handleBlurTiresPercentField}
                onKeyDown={handleEnter}
                disabled={disabled}
            />
        </HideBlock>
    );
};
