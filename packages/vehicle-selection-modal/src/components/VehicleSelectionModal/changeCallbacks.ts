import { FormikContextType, setIn } from 'formik';
import { omit } from 'lodash-es';

import { createDatECode, createConstructionTimes, convertDateToConstructionPeriod } from '@dat/core/utils';
import { FieldEventCallback } from '@wedat/ui-kit/Formik';
import { toastEffects } from '@dat/shared-models/smart-components/Toast/store';
import { fullDatECodeLength } from '@dat/core/constants/contract';

import { vehicleSelectionEvents, vehicleSelectionEffects, vehicleSelectionStores } from '../../stores/vehicleSelection';
import { vehicleImagesEffects, vehicleImagesEvents } from '../../stores/vehicleImages';
import { vehicleIdentificationStores } from '../../stores/vehicleIdentification';
import { vehicleRepairEffects, vehicleRepairEvents } from '../../stores/vehicleRepair';
import { commonEffects, commonEvents, commonStores } from '../../stores/common';
import { CurrentFormikValues, VehicleTypeAdditionalOptions } from '../../types/formik';
import { extractAndFormatDateFromBaseModel } from '../../utils/extractAndFormatDateFromBaseModel';

export const containerChangeCallback: FieldEventCallback<CurrentFormikValues> = async (
    container: string,
    formikContext
) => {
    const { datECode, constructionPeriod } = formikContext.values;

    if (container && datECode.length === fullDatECodeLength) {
        const periods = await vehicleSelectionEffects.getConstructionPeriodsFx({ datECode, container });

        let constructionTime = +constructionPeriod;

        if (!constructionTime) {
            constructionTime = periods.constructionPeriods.length
                ? Math.max(...periods.constructionPeriods.map(({ key }) => +key))
                : Number(periods.constructionTimeMax);
        }

        if (constructionTime) {
            vehicleSelectionEffects.getTechnicalDataFx({
                datECode,
                container,
                constructionTime
            });
        }
    }
};

export const constructionPeriodChangeCallback: FieldEventCallback<CurrentFormikValues> = (
    constructionPeriod: string,
    formikContext
) => {
    const { datECode, container, repairType } = formikContext.values;

    if (datECode.length === fullDatECodeLength && constructionPeriod) {
        vehicleSelectionEffects.getTechnicalDataFx({
            datECode,
            container: repairType === 'APPRAISAL' ? container : '',
            constructionTime: +constructionPeriod
        });
    }
};

//
/*** Main model callbacks ***/
//

// TODO: fix Can't perform a React state update - remove setFieldValue after bad requests
// These callbacks are chained.
// vehicleTypeChange -> manufacturerChange -> baseModelChange -> subModelChange
// e.g. when vehicleTypeChangeCallback is called, it resets manufacturers stores
// and when manufacturers stores is reset, manufacturers SelectField
// resets it's value and calls manufacturerChangeCallback with an empty string.
// And so on.

export const vehicleTypeChangeCallback: FieldEventCallback<CurrentFormikValues, VehicleTypeAdditionalOptions> = async (
    value,
    formikContext,
    { cancelFocus = false }
) => {
    const {
        setFieldValue,
        values: {
            firstRegistration,
            firstRegistrationFilter,
            vehicleSelects: { vehicleType }
        }
    } = formikContext;

    if (vehicleIdentificationStores.lastSuccessfullyRequestedVin.getState()) {
        const doChange = await commonEffects.getVinRequestResetPromptAnswerFx();

        if (!doChange) {
            setFieldValue('vehicleSelects.vehicleType', vehicleType);

            return;
        }
    }

    // Reset manufacturers
    vehicleSelectionEvents.resetManufacturers();

    if (value) {
        // Request
        const constructionTimes = createConstructionTimes(firstRegistration, firstRegistrationFilter);
        await vehicleSelectionEffects.getManufacturersFx({
            vehicleType: +value,
            constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
            constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
        });

        // Create DAT-code & focus
        setFieldValue('datECode', createDatECode({ vehicleType: value }));
        if (!cancelFocus) {
            commonEvents.focusSelect('vehicleSelects.manufacturer');
        }
    }
};

export const manufacturerChangeCallback: FieldEventCallback<CurrentFormikValues> = async (value, formikContext) => {
    const {
        setFieldValue,
        values: {
            vehicleSelects: { vehicleType, manufacturer },
            firstRegistration,
            firstRegistrationFilter
        }
    } = formikContext;

    if (vehicleIdentificationStores.lastSuccessfullyRequestedVin.getState()) {
        const doChange = await commonEffects.getVinRequestResetPromptAnswerFx();

        if (!doChange) {
            setFieldValue('vehicleSelects.manufacturer', manufacturer);

            return;
        }
    }

    // Reset baseModels
    vehicleSelectionEvents.resetBaseModels();

    if (value) {
        // Check validity
        if (!vehicleType) {
            return;
        }

        // Request
        const constructionTimes = createConstructionTimes(firstRegistration, firstRegistrationFilter);
        await vehicleSelectionEffects.getBaseModelsFx({
            vehicleType: +vehicleType,
            manufacturer: +value,
            constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
            constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
        });

        // Create DAT-code & focus
        setFieldValue('datECode', createDatECode({ vehicleType, manufacturer: value }));
        commonEvents.focusSelect('vehicleSelects.baseModel');
    }
};

export const baseModelChangeCallback: FieldEventCallback<CurrentFormikValues> = async (value, formikContext) => {
    const {
        setFieldValue,
        initialValues,
        values: {
            vehicleSelects: { vehicleType, manufacturer, baseModel },
            firstRegistration,
            firstRegistrationFilter
        }
    } = formikContext;

    if (vehicleIdentificationStores.lastSuccessfullyRequestedVin.getState()) {
        const doChange = await commonEffects.getVinRequestResetPromptAnswerFx();

        if (!doChange) {
            setFieldValue('vehicleSelects.baseModel', baseModel);

            return;
        }
    }

    // Reset
    vehicleSelectionEvents.resetSubModels();
    vehicleRepairEvents.resetPaintTypes();
    setFieldValue('paintType', initialValues.paintType);

    if (value) {
        // Get selected option object from baseModels stores
        const baseModels = vehicleSelectionStores.baseModels.getState();
        const selectedOptionObject = baseModels.find(({ key }) => key === value);

        // Check validity
        if (!vehicleType || !manufacturer) {
            return;
        }

        // Data for requests
        const requestData = {
            vehicleType,
            manufacturer
        };
        const subModelsRequestData = {
            ...requestData,
            ...createConstructionTimes(firstRegistration, firstRegistrationFilter)
        };

        // Alternative model request
        if (selectedOptionObject?.repairIncomplete) {
            const alternativeModelValues = {
                ...subModelsRequestData,
                baseModel: selectedOptionObject.alternativeBaseType || ''
            };

            vehicleSelectionEvents.setIsOptionWithRepairIncompleteSelected(true);
            if (selectedOptionObject.alternativeBaseType) {
                await vehicleSelectionEffects.getAlternativeSubModelsFx({
                    vehicleType: +subModelsRequestData.vehicleType,
                    baseModel: +selectedOptionObject.alternativeBaseType,
                    manufacturer: +manufacturer
                });
            }

            setFieldValue('alternativeModelDatECode', createDatECode({ ...alternativeModelValues }));

            setFieldValue('alternativeModelSelects', {
                ...alternativeModelValues,
                subModel: ''
            });
        } else {
            vehicleSelectionEvents.setIsOptionWithRepairIncompleteSelected(false);
        }

        // Main model and colors requests
        await Promise.all([
            vehicleSelectionEffects.getSubModelsFx({
                vehicleType: +subModelsRequestData.vehicleType,
                manufacturer: +subModelsRequestData.manufacturer,
                baseModel: +value,
                constructionTimeTo: Number(subModelsRequestData.constructionTimeTo) || undefined,
                constructionTimeFrom: Number(subModelsRequestData.constructionTimeFrom) || undefined
            }),
            vehicleRepairEffects.getPaintTypesFx({
                ...requestData,
                mainType: value
            })
        ]);

        // Create DAT-code & focus
        setFieldValue('datECode', createDatECode({ vehicleType, manufacturer, baseModel: value }));
        commonEvents.focusSelect('vehicleSelects.subModel');
    } else {
        vehicleSelectionEvents.setIsOptionWithRepairIncompleteSelected(false);
    }
};

export const subModelChangeCallback: FieldEventCallback<CurrentFormikValues> = async (value, formikContext) => {
    const {
        setFieldValue,
        initialValues,
        values: { vehicleSelects, firstRegistration, firstRegistrationFilter }
    } = formikContext;

    // Get selected option object from baseModels stores
    const baseModels = vehicleSelectionStores.baseModels.getState();
    const selectedOptionObject = baseModels.find(({ key }) => key === vehicleSelects.baseModel);

    if (vehicleIdentificationStores.lastSuccessfullyRequestedVin.getState()) {
        const doChange = await commonEffects.getVinRequestResetPromptAnswerFx();

        if (!doChange) {
            setFieldValue('vehicleSelects.subModel', vehicleSelects.subModel);

            return;
        }
    }

    // Reset values
    vehicleImagesEvents.resetVehicleImages();
    vehicleSelectionEvents.resetAllEquipment();

    commonEvents.resetOptionsDependentOnEquipment();
    setFieldValue('equipmentSelects', {
        ...initialValues.equipmentSelects
    });

    if (value) {
        const { vehicleType, manufacturer, baseModel } = vehicleSelects;

        // Check validity
        if (!vehicleType || !manufacturer || !baseModel) {
            return;
        }

        // Reset focus to avoid focus blinks during requests
        commonEvents.focusSelect('');

        // Create DAT-code
        const datECode = createDatECode({ ...vehicleSelects, subModel: value });

        // Request
        await Promise.all([
            vehicleSelectionEffects.getAllEquipmentObjectFx({
                vehicleType: +vehicleType,
                manufacturer: +manufacturer,
                baseModel: +baseModel,
                subModel: +value,
                ...createConstructionTimes(firstRegistration, firstRegistrationFilter)
            })
        ]);

        // Set DAT-code
        setFieldValue('datECode', datECode);
        // Focus select that was chosen during equipment requests
        const selectToFocus = commonStores.focusedSelectName.getState();

        commonEvents.setEquipmentSelectsWasChanged(true);

        if (selectToFocus) {
            commonEvents.focusSelect(selectToFocus);
        } else {
            commonEvents.focusFirstNotSingleOptionEquipmentSelect();
        }

        //NOTE:
        // a refactor is needed here. The event completely rewrites the store.
        // There is no point in this action now, because this event is called in another place and rewrites this data.
        // But, this only triggers useEffects in VSM components once again
        // Initially, this was done only for the case when we do not have construction periods and we need to get them from the model description.
        const constructionTimeFromBaseModelsLabel =
            selectedOptionObject?.label && extractAndFormatDateFromBaseModel(selectedOptionObject?.label);

        if (constructionTimeFromBaseModelsLabel) {
            const constructionTimeValueFromBaseModelsLabel = extractAndFormatDateFromBaseModel(
                selectedOptionObject?.label || '',
                true
            );
            vehicleSelectionEvents.setConstructionPeriods([
                {
                    key: convertDateToConstructionPeriod(new Date(constructionTimeFromBaseModelsLabel)),
                    value: constructionTimeValueFromBaseModelsLabel,
                    label: constructionTimeValueFromBaseModelsLabel
                }
            ]);
        }
    }
};

//
/*** Alternative model callbacks ***/
//
export const alternativeVehicleTypeChangeCallback: FieldEventCallback<CurrentFormikValues> = async (
    value,
    formikContext
) => {
    const {
        setFieldValue,
        values: { firstRegistration, firstRegistrationFilter }
    } = formikContext;

    vehicleSelectionEvents.resetAlternativeManufacturers();

    const constructionTimes = createConstructionTimes(firstRegistration, firstRegistrationFilter);
    await vehicleSelectionEffects.getAlternativeManufacturersFx({
        vehicleType: +value,
        constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
        constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
    });

    commonEvents.focusSelect('alternativeModelSelects.manufacturer');

    setFieldValue('alternativeModelDatECode', createDatECode({ vehicleType: value }));
};

export const alternativeManufacturerChangeCallback: FieldEventCallback<CurrentFormikValues> = async (
    value,
    formikContext
) => {
    vehicleSelectionEvents.resetAlternativeBaseModels();

    if (value) {
        const {
            setFieldValue,
            values: {
                alternativeModelSelects: { vehicleType },
                firstRegistration,
                firstRegistrationFilter
            }
        } = formikContext;

        if (!vehicleType) {
            return;
        }

        vehicleSelectionEvents.resetAlternativeBaseModels();

        const constructionTimes = createConstructionTimes(firstRegistration, firstRegistrationFilter);
        await vehicleSelectionEffects.getAlternativeBaseModelsFx({
            vehicleType: +vehicleType,
            manufacturer: +value,
            constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
            constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
        });

        commonEvents.focusSelect('alternativeModelSelects.baseModel');

        setFieldValue('alternativeModelDatECode', createDatECode({ vehicleType, manufacturer: value }));
    }
};

export const alternativeBaseModelChangeCallback: FieldEventCallback<CurrentFormikValues> = async (
    value,
    formikContext
) => {
    vehicleSelectionEvents.resetAlternativeSubModels();

    if (value) {
        const {
            setFieldValue,
            values: {
                alternativeModelSelects: { vehicleType, manufacturer },
                firstRegistration,
                firstRegistrationFilter
            }
        } = formikContext;

        if (!vehicleType || !manufacturer) {
            return;
        }

        vehicleSelectionEvents.resetAlternativeSubModels();

        const constructionTimes = createConstructionTimes(firstRegistration, firstRegistrationFilter);
        await vehicleSelectionEffects.getAlternativeSubModelsFx({
            vehicleType: +vehicleType,
            manufacturer: +manufacturer,
            baseModel: +value,
            constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
            constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
        });

        setFieldValue('alternativeModelDatECode', createDatECode({ vehicleType, manufacturer, baseModel: value }));

        commonEvents.focusSelect('alternativeModelSelects.subModel');
    }
};

export const alternativeSubModelChangeCallback: FieldEventCallback<CurrentFormikValues> = async (
    value,
    formikContext
) => {
    const {
        setFieldValue,
        values: {
            alternativeModelSelects: { vehicleType, manufacturer, baseModel },
            firstRegistration,
            firstRegistrationFilter
        }
    } = formikContext;

    const dataForECode = { vehicleType, manufacturer, baseModel, subModel: value };
    const datECode = createDatECode(dataForECode);

    await Promise.all([
        vehicleImagesEffects.getVehicleImagesFx({
            datECode
        })
    ]);

    setFieldValue('alternativeModelDatECode', datECode);

    const allEquipmentObject = await vehicleSelectionEffects.getAlternativeAllEquipmentObjectFx({
        vehicleType: +vehicleType,
        manufacturer: +manufacturer,
        baseModel: +baseModel,
        subModel: +value,
        ...createConstructionTimes(firstRegistration, firstRegistrationFilter)
    });

    const groupsArray = Object.values(allEquipmentObject);

    const selectedOptions = groupsArray
        .map(options => {
            if (options) {
                return +options[0].key;
            }

            return 0;
        })
        .filter(option => !!option);

    await vehicleSelectionEffects.compileAlternativeDatECodeFx({
        vehicleType: +vehicleType,
        manufacturer: +manufacturer,
        baseModel: +baseModel,
        subModel: +value,
        selectedOptions
    });

    commonEvents.focusFirstNotSingleOptionEquipmentSelect();
};

//
/*** First registration callbacks ***/
//
const updateEverythingWithNewFirstRegistration = async (formikContext: FormikContextType<CurrentFormikValues>) => {
    const {
        setFieldValue,
        initialValues,
        values: {
            vehicleSelects: { vehicleType, manufacturer, baseModel, subModel },
            alternativeModelSelects,
            equipmentSelects,
            firstRegistration,
            firstRegistrationFilter
        }
    } = formikContext;
    const constructionTimes = createConstructionTimes(firstRegistration, firstRegistrationFilter);

    commonEvents.resetOptionsDependentOnEquipment();
    setFieldValue('equipmentSelects', {
        ...initialValues.equipmentSelects
    });

    // Requests
    const selectedEquipment = Object.values(equipmentSelects).filter(option => !!option);

    const [vehicleTypes, manufacturers, baseModels, subModels] = await Promise.all([
        // Main model
        vehicleSelectionEffects.getVehicleTypesFx({
            constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
            constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
        }),
        vehicleSelectionEffects.getManufacturersFx({
            vehicleType: +vehicleType,
            constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
            constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
        }),
        vehicleSelectionEffects.getBaseModelsFx({
            vehicleType: +vehicleType,
            manufacturer: +manufacturer,
            constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
            constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
        }),
        vehicleSelectionEffects.getSubModelsFx({
            vehicleType: +vehicleType,
            manufacturer: +manufacturer,
            baseModel: +baseModel,
            constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
            constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
        }),
        // // Alternative model
        vehicleSelectionEffects.getAlternativeManufacturersFx({
            vehicleType: +alternativeModelSelects.vehicleType,
            constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
            constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
        }),
        vehicleSelectionEffects.getAlternativeBaseModelsFx({
            vehicleType: +alternativeModelSelects.vehicleType,
            manufacturer: +alternativeModelSelects.manufacturer,
            constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
            constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
        }),
        vehicleSelectionEffects.getAlternativeSubModelsFx({
            vehicleType: +alternativeModelSelects.vehicleType,
            manufacturer: +alternativeModelSelects.manufacturer,
            baseModel: +alternativeModelSelects.baseModel,
            constructionTimeTo: Number(constructionTimes.constructionTimeTo) || undefined,
            constructionTimeFrom: Number(constructionTimes.constructionTimeFrom) || undefined
        }),
        vehicleSelectionEffects
            .getAllEquipmentObjectFx({
                vehicleType: +vehicleType,
                manufacturer: +manufacturer,
                baseModel: +baseModel,
                subModel: +subModel,
                ...constructionTimes
            })
            .then(() =>
                // "availableEquipmentObject" is dependent on "allEquipmentObject"
                // so it should be requested only after "getAllEquipmentObjectFx"
                vehicleSelectionEffects.getAvailableEquipmentObjectFx({
                    vehicleType: +vehicleType,
                    manufacturer: +manufacturer,
                    baseModel: +baseModel,
                    subModel: +subModel,
                    availableOptions: selectedEquipment as unknown[] as number[],
                    ...constructionTimes
                })
            )
    ]);

    // Focus necessary select
    let selectToFocus = commonStores.focusedSelectName.getState();

    if (!subModels.length || !subModels.some(({ key }) => key === subModel)) {
        selectToFocus = 'vehicleSelects.subModel';
    }
    if (!baseModels.length || !baseModels.some(({ key }) => key === baseModel)) {
        selectToFocus = 'vehicleSelects.baseModel';
    }
    if (!manufacturers.length || !manufacturers.some(({ key }) => key === manufacturer)) {
        selectToFocus = 'vehicleSelects.manufacturer';
    }
    if (!vehicleTypes.length || !vehicleTypes.some(({ key }) => key === vehicleType)) {
        selectToFocus = 'vehicleSelects.vehicleType';
    }

    commonEvents.focusSelect(selectToFocus);
};

// First registration datepicker callback
export const firstRegistrationChangeCallback: FieldEventCallback<CurrentFormikValues, void, Date | undefined> = async (
    value,
    formikContext
) => {
    const { constructionPeriod, firstRegistrationFilter } = formikContext.values;

    vehicleSelectionEvents.setFilterFirstRegistrationDate(firstRegistrationFilter ? value : null);

    if (!constructionPeriod && !firstRegistrationFilter) {
        formikContext.setFieldValue('firstRegistration', null);

        return toastEffects.showErrorToastFx({
            message: {
                namespace: 'vehicle-selection',
                key: 'error.firstSelectConstructionTime'
            }
        });
    }

    if (Object.keys(formikContext.errors).length) {
        const errors = omit(formikContext.errors, 'firstRegistration');
        formikContext.setErrors(errors);
    }

    if (firstRegistrationFilter && !vehicleIdentificationStores.lastSuccessfullyRequestedVin.getState()) {
        const updatedFormikContext = setIn(formikContext, 'values.firstRegistration', value);

        await updateEverythingWithNewFirstRegistration(updatedFormikContext);
    }
};

// First registration filter callback
export const firstRegistrationFilterChangeCallback: FieldEventCallback<CurrentFormikValues, void, boolean> = async (
    value,
    formikContext
) => {
    const {
        setFieldValue,
        values: { constructionPeriod, firstRegistration }
    } = formikContext;

    vehicleSelectionEvents.setFilterFirstRegistrationDate(value ? firstRegistration : null);

    if (!constructionPeriod && firstRegistration) {
        setFieldValue('firstRegistration', '');
    }

    if (
        formikContext.values.firstRegistration &&
        !vehicleIdentificationStores.lastSuccessfullyRequestedVin.getState()
    ) {
        const updatedFormikContext = setIn(formikContext, 'values.firstRegistrationFilter', value);
        await updateEverythingWithNewFirstRegistration(updatedFormikContext);
    }
};
