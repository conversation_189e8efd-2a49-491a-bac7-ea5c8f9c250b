import styled from 'styled-components/macro';

export const SubmenuTitle = styled.div`
    margin-bottom: 20px;
`;

export const ValuesContainer = styled.div`
    margn-left: 20px;
`;

export const VinResultVuesWrapper = styled.div`
    display: flex;
    gap: 10px;
    border-bottom: ${({ theme }) => `1px solid ${theme.colors.gray['20']}`};
    padding: 5px 0;
`;

export const VinResultVuesContainer = styled.div`
    width: 33.3%;
    word-break: break-all;
    text-align: center;
`;

export const VinEquipmentHeader = styled.div`
    position: sticky;
    top: -4px;
    display: flex;
    gap: 10px;
    padding: 10px 0;
    border-bottom: ${({ theme }) => `1px solid ${theme.colors.gray['20']}`};
    background-color: ${({ theme }) => theme.colors.white};
    box-shadow: rgba(33, 35, 38, 0.1) 0px 10px 10px -10px;
`;

export const VinEquipmentItemTitle = styled.p`
    display: flex;
    justify-content: center;
    text-align: center;
    flex: 1;
    word-break: break-word;
    font-weight: bold;
    color: ${({ theme }) => theme.colors.dustBlue['800']};
`;
