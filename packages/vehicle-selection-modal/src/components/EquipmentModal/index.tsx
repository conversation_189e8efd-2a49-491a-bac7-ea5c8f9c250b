import { useCallback, useEffect, useState } from 'react';
import { useFormikContext } from 'formik';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';

import { EquipmentPluginResult } from '@dat/core/types';
import { contractStores } from '@dat/shared-models/contract';
import { Equipment, EquipmentPluginOptions } from '@dat/equipment';
import { vehicleSelectionModalStores } from '@dat/shared-models/vsm';
import { useMedia } from '@dat/core/hooks/useMedia';

import { Modal } from '@wedat/kit';
import { sizes } from '@wedat/ui-kit/mediaQueries';
import { Preloader } from '@wedat/ui-kit';

import { modalsEvents, modalsStores } from '../../stores/modals';
import { commonStores } from '../../stores/common';
import { vehicleSelectionEffects, vehicleSelectionStores } from '../../stores/vehicleSelection';
import { CurrentFormikValues } from '../../types/formik';
import { equipmentEvents, equipmentStores } from '../../stores/equipment';

export const EquipmentModal = () => {
    const { t } = useTranslation();
    const isMobile = useMedia(sizes.laptop);
    const { values } = useFormikContext<CurrentFormikValues>();

    const isOpen = useUnit(modalsStores.isEquipmentModalOpen);
    const vinEquipmentIds = useUnit(vehicleSelectionModalStores.vinEquipmentIds);
    const vehicleTypes = useUnit(vehicleSelectionStores.vehicleTypes);
    const isLoading = useUnit(commonStores.isLoading);
    const currentConstructionTimeForRequest = useUnit(equipmentStores.currentConstructionTimeForRequest);
    const [options, setOptions] = useState<EquipmentPluginOptions | null>(null);

    const existingEquipment = useUnit(equipmentStores.existingEquipmentStore);

    const contractId = useUnit(contractStores.contractId);

    const [isAfterLoadingVsm, setAfterLoadingVsm] = useState(false);

    const { datECode, constructionPeriod, container, alternativeModelDatECode } = values;
    const datECodeRequestData = alternativeModelDatECode || datECode;

    useEffect(() => {
        equipmentEvents.setExistingEquipmentStore({
            appraisalEquipment: null,
            repairEquipment: null
        });
    }, [datECodeRequestData]);

    const setCurrentOptins = useCallback(() => {
        const existingEquipmentIds = {
            appraisalEquipment:
                existingEquipment.appraisalEquipment &&
                Object.values(existingEquipment.appraisalEquipment).flatMap(
                    equipments => equipments?.map(({ DatEquipmentId }) => DatEquipmentId) || []
                ),
            repairEquipment:
                existingEquipment.repairEquipment &&
                Object.values(existingEquipment.repairEquipment).flatMap(
                    equipments => equipments?.map(({ DatEquipmentId }) => DatEquipmentId) || []
                )
        };

        setOptions({
            onComplete: (result: EquipmentPluginResult) => {
                equipmentEvents.setExistingEquipmentStore(result);
            },
            requestData: {
                datECode: datECodeRequestData,
                constructionTime: currentConstructionTimeForRequest
                    ? +currentConstructionTimeForRequest
                    : +constructionPeriod,
                container
            },
            existingEquipmentIds,
            vinEquipmentIds,
            isComponent: true,
            inVSM: true,
            vin: values.vin
        });
    }, [
        currentConstructionTimeForRequest,
        datECodeRequestData,
        constructionPeriod,
        container,
        existingEquipment,
        vinEquipmentIds,
        values.vin
    ]);

    useEffect(() => {
        if (
            isAfterLoadingVsm &&
            datECodeRequestData &&
            datECodeRequestData !== vehicleTypes[0].key.padStart(2, '0') &&
            +constructionPeriod
        ) {
            setAfterLoadingVsm(false);
            setCurrentOptins();
        }
    }, [isAfterLoadingVsm, constructionPeriod, datECodeRequestData, contractId, setCurrentOptins, vehicleTypes]);

    useEffect(() => {
        const unwatch = modalsEvents.openEquipmentModal.watch(() => {
            if (constructionPeriod) {
                setCurrentOptins();
            } else {
                const unwatchConstructionPeriod = vehicleSelectionEffects.getConstructionPeriodsFx.doneData.watch(
                    () => {
                        setAfterLoadingVsm(true);
                        return unwatchConstructionPeriod();
                    }
                );

                modalsEvents.toggleIsVSMModalHasBeenOpened(true);
            }
        });

        return () => unwatch();
    }, [constructionPeriod, contractId, setCurrentOptins]);

    if (isOpen && !options && isLoading) {
        return <Preloader isLoading={isLoading} />;
    }

    return options ? (
        <Modal
            isAutoZIndex
            aria-label={t('equipmentModal')}
            bodyNoPadding
            visible={isOpen}
            onHide={modalsEvents.closeEquipmentModal}
            showHeader={false}
            fullWidth
            maxWidth={isMobile ? '100%' : '90vw'}
            bodyHeight={isMobile ? '100%' : '90vh'}
            fullScreen={isMobile}
        >
            <Equipment options={options} closeEquipmentModal={modalsEvents.closeEquipmentModal} />
        </Modal>
    ) : null;
};
