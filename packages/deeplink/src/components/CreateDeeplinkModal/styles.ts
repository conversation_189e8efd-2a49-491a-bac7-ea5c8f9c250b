import styled, { css } from 'styled-components/macro';

export const Modal = styled.div<{ isMobile: boolean }>`
    width: 290px;
    padding: 16px;
    position: absolute;
    top: 0;
    left: 56px;
    background-color: ${({ theme }) => theme.colors.white};
    border-radius: 16px;
    box-shadow:
        0 8px 32px ${({ theme }) => theme.colors.white},
        0 -8px 32px #aeaec099;
    z-index: 10000;

    ${({ isMobile }) =>
        isMobile &&
        css`
            position: fixed;
            width: 100%;
            height: calc(100vh - 220px);
            max-height: calc(100vh - 220px);
            bottom: 0;
            left: 0;
            right: 0;
            top: unset;
        `}
`;
