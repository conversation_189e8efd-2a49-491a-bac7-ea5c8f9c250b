import styled from 'styled-components/macro';
import { ButtonIcon } from '@wedat/ui-kit';

export const ButtonWrapper = styled.div`
    position: absolute;
    bottom: -25px;
    left: 0;
`;

export const StyledButtonIcon = styled(ButtonIcon)`
    min-width: 48px;
    width: 48px;
    background-color: ${({ theme }) => theme.colors.white};
    color: ${({ theme }) => theme.colors.dustBlue[900]};
    border: ${({ theme }) => `1px solid ${theme.colors.dustBlue[300]}`};

    &:hover,
    &:active {
        background-color: ${({ theme }) => theme.colors.white};
        color: ${({ theme }) => theme.colors.blue[800]};
        border: ${({ theme }) => `1px solid ${theme.colors.blue[400]}`};
    }

    &:focus-visible,
    &:focus {
        background-color: ${({ theme }) => theme.colors.white};
        color: ${({ theme }) => theme.colors.dustBlue[900]};
        border: ${({ theme }) => `1px solid ${theme.colors.dustBlue[300]}`};
    }
`;
