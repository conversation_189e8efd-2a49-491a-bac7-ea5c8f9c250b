import styled from 'styled-components/macro';
import { Text } from '@wedat/ui-kit';

export const Wrapper = styled.div`
    width: 100%;
    height: calc(100vh - 80px); //80px header height
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: ${({ theme }) => theme.colors.gray[100]};
`;

export const Container = styled.div`
    width: 100%;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 8px 32px 0 rgba(174, 174, 192, 0.2);
    border-radius: 16px;
    background-color: ${({ theme }) => theme.colors.white};
    padding: 40px 0;
`;

export const Info = styled.div`
    width: 100%;
    padding: 24px;
`;

export const Icon = styled.img`
    width: 100%;
`;

export const Title = styled(Text)<{ withMarginBottom: boolean }>`
    width: 100%;
    margin-bottom: ${({ withMarginBottom }) => (withMarginBottom ? '8px' : '0')};
    text-align: center;
    ${({ theme }) => theme.typography.defaultBold};
    color: ${({ theme }) => theme.colors.dustBlue[900]};
`;

export const Description = styled(Text)`
    width: 100%;
    text-align: center;
    ${({ theme }) => theme.typography.defaultBold};
    color: ${({ theme }) => theme.colors.dustBlue[900]};
`;
