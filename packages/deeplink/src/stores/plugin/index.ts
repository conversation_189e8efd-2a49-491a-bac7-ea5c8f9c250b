import { restore } from 'effector';
import { PluginOptions } from '../../types/plugin';
import { pluginDomain } from '../domain';

const { createStore, createEvent } = pluginDomain;

const initPlugin = createEvent<PluginOptions>();
const unmountPlugin = createEvent<PluginOptions>();
const pluginInited = createStore(false).on(initPlugin, () => true);

const pluginOptions = restore(initPlugin, null);
const deeplinkId = createStore<string | null>(null);

export const pluginEvents = {
    initPlugin,
    unmountPlugin
};

export const pluginStores = {
    pluginOptions,
    pluginInited,
    deeplinkId
};
