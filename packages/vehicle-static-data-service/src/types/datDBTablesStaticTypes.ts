// types directly from db for available static access

export type AssemblyGroupDescription_XGRHStaticSVG = {
    DTBA_BGR: number; // NUMBER(5, 0) -- graphic id
    DTBA_STNR: number; // NUMBER(14, 0) DEFAULT 0, -- locale id string for group description
    DTBA_BENN: string; // VARCHAR2(100 CHAR), -- description German
    // XLLA_TE?: string; // VARCHAR2(4000 BYTE) -- translation english
    DTBA_KZ_ART?: string; // CHAR(1 BYTE)  DEFAULT ' ', -- can be 'H' hail graphic tag
    DTBA_BZ_VON?: number; // NUMBER(5, 0), -- period begin
    DTBA_AV_VEKTOR?: string; // VARCHAR2(200 BYTE), -- condition FOR AV
    DTBA_UT_VEKTOR?: string; // VARCHAR2(200 BYTE), -- subModel filter
    // Example value: "DTBA_UT_VEKTOR": #1#2#3#4#5#6#7#8#9#10#11#12#13#14#15#16#17#18#19#20#21#22#25#26#27#28#30#31#33#34#
    DTBA_AV_VEKT_2?: string; // VARCHAR2(200 BYTE), -- condition FOR AV
    // Example value:
    // "DTBA_AV_VEKT_2": "NOT2904&16708",
    // "DTBA_AV_VEKT_2": "NOT(2904/16708)",
    // "DTBA_AV_VEKT_2": "84720/84721/84730/84731",
    DTBA_ZONE_VEKT?: string; // VARCHAR2(200 BYTE), -- filter and order for zone
    // Example value: "DTBA_ZONE_VEKT": #4-3#20-2#40-1#
    // XGRH_DATEN?: Uint8Array; // LONG RAW   -- XVG data Zipped
    SVG?: string; // -- unzipped XGRH_DATEN;
};

export type AssemblyGroupGraphic_XGRHStaticSVG = {
    DTBA_BGR: number; // NUMBER(5, 0) -- graphic id
    // DTBA_STNR: number; // NUMBER(14, 0) DEFAULT 0, -- locale id string for group description
    DTBA_BENN: string; // VARCHAR2(100 CHAR), -- description German
    // XLLA_TE?: string; // VARCHAR2(4000 BYTE) -- translation english
    DTBA_KZ_ART?: string; // CHAR(1 BYTE)  DEFAULT ' ', -- can be 'H' hail graphic tag
    DTBA_BZ_VON?: number; // NUMBER(5, 0), -- period begin
    DTBA_AV_VEKTOR?: string; // VARCHAR2(200 BYTE), -- condition FOR AV
    DTBA_AV_VEKT_2?: string; // VARCHAR2(200 BYTE), -- condition FOR AV
    // Example value:
    // "DTBA_AV_VEKT_2": "NOT2904&16708",
    // "DTBA_AV_VEKT_2": "NOT(2904/16708)",
    // "DTBA_AV_VEKT_2": "84720/84721/84730/84731",
    DTBA_UT_VEKTOR?: string; // VARCHAR2(200 BYTE), -- subModel filter
    // Example value: "DTBA_UT_VEKTOR": #1#2#3#4#5#6#7#8#9#10#11#12#13#14#15#16#17#18#19#20#21#22#25#26#27#28#30#31#33#34#
    DTBA_ZONE_VEKT?: string; // VARCHAR2(200 BYTE), -- filter and order for zone
    // Example value: "DTBA_ZONE_VEKT": #4-3#20-2#40-1#
    XGRH_DATEN?: Uint8Array; // LONG RAW   -- XVG data Zipped
    XGR3_DATEN?: Uint8Array; // LONG RAW   -- XVG data Zipped
    SVG?: string; // -- unzipped XGRH_DATEN;
};

// dvn info
export type DvnInfo_DatrDTAB_Static = {
    DTAB_BGR: number; //        NUMBER(5, 0), -- graphic id
    DTAB_POS: number; //        NUMBER(5, 0),
    DTAB_POLYGON: number; //    NUMBER(5, 0)  DEFAULT 0,
    // Example in SVG id: "DATID_016007"   "016" - DTAB_POS  "007" - DTAB_POLYGON

    DTAB_DVNGR: number; //      NUMBER(5, 0)  DEFAULT 0, -- group for selection

    DTAB_TH_DVN_BEN: string; // VARCHAR2(70 CHAR), - description
    DTAB_STNR_1: number; //     NUMBER(14, 0) DEFAULT 0, -- locale string id
    // XLLA_TE?: string; // VARCHAR2(4000 BYTE) -- translation english
    // not fell every there need to be clarify when used
    DTAB_STNR_2: number | null; //     NUMBER(14, 0), -- locale string id 2
    DTAB_FN_TE: string | null; //      VARCHAR2(100 CHAR), // fill with DTAB_STNR_2

    DTAB_UMF_STUFE: string | null; //  VARCHAR2(10 CHAR), // some groups can be '85' '851' 'null' '801/8021'

    DTAB_DVN_LI: number | null; //     NUMBER(10, 0), - dvn Left
    DTAB_DVN_RE: number | null; //     NUMBER(10, 0), - dvn right

    DTAB_KZ_SEITE: string; //   CHAR(1 BYTE)  DEFAULT ' ', // lacquerMethods can be 'h' or 'e' or ' '

    DTAB_RC_LI_1: string; //    CHAR(1 BYTE)  DEFAULT ' ',
    DTAB_RC_LI_2: string; //    CHAR(1 BYTE)  DEFAULT ' ',
    DTAB_RC_LI_3: string; //    CHAR(1 BYTE)  DEFAULT ' ',
    DTAB_RC_LI_4: string; //    CHAR(1 BYTE)  DEFAULT ' ',
    DTAB_RC_RE_1: string; //    CHAR(1 BYTE)  DEFAULT ' ',
    DTAB_RC_RE_2: string; //    CHAR(1 BYTE)  DEFAULT ' ',
    DTAB_RC_RE_3: string; //    CHAR(1 BYTE)  DEFAULT ' ',
    DTAB_RC_RE_4: string; //    CHAR(1 BYTE)  DEFAULT ' ',
};

export interface SymbolsDvn_DatrDTSD_Static {
    DTSD_BGR: number; // NUMBER(5),
    DTSD_DTSY_LNR: number; // NUMBER(10)
    DTSD_LNR: number; // NUMBER(5),
    DTSD_SORT: number; // NUMBER(5),

    DTSD_DVNGR: number; // NUMBER(5),

    DTSD_TH_DVN_BEN: string; // VARCHAR2(70 char),
    DTSD_STNR_1: number; // NUMBER(14),
    DTSD_STNR_2: number; // NUMBER(14),
    DTSD_FN_TE: string; // VARCHAR2(100 char),

    DTSD_UMF_STUFE: string; // VARCHAR2(10 char), // some groups can be '85' '851' 'null' '801/8021'

    DTSD_DVN_LI: number; // NUMBER(10),
    DTSD_DVN_RE: number; // NUMBER(10),

    DTSD_KZ_LA_METH: string; // CHAR,  // LacquerMethods

    DTSD_RC_LI_1: string; // CHAR,
    DTSD_RC_LI_2: string; // CHAR,
    DTSD_RC_LI_3: string; // CHAR,
    DTSD_RC_LI_4: string; // CHAR,
    DTSD_RC_RE_1: string; // CHAR,
    DTSD_RC_RE_2: string; // CHAR,
    DTSD_RC_RE_3: string; // CHAR,
    DTSD_RC_RE_4: string; // CHAR,
}

export type ManufacturerImagesGroup_DatrDTBG_Static = {
    DTBG_BGR: number; // NUMBER(5),
    DTBG_XGRZ_LNR: number; // NUMBER(5),
    DTBG_SORT: number; // NUMBER(5) default 0,

    DTBG_AV_VEKTOR: string; // VARCHAR2(200),
    DTBG_BZ_VON: number; // NUMBER(5),
    DTBG_BZ_BIS: number; // NUMBER(5),

    DTBG_HT_REF: number; // NUMBER(5),

    DTBG_STNR: number; // NUMBER(14),
    DTBG_HW_TEXT: string; // VARCHAR2(200 char),
    DTBG_BEM: string; // VARCHAR2(200 char),
};

export type PartInfo_DatrLEHB_Static = {
    LEHB_DVN: number; //        NUMBER(10, 0),
    LEHB_RC: string; //         CHAR(1 BYTE),  -- can be 'E' 'I' 'R'

    LEHB_BZ_VON: number; //     NUMBER(5, 0),  -- construction time FROM
    LEHB_BZ_BIS: number; //     NUMBER(5, 0) DEFAULT 0, -- construction time to

    LEHB_AV: number; //         NUMBER(10, 0), -- av equipment filter
    LEHB_AV_KO: string; //      VARCHAR2(45 BYTE), -- av equipment filter
    LEHB_ANZ_AV: number; //     NUMBER(5, 0), -- av equipment filter

    LEHB_AV_CODE_KO: string | null; //  - manufacturer  conditions
    LEHB_LNR_AV_KO: number; //
    LEHB_AV_KO_MI: string | null; //
    LEHB_UT: string; // subtypes example value: '002,004,005,006'   fBitsToFormattedString(LEHB_UT_KO,'fm000')

    LEHB_GLAS: string; //       CHAR(1 BYTE) DEFAULT ' ',  'J', 'U'

    LEHD_HUA_2: number; //  NUMBER(10, 0), spare part internal id

    DEET_LNR_WMERK: number | null; //  NUMBER(5, 0), - material 8004  8001 8007 8006 8008 (null) 8010 8009 8002 8005

    DEET_ET: string; // partNumber

    DETE_STNR: number; //      NUMBER(14, 0) DEFAULT 0, -- string id translation
    DETE_BENN: string; //      VARCHAR2(110 CHAR), -- description
    // XLLA_TE: string; // VARCHAR2(4000 BYTE), -- translation string
    // LLPO_KZ_AUSGEBA: string; // CHAR,            -- 'M' or ' '
    // LLPO_APA_HA_L: string; //   CHAR         default ' ',   -- LacquerPositionTypes  1 3 4 s
    // LLPO_ET_BAUART: string; //  CHAR         default ' ',   -- lacquerPartType '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', 'E', 'H'
    // LLPO_KZ_OPTI_1: string; //  CHAR         default ' ',   -- optimization "etBauartOptKz": optimizationType ' ' '1' '3'
};

export type LocaleString_DatrXLLA_Static = {
    XLLA_STNR: number; // NUMBER(14, 0), -- string id translation
    // XLLA_SPR: string; // CHAR(3 BYTE),
    // XLLA_LKZ_SPR: string; // CHAR(3 BYTE),
    XLLA_TE: string; // VARCHAR2(4000 BYTE), -- translation string
};

export type AdditionalRepairProcessGroups_DatrDTTG_Static = {
    DTTG_TEGR: number; //     NUMBER(5),  from 500 ... to 550  is additionalRepairProcessGroups
    DTTG_STNR_1: number; //   NUMBER(14) default 0,
    DTTG_TEGR_TE: string; //  VARCHAR2(50 char),   -- name
};

export type SymbolsSVG_DatrDTSY_Static = {
    DTSY_LNR: number; // NUMBER(10), primary key
    DTSY_BENN: string; // VARCHAR2(100 char), name/description of the symbol
    DTSY_DATEN: string; // BLOB, SVG data
};

export type AdditionalRepairProcess_DatrDTHD_Static = {
    DTHD_DVN: number; //      NUMBER(10),

    DTHD_RC_1: string; //     CHAR       default ' ',
    DTHD_RC_2: string; //     CHAR       default ' ',
    DTHD_RC_3: string; //     CHAR       default ' ',
    DTHD_RC_4: string; //     CHAR       default ' ',
    DTHD_KZ_PREIS: string; // CHAR       default ' ',
    DTHD_KZ_ZEIT: string; //  CHAR       default ' ',

    DTHD_TEGR: number; //     NUMBER(5)  default 0,  - group  additionalRepairProcessGroups

    DTHD_STNR: number; //     NUMBER(14) default 0,
    DTHD_BENN: string; //     VARCHAR2(100 char),
};

export type WorkInfo_DatrLAHB_Static = {
    LAHB_DVN: number; //        NUMBER(10),
    LAHB_RC: string; //         CHAR,
    LAHB_AV: number; //         NUMBER(10),
    LAHB_ANZ_AV: number; //     NUMBER(5),
    LAHB_LNR_AV_KO: number; //  NUMBER(5),
    LAHB_BZ_VON: number; //     NUMBER(5),
    LAHB_BZ_BIS: number; //     NUMBER(5) default 0,
    // LAHB_HUA_2: number; //      NUMBER(10),

    LAHB_AV_KO: string | null; //      VARCHAR2(45),
    LAHB_UT: string; //   // subtypes example value: '002,004,005,006'   fBitsToFormattedString(LAHB_UT_KO,'fm000')

    // LAHD_AP: string; //         CHAR(15)     default ' ',   -- worknumber
    LAHD_APA: string; //        CHAR         default ' ',   -- origin  available value M E I A 6 5 K 4 T

    LAHD_MERK: string; //       CHAR(2)      default ' ',   -- attribute
    LAHD_AW: number; //         NUMBER(9, 3) default 0,     -- workTime

    LAHD_KZ_RS: number | null; //      NUMBER(5),                  -- workLevel
    LAHD_AW_G: number | null; //       NUMBER(9, 3),               -- workTimeInternational
    LAHD_KZ_ARBA: number; //    NUMBER(5)    default 0,     -- workType
    LAHD_AP_ZEILE: number | null; //   NUMBER(5),                  -- rownumber
};

export type LacquerInfo_LLHB_Static = {
    LLHB_DVN: number; //        NUMBER(10),

    LLHB_RC: string; //         CHAR      default ' ',
    LLHB_AV: number; //         NUMBER(10),
    LLHB_ANZ_AV: number; //     NUMBER(5),
    LLHB_LNR_AV_KO: number; //  NUMBER(5),
    LLHB_BZ_VON: number; //     NUMBER(5),
    LLHB_BZ_BIS: number; //     NUMBER(5) default 0,
    LLHB_AV_KO: string; //      VARCHAR2(45),

    LLHB_HUA_2: number; //      NUMBER(10),

    LLPO_KZ_AUSGEBA: string; // CHAR,            -- 'M' or ' '
    LLPO_APA_HA_L: string; //   CHAR         default ' ',   -- LacquerPositionTypes  1 3 4 s
    LLPO_ET_BAUART: string; //  CHAR         default ' ',   -- lacquerPartType '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', 'E', 'H'
    LLPO_KZ_OPTI_1: string; //  CHAR         default ' ',   -- optimization "etBauartOptKz": optimizationType ' ' '1' '3'
};
