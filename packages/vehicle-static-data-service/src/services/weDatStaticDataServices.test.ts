// import { GetCountryVehicleTypeStaticData } from '../types/weDatStaticDataServicesTypes';
// import { MainVehicleType } from '../types/weDatStaticDataTypes';
// import { setIsBrowser } from './commonRequest';
// import { getCountryVehicleTypeStaticData } from './weDatStaticDataServices';

// // Example request:
// // https://grapa-data.fra1.digitaloceanspaces.com/grapa-service2/1/150/21/1_150_21_ENG_D.json

describe('weDatStaticDataServices', () => {
    test.todo('todo weDatStaticDataServices');
    // it.skip('getCountryVehicleTypeStaticData', async () => {
    // setIsBrowser(false);
    // // setDefaultOption({ getDatToken: () => '' });
    // const mainVehicleType: MainVehicleType = {
    //     vehicleType: 2,
    //     manufacturer: 280,
    //     mainType: 112
    // };
    // const params: GetCountryVehicleTypeStaticData = {
    //     mainVehicleType,
    //     country: 'D'
    // };
    // const result = await getCountryVehicleTypeStaticData(params);
    // expect(result?.mainVehicleType.mainType).toEqual(mainVehicleType.mainType);
    //     }, 100_000);
});
