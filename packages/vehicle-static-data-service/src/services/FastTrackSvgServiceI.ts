// FastTrackSVG Service
//
// This service provides access to the FastTrack SVG data:
// 1. A single JSON response containing a list of all available files, along with their graphic types and metadata.
// 2. Individual files accessible.

export interface FastTrackSvgServiceI {
    // Retrieves a list of available SVG data.
    getFtSVGConfigAndList: () => Promise<FtSVGConfigAndList>;

    // Retrieves SVG values based on specified parameters, base on ftSVGConfigAndList.
    getFtSvgValues: (ftSVGConfigAndList: FtSVGConfigAndList, param: GetFastTrackSvgParams) => FastTrackSvgValue[];

    // Fetches a specific SVG file.
    getFtSVG: (ftListAvailableSvgUrl: string) => Promise<string>;
}

export const fastTrackSvg = 'fastTrackSvg';
export const ftDefaultVehicleType = 'ftDefaultVehicleType';

// Represents the service configuration and available SVG resources.
export type FtSVGConfigAndList = {
    ftGraphicTypes: FtSvgGraphicType[];
    ftVehicleTypes: FtSvgVehicleType[];
    ftListAvailableSvgUrl: string[];
};

// Defines possible vehicle graphic types base on perspectives.
export type FtSvgGraphicType =
    | 'topWithSides' // Default
    | 'top'
    | 'left'
    | 'right'
    | 'front'
    | 'back'
    | 'frontLeft'
    | 'frontRight'
    | 'backLeft'
    | 'backRight'
    | string;

// Defines possible vehicle types.
// todo Require to fix because look strange to se in this list 'RRover' 'ID4'
export type FtSvgVehicleType =
    | 'ftDefaultVehicleType'
    | '2_doors'
    | '3_doors'
    | '4_doors'
    | '5_doors'
    | 'SUV'
    | 'SUV_coupe'
    | 'PickUp'
    | 'Fixed_capote'
    | 'Cross'
    | 'MV'
    | 'SW'
    | 'RRover'
    | 'EQA'
    | 'ID4'
    | 'all'
    | string;

export type GetFastTrackSvgParams = {
    // versionId?: string; // Can be used as a filename for custom configurations
    datECode: string;
    ftVehicleType?: FtSvgVehicleType; // If undefined, returns a list of available files
    // ftGraphicType?: FtSvgGraphicType; // If undefined, returns a list of available files
};

export type FastTrackSvgValue = {
    // versionId?: string; // Can be used as a filename for custom configurations
    datECodePrefix?: string;
    ftGraphicType?: FtSvgGraphicType; // 'topWithSides' is the default graphic type
    ftVehicleType?: FtSvgVehicleType; // 'ftDefaultVehicleType' is the default vehicle type
    relevance?: number; // 1 basic then more then better
    svgUrl: string;
    svg?: string; // SVG content as a string
};

// SVG File Path Structure (ftListAvailableSvgUrl):
//
// An SVG file path is composed of the following elements:
// 1. versionId
// 2. Vehicle type (2-digit code, zero-padded: "01", "02", etc.)
// 3. Manufacturer (3-digit code, zero-padded: "190", "380", etc.)
// 4. Model (3-digit code, zero-padded: "151", "152", etc.)
// 5. SubModel (3-digit code, zero-padded: "151", "152", etc.)
// 6. Constant "fastTrackSvg" (indicating an SVG data folder)
// 7. FtSvgVehicleType
// 8. FtSvgGraphicType
// 9. ".svg" file extension

// // Example Configuration
// const exampleFtSVGConfigAndList: FtSVGConfigAndList = {
//     ftGraphicTypes: ["topWithSides", "top", "left"],
//     ftVehicleTypes: ["ftDefaultVehicleType", "2_doors", "3_doors"],
//     ftListAvailableSvgUrl: [
//         '01/fastTrackSvg/ftDefaultVehicleType/topWithSides.svg',
//         '01/190/fastTrackSvg/ftDefaultVehicleType/top.svg',
//         '01/190/180/fastTrackSvg/ftDefaultVehicleType/left.svg',
//         '01/190/180/380/fastTrackSvg/ftDefaultVehicleType/right.svg',

//         '01/fastTrackSvg/ftDefaultVehicleType/topWithSides.svg',
//         '01/fastTrackSvg/ftDefaultVehicleType/front.svg',
//         '01/fastTrackSvg/ftDefaultVehicleType/rear.svg',
//         '01/fastTrackSvg/ftDefaultVehicleType/frontLeft.svg',
//         '01/fastTrackSvg/ftDefaultVehicleType/frontRight.svg',
//         '01/fastTrackSvg/ftDefaultVehicleType/topWithSides.svg',

//         '01/fastTrackSvg/2_doors/topWithSides.svg',
//         '01/fastTrackSvg/2_doors/front.svg',
//         '01/fastTrackSvg/2_doors/rear.svg',
//         '01/fastTrackSvg/2_doors/frontLeft.svg',
//         '01/fastTrackSvg/2_doors/frontRight.svg',
//         '01/fastTrackSvg/2_doors/topWithSides.svg',

//         '01/190/150/001/fastTrackSvg/3_doors/topWithSides.svg',
//         '01/190/150/001/fastTrackSvg/3_doors/front.svg',
//         '01/190/150/001/fastTrackSvg/3_doors/rear.svg',
//         '01/190/150/001/fastTrackSvg/3_doors/frontLeft.svg',
//         '01/190/150/001/fastTrackSvg/3_doors/frontRight.svg',
//         '01/190/150/001/fastTrackSvg/3_doors/topWithSides.svg'
//     ]
// };
