import { ButtonIcon } from '@wedat/ui-kit';
import { media } from '@wedat/ui-kit/mediaQueries';
import styled from 'styled-components';

export const FormWrapper = styled.div`
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 24px;
    margin-top: 12px;
    transition: all 0.1s linear;

    border: ${({ theme }) => `2px dashed ${theme.colors.dustBlue['50']}`};

    &:hover {
        border: ${({ theme }) => `2px dashed ${theme.colors.dustBlue['100']}`};
    }
`;

export const InputWrapper = styled.div`
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
`;
export const FlexWrapper = styled.div`
    width: 100%;
    display: flex;
    gap: 12px;
`;

export const StyledButtonIcon = styled(ButtonIcon)`
    min-width: 48px;
    width: 48px;
    flex: none;

    ${media.phoneMedium`
        width: 35px;
    `}
`;
