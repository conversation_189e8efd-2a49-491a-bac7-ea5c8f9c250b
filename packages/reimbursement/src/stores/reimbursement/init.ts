import { sample } from 'effector';
import { AxiosError } from 'axios';

import { NotificationParams, toastEffects } from '@dat/shared-models/smart-components/Toast';

import { filterEvents, filterStores } from '../filters';
import { pluginEvents } from '../plugin';
import { reimbursementEffects, reimbursementEvents } from '.';

const { getReimbursementListFx, uploadFileFx, processReimbursementFileFx } = reimbursementEffects;
const { resetReimbursementList } = reimbursementEvents;
const { getReimbursementFiles, toggleIsOpenReimbursement } = filterEvents;
const { isOpenReimbursement } = filterStores;

//for Standalone usage
sample({
    clock: pluginEvents.initPlugin,
    filter: options => !options.isComponent,
    target: getReimbursementFiles
});

//fetch list
sample({
    clock: isOpenReimbursement,
    filter: isOpenReimbursement => !!isOpenReimbursement,
    target: getReimbursementFiles
});

sample({
    clock: getReimbursementFiles,
    target: getReimbursementListFx
});

//fetch list after upload new file
sample({
    clock: uploadFileFx.doneData,
    target: getReimbursementListFx
});

sample({
    clock: uploadFileFx.failData,
    fn: failData => {
        const error = failData as AxiosError<{ error?: { message?: string } }>;

        const serverMessage = error.response?.data?.error?.message;

        return {
            message: {
                namespace: 'inbox',
                key: serverMessage || error.message || 'reimbursement.upload.failed'
            }
        } as NotificationParams;
    },
    target: toastEffects.showErrorToastFx
});

sample({
    clock: uploadFileFx.doneData,
    fn: () => {
        return {
            message: {
                namespace: 'inbox',
                key: 'reimbursement.upload.success'
            }
        } as NotificationParams;
    },
    target: toastEffects.showSuccessToastFx
});

sample({
    clock: processReimbursementFileFx.failData,
    fn: failData => {
        const error = failData as AxiosError<{ error?: { message?: string } }>;

        const serverMessage = error.response?.data?.error?.message;

        return {
            message: {
                namespace: 'inbox',
                key: serverMessage || error.message || 'reimbursement.process.failed'
            }
        } as NotificationParams;
    },
    target: toastEffects.showErrorToastFx
});

sample({
    clock: processReimbursementFileFx.doneData,
    fn: () => {
        return {
            message: {
                namespace: 'inbox',
                key: 'reimbursement.process.success'
            }
        } as NotificationParams;
    },
    target: toastEffects.showSuccessToastFx
});

sample({
    clock: processReimbursementFileFx.doneData,
    target: [resetReimbursementList, toggleIsOpenReimbursement]
});

sample({
    clock: filterStores.activeTab,
    filter: data => !!data.value,
    target: getReimbursementListFx
});
