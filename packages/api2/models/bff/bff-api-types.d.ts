declare namespace DAT2 {
    namespace BFF {
        namespace DTO {
            export interface AssignPartnerToContractDto {
                contractId: number;
                partner: PartnerDto;
            }
            export interface AttachmentDeleteResponse {
                deleted: boolean;
            }
            export interface AttachmentFolderResponse {
                ETag: string;
                Location: string;
                key: string;
                Key: string;
                Bucket: string;
            }
            export interface AttachmentResponse {
                type: string;
                link: string;
                extension: string;
                id: number;
            }
            export interface AttachmentsConvertPDFResponse {
                responseObj: ResponseObj;
                resXMLText: string;
            }
            export interface AttachmentsResponse {
                id: number;
                fileName: string;
                mimeType: string;
                dataSize: number;
                binaryData: string;
                published: string;
                uploaded: string;
                documentType: string;
                documentID: number;
                attachmentType: string;
                uploaderId: number;
                uploaderCustomerNumber: string;
                uploaderRole: string;
                uploaderName: string;
                uploaderUserId: number;
                uploaderUserLogin: string;
                uploaderUserName: string;
            }
            export interface CreateDialogDto {
                contractId: number;
                dialogId: string;
                message: MessageDto;
                receiver: string;
                members: {
                    [key: string]: any;
                }[];
                type: {
                    [key: string]: any;
                };
            }
            export interface CreatePluginDto {
                pluginName: string;
            }
            export interface CredentialsDto {
                customerNumber: string;
                user: string;
                password: string;
                client: boolean;
                customerOf: string;
                interfacePartnerNumber: string;
                interfacePartnerSignature: string;
            }
            export interface EmbedAiPhotosResponse {
                return: string;
            }
            export interface ErrorResponse {
                message: string;
                statusCode: number;
                error?: string;
            }
            export interface GetMailPrice {
                contractId: number;
                printoutId: string;
                recipients: RecipientItem[];
                sender: Sender;
            }
            export interface GetSubjectsDto {
                networkOwnerCustomerNumber: string;
            }
            export interface GetTranslationsDto {
                language: string;
                namespaces: string[];
            }
            export interface GetVehicleTypesDto {
                restriction: string;
                constructionTimeFrom: number;
                constructionTimeTo: number;
            }
            export interface HistoryEntity {}
            export interface InfosEntity {}
            export interface LoginDto {
                token: string;
                credentials: CredentialsDto;
            }
            export interface MessageDto {
                id: string;
                sender: string;
                text: string;
                created: number;
                read: boolean;
            }
            export interface PartnerDto {
                role: string;
                partnerId: number;
                customerNumber: number;
            }
            export interface ReadMessagesDto {
                contractId: number;
                dialogId: {
                    [key: string]: any;
                };
            }
            export interface RecipientItem {
                AddressLine1: string;
                AddressLine2: string;
                AddressLine3: string;
                CAP: string;
                City: string;
                LocalCode: string;
                Country: string;
                DocumentNumber: string;
            }
            export interface ResponseObj {
                return: boolean;
            }
            export interface SendMail {
                contractId: number;
                printoutId: string;
                recipients: RecipientItem[];
                sender: Sender;
                sessionGUID: string;
                token: string;
            }
            export interface SendMessageDto {
                contractId: number;
                dialogId: string;
                message: MessageDto;
                receiver: string;
            }
            export interface Sender {
                test: string;
                name: string;
                address: string;
                zip: string;
                city: string;
                province: string;
                country: string;
                mailService: string;
            }
            export interface UnAssignPartnerFromContractDto {
                contractId: number;
                rolePartner: string;
            }
            export interface UpdateNotificationsDto {
                ids: number[];
            }
            export interface UpdatePluginTranslationsDto {
                pluginName: string;
                locale: string;
                translations: string;
            }
            export interface UpdateSubjectsDto {
                subjectType: string;
                customerNumber: {
                    [key: string]: any;
                };
                toAdd: string[];
                toUpdate: string[];
                toDelete: string[];
            }
            export interface ValidateTokenDto {
                token: string;
            }
        }
    }
}
declare namespace DAT2 {
    namespace BFF {
        namespace AddressBookControllerGetSubject {
            namespace Parameters {
                export type CustomerNumber = number;
                export type SubjectType = string;
            }
            export interface PathParameters {
                customerNumber: Parameters.CustomerNumber;
            }
            export interface QueryParameters {
                subjectType: Parameters.SubjectType;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace AddressBookControllerGetSubjects {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace AddressBookControllerGetZipCode {
            namespace Parameters {
                export type Area = string;
                export type City = string;
            }
            export interface QueryParameters {
                area: Parameters.Area;
                city: Parameters.City;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace AddressBookControllerUpdateSubject {
            export type RequestBody = DAT2.BFF.DTO.UpdateSubjectsDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace AiGalleryControllerEmbedAIPhotos {
            namespace Responses {
                export type $200 = DAT2.BFF.DTO.EmbedAiPhotosResponse;
                export type $400 = DAT2.BFF.DTO.ErrorResponse;
                export type $403 = DAT2.BFF.DTO.ErrorResponse;
                export type $500 = DAT2.BFF.DTO.ErrorResponse;
            }
        }
        namespace AspisControllerProxy {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace AttachmentControllerConvertToPdf {
            namespace Parameters {
                export type ContractId = number;
                export type TemplateId = number;
                export type UploadId = number;
            }
            export interface QueryParameters {
                contractId: Parameters.ContractId;
                templateId: Parameters.TemplateId;
                uploadId: Parameters.UploadId;
            }
            export type RequestBody = string[];
            namespace Responses {
                export type $200 = DAT2.BFF.DTO.AttachmentsConvertPDFResponse;
                export type $400 = DAT2.BFF.DTO.ErrorResponse;
                export type $403 = DAT2.BFF.DTO.ErrorResponse;
                export type $500 = DAT2.BFF.DTO.ErrorResponse;
            }
        }
        namespace AttachmentControllerDeleteAttachment {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export type $200 = DAT2.BFF.DTO.AttachmentDeleteResponse;
                export type $400 = DAT2.BFF.DTO.ErrorResponse;
                export type $403 = DAT2.BFF.DTO.ErrorResponse;
                export type $500 = DAT2.BFF.DTO.ErrorResponse;
            }
        }
        namespace AttachmentControllerGetAttachemnts {
            namespace Parameters {
                export type ContractId = number;
            }
            export interface QueryParameters {
                contractId: Parameters.ContractId;
            }
            namespace Responses {
                export type $200 = DAT2.BFF.DTO.AttachmentsResponse[];
                export type $400 = DAT2.BFF.DTO.ErrorResponse;
                export type $403 = DAT2.BFF.DTO.ErrorResponse;
                export type $500 = DAT2.BFF.DTO.ErrorResponse;
            }
        }
        namespace AttachmentControllerGetAttachments {
            namespace Parameters {
                export type AttachmentIds = string;
                export type ContractId = number;
                export type IsSVDExportEnabled = string;
                export type PrintoutIds = string;
            }
            export interface QueryParameters {
                contractId: Parameters.ContractId;
                attachmentIds: Parameters.AttachmentIds;
                printoutIds: Parameters.PrintoutIds;
                isSVDExportEnabled: Parameters.IsSVDExportEnabled;
            }
            namespace Responses {
                export interface $200 {}
                export type $400 = DAT2.BFF.DTO.ErrorResponse;
                export type $403 = DAT2.BFF.DTO.ErrorResponse;
                export type $500 = DAT2.BFF.DTO.ErrorResponse;
            }
        }
        namespace AttachmentControllerUploadJsonFileToDo {
            namespace Parameters {
                export type FolderPath = string;
            }
            export interface PathParameters {
                folderPath: Parameters.FolderPath;
            }
            namespace Responses {
                export type $200 = DAT2.BFF.DTO.AttachmentFolderResponse;
                export type $400 = DAT2.BFF.DTO.ErrorResponse;
                export type $403 = DAT2.BFF.DTO.ErrorResponse;
                export type $500 = DAT2.BFF.DTO.ErrorResponse;
            }
        }
        namespace AttachmentControllerUploadRelease {
            namespace Responses {
                export type $200 = DAT2.BFF.DTO.AttachmentResponse;
                export type $400 = DAT2.BFF.DTO.ErrorResponse;
                export type $403 = DAT2.BFF.DTO.ErrorResponse;
                export type $500 = DAT2.BFF.DTO.ErrorResponse;
            }
        }
        namespace AuthControllerCreateInternalCustomer {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace AuthControllerFindInternalCustomers {
            namespace Parameters {
                export type CustomerId = string;
            }
            export interface PathParameters {
                customerId: Parameters.CustomerId;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace AuthControllerInternalCustomerLogin {
            export type RequestBody = DAT2.BFF.DTO.CredentialsDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace AuthControllerLogout {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace AuthControllerRefreshToken {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ChatControllerCreateDialog {
            export type RequestBody = DAT2.BFF.DTO.CreateDialogDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ChatControllerReadMessages {
            export type RequestBody = DAT2.BFF.DTO.ReadMessagesDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ChatControllerSendMessage {
            export type RequestBody = DAT2.BFF.DTO.SendMessageDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ContractControllerAssignPartnerToContract {
            export type RequestBody = DAT2.BFF.DTO.AssignPartnerToContractDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ContractControllerCreateClaimWithAnotherAccount {
            export type RequestBody = string[];
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ContractControllerCreateOrUpdateContract {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ContractControllerGetContract {
            namespace Parameters {
                export type ContractId = number;
                export type CustomerNumber = number;
            }
            export interface QueryParameters {
                contractId: Parameters.ContractId;
                customerNumber: Parameters.CustomerNumber;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace ContractControllerGetPostPayment {
            export type RequestBody = DAT2.BFF.DTO.GetSubjectsDto;
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace ContractControllerUnAssignPartnerFromContract {
            export type RequestBody = DAT2.BFF.DTO.UnAssignPartnerFromContractDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ContractControllerUploadAttachment {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ContractControllerUploadPhoto {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ContractValuationControllerPhoto {
            namespace Parameters {
                export type ContractId = string;
                export type CustomerName = string;
                export type CustomerNumber = string;
            }
            export interface QueryParameters {
                contractId: Parameters.ContractId;
                customerNumber: Parameters.CustomerNumber;
                customerName: Parameters.CustomerName;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace CustomerControllerCreateOrUpdateAppointment {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace CustomerControllerCreateOrUpdateExpirationDate {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace CustomerControllerGetAppointments {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace CustomerControllerGetIncrementalData {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace CustomerControllerGetProfiles {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace CustomerControllerUpdateIncrementalData {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace DeeplinkControllerAuthorize {
            namespace Parameters {
                export type UrlEncrypted = string;
            }
            export interface PathParameters {
                urlEncrypted: Parameters.UrlEncrypted;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace DeeplinkControllerComplete {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace DeeplinkControllerGenerateDeeplink {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace DomusControllerGetDomusTime {
            namespace Parameters {
                export type DateECode = string;
                export type Doors = string;
            }
            export interface PathParameters {
                dateECode: Parameters.DateECode;
                doors: Parameters.Doors;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace DomusControllerGetVehicleType {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace EfficiencyControllerPhoto {
            namespace Parameters {
                export type TemplateId = string;
                export type TimeFrom = string;
                export type TimeTo = string;
            }
            export interface QueryParameters {
                templateId: Parameters.TemplateId;
                timeFrom: Parameters.TimeFrom;
                timeTo: Parameters.TimeTo;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace FuelCodesControllerFindByFitCode {
            namespace Parameters {
                export type Code = string;
            }
            export interface PathParameters {
                code: Parameters.Code;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace HistoryControllerCreate {
            export type RequestBody = DAT2.BFF.DTO.HistoryEntity;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace HistoryControllerFilter {
            namespace Parameters {
                export type Username = string;
            }
            export interface QueryParameters {
                username: Parameters.Username;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace HistoryControllerFindAll {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace HistoryControllerGroupHistory {
            namespace Parameters {
                export type ClaimId = number;
            }
            export interface QueryParameters {
                claimId: Parameters.ClaimId;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace InfosControllerCreate {
            export type RequestBody = DAT2.BFF.DTO.InfosEntity;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace InfosControllerFindAll {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace InfosControllerFindByNetworkTypes {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace InfosControllerFindOne {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace InfosControllerRemove {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace InfosControllerUpdate {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace MaintenanceControllerFindByDatECode {
            namespace Parameters {
                export type DatECode = string;
            }
            export interface PathParameters {
                datECode: Parameters.DatECode;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace MaintenanceControllerUploadFile {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace MetricsControllerMetrics {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace MyClaimProxyControllerPhoto {
            namespace Parameters {
                export type AttachmentId = number;
                export type ClaimId = number;
                export type FolderId = number;
                export type Thumbnail = boolean;
                export type Token = string;
            }
            export interface QueryParameters {
                token: Parameters.Token;
                claimId: Parameters.ClaimId;
                thumbnail: Parameters.Thumbnail;
                folderId: Parameters.FolderId;
                attachmentId: Parameters.AttachmentId;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace MyClaimProxyControllerProxy {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace NotificationsControllerCreateNotification {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace NotificationsControllerCreateNotificationsByCountry {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace NotificationsControllerGetNotifications {
            namespace Parameters {
                export type CustomerNumber = string;
                export type UserName = string;
            }
            export interface PathParameters {
                customerNumber: Parameters.CustomerNumber;
            }
            export interface QueryParameters {
                userName: Parameters.UserName;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace NotificationsControllerUpdateNotificationsByIds {
            export type RequestBody = DAT2.BFF.DTO.UpdateNotificationsDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace PartnersControllerAssignPartner {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace PartnersControllerCreatePartner {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace PartnersControllerGetAssignerPartner {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace PartnersControllerGetPartners {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace PartnersControllerUnAssignPartner {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace PartnersControllerUploadFile {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace PlateSearchItalyControllerProxy {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace PostaProntaControllerGetMailPrice {
            export type RequestBody = DAT2.BFF.DTO.GetMailPrice;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace PostaProntaControllerSendMail {
            export type RequestBody = DAT2.BFF.DTO.SendMail;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ReleaseControllerCreate {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ReleaseControllerCreateFeature {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ReleaseControllerDeleteFeature {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace ReleaseControllerFindOne {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace ReleaseControllerGetFeatureById {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace ReleaseControllerGetFeaturesByNetworkType {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ReleaseControllerGetFeaturesByRelease {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace ReleaseControllerGetReleases {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace ReleaseControllerRemove {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace ReleaseControllerUpdate {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace ReleaseControllerUpdateFeature {
            namespace Parameters {
                export type Id = number;
            }
            export interface PathParameters {
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace SpoControllerGetSuppliers {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace SpoControllerOptimize {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace TranslationsControllerCreateNamespace {
            export type RequestBody = DAT2.BFF.DTO.CreatePluginDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace TranslationsControllerCreateTranslation {
            export type RequestBody = DAT2.BFF.DTO.UpdatePluginTranslationsDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace TranslationsControllerDeleteNameSpace {
            export type RequestBody = DAT2.BFF.DTO.CreatePluginDto;
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace TranslationsControllerGetAvailableLanguages {
            namespace Parameters {
                export type Namespace = string;
            }
            export interface QueryParameters {
                namespace: Parameters.Namespace;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace TranslationsControllerGetAvailableNamespaces {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace TranslationsControllerGetTranslations {
            export type RequestBody = DAT2.BFF.DTO.GetTranslationsDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace UserConfigControllerCreateOrUpdateParentConfig {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace UserConfigControllerCreateOrUpdateRemoteConfig {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace UserConfigControllerCreateUserConfig {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace UserConfigControllerDeleteUser {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserConfigControllerFindMemoField {
            namespace Parameters {
                export type CustomerNumber = string;
                export type Memo = string;
            }
            export interface PathParameters {
                customerNumber: Parameters.CustomerNumber;
                memo: Parameters.Memo;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserConfigControllerFindMemoFieldById {
            namespace Parameters {
                export type CustomerNumber = string;
                export type Id = string;
            }
            export interface PathParameters {
                customerNumber: Parameters.CustomerNumber;
                id: Parameters.Id;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserConfigControllerFindParentConfig {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserConfigControllerGetConfigs {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserConfigControllerGetRemoteConfigByCustomer {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserConfigControllerGetUserConfig {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserConfigControllerSearchUserConfig {
            namespace Parameters {
                export type Limit = number;
                export type SearchTerm = string;
            }
            export interface QueryParameters {
                searchTerm: Parameters.SearchTerm;
                limit: Parameters.Limit;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserConfigControllerUpdateMemoField {
            namespace Parameters {
                export type CustomerNumber = number;
            }
            export interface PathParameters {
                customerNumber: Parameters.CustomerNumber;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserConfigControllerUpdateUserConfig {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserControllerGetConfiguration {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserControllerInvalidateCache {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace UserControllerLoginNew {
            export type RequestBody = DAT2.BFF.DTO.LoginDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace UserControllerUpdate {
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace UserControllerValidateToken {
            export type RequestBody = DAT2.BFF.DTO.ValidateTokenDto;
            namespace Responses {
                export interface $201 {}
            }
        }
        namespace VehicleDataControllerGetStaticData {
            namespace Parameters {
                export type BucketVersion = string;
                export type ContentEncoding = string;
            }
            export interface PathParameters {
                bucketVersion: Parameters.BucketVersion;
            }
            export interface QueryParameters {
                contentEncoding: Parameters.ContentEncoding;
            }
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace VehicleDataControllerGrapaData {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace VehicleDataControllerStaticImages {
            namespace Responses {
                export interface $200 {}
            }
        }
        namespace VehicleSelectionControllerGetVehicleTypes {
            export type RequestBody = DAT2.BFF.DTO.GetVehicleTypesDto;
            namespace Responses {
                export interface $201 {}
            }
        }
    }
}
