declare namespace BFF {
    namespace ContractService {
        namespace Request {
            interface ImportIni {
                file: File;
                templateId?: number;
                withDetailedResult?: boolean;
            }
        }
        namespace Response {
            interface ImportIni extends ImportIniDebugInfo {
                contractId: number;
            }
        }

        namespace ErrorResponse {
            interface ImportIni extends ImportIniDebugInfo {
                error: {
                    errorCode: number;
                    errorMessage: string;
                };
            }
        }

        interface ImportIniDebugInfo {
            /** if withDetailedResult = true, we get this data for debugging **/
            parsedIni?: Record<string, Array<Record<string, string | string[]>>>;
            detectedConfig?: DAT2.IniMapping.IniMappingContent | null;
            payloadForCreateContract?: DAT2.API2DAT5.MyClaimExternalService_schema1.createOrUpdateContract | null;
        }
    }
}
