declare namespace BFF {
    type I18nNamespace = import('@dat/core/types/i18n').I18nNamespace;
    type PlainObject = import('@dat/core/types/common').PlainObject;

    namespace Translations {
        namespace Request {
            interface GetTranslations {
                language: DAT2.Locale;
                namespaces: I18nNamespace[];
            }
        }

        namespace Response {
            type GetTranslations = {
                [TNamespace in I18nNamespace]?: {
                    [TLocale in DAT2.Locale]?: PlainObject;
                };
            };

            type GetPlugins = {
                pluginName: string;
                files: string[];
            }[];
        }
    }
}
