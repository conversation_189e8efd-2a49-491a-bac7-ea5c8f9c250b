declare namespace BFF {
    namespace Attachments {
        namespace Request {
            interface GetGenericZipFile {
                contractId: string;
                attachmentsFolderId: string;
                printoutId: string;
                isSVDExportEnabled?: boolean;
            }

            interface ConvertImagesToPdf {
                contractId: number;
                uploadId: string;
                filename?: string;
                templateId: number | null;
                attachmentIds: number[];
            }
        }
    }
}
