declare namespace DAT2 {
    interface VXS {
        Dossier?: SingleOrArray<Dossier>;
        Vehicle?: Dossier['Vehicle'];
    }

    interface Dossier {
        Name?: string;
        Description?: string;
        DossierId?: number;
        IdSD3Network?: number;
        // TODO: replace with keyof countries / languages
        Country?: CountryCode;
        Language?: string;
        Currency?: string;
        DatCustomerId?: number;
        CreateUser?: string;
        DossierType?: string;
        DossierOrigin?: string;
        CreateDate?: string; // Date
        ChangeDate?: string; // Date
        DatCustomerAddress?: DatCustomerAddress;
        Vehicle?: Vehicle;
        VAT?: VAT;
        TradingData?: TradingData;
        Valuation?: Valuation;
        RepairCalculation?: RepairCalculation;
        RepairOrder?: RepairOrder;
        ProcessManagement?: {
            CustomDataList?: {
                source?: ContractType;
            };
        };
    }

    interface VehicleDataItaly {
        VehicleTypeItaly?: string;
        ManufacturerItaly?: string;
        BaseModelItaly?: string;
        SubModelItaly?: string;
        VehicleTypeNameItaly?: string;
        ManufacturerNameItaly?: string;
        BaseModelNameItaly?: string;
        SubModelNameItaly?: string;
    }
    interface Vehicle {
        DatECode?: string; //TODO: string | number
        VehicleIdentNumber?: string;
        RegistrationData?: {
            LicenseNumber?: string;
            CNIT?: string;
            KbaCode?: number;
        };
        OriginalPriceInfo?: {
            OriginalPriceNoVARate?: string;
            DatOriginalPriceNoVARate?: string;
        };
        NationalCodeAustria?: {
            NationalCodeAustria?: SingleOrArray<number>;
        };
        KbaNumbersN?: {
            KbaNumber?: SingleOrArray<string>;
        };
        Container?: string;
        ConstructionYear?: number;
        ConstructionMonth?: number;
        ConstructionTime?: number;
        ConstructionTimeFrom?: number;
        ConstructionTimeTo?: number;
        VehicleTypeName?: Field.TextWithPartialOrigin<string>;
        VehicleTypeNameN?: Field.TextWithPartialOrigin<string>;
        ManufacturerName?: Field.TextWithPartialOrigin<string>;
        BaseModelName?: Field.TextWithPartialOrigin<string>;
        SubModelName?: Field.TextWithPartialOrigin<string>;
        ContainerName?: string;
        ContainerNameN?: Field.TextWithPartialOrigin<string>;
        VehicleType?: number;
        Manufacturer?: number;
        BaseModel?: number;
        SubModel?: number;
        Equipment?: Equipment;
        Engine?: Engine;
        InitialRegistration?: string; // Date
        MileageEstimated?: number;
        MileageOdometer?: number;
        AlternativeVehicleType?: string;
        AlternativeManufacturer?: string;
        AlternativeBaseModel?: string;
        AlternativeSubModel?: string;
        IdentificationSource?: string;
        Country?: string;
        VinActive?: boolean;
        SubModelVariant?: number;
        DATECodeEquipment?: DATECodeEquipment;
        PaintTypes?: {
            PaintType?: SingleOrArray<PaintTypeId>;
        };
        MetaPositions?: {
            MetaPosition?: SingleOrArray<Field.KeyValue<string, string>>;
        };
        TokenOfVinResult?: string;
        VinAccuracy?: number;
        ReleaseIndicator?: string;
        VINResult?: {
            VinInterfaceVersion?: string;
            VinDatProcedure?: boolean;
            CrossBorder?: boolean;
            VINEquipments?: {
                VINEquipment?: Array<{
                    AvNumberDat?: string;
                    ManufacturerCode?: string;
                    ShortName?: string;
                }>;
            };
            VINColors?: {
                VINColor?: Array<VINColor>;
            };
            VINVehicle?: {
                VINumber?: {
                    VinCode?: string;
                    OrderCode?: string;
                    Manufacturer?: string;
                };
                ManufacturerCarCode: string;
                ManufacturerEngineCode: string;
                ManufacturerTransmissionCode: string;
            };
            VINECodes?: {
                VINECode: Array<{
                    Sign?: number;
                    Country?: string;
                    VehicleTypeKey?: number;
                    ManufacturerKey?: number;
                    VehicleMainTypeKey?: number;
                    VehicleSubTypeKey?: number;
                    VehicleSubTypeVariantKey?: number;
                    ConstructionTimeMin?: number;
                    ConstructionTime?: number;
                    ConstructionTimeEdge?: number;
                    ConstructionTimeProd?: number;
                    ConstructionTimePriceList?: number;
                    VINContainers?: {
                        VINContainer: Array<{
                            Container?: string;
                            VehicleTypeKey?: number;
                            ManufacturerKey?: number;
                            VehicleMainTypeKey?: number;
                            VehicleSubTypeKey?: number;
                            VehicleConstructionTime?: number;
                        }>;
                    };
                }>;
            };
        };
        TechInfo?: TechInfo;
        Tires?: {
            Axles?: any; //TODO: any
        };

        VehicleDataItaly?: VehicleDataItaly;

        IsWithManualTypeNames?: boolean;

        EngineNameManual?: string;
        BodyNameManual?: string;
        WheelbaseNameManual?: string;
        PropulsionNameManual?: string;
        DrivingCabNameManual?: string;
        TonnageNameManual?: string;
        ConstructionNameManual?: string;
        SuspensionNameManual?: string;
        AxleCountNameManual?: string;
        EquipmentLineNameManual?: string;
        GearboxNameManual?: string;
        IsDisengagedN?: boolean; // only in valuate Dossier, used for free valuation
    }

    interface VINColor {
        ColorID?: string;
        Code?: number | string;
        Description?: string;
        PaintType?: PaintTypeId;
        CountCoat?: string;
        StandardColor?: StandardColor;
    }

    type StandardColor =
        | 0 //white
        | 1 //yellow/beige
        | 2 //orange
        | 3 //red
        | 4 //violet
        | 5 //blue
        | 6 //green
        | 7 //silver/grey
        | 8 //brown
        | 9; //black;

    type PaintTypeId = string | number;
    type LacquerTypeId = PaintTypeId;

    interface Equipment {
        EquipmentValue?: Field.TextWithPartialOrigin<number>;
        EquipmentValueGross?: Field.TextWithPartialOrigin<number>;
        OriginalEquipmentValue?: Field.TextWithPartialOrigin<number>;
        OriginalEquipmentValueGross?: Field.TextWithPartialOrigin<number>;
        EquipmentValueType?: string;
        DeselectedSeriesEquipment?: {
            EquipmentPosition?: SingleOrArray<EquipmentPosition>;
        };
        SeriesEquipment?: {
            EquipmentPosition?: SingleOrArray<EquipmentPosition>;
        };
        SpecialEquipment?: {
            EquipmentPosition?: SingleOrArray<EquipmentPosition>;
        };
        AdditionalEquipment?: {
            EquipmentPosition?: SingleOrArray<EquipmentPosition>;
        };
    }

    interface Engine {
        Capacity?: Field.TextWithPartialOrigin<number>;
        Co2Emission?: number;
        Consumption?: number;
        Cylinders?: Field.TextWithPartialOrigin<number>;
        EnginePowerHp?: Field.TextWithPartialOrigin<number>;
        EnginePowerKw?: Field.TextWithPartialOrigin<number>;
    }

    interface DATECodeEquipment {
        EquipmentPosition?: SingleOrArray<EquipmentPosition>;
    }

    // https://www.dat.de/fileadmin/de/support/interface-documentation/EN/SilverDAT_interface_compendium/index.htm#1390
    type DecreaseType = 'residual value' | 'table1' | 'table2' | 'table3' | 'table4' | 'default table';

    interface TechInfo {
        Acceleration?: Field.TextWithPartialOrigin<number>; // Acceleration from 0 - 100 km/h in seconds
        BatteryCapacity?: Field.TextWithPartialOrigin<number>; // Battery capacity (kWh)
        BatteryConstructionType?: Field.TextWithPartialOrigin<string>; // Battery type
        BatteryVoltage?: Field.TextWithPartialOrigin<number>; // Battery voltage (V)
        BatteryWeight?: Field.TextWithPartialOrigin<number>; // Battery weight (kg)
        Capacity?: Field.TextWithPartialOrigin<number>; // Capacity in ccm
        ChargingCurrentPlugType?: Field.TextWithPartialOrigin<string>; // Charge current connector type
        Co2Emission?: Field.TextWithPartialOrigin<number>; // CO2 emission (g/km)
        Consumption?: Field.TextWithPartialOrigin<number>; // Fuel consumption (l/100km) combined
        ConsumptionElectricalCurrent?: Field.TextWithPartialOrigin<number>; // Energy consumption (kWh/100 km)
        ConsumptionInnerCng?: Field.TextWithPartialOrigin<number>; // Fuel consumption, city (CNG)
        ConsumptionInnerH?: Field.TextWithPartialOrigin<number>; // Fuel consumption, city (H)
        ConsumptionInnerLpg?: Field.TextWithPartialOrigin<number>; // Fuel consumption, city (LPG)
        ConsumptionInTown?: Field.TextWithPartialOrigin<number>; // Fuel consumption, city (l/100km)
        ConsumptionMixCng?: Field.TextWithPartialOrigin<number>; // Fuel consumption, combined (CNG)
        ConsumptionMixH?: Field.TextWithPartialOrigin<number>; // Fuel consumption, combined (H)
        ConsumptionMixLpg?: Field.TextWithPartialOrigin<number>; // Fuel consumption, combined (LPG)
        ConsumptionOuterCng?: Field.TextWithPartialOrigin<number>; // Fuel consumption, suburban (CNG)
        ConsumptionOuterH?: Field.TextWithPartialOrigin<number>; // Fuel consumption, suburban (H)
        ConsumptionOuterLpg?: Field.TextWithPartialOrigin<number>; // Fuel consumption, suburban (LPG)
        ConsumptionOutOfTown?: Field.TextWithPartialOrigin<number>; // Fuel consumption (l/100km) rural
        CountOfAirbags?: Field.TextWithPartialOrigin<number>; // Number of airbags
        CountOfAxles?: Field.TextWithPartialOrigin<number>; // Number of axles
        CountOfDrivedAxles?: Field.TextWithPartialOrigin<number>; // Number of driven axles
        Cylinder?: Field.TextWithPartialOrigin<number>; // Number of cylinders
        CylinderArrangement?: Field.TextWithPartialOrigin<string>; // Cylinder arrangement
        Drive?: Field.TextWithPartialOrigin<string>; // Type of drive
        DriveN?: Field.TextWithPartialOrigin<string>; // Type of drive
        DriveCode?: string; // Type of drive codes
        EmissionClass?: Field.TextWithPartialOrigin<string>; // Emission class (outdated)
        EmissionClassN?: {
            // Emission class (structure consisting of values for type, description and OBD Yes/No)
            EmissionClassItemN?: {
                description: string;
                type: string;
            };
        };
        EngineCycle?: Field.TextWithPartialOrigin<number>; // Engine timing (cycle)
        FuelMethod?: Field.TextWithPartialOrigin<string>; // Fuel type
        FuelMethodCode?: string; // Fuel method codes
        FuelMethodType?: Field.TextWithPartialOrigin<string>; // Fuel type (petrol, diesel or other)
        Height?: Field.TextWithPartialOrigin<number>; // Vehicle height (mm)
        Length?: Field.TextWithPartialOrigin<number>; // Vehicle length (mm)
        LoadingHeight?: Field.TextWithPartialOrigin<number>; // Cargo space height (mm)
        LoadingLength?: Field.TextWithPartialOrigin<number>; // Cargo space length (mm)
        LoadingSpace?: Field.TextWithPartialOrigin<number>; // Cargo space volume (dm³)
        LoadingSpaceMax?: Field.TextWithPartialOrigin<number>; // Cargo space volume max. (dm³)
        LoadingWidth?: Field.TextWithPartialOrigin<number>; // Cargo space width (mm)
        NormalChargeDuration?: Field.TextWithPartialOrigin<number>; // Charge time at quick-charge (hrs) (wall box/charging station)
        NormalChargeVoltage?: Field.TextWithPartialOrigin<number>; // Standard charge (V)
        Payload?: Field.TextWithPartialOrigin<number>; // Payload in kg
        PermissableTotalWeight?: Field.TextWithPartialOrigin<number>; // Permissible total weight
        PluginSystem?: Field.TextWithPartialOrigin<boolean>; // Plug-in system (Y/N)
        PowerHp?: Field.TextWithPartialOrigin<number>; // Power in HP
        PowerHpMax?: Field.TextWithPartialOrigin<number>; // Engine power (HP) (max.)
        PowerHpMaxSecondary?: Field.TextWithPartialOrigin<number>; // Engine power (HP) (max.), secondary drive
        PowerHpPermanent?: Field.TextWithPartialOrigin<number>; // Engine power (HP) (full-time/continuous)
        PowerHpPermanentSecondary?: Field.TextWithPartialOrigin<number>; // Engine power (HP) (full-time/continuous), secondary drive
        PowerHpSystem?: Field.TextWithPartialOrigin<number>; // System power (HP)
        PowerKw?: Field.TextWithPartialOrigin<number>; // Power in kW
        PowerKwMax?: Field.TextWithPartialOrigin<number>; // Engine power (kW) (max.)
        PowerKwMaxSecondary?: Field.TextWithPartialOrigin<number>; // Engine power (kW) (max) secondary drive
        PowerKwPermanent?: Field.TextWithPartialOrigin<number>; // Engine power (kW) (full-time/continuous)
        PowerKwPermanentSecondary?: Field.TextWithPartialOrigin<number>; // Engine power (kW) (full-time/continuous), secondary drive
        PowerKwSystem?: Field.TextWithPartialOrigin<number>; // System power (kW)
        QuickChargeDuration?: Field.TextWithPartialOrigin<number>; // Charge time at quick-charge (hrs) (wall box/charging station)
        QuickChargeVoltage?: Field.TextWithPartialOrigin<number>; // Quick-charge (V)
        QuickdropSystem?: Field.TextWithPartialOrigin<boolean>; // Quick-drop system (Y/N)
        RangeOfElectricMotor?: Field.TextWithPartialOrigin<number>; // Range with electric motor (km)
        RangeTotal?: Field.TextWithPartialOrigin<number>; // Total range (km)
        RoofLoad?: Field.TextWithPartialOrigin<number>; // Roof load
        RotationsOnMaxPower?: Field.TextWithPartialOrigin<number>; // Revolutions per minute at max. power
        RotationsOnMaxTorque?: Field.TextWithPartialOrigin<number>; // Revolutions per minute at max. torque
        SpeedMax?: Field.TextWithPartialOrigin<number>; // Top speed
        SuitableForE10?: Field.TextWithPartialOrigin<boolean>; // E10-compatible
        TankVolume?: Field.TextWithPartialOrigin<number>; // Tank volume in litres
        TankVolumeAlternative?: Field.TextWithPartialOrigin<number>; // Tank volume in litres (alternative)
        Torque?: Field.TextWithPartialOrigin<number>; // Torque (Nm)
        TrailerLoadBraked?: Field.TextWithPartialOrigin<number>; // Axle load, braked
        TrailerLoadUnbraked?: Field.TextWithPartialOrigin<number>; // Axle load, non-braked
        UnloadedWeight?: Field.TextWithPartialOrigin<number>; // Kerb weight
        VehicleDoors?: Field.TextWithPartialOrigin<number>; // Number of doors
        VehicleSeats?: Field.TextWithPartialOrigin<number>; // Number of seats
        WeightTotalCombination?: Field.TextWithPartialOrigin<number>; // Permissible total weight of vehicle combination [kg]
        WheelBase?: Field.TextWithPartialOrigin<number>; // Wheelbase
        Width?: Field.TextWithPartialOrigin<number>; // Vehicle width (mm)
        WidthForGarage?: Field.TextWithPartialOrigin<number>; // Garage width of vehicle (mm)
        WltpConsumptionMixedMin?: Field.TextWithPartialOrigin<number>; // Minimum fuel consumption (l/100 km) at mixed speeds according to WLTP
        WltpConsumptionMixedMax?: Field.TextWithPartialOrigin<number>; // Maximum fuel consumption (l/100 km) at mixed speeds according to WLTP
        WltpConsumptionBivalentMixedCngMin?: Field.TextWithPartialOrigin<number>; // Minimum fuel consumption CNG (kg/100 km) at mixed speeds according to WLTP
        WltpConsumptionBivalentMixedCngMax?: Field.TextWithPartialOrigin<number>; // Maximum fuel consumption CNG (kg/100 km) at mixed speeds according to WLTP
        WltpConsumptionBivalentMixedLpgMin?: Field.TextWithPartialOrigin<number>; // Minimum fuel consumption LPG (l/100 km) at mixed speeds according to WLTP
        WltpConsumptionBivalentMixedLpgMax?: Field.TextWithPartialOrigin<number>; // Maximum fuel consumption LPG (l/100 km) at mixed speeds according to WLTP
        WltpConsumptionBivalentMixedHMin?: Field.TextWithPartialOrigin<number>; // Minimum fuel consumption H (kg/100 km) at mixed speeds according to WLTP
        WltpConsumptionBivalentMixedHMax?: Field.TextWithPartialOrigin<number>; // Maximum fuel consumption H (kg/100 km) at mixed speeds according to WLTP
        WltpCo2EmissionMin?: Field.TextWithPartialOrigin<number>; // Minimum CO2 emission (g/km) according to WLTP
        WltpCo2EmissionMax?: Field.TextWithPartialOrigin<number>; // Maximum CO2 emission (g/km) according to WLTP
        WltpConsumptionElectricalMin?: Field.TextWithPartialOrigin<number>; // Minimum energy consumption (kWh/100 km) according to WLTP
        WltpConsumptionElectricalMax?: Field.TextWithPartialOrigin<number>; // Maximum energy consumption (kWh/100 km) according to WLTP
        AxleLoadFront?: number; // Axle load, front
        AxleLoadBack?: number; // Axle load, rear
        AxleLoadMiddle?: number; // Axle load, centre
        CabineStructureDescription?: string; // Cab type (name)
        CabineStructureType?: string; // Cab type (code)
        DustBadge?: string; // Emissions sticker
        //TODO: [any]
        FillingQuantities?: any; // Fill levels; only vehicle types 1 and 2
        GearboxType?: string; // Transmission type; manual; automatic; other
        InsuranceTypeClassCascoComplete?: string; // Group rating for MOD (full)
        InsuranceTypeClassCascoPartial?: string; // Group rating for MOD (partly)
        InsuranceTypeClassLiability?: string; // Group rating for MTPL
        NrOfGears?: string; // Number of gears
        OriginalTireSizeAxle1?: string; // Standard tires front axle
        OriginalTireSizeAxle2?: string; // Standard tires rear axle
        ProductGroupName?: string; // Product group (name)
        StructureDescription?: string; // Vehicle body abbreviated designation
        StructureType?: string; // Vehicle body (sedan, estate, …)
        TonnageClass?: string; // Tonnage class
        UpperBodyMaterial?: string; // Body material
        UpperBodyStructureAndVersion?: string; // HCV body version
        UpperBodyStructureDescription?: string; // HCV body description
        UpperBodyStructureType?: string; // HCV body type
        WheelBase2?: number; // Wheelbase alternative (rear axle)
    }

    interface DATProcessIdCommentList {
        DATProcessIdComment: DATProcessIdComment[];
    }

    interface RepairOrder {
        DamageDate?: string; // Date
        RepairCoverage?: string;
        OrderNumber?: string;
        InvoiceNumber?: string;
        JobNumber?: string;
        DamageNumber?: string;
        PolicyNumber?: string;
        Retention?: boolean;
        RetentionAmount?: number;
        InsuranceId?: string;
        InsuranceGroupId?: string;
        ServiceProviderId?: string;
        InsuranceType?: string;
        InsuranceNumber?: string;
        InsuranceAgency?: string;
        TypeOfInsurance?: number;
        LossLocation?: string;
        DamageType?: number;
        InspectionDate?: string; // Date
        BillingCategory?: number;
        DeclarationOfAssignment?: boolean;
        InsuranceCase?: boolean;
        AdditionalData?: any;
        CreationDateTime?: string; // Date
        MetaPositions?: any;
        InvoiceDate?: string; // Date
        CountryFlagDamageEvent?: string;
        InsuranceName?: string;
        TokenContributionInInsuranceCase?: string;
        Deleted?: boolean;
        GDVRoutingType?: string;
        Comment?: string;
        DATProcessIdCommentList?: DATProcessIdCommentList;
        EstimatedRepairTimeInDays?: number;
        ReplacementCar?: boolean;
        ReplacementCarTimeInDays?: number;
        ReplacementCarCosts?: number;
        OtherCostsDescription?: string;
        OtherCosts?: number;
        TowingRecoveryCosts?: number;
        OtherDeductionsDescription?: string;
        OtherDeductions?: number;
    }

    interface VAT {
        VatType?: VatType;
        VatAtConstructionTime?: number;
        VatAtCalculationTime?: number;
        VatAtValuationTime?: Field.TextWithPartialOrigin<number>;
    }

    // In compendium there are 2 more possible values of VatType: "R" and "D"
    // If they appear in some response, they will need to be added here
    type VatType = 'regular' | 'difference';

    interface Valuation {
        OriginalPrice?: Field.TextWithPartialOrigin<number>;
        DatOriginalPrice?: number;
        OriginalPriceGross?: Field.TextWithPartialOrigin<number>;
        DatOriginalPriceGross?: number;
        BasePrice?: Field.TextWithPartialOrigin<number>;
        DatBasePrice?: number;
        ReferenceMileage?: number;
        MileageCorr?: Field.TextWithPartialOrigin<number>;
        DatMileageCorr?: number;
        InitialRegistrationCorr?: Field.TextWithPartialOrigin<number>;
        DatInitialRegistrationCorr?: number;
        BasePrice2?: Field.TextWithPartialOrigin<number>;
        DatBasePrice2?: number;
        EquipmentSign?: EquipmentSign;
        EquipmentOriginalPrice?: Field.TextWithPartialOrigin<number>;
        DatEquipmentOriginalPrice?: number;
        EquipmentPrice?: Field.TextWithPartialOrigin<number>;
        DatEquipmentPrice?: number;
        ValuationCorrection?: Field.TextWithPartialOrigin<number>;
        DatValuationCorrection?: number;
        BasePrice3?: Field.TextWithPartialOrigin<number>;
        DatBasePrice3?: number;
        ConditionCorrectionPerc?: Field.TextWithPartialOrigin<number>;
        DatConditionCorrectionPerc?: number;
        SalesPrice?: Field.TextWithPartialOrigin<number>;
        DatSalesPrice?: number;
        SalesPriceGross?: Field.TextWithPartialOrigin<number>;
        DatSalesPriceGross?: number;
        Margin?: Field.TextWithPartialOrigin<number>;
        DatMargin?: number;
        MarginGross?: Field.TextWithPartialOrigin<number>;
        DatMarginGross?: number;
        PurchasePrice?: Field.TextWithPartialOrigin<number>;
        DatPurchasePrice?: number;
        PurchasePriceGross?: Field.TextWithPartialOrigin<number>;
        DatPurchasePriceGross?: number;
        DefaultTiresPrice?: Field.TextWithPartialOrigin<number>;
        DatDefaultTiresPrice?: number;
        LastValuationDataYear?: number;
        LastValuationDataMonth?: number;
        LastValuationDate?: string;
        ValuationType?: string;
        DisplayGross?: boolean;
        ManualEquipmentOriginalPrice?: number;
        DeterminatedDate?: string; // Date
        EquipmentPercentage?: number;
        EquipmentDecreaseType?: DecreaseType;
        EquipmentDecreaseTypeRemaining?: DecreaseType;
        Condition?: ValuationCondition;
        Forecasts?: {
            Forecast?: Forecast;
        };
        QuestionAnswers?: BFF.Customer.ValuationQuestionAnswers;
    }

    type EquipmentSign = 'calculated value' | 'flat-rate value';

    interface ValuationCondition {
        OwnerCorrectionPerc?: number;
        DatOwnerCorrectionPerc?: number;
        OwnerCorrectionAmount?: number;
        OwnerCorrectionAmountGross?: number;
        ConditionCorrectionFactorPerc?: Field.TextWithPartialOrigin<number>;
        DatConditionCorrectionFactorPerc?: number;
        ConditionCorrectionAmount?: number;
        ConditionCorrectionAmountGross?: number;
        NumberOfOwners?: number;
        NumberOfOwnersN?: NumberOfOwnersN;
        AccidentDamage?: AccidentDamage;
        IncreaseInValue?: Field.TextWithPartialOrigin<number>;
        DatIncreaseInValue?: number;
        IncreaseInValueGross?: Field.TextWithPartialOrigin<number>;
        DatIncreaseInValueGross?: number;
        CommentIncreaseInValue?: Field.TextWithPartialOrigin<string>;
        CommentDecreaseInValue?: Field.TextWithPartialOrigin<string>;
        DecreaseInValue?: Field.TextWithPartialOrigin<number>;
        DatDecreaseInValue?: number;
        DecreaseInValueGross?: Field.TextWithPartialOrigin<number>;
        DatdecreaseInValueGross?: number;
        TiresMountedValue?: Field.TextWithPartialOrigin<number>;
        DatTiresMountedValue?: number;
        TiresMountedValueGross?: Field.TextWithPartialOrigin<number>;
        DatTiresMountedValueGross?: number;
        TiresUnmountedValue?: Field.TextWithPartialOrigin<number>;
        DatTiresUnmountedValue?: number;
        TiresUnmountedValueGross?: Field.TextWithPartialOrigin<number>;
        DatTiresUnmountedValueGross?: number;
        RepairCosts?: Field.TextWithPartialOrigin<number>;
        DatRepairCosts?: number;
        RepairCostsGross?: Field.TextWithPartialOrigin<number>;
        DatRepairCostsGross?: number;
        ConditionSubTotal1?: number;
        ConditionSubTotal1Gross?: number;
        ConditionSubTotal2?: number;
        ConditionSubTotal2Gross?: number;
        RepairCostsInTradeMargin?: boolean;
    }

    type AccidentDamage = 'accident free' | 'damage unrepaired' | 'damage repaired' | 'unknown' | 'no' | 'yes';
    type NumberOfOwnersN = 'unknown' | number;

    interface Forecast {
        ForecastType?: string;
        PriceType?: string;
        IncludeVat?: boolean;
        CurveType?: string;
        DecreaseType?: string;
        StartType?: string;
        ValueType?: string;
        MileageType?: string;
        ForecastItems?: {
            ForecastItem: SingleOrArray<ForecastItem>;
        };
    }

    interface ForecastItem {
        Months?: string;
        MileagePerYear?: string;
        MileageTotal?: string;
        Value?: string | number;
        Percentage?: string | number;
        Error?: string;
    }

    type LacquerMethod =
        // TODO: remove string values when fully transition to rest api
        | 'EURO_LACQUER'
        | 'MANUFACTURER_SPECIFIC'
        | 'AZT'
        | 'CZ'
        // 1 - euroLacquerFactor
        | 1
        // 2 - manufacturerLacquerFactor
        | 2
        //3-CZ
        | 3
        //5-AZT
        | 5
        //Manual
        | 6;

    type PaintWorkType = 'WAGE_MATERIAL_SEPARATELY' | 'WAGE_INCLUSIVE_MATERIAL';

    interface VXSResponse {
        VXS: VXS;
    }
}
