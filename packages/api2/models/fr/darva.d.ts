declare namespace FR {
    namespace DARVA {
        interface InsuranceSummaryResponse {
            insurance_deductible: number;
            obsolescence: number;
            tva: number;
            exclusion: number;
            total_insured: number;
            total_insurance: number;
            total: number;
        }
        interface DarvaCalculationPrice {
            ht: number;
            discount: number;
            tva: number;
            ttc: number;
        }
        interface DarvaCalculation {
            labour: DarvaCalculationPrice;
            part: DarvaCalculationPrice;
            paint: DarvaCalculationPrice;
            furniture: DarvaCalculationPrice;
            package: DarvaCalculationPrice;
            totalWithoutSgc: DarvaCalculationPrice;
            sgc: DarvaCalculationPrice;
            discount: DarvaCalculationPrice;
            total: DarvaCalculationPrice;
            wear: number;
            amountChargedToAssured: number;
            amountChargedToInsurance: number;
        }
        interface MessageFilter {
            claimId: number;
            messageCode?: string;
        }
        interface Message {
            createdAt: string;
            messageLabel: string | null;
            direction: 'dat_to_darva' | 'darva_to_dat';
            isValid: boolean;
            link: string | null;
            errors?: string[] | null;
        }
        type FranchiseType =
            | '1' // Totality
            | '2' // Vehicle
            | '3' // Non-audiovisual accessories
            | '4' // Towing-vehicle transport
            | '5' // Options
            | '6' // Audiovisual
            | '7' // Professional fittings
            | '8' // Tires
            | '9' // Transported goods
            | 'A' // Glass breakage
            | 'B' // Optics
            | 'C' // Options and accessories
            | 'D' // Front collision in a chain accident
            | 'E' // Rear collision in a chain accident
            | 'F' // Paint
            | 'G' // Impound
            | 'H' // Parking, guarding
            | 'I' // Breakdown
            | 'J' // Sunroofs, windows
            | 'K' // Vehicle rental
            | 'L' // Vehicle rental days
            | 'M' // Helmet
            | 'N' // Private vehicle contents
            | 'O' // Motorcyclist clothing
            | 'P' // City clothing
            | 'Q' // Traction battery
            | 'Z'; // Other
        interface Franchise<T extends FranchiseType = FranchiseType> {
            type: T;
            description: string;
            value: number;
            percent: number;
            minPercent: number;
            maxPercent: number;
            support: number;
            maxSupport: number;
            wear: boolean;
            tvaRecoverable: boolean;
            included: boolean;
        }
        type FranchiseMemo = {
            [key in FranchiseType]?: Franchise<key>;
        };
        interface AccountPayload {
            token: string;
            customerNumber: string;
            detail: {
                production: boolean;
                code: string;
            };
        }
    }
}
