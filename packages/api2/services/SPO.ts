import axios from 'axios';

const customFetch = axios.create({
    headers: {
        'Content-Type': 'application/json'
    },
    baseURL: 'https://api.plugins.wedat.eu/'
});

type Part = {
    oePart?: string;
    dvn?: number;
    id?: number;
    description?: string;
    price?: number;
    amount?: number;
    inputType?: string;
};

type DataForOptimization = {
    optimized: boolean;
    provider: string;
    country: string;
    environment: string;
    allowedSuppliers: any[][];
    allowedQualities: any[];
    eVehicleType: number;
    eCodeBrand: number;
    eCodeType: number;
    eCodeModel: number;
    priceDate: string;
    vehicleAge: number;
    profileName: string;
    productionTime: number;
    customerNumber: string;
    claimNumber: string;
    parts: Part[];
    currency: string;
    vin: string;
    paintType: string;
    firstRegistrationDate: string;
    mileage: number;
    insurer: string;
    rulesApplicable: boolean;
};

export type AlternativePositionResponse = {
    amNumber: string;
    changed: boolean;
    dataSource: string;
    description: string;
    distributor: string;
    distributorNumber: number;
    manufacturer: string;
    partID: number;
    partPicture: any;
    priceDate: string;
    quality: string;
    spDate: string;
    unitPrice: number;
};

interface OptimizedPart extends Part {
    amParts: Array<AlternativePositionResponse>;
}

export type OptimizedData = Omit<DataForOptimization, 'parts'> & {
    parts: Array<OptimizedPart>;
    suppliers: Array<any>;
};

export type SuppliersForCountry = Array<{
    id: string;
    name: string;
    address: string;
    lastUpdate: string;
    currency: string;
}>;

export const getSuppliers = async (country: string): Promise<SuppliersForCountry> => {
    return customFetch({
        url: `/spo/get-suppliers/${country}`,
        method: 'GET'
    }).then(data => data.data);
};

export const optimizeWithSPO = async (data: DataForOptimization): Promise<OptimizedData> => {
    return customFetch({
        url: '/spo/optimize',
        method: 'POST',
        data
    }).then(data => data.data);
};
