import { createDomain, restore } from 'effector';

import { PluginOptions } from '../../types/plugin';

export const partSelectionDomain = createDomain();
const { createEvent, createStore } = partSelectionDomain;

const initPlugin = createEvent<PluginOptions>();
const unmountPlugin = createEvent<PluginOptions>();
const pluginOptions = restore(initPlugin, null);

const pluginInited = createStore(false).on(initPlugin, () => true);

export const pluginEvents = {
    initPlugin,
    unmountPlugin
};
export const pluginStores = {
    pluginOptions,
    pluginInited
};
