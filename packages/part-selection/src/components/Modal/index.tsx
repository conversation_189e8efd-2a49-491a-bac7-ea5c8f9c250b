import { FC } from 'react';
import { useUnit } from 'effector-react';
import { Formik, Form, FormikHelpers } from 'formik';
import { useTranslation } from 'react-i18next';

import { NumberInputField } from '@wedat/ui-kit/Formik';
import { InputField, SelectField } from '@dat/smart-components/FormikFields';

import { ModalContainer, Field, ModalButtonsContainer } from './styles';
import { getRepairTypeOptions } from '@dat/shared-models/partSelection/utils/getRepairTypeOptions';
import { validatePredefinedPosition } from '@dat/shared-models/partSelection/utils/validatePredefinedPosition';
import {
    REPAIR_TYPE_COMMENT,
    REPAIR_TYPES_WITH_PRICE_AND_QUANTITY,
    REPAIR_TYPES_WITH_REQUIRED_PRICE,
    REPAIR_TYPES_WITH_REQUIRED_TIME
} from '@dat/shared-models/partSelection/constants/index';
import { PART_SELECTION_VALIDATION } from './constants';
import { partSelectionEffects, partSelectionStores } from '@dat/shared-models/partSelection';
import { PartSelectionFormState } from '@dat/shared-models/partSelection/types';
import { Button, Modal, ModalProps } from '@wedat/kit';

type Props = ModalProps;

export const PartSelectionModal: FC<Props> = ({ onHide, visible }) => {
    const { t } = useTranslation();
    const initialFormState = useUnit(partSelectionStores.initialFormState);
    const options = useUnit(partSelectionStores.options);
    const repairTypeOptions = getRepairTypeOptions(t);

    const handleSubmit = (payload: PartSelectionFormState, { resetForm }: FormikHelpers<PartSelectionFormState>) => {
        if (initialFormState.id) {
            partSelectionEffects.editPartEffectFx({ id: initialFormState.id, payload });
            return;
        }

        partSelectionEffects.createPartEffectFx(payload);
        onHide?.();
        resetForm();
    };

    return (
        <Formik
            validationSchema={PART_SELECTION_VALIDATION}
            enableReinitialize
            onSubmit={handleSubmit}
            initialValues={initialFormState}
        >
            {({ handleSubmit, values }) => {
                const { country, repairType, description, workingTime, price, quantity } = values;
                const showPriceAndQuantity = REPAIR_TYPES_WITH_PRICE_AND_QUANTITY.includes(repairType);
                const isComment = repairType === REPAIR_TYPE_COMMENT;
                const isTimeRequired = REPAIR_TYPES_WITH_REQUIRED_TIME.includes(repairType);
                const isPriceRequired = REPAIR_TYPES_WITH_REQUIRED_PRICE.includes(repairType);
                const isValidPosition = validatePredefinedPosition(values);

                return (
                    <Form
                        onSubmit={async () => {
                            handleSubmit();
                        }}
                    >
                        <Modal
                            unsetOverflow
                            bodyHeight="auto"
                            header={t('modal.title')}
                            onHide={onHide}
                            visible={visible}
                            style={{ padding: '12px' }}
                        >
                            <ModalContainer>
                                <Field>
                                    <SelectField
                                        name="country"
                                        options={options.country}
                                        label={t('modal.label.country')}
                                        status={country ? undefined : 'error'}
                                        aria-label={t('modal.label.country')}
                                        inputId="partSelection_modal_country"
                                    />
                                </Field>
                                <Field>
                                    <SelectField
                                        id="repairType"
                                        options={repairTypeOptions}
                                        name="repairType"
                                        label={t('modal.label.repairType')}
                                        status={repairType ? undefined : 'error'}
                                        aria-label={t('modal.label.repairType')}
                                        inputId="partSelection_modal_repairType"
                                    />
                                </Field>
                                <Field>
                                    <InputField
                                        id="partSelection_modal_description"
                                        label={t('modal.label.description')}
                                        name="description"
                                        status={description ? undefined : 'error'}
                                    />
                                </Field>
                                {!isComment && (
                                    <>
                                        {showPriceAndQuantity && (
                                            <Field>
                                                <NumberInputField
                                                    hideZeroValue
                                                    id="price"
                                                    name="price"
                                                    label={t('modal.label.price')}
                                                    defaultLeftPadding={5}
                                                    status={isPriceRequired && !price ? 'error' : undefined}
                                                />
                                            </Field>
                                        )}
                                        <Field>
                                            <NumberInputField
                                                hideZeroValue
                                                id="workingTime"
                                                label={t('modal.label.workingTime')}
                                                name="workingTime"
                                                defaultLeftPadding={5}
                                                status={isTimeRequired && !workingTime ? 'error' : undefined}
                                            />
                                        </Field>
                                        {showPriceAndQuantity && (
                                            <Field>
                                                <NumberInputField
                                                    hideZeroValue
                                                    id="quantity"
                                                    label={t('modal.label.quantity')}
                                                    name="quantity"
                                                    defaultLeftPadding={5}
                                                    status={isPriceRequired && !quantity ? 'error' : undefined}
                                                />
                                            </Field>
                                        )}
                                    </>
                                )}

                                <ModalButtonsContainer>
                                    <Button
                                        onClick={() => {
                                            handleSubmit();
                                        }}
                                        type="submit"
                                        disabled={!isValidPosition}
                                        label={t('modal.label.save')}
                                        aria-label={t('modal.label.save')}
                                    />

                                    <Button
                                        onClick={onHide}
                                        variant="transparent"
                                        label={t('modal.label.cancel')}
                                        aria-label={t('modal.label.cancel')}
                                    />
                                </ModalButtonsContainer>
                            </ModalContainer>
                        </Modal>
                    </Form>
                );
            }}
        </Formik>
    );
};
