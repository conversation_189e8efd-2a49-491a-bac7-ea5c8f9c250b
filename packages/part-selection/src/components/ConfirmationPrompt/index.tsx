import { FC } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Modal, Text } from '@wedat/kit';

import { ButtonsContainer, TitleWrapper } from './styles';

interface Props {
    handlePromptDismiss: () => void;
    onConfirm: (...args: any[]) => Promise<any> | void;
    isOpen: boolean;
}

export const ConfirmationPrompt: FC<Props> = ({ handlePromptDismiss, onConfirm, isOpen }) => {
    const { t } = useTranslation();

    return (
        <Modal bodyHeight="auto" header={t('confirmationModal.title')} onHide={handlePromptDismiss} visible={isOpen}>
            <TitleWrapper>
                <Text>{t('confirmationModal.question')}</Text>
            </TitleWrapper>
            <ButtonsContainer>
                <Button
                    label={t('confirmationModal.yesAnswer')}
                    aria-label={t('confirmationModal.yesAnswer')}
                    onClick={() => {
                        handlePromptDismiss();
                        onConfirm();
                    }}
                />

                <Button
                    onClick={handlePromptDismiss}
                    variant="transparent"
                    label={t('confirmationModal.noAnswer')}
                    aria-label={t('confirmationModal.noAnswer')}
                />
            </ButtonsContainer>
        </Modal>
    );
};
