{"name": "@dat/inbox", "version": "1.1.22", "type": "module", "scripts": {"start": "yarn run -T env-cmd -e local -r ../../.env.json env-cmd -e local -r .env.json vite", "test": "yarn run -T vitest", "preview": "yarn run -T env-cmd -e local -r ../../.env.json env-cmd -e local -r .env.json vite preview", "analyze": "yarn run -T source-map-explorer './build/**/*.js'", "check-circular-deps": "yarn run -T dpdm --circular --warning=false --tree=false index.ts", "build:env": "yarn run -T env-cmd -e $0 -r ../../.env.json env-cmd -e $0 -r .env.json vite build", "build:test": "yarn build:env test", "build:prod": "yarn build:env prod", "build:gold": "yarn build:env gold", "build:acc": "yarn build:env acc", "build:standalone:env": "IS_STANDALONE_BUILD=true yarn build:env", "build:standalone:local": "yarn build:standalone:env local", "build:standalone:test": "yarn build:standalone:env test", "build:standalone:prod": "yarn build:standalone:env prod", "build:standalone:gold": "yarn build:standalone:env gold", "build:standalone:acc": "yarn build:standalone:env acc"}, "dependencies": {"@caldwell619/react-kanban": "^0.0.12", "@hello-pangea/dnd": "^18.0.1", "file-saver": "^2.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/react-window-infinite-loader": "^1.0.5", "babel-plugin-macros": "^3.1.0", "babel-plugin-styled-components": "^2.0.7"}}