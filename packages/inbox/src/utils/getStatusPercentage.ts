export const getStatusPercentage = (
    statusSteps: Array<string[]> | undefined,
    statusName: string | undefined,
    LEVELS: number[]
) => {
    let primaryIndex = 0;

    // return 0 if no list of statuses is provided
    if (!statusSteps) {
        return 0;
    }

    statusSteps &&
        statusSteps.forEach(el => {
            if (el.includes(String(statusName))) primaryIndex = statusSteps.indexOf(el);
        });
    const step = statusSteps && Math.floor(49 / statusSteps[primaryIndex].length);
    const level = LEVELS[primaryIndex];
    const secondaryIndex = statusSteps && statusSteps[primaryIndex].indexOf(String(statusName)) + 1;
    return level === 100 ? level : level + step * secondaryIndex;
};
