import { createEvent, restore } from 'effector';
import { createToggle } from '@dat/core/utils/effector/createToggle';

const [isFilterVisible, setIsFilterVisible] = createToggle(false);

const setFilter = createEvent<string | null>();
const filter = restore(setFilter, null);

export const vinFilterStores = {
    isFilterVisible,
    filter
};

export const vinFilterEvents = {
    setIsFilterVisible,
    setFilter
};
