import { combine, restore } from 'effector';

import { sharedFleetViewEffects, sharedFleeViewStores } from '@dat/shared-models/fleetView';
import { sharedClaimManagementStores } from '@dat/shared-models/claim-management';
import { CreateRestCopyClaim } from '@dat/api2/services/datCopyClaim';
import { contractEffects } from '@dat/shared-models/contract';

import { inboxDomain, pluginStores } from '../plugin';
import { contractsListEffects, contractsListStores } from '../contractsList';

const { createEffect, createEvent } = inboxDomain;

//Effects
const getVehicleFolderListFx = createEffect(contractsListEffects.listClaimsFx);

const getCheckInFolderListFx = createEffect(contractsListEffects.listClaimsFx);

const copyClaimFx = createEffect(CreateRestCopyClaim);

const getVehicleFolderFx = createEffect(sharedFleetViewEffects.getBaseFilterFx);

const getCheckInFolderFx = createEffect(sharedFleetViewEffects.getBaseFilterFx);

const updatedCopiedClaimFx = createEffect(contractEffects.createOrUpdateContractFx);

//Events
const resetVehicleFolderClaimList = createEvent();

//Stores
const vehicleFolderClaimList = restore(getVehicleFolderListFx.doneData, null).reset(resetVehicleFolderClaimList);

const checkInFolderClaimList = restore(getCheckInFolderListFx.doneData, null);

const isAppointmentsFolderActive = combine(
    [
        contractsListStores.activeFolder,
        pluginStores.pluginOptions,
        sharedClaimManagementStores.configuration,
        sharedFleeViewStores.activeFolder,
        contractsListStores.listUserSettings
    ],
    ([generalActiveFolder, pluginOptions, configuration, fleetActiveFolder, listUserSettings]) => {
        const isFleetViewFromPluginOptions = pluginOptions?.fleetView;
        const attachmentFolder = listUserSettings?.folderFilters?.find(item => {
            return (
                (!isFleetViewFromPluginOptions && item.id === generalActiveFolder?.folderId) ||
                (isFleetViewFromPluginOptions && item.id === Number(fleetActiveFolder?.id))
            );
        });
        if (configuration?.fleetView && attachmentFolder?.name === 'Appointments') {
            return true;
        }
        return false;
    }
);

export const inboxFleetViewEvents = {
    resetVehicleFolderClaimList
};
export const inboxFleetViewStores = {
    isAppointmentsFolderActive,
    vehicleFolderClaimList,
    checkInFolderClaimList
};

export const inboxFleetViewEffects = {
    getVehicleFolderListFx,
    copyClaimFx,
    updatedCopiedClaimFx,
    getVehicleFolderFx,
    getCheckInFolderFx,
    getCheckInFolderListFx
};
