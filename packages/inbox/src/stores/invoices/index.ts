import {
    createInvoice,
    createInvoiceRow,
    getInvoice,
    updateInvoice,
    getInvoicesByUsername,
    getAllInvoices,
    loadMoreInvoices,
    loadMoreInvoicesByUsername
} from '@dat/api2/services/parcella';
import { Invoice, InvoiceRow, InvoicesByUsername } from '@dat/api2/types';
import { createIsLoadingStore } from '@dat/core/utils/createIsLoadingStore';
import { sharedClaimManagementStores } from '@dat/shared-models/claim-management';
import { transformInvoiceDataForExport } from '@dat/shared-models/parcella/utils';
import { combine, createEffect, createEvent, createStore, restore } from 'effector';
import { TFunction } from 'react-i18next';
import { Row } from 'react-table';
import { Primitive } from 'type-fest';
import { InvoiceTableRows, InvoicingValues } from '../../types/invoice';
import { TableData, TemplateDataForInvoice } from '../../types/parcella';

const invoiceOptions = combine(
    [sharedClaimManagementStores.configuration],
    ([configuration]) => !configuration.hideBlocks?.header.includes('invoice')
);

const setInvoicingValues = createEvent<InvoicingValues>();
const invoicingValues = createStore<InvoicingValues>({}).on(setInvoicingValues, (_, payload) => payload);

const getInvoicesFx = createEffect(getInvoice);
const getAllInvoicesFx = createEffect(getAllInvoices);
const createInvoiceFx = createEffect(createInvoice);
const createInvoiceRowFx = createEffect(createInvoiceRow);
const updateInvoiceFx = createEffect(updateInvoice);
const getInvoicesByUsernameFx = createEffect(getInvoicesByUsername);
const loadMoreInvoicesFx = createEffect(loadMoreInvoices);
const loadMoreInvoicesByUsernameFx = createEffect(loadMoreInvoicesByUsername);

const resetInvoiceStore = createEvent();
const createdInvoiceStore = createStore<Invoice | null>(null)
    .on(createInvoiceFx.doneData, (_, payload) => payload)
    .reset(resetInvoiceStore);

const resetInvoiceData = createEvent();
const setInvoiceData = createEvent<Invoice>();
const invoiceData = restore(setInvoiceData, null).reset(resetInvoiceData);

const setInvoiceRowClaims = createEvent<Row<TableData>[]>();
const invoiceRowsClaims = createStore<Row<TableData>[]>([])
    .on(setInvoiceRowClaims, (_, payload) => payload)
    .reset(resetInvoiceStore);
const invoiceRowsStore = createStore<InvoiceRow[]>([]);

const invoiceContractId = createStore<number>(0).reset(resetInvoiceStore);

const invoicesStore = createStore<Invoice[]>([])
    .on(getInvoicesFx.doneData, (_, payload) => payload.list)
    .on(getInvoicesByUsernameFx.doneData, (_, payload) => payload.items)
    .on(loadMoreInvoicesFx.doneData, (state, payload) => [...state, ...payload.list])
    .on(loadMoreInvoicesByUsernameFx.doneData, (state, payload) => [...state, ...payload.items]);

const invoiceStoreForTable = createStore<InvoiceTableRows[]>([]);

const resetInvoiceTemplateData = createEvent();
const setTemplateDataForInvoiceContract = createEvent<TemplateDataForInvoice>();
const templateDataForInvoiceContract = createStore<TemplateDataForInvoice>({
    templateId: 0,
    templateData: { entry: [] }
})
    .on(setTemplateDataForInvoiceContract, (_, payload) => payload)
    .reset(resetInvoiceTemplateData);

const setIsNeedCreateInvoiceRow = createEvent<boolean>();
const isNeedCreateInvoiceRow = restore(setIsNeedCreateInvoiceRow, false);

const setPrintingInvoiceId = createEvent<number>();
const printingInvoiceId = restore(setPrintingInvoiceId, 0);

const setSelectedInvoices = createEvent<Invoice>();
const resetSelectedInvoices = createEvent();
const setAllInvoices = createEvent<Invoice[]>();
const selectedInvoices = createStore<Invoice[]>([])
    .on(setSelectedInvoices, (state, payload) => {
        const existingState = state.find(row => row.id === payload.id);
        if (existingState) {
            return state.filter(row => row.id !== payload.id);
        } else {
            return [...state, payload];
        }
    })
    .reset(resetSelectedInvoices)
    .on(setAllInvoices, (_, payload) => payload);

const setIsAllInvoicesSelected = createEvent<boolean>();
const isAllInvoicesSelected = restore(setIsAllInvoicesSelected, false);

const setIsNeedSelectAllInvoices = createEvent<boolean>();
const isNeedSelectAllInvoices = restore(setIsNeedSelectAllInvoices, false);

const isLoading = createIsLoadingStore(
    Object.values({
        getInvoicesFx,
        createInvoiceFx,
        createInvoiceRowFx,
        updateInvoiceFx,
        getInvoicesByUsernameFx,
        getAllInvoicesFx,
        loadMoreInvoicesFx,
        loadMoreInvoicesByUsernameFx
    })
);

const getInvoicesForExportFx = createEffect(async ({ payload, t }: { payload: InvoicesByUsername; t: TFunction }) => {
    const data = await getInvoicesByUsername(payload);
    return { data, t };
});

const invoiceExportData = createStore<Record<string, Primitive>[]>([]).on(
    getInvoicesForExportFx.doneData,
    (_, { data, t }) => {
        return transformInvoiceDataForExport(data.items, t);
    }
);

export const invoiceOptionsEffects = {
    getInvoicesFx,
    createInvoiceFx,
    createInvoiceRowFx,
    updateInvoiceFx,
    getInvoicesByUsernameFx,
    getAllInvoicesFx,
    loadMoreInvoicesFx,
    loadMoreInvoicesByUsernameFx,
    getInvoicesForExportFx
};

export const invoiceOptionsStores = {
    invoiceOptions,
    invoicingValues,
    invoicesStore,
    invoiceStoreForTable,
    createdInvoiceStore,
    invoiceRowsStore,
    invoiceRowsClaims,
    invoiceContractId,
    templateDataForInvoiceContract,
    invoiceData,
    isNeedCreateInvoiceRow,
    printingInvoiceId,
    selectedInvoices,
    isAllInvoicesSelected,
    isNeedSelectAllInvoices,
    isLoading,
    invoiceExportData
};

export const invoiceOptionsEvents = {
    setInvoicingValues,
    setInvoiceRowClaims,
    setTemplateDataForInvoiceContract,
    setInvoiceData,
    resetInvoiceStore,
    setIsNeedCreateInvoiceRow,
    resetInvoiceTemplateData,
    resetInvoiceData,
    setPrintingInvoiceId,
    setSelectedInvoices,
    resetSelectedInvoices,
    setAllInvoices,
    setIsAllInvoicesSelected,
    setIsNeedSelectAllInvoices
};
