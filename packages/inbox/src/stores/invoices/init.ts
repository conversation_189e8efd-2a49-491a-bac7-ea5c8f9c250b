import { Invoice, InvoiceRow } from '@dat/api2/types';
import { contractEffects } from '@dat/shared-models/contract/stores';
import { convertToValidValue } from '@dat/shared-models/parcella/utils';
import { sharedPrintoutEffects } from '@dat/shared-models/printout';
import { sharedUserStores } from '@dat/shared-models/user';
import { NotificationParams, toastEffects } from '@dat/shared-models/smart-components/Toast';
import { sample } from 'effector';
import { invoiceOptionsEffects, invoiceOptionsEvents, invoiceOptionsStores } from '.';
import {
    INVOICE_CONTRACT_TYPE,
    INVOICE_DOCUMENT_DESCRIPTION,
    INVOICE_NETWORK_TYPE,
    INVOICE_REPORT_IDENTIFICATION,
    PARCELLA_TABLE_COLS
} from '../../constants/parcella';
import {
    calculateValuesSum,
    getClaimFromParcel,
    getIssuerUser,
    getValueFromTableData
} from '../../utils/calculateValuesSum';
import { generateDataForInvoiceTable } from '../../utils/generateDataForInvoiceTable';
import { parcellaClaimsStores } from '../parcellaClaims';
import { createOrUpdateContractBaseFx } from '@dat/shared-models/baseEffects';
import { format, parseISO } from 'date-fns';
import { sharedConfigurationStores } from '@dat/shared-models/configuration';
import { parcellaEffects } from '@dat/shared-models/parcella';

const { selectedClaims } = parcellaClaimsStores;
const {
    invoicingValues,
    invoicesStore,
    invoiceStoreForTable,
    createdInvoiceStore,
    invoiceRowsStore,
    invoiceRowsClaims,
    invoiceData,
    invoiceExportData
} = invoiceOptionsStores;
const { createInvoiceFx, createInvoiceRowFx, getInvoicesByUsernameFx } = invoiceOptionsEffects;
const { createOrUpdateContractFx } = contractEffects;
const { invoiceContractId, templateDataForInvoiceContract, printingInvoiceId } = invoiceOptionsStores;
const { getDocumentWithConfFx } = sharedPrintoutEffects;
const { setIsNeedCreateInvoiceRow, resetInvoiceTemplateData, resetInvoiceData } = invoiceOptionsEvents;
const { isOpenInvoicingModal, parcelUserRole } = parcellaClaimsStores;
const { customerNumber, username } = sharedUserStores;
const { showSuccessToastFx, showErrorToastFx } = toastEffects;
const { generateExcelFx } = parcellaEffects;

sample({
    clock: selectedClaims.updates,
    source: { selectedClaims },
    fn: ({ selectedClaims }) => {
        const basicPaymentSum = calculateValuesSum(selectedClaims, 'basicPayment');
        const totalTaxableSum = calculateValuesSum(selectedClaims, 'totalTaxable');
        const totalNotTaxableSum = calculateValuesSum(selectedClaims, 'totalNotTaxable');
        const totalSum = calculateValuesSum(selectedClaims, 'total');
        const withHoldingTaxAmount = calculateValuesSum(selectedClaims, 'withHoldingTaxAmount');
        const socialSecurityAmount = calculateValuesSum(selectedClaims, 'socialSecurityAmount');
        const vatAmount = calculateValuesSum(selectedClaims, 'vatAmount');
        const withHoldingTaxPercent = getValueFromTableData(selectedClaims, 'withHoldingTaxPercent');
        const socialSecurityPercent = getValueFromTableData(selectedClaims, 'socialSecurityPercent');
        const vatPercent = getValueFromTableData(selectedClaims, 'vatPercent');
        const claim = getClaimFromParcel(selectedClaims);
        const user = getIssuerUser(selectedClaims);
        return {
            invoiceBasicPayment: convertToValidValue(basicPaymentSum),
            totalGross: convertToValidValue(totalNotTaxableSum + vatAmount),
            invoiceTotalTaxable: convertToValidValue(totalTaxableSum),
            invoiceTotalNotTaxable: convertToValidValue(totalNotTaxableSum),
            invoiceTotal: convertToValidValue(totalSum),
            invoiceIvaPercent: convertToValidValue(vatPercent),
            invoiceIva: convertToValidValue(vatAmount),
            invoiceContributionPercent: convertToValidValue(socialSecurityPercent),
            invoiceContribution: convertToValidValue(socialSecurityAmount),
            invoiceContributionSecondCasaPercent: convertToValidValue(withHoldingTaxPercent),
            invoiceContributionSecondCasa: convertToValidValue(withHoldingTaxAmount),
            issuer: user,
            claim: claim,
            invoiceDate: new Date().toISOString()
        };
    },
    target: invoicingValues
});

sample({
    clock: invoicesStore.updates,
    source: { invoicesStore },
    fn: ({ invoicesStore }) => {
        return invoicesStore && generateDataForInvoiceTable(invoicesStore);
    },
    target: invoiceStoreForTable
});

sample({
    clock: createInvoiceFx.doneData,
    source: { parcelUserRole, customerNumber, username },
    fn: ({ customerNumber, username }) => {
        return {
            customerNumber: customerNumber,
            username: username
        };
    },
    target: getInvoicesByUsernameFx
});

sample({
    clock: [createInvoiceFx.doneData, invoiceRowsClaims.updates],
    source: { createdInvoiceStore, invoiceRowsClaims },
    filter: ({ createdInvoiceStore }) => !!createdInvoiceStore,
    fn: ({ invoiceRowsClaims, createdInvoiceStore }) => {
        const data = invoiceRowsClaims.map(claim => {
            const totalTaxable = PARCELLA_TABLE_COLS.totalTaxable as keyof typeof claim.original;
            const totalNotTaxable = PARCELLA_TABLE_COLS.totalNotTaxable as keyof typeof claim.original;
            const taxableAccessoryExpenses = PARCELLA_TABLE_COLS.totalExpensesAccessory as keyof typeof claim.original;
            return {
                invoice_id: createdInvoiceStore?.id,
                parcel_id: claim?.original?.id,
                row_number: 0,
                description: '',
                total_taxable: parseFloat((claim.original[totalTaxable] as string)?.slice(0, -1).replace(',', '.')),
                not_taxable_accessory_expenses: parseFloat(
                    (claim.original[totalNotTaxable] as string)?.slice(0, -1).replace(',', '.')
                ),
                taxable_accessory_expenses: parseFloat(
                    (claim.original[taxableAccessoryExpenses] as string)?.slice(0, -1).replace(',', '.')
                )
            };
        }) as unknown as InvoiceRow[];

        return data;
    },
    target: invoiceRowsStore
});

sample({
    clock: printingInvoiceId.updates,
    source: { printingInvoiceId, invoicesStore, invoiceContractId },
    fn: ({ printingInvoiceId, invoicesStore }) => {
        const selectedInvoice = invoicesStore?.find(invoice => invoice.id === printingInvoiceId);
        return {
            contractId: selectedInvoice?.contractId as number,
            reportIdentification: INVOICE_REPORT_IDENTIFICATION,
            description: INVOICE_DOCUMENT_DESCRIPTION
        };
    },
    target: getDocumentWithConfFx
});

sample({
    clock: templateDataForInvoiceContract.updates,
    source: {
        templateDataForInvoiceContract,
        invoiceTemplateId: sharedConfigurationStores.userSettings.invoiceTemplateId
    },
    filter: ({ invoiceTemplateId }) => {
        return !!invoiceTemplateId;
    },
    fn: ({ templateDataForInvoiceContract }): DAT2.Request.CreateOrUpdateContract => {
        return {
            contractType: INVOICE_CONTRACT_TYPE,
            networkType: INVOICE_NETWORK_TYPE,
            ...templateDataForInvoiceContract
        };
    },
    target: createOrUpdateContractFx
});

sample({
    clock: templateDataForInvoiceContract.updates,
    source: { invoiceTemplateId: sharedConfigurationStores.userSettings.invoiceTemplateId },
    filter: ({ invoiceTemplateId }) => {
        return !invoiceTemplateId;
    },
    fn: (): NotificationParams => ({ message: { namespace: 'inbox', key: 'error.invoice.missingTemplateId' } }),
    target: showErrorToastFx
});

sample({
    clock: createOrUpdateContractFx.doneData,
    source: { templateDataForInvoiceContract, isOpenInvoicingModal },
    filter: ({ templateDataForInvoiceContract }) =>
        !!templateDataForInvoiceContract.templateId || !!isOpenInvoicingModal,
    fn: (_, clockData) => clockData,
    target: invoiceContractId
});

sample({
    clock: [createOrUpdateContractFx.doneData, invoiceContractId.updates],
    source: { invoiceData, invoiceContractId },
    filter: ({ invoiceContractId, invoiceData }) => !!invoiceContractId && !!invoiceData?.issuer_user_id,
    fn: ({ invoiceData, invoiceContractId }) => {
        return {
            ...invoiceData,
            contractId: invoiceContractId
        } as Invoice;
    },
    target: createInvoiceFx
});

const transformedContracts = sample({
    clock: createInvoiceFx.doneData,
    source: { selectedClaims, invoiceData },
    fn: ({ selectedClaims, invoiceData }) => {
        return selectedClaims.map(claim => {
            const claimId = claim?.original?.claim?.myclaim_id;
            const templateId = claim?.original?.claim?.templateId;
            const networkType = claim?.original?.claim?.networkType;
            const formattedDate = format(parseISO(invoiceData?.invoice_date || ''), 'dd.MM.yy');

            return {
                contractId: claimId,
                networkType: networkType,
                templateId: templateId,
                templateData: {
                    entry: [
                        {
                            key: 'invoice_number',
                            value: invoiceData?.invoice_number || ''
                        },
                        {
                            key: 'invoice_date',
                            value: formattedDate
                        }
                    ]
                }
            };
        });
    }
});

transformedContracts.watch(contracts => {
    contracts.forEach(contract => {
        if (contract.contractId) createOrUpdateContractBaseFx(contract);
    });
});

sample({
    clock: createInvoiceFx.doneData,
    fn: () => true,
    target: resetInvoiceData
});

sample({
    clock: createInvoiceFx.doneData,
    fn: (): NotificationParams => ({ message: { namespace: 'inbox', key: 'success.invoice.created' } }),
    target: showSuccessToastFx
});

sample({
    clock: createInvoiceFx.failData,
    fn: (): NotificationParams => ({ message: { namespace: 'inbox', key: 'error.invoice.create' } }),
    target: showErrorToastFx
});

sample({
    clock: createInvoiceRowFx.doneData,
    fn: () => false,
    target: setIsNeedCreateInvoiceRow
});

sample({
    clock: invoiceContractId.updates,
    target: resetInvoiceTemplateData
});

sample({
    clock: invoiceExportData,
    source: { invoiceExportData },
    filter: ({ invoiceExportData }) => !!invoiceExportData.length,
    fn: ({ invoiceExportData }) => {
        return {
            data: invoiceExportData,
            filename: 'invoices'
        };
    },
    target: generateExcelFx
});
