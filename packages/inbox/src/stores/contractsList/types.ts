export type PayloadForSetStatusOfContract = {
    contractId: number;
    status: DAT2.API2DAT5.MyClaimExternalService_schema1.contractStatus;
};

export type ListerUserSettings = DAT2.Internal.Response.LoadModuleConfig;

export enum FolderType {
    ALL_CLAIMS = 'ALL_CLAIMS',
    MY_CLAIMS = 'MY_CLAIMS',
    FILTER = 'DYNAMIC',
    STATIC = 'STATIC'
}

export type SearchOrFilterPayload = { columnName: string; value: string; type: DAT2.Plugins.Inbox.SearchOrFilter };

export type PartnerProposal = {
    value: number[];
    label: (string | null)[];
    count: number;
};
