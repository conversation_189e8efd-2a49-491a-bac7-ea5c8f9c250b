import { BellIcon, CloseIcon, ProfileIcon, MessageIcon } from '@wedat/ui-kit/components/Icons';
import { makeCustomScrollBar, media } from '@wedat/ui-kit/mediaQueries';

import styled from 'styled-components/macro';

export const TopContainer = styled.div`
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 5px 10px 5px;
`;

interface ContainerProps {
    isOpen: boolean;
}

export const Container = styled.div<ContainerProps>`
    @keyframes slideInFromRight {
        from {
            transform: translateX(100%);
        }
        to {
            transform: translateX(0);
        }
    }

    @keyframes slideOutToRight {
        from {
            transform: translateX(0);
        }
        to {
            transform: translateX(100%);
        }
    }

    animation: ${props =>
        props.isOpen ? 'slideInFromRight 0.5s ease-in-out forwards' : 'slideOutToRight 0.5s ease-in-out forwards'};

    position: fixed;
    z-index: 1001;
    width: 30%;
    height: 100%;
    right: 0;
    top: 0;
    padding: 20px 10px 10px 10px;
    background-color: ${({ theme }) => theme.colors.white};
    box-shadow: 0 0 8px 0 ${({ theme }) => theme.colors.gray['300']};
    display: flex;
    flex-direction: column;

    ${media.tablet`
        width: 70%;
    `}

    ${media.phoneBig`
        width: 100%;
    `}
`;

export const ArrowWrapper = styled.div`
    width: 40px;
    height: 40px;
    cursor: pointer;
    margin: 0 12px 10px 12px;

    display: flex;
    justify-content: center;
    align-items: center;

    text-align: center;
    border: 1px solid ${({ theme }) => theme.colors.dustBlue[300]};
    border-radius: 8px;

    svg {
        transform: rotate(180deg);
    }
`;

export const EmptyContent = styled.div`
    width: 100%;
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
`;

export const Content = styled.div`
    height: 93%;
    padding: 0 12px 15px 12px;
`;

export const Title = styled.div`
    margin-left: 10px;
    ${({ theme }) => theme.typography.mobileHeader}
    color: ${({ theme }) => theme.colors.dustBlue[900]};
`;

export const StyledCloseIcon = styled(CloseIcon)`
    cursor: pointer;
    color: ${({ theme }) => theme.colors.gray['400']};
    position: absolute;
    top: 10px;
    right: 10px;

    :active {
        color: ${({ theme }) => theme.colors.gray['600']};
    }
`;

export const StyledProfileIcon = styled(ProfileIcon)`
    height: 48px;
    width: 48px;
    min-width: 48px;
    min-height: 48px;
    margin-right: 10px;
`;

export const Image = styled.img`
    height: 48px;
    width: 48px;
    min-width: 48px;
    min-height: 48px;
    margin-right: 10px;
    border-radius: 50%;
    overflow: hidden;
`;

export const Card = styled.div`
    border-bottom: 2px solid ${({ theme }) => theme.colors.gray_10};
    border-radius: 12px;
    padding: 10px;
    display: flex;
    cursor: pointer;
    flex-direction: column;
    position: relative;

    &:hover {
        background-color: ${({ theme }) => theme.colors.blue['50']};
    }

    margin-bottom: 10px;
`;

export const ContractLink = styled.button`
    font-size: 16px;
    color: ${({ theme }) => theme.colors.gray_300};
    cursor: pointer;
    margin-left: 16px;
    margin-right: 4px;
    border-bottom: 2px solid ${({ theme }) => theme.colors.yellow['10']};
    transition:
        border-bottom 0.3s,
        opacity 0.3s;

    &:hover {
        border-bottom: 2px solid transparent;
        opacity: 0.5;
    }
`;

export const ProfileWrapper = styled.div`
    display: flex;
    align-items: center;
`;

export const ProfileInfoWrapper = styled.div`
    display: flex;
    flex-direction: column;
`;

export const Wrapper = styled.div`
    max-width: 100%;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid ${({ theme }) => theme.colors.gray['300']};
    margin: 10px;
    height: 50%;
    overflow-y: auto;

    ${makeCustomScrollBar()}
`;

export const Tag = styled.span`
    color: ${({ theme }) => theme.colors.textPrimary};
    border: 1px solid ${({ theme }) => theme.colors.dustBlue['100']};
    background-color: ${({ theme }) => theme.colors.dustBlue['100']};
    padding: 4px 8px 4px 8px;
    border-radius: 8px;
`;

export const StyledBellIcon = styled(BellIcon)`
    color: ${({ theme }) => theme.colors.dustBlue['200']};
    width: 35px;
    height: 35px;
`;

export const StyledMessageIcon = styled(MessageIcon)`
    color: ${({ theme }) => theme.colors.dustBlue['200']};
    width: 35px;
    height: 35px;
`;

export const EmptyText = styled.span`
    color: ${({ theme }) => theme.colors.dustBlue['400']};
    font: ${({ theme }) => theme.typography.font14.font};
    margin-top: 10px;
`;

export const Description = styled.span`
    color: ${({ theme }) => theme.colors.dustBlue['800']};
    font: ${({ theme }) => theme.typography.font14.font};

    a {
        color: ${({ theme }) => theme.colors.blue['700']};
    }
`;
