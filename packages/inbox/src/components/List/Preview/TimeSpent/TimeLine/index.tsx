import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';
import { previewStores } from '../../../../../stores/preview';
import { TimeLineProps } from '../../../../../types/productivity';
import { transformData } from '../../../../../utils/transformProductivityData';
import { COLORS_FOR_PRODUCTIVITY_LINE } from '../../constants';
import { Time, TimeLineComp, TimeLineItem, TimeLineWrapper, Title, UpperPart } from './styles';

export const TimeLine: React.FC<TimeLineProps> = ({ wholeTime = 0, convertTime }) => {
    const { t } = useTranslation();

    const productivityData = useUnit(previewStores.productivityStore);
    const convertedData = transformData(productivityData);

    return wholeTime !== 0 ? (
        <TimeLineWrapper>
            <UpperPart>
                <Title>{t(`previewTabs.totalSpend`)}</Title>

                <Time>{convertTime(wholeTime)}</Time>
            </UpperPart>
            <TimeLineComp>
                {convertedData.map(({ role, spendTime }, index) => (
                    <TimeLineItem
                        key={role}
                        width={(spendTime * 100) / wholeTime}
                        color={COLORS_FOR_PRODUCTIVITY_LINE[index]}
                        isFirst={index === 0}
                        isLast={index === convertedData?.length - 1}
                        isUnique={convertedData?.length === 1}
                    />
                ))}
            </TimeLineComp>
        </TimeLineWrapper>
    ) : null;
};
