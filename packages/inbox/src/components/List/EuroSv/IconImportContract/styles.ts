import styled from 'styled-components/macro';
import { media } from '@wedat/ui-kit/mediaQueries';

export const ImportIconWrapper = styled.div<{ isEuroSv?: boolean; isGreen?: boolean }>`
    position: absolute;
    top: 4px;
    right: 0;
    z-index: 0;
    ${({ isEuroSv }) => isEuroSv && 'transform: translateX(50%);'};
    color: ${({ isGreen, theme }) => (isGreen ? theme.colors.green[800] : theme.colors.blue[800])};

    ${media.phoneBig`
        left: 0;
        transform: translateX(0);
    `}
`;
