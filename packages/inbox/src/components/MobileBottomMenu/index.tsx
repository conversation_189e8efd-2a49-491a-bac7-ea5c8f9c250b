import { FC, useCallback, useMemo } from 'react';
import { useUnit } from 'effector-react';
import { EuroSvClaims, ReloadIcon, SearchIcon, WedatClaims, ActivityClaims } from '@wedat/ui-kit/components/Icons';
import { sharedTemplateStores } from '@dat/shared-models/template';

import { NewOrder } from '../NewOrder';
import { contractsListEuroSvEvents } from '../../stores/euroSv/contractsListEuroSv';
import { contractsEuroSvDataStores } from '../../stores/euroSv/contractsEuroSvData';
import { configurationStores } from '../../stores/configuration';
import { contractsListEffects, contractsListEvents } from '../../stores/contractsList';
import { FooterMobile, MenuProps } from '@dat/smart-components/FooterMobile';
import { ProfileAddressBook } from '../ProfileAdressBook';
import { notificationsEvents } from '../../stores/notifications';
import { infosStores } from '../../stores/infos';
import { commonEvents, commonStores } from '../../stores/common';
import { pluginStores } from '../../stores/plugin';
import { FleetViewButtons } from '../FleetViewButtons';
import { sharedActivityManagerStores } from '@dat/shared-models/activityManager';
import { AssessorVehicleManager } from '../ActivityManager/AssessorVehicleManager';
import { NewOrderWrapper, IconWrapper, StyledInfoIcon } from './styles';
import { SlideUpMobileMenu } from '../List/InboxList/MobileList/Item/SlideUpMobileMenu';
import { SlideUpMenuItems } from '../List/InboxList/MobileList/Item/SlideUpMobileMenu/types';
import { INBOX_TABS_IDS } from '../../stores/common/constants';
import { useToggle } from '@dat/core/hooks/useToggle';
import { activityManagerEvents } from '../../stores/activityManager';

interface Props {
    showCreateNewClaim: boolean;
}

export const MobileBottomMenu: FC<Props> = ({ showCreateNewClaim }) => {
    const configuration = useUnit(configurationStores.configuration);
    const activityManagerEnabled = useUnit(sharedActivityManagerStores.activityManagerEnabled);
    const isEuroSvEnabled = useUnit(contractsEuroSvDataStores.isEuroSvEnabled);
    const isFleetView = useUnit(pluginStores.pluginOptions)?.fleetView;
    const productsConfiguration = useUnit(sharedTemplateStores.productsConfiguration);
    const activeTab = useUnit(commonStores.inboxActiveTab);
    const [isOpenMenu, toggleOpenMenu] = useToggle(false);
    const isFleetViewFromConfig = productsConfiguration?.['claim-management']?.fleetView;

    const handleSearchToggleMobile = () => contractsListEvents.toggleMobileSearch();
    const handleReload = useCallback(() => {
        if (activeTab === INBOX_TABS_IDS.EUROSV) {
            contractsListEuroSvEvents.reloadContractsFn();
        } else {
            contractsListEffects.reloadContractsFx();
        }
    }, [activeTab]);

    const infos = useUnit(infosStores.infos);
    const handleInfo = () => notificationsEvents.openNotificationAside();

    const slideUpMenuItems: SlideUpMenuItems = useMemo(
        () => [
            {
                title: 'Inbox',
                Icon: WedatClaims,
                onClick: () => {
                    commonEvents.setInboxActiveTab(INBOX_TABS_IDS.INBOX);
                    toggleOpenMenu();
                },
                isSelected: activeTab === INBOX_TABS_IDS.INBOX
            },
            {
                title: 'EuroSv',
                Icon: EuroSvClaims,
                onClick: () => {
                    contractsListEuroSvEvents.setIsEuroSvListOpened(activeTab !== INBOX_TABS_IDS.EUROSV);

                    commonEvents.setInboxActiveTab(INBOX_TABS_IDS.EUROSV);
                    toggleOpenMenu();
                },
                isSelected: activeTab === INBOX_TABS_IDS.EUROSV
            },
            {
                title: 'Activity manager',
                Icon: ActivityClaims,
                onClick: () => {
                    activityManagerEvents.setIsOpenedActivityManagerList(activeTab !== INBOX_TABS_IDS.ACTIVITY_MANAGER);

                    commonEvents.setInboxActiveTab(INBOX_TABS_IDS.ACTIVITY_MANAGER);
                    toggleOpenMenu();
                },
                isSelected: activeTab === INBOX_TABS_IDS.ACTIVITY_MANAGER
            }
        ],
        [activeTab, toggleOpenMenu]
    );

    const menu = useMemo(() => {
        const menuItems: MenuProps[] = [];

        menuItems.push({
            component: () => (
                <IconWrapper onClick={handleSearchToggleMobile}>
                    <SearchIcon />
                </IconWrapper>
            )
        });

        // NOTE: without this check, component returns null and an empty square appears in footer
        if (configuration?.subjects) {
            menuItems.push({
                component: () => <ProfileAddressBook inFooter />
            });
        }

        menuItems.push({
            component: () => (
                <IconWrapper onClick={handleReload}>
                    <ReloadIcon />
                </IconWrapper>
            )
        });

        if (activityManagerEnabled) {
            menuItems.push({
                component: () => (
                    <IconWrapper>
                        <AssessorVehicleManager />
                    </IconWrapper>
                )
            });
        }

        if (isEuroSvEnabled && activityManagerEnabled) {
            menuItems.push({
                component: () => (
                    <IconWrapper onClick={toggleOpenMenu}>
                        {activeTab === INBOX_TABS_IDS.EUROSV ? (
                            <EuroSvClaims />
                        ) : activeTab === INBOX_TABS_IDS.ACTIVITY_MANAGER ? (
                            <ActivityClaims />
                        ) : (
                            <WedatClaims />
                        )}
                    </IconWrapper>
                )
            });
        }

        if (isEuroSvEnabled && !activityManagerEnabled) {
            menuItems.push({
                component: () => (
                    <IconWrapper
                        onClick={() => {
                            contractsListEuroSvEvents.setIsEuroSvListOpened(activeTab !== INBOX_TABS_IDS.EUROSV);
                            commonEvents.setInboxActiveTab(
                                activeTab === INBOX_TABS_IDS.EUROSV ? INBOX_TABS_IDS.INBOX : INBOX_TABS_IDS.EUROSV
                            );
                        }}
                    >
                        {activeTab === INBOX_TABS_IDS.EUROSV ? <WedatClaims /> : <EuroSvClaims />}
                    </IconWrapper>
                )
            });
        }

        if (!isEuroSvEnabled && activityManagerEnabled) {
            menuItems.push({
                component: () => (
                    <IconWrapper
                        onClick={() => {
                            activityManagerEvents.setIsOpenedActivityManagerList(
                                activeTab !== INBOX_TABS_IDS.ACTIVITY_MANAGER
                            );
                            commonEvents.setInboxActiveTab(
                                activeTab === INBOX_TABS_IDS.ACTIVITY_MANAGER
                                    ? INBOX_TABS_IDS.INBOX
                                    : INBOX_TABS_IDS.ACTIVITY_MANAGER
                            );
                        }}
                    >
                        {activeTab === INBOX_TABS_IDS.ACTIVITY_MANAGER ? <WedatClaims /> : <ActivityClaims />}
                    </IconWrapper>
                )
            });
        }

        if (!!infos.length) {
            menuItems.push({
                component: () => (
                    <IconWrapper onClick={handleInfo}>
                        <StyledInfoIcon />
                    </IconWrapper>
                )
            });
        }

        return menuItems;
    }, [
        configuration?.subjects,
        activityManagerEnabled,
        isEuroSvEnabled,
        infos.length,
        handleReload,
        toggleOpenMenu,
        activeTab
    ]);

    return (
        <>
            {showCreateNewClaim && activeTab !== INBOX_TABS_IDS.EUROSV && !isFleetView && (
                <NewOrderWrapper>
                    <NewOrder withoutText fillHeight />
                </NewOrderWrapper>
            )}

            {activeTab !== INBOX_TABS_IDS.EUROSV && isFleetViewFromConfig && (
                <NewOrderWrapper isDoubleButtons>
                    <FleetViewButtons withoutText />
                </NewOrderWrapper>
            )}

            <FooterMobile menu={menu} disableBurgerMenu />

            <SlideUpMobileMenu
                isOpenMenu={isOpenMenu}
                closeSlideUpMenu={toggleOpenMenu}
                contractId={''}
                slideUpMenuItems={slideUpMenuItems}
            />
        </>
    );
};
