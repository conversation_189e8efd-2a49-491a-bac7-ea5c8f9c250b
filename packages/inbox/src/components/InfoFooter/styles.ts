import styled from 'styled-components';
import { InfoIcon } from '@wedat/ui-kit';

export const StyledInfoFooter = styled.div`
    height: 44px;
    display: flex;
    align-items: center;
    overflow: hidden;
`;

export const StyledInfoIcon = styled(InfoIcon)`
    color: ${({ theme }) => theme.colors.blue['400']};
    cursor: pointer;
`;

export const StyledInfoContent = styled.div`
    background-color: ${({ theme }) => theme.colors.blue['50']};
    height: 100%;
    border-radius: 4px;
    padding: 4px 10px 4px 10px;
    display: flex;
    overflow: hidden;

    a {
        color: ${({ theme }) => theme.colors.blue['700']};
    }
`;

export const StyledInfo = styled.div`
    width: 100%;
    height: 28px;
    display: flex;
    align-items: center;
    margin-left: 4px;
    overflow: hidden;
`;
