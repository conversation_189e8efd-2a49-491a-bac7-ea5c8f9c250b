import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { Formik } from 'formik';

import { getKindOptions, getStatusOptions } from './utils';
import { EditableAssessorVehicle } from '../types';
import { INITIAL_FORM_VALUATION_VALUES } from './constants';

import { ModalPrimary } from '@wedat/ui-kit';
import { DatepickerField, InputField, SelectField } from '@wedat/ui-kit/Formik';
import {
    InputsWrapper,
    Form,
    TextareaFieldStyled,
    ButtonsWrapper,
    ButtonCloseStyled,
    ButtonSubmitStyled
} from './styles';

interface CreateOrUpdateVehicleModalProps {
    onCancel: () => void;
    onSave: (data: EditableAssessorVehicle) => void;
    vehicle?: EditableAssessorVehicle;
}

export const CreateOrUpdateVehicleModal: FC<CreateOrUpdateVehicleModalProps> = ({ onCancel, onSave, vehicle }) => {
    const { t } = useTranslation('inbox');

    const handleSubmit = (data: EditableAssessorVehicle) => {
        onSave({
            ...data,
            firstRegistration: new Date(data.firstRegistration).toISOString()
        });
        onCancel();
    };

    return (
        <ModalPrimary
            isOpen
            bodyWidth="600px"
            maxWidth="600px"
            bodyHeight="fit-content"
            borderRadius="8px"
            title={t(`vehicleManager.${vehicle ? 'editVehicleModal' : 'addVehicleModal'}.title`)}
            bodyNoPadding
            onDismiss={onCancel}
        >
            <Formik initialValues={vehicle || INITIAL_FORM_VALUATION_VALUES()} onSubmit={handleSubmit}>
                {({ values }) => (
                    <Form>
                        <InputsWrapper>
                            <TextareaFieldStyled
                                name="description"
                                label={t('vehicleManager.addVehicleModal.description')}
                                defaultHeight="100px"
                                maxRows={4}
                                expandable
                            />

                            <DatepickerField
                                maxDate={new Date()}
                                name="firstRegistration"
                                label={t('vehicleManager.addVehicleModal.firstRegistration')}
                                portalId="firstRegistrationDatepicker"
                            />
                            <InputField name="regNumber" label={t('vehicleManager.addVehicleModal.regNumber')} />
                            <SelectField
                                name="kind"
                                label={t('vehicleManager.addVehicleModal.kind.label')}
                                options={getKindOptions(t)}
                                valueKey="key"
                            />

                            <SelectField
                                name="status"
                                label={t('vehicleManager.addVehicleModal.status.label')}
                                options={getStatusOptions(t)}
                                valueKey="key"
                            />
                        </InputsWrapper>

                        <ButtonsWrapper>
                            <ButtonCloseStyled typeStyle={{ type: 'outline' }} onClick={onCancel}>
                                {t('vehicleManager.addVehicleModal.close')}
                            </ButtonCloseStyled>
                            <ButtonSubmitStyled
                                type="submit"
                                disabled={!values.description || !values.regNumber || !values.firstRegistration}
                            >
                                {t('vehicleManager.addVehicleModal.save')}
                            </ButtonSubmitStyled>
                        </ButtonsWrapper>
                    </Form>
                )}
            </Formik>
        </ModalPrimary>
    );
};
