import { FC } from 'react';
import { useMedia } from '@dat/core/hooks/useMedia';
import { Drawer } from '@wedat/kit';
import { sizes } from '@wedat/ui-kit/mediaQueries';

type Props = {
    isModalOpen: boolean;
    onDismissModal: () => void;
    children: JSX.Element;
    title: string;
    width?: number;
    halfScreen?: boolean;
};

export const ParcellaDrawer: FC<Props> = ({ isModalOpen, onDismissModal, children, title, width, halfScreen }) => {
    const isMobile = useMedia(sizes.phoneBig);

    return isModalOpen ? (
        <Drawer
            aria-label={title}
            header={title}
            clickable
            visible={isModalOpen}
            onHide={onDismissModal}
            width={width ? `${width}px` : '480px'}
            position="right"
            arrowPosition="left"
            fullScreen={isMobile}
            halfScreen={halfScreen}
            bodyNoPadding
        >
            {children}
        </Drawer>
    ) : null;
};
