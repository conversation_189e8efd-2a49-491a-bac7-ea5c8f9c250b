import { ChangeEvent, FC } from 'react';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';
import { sharedConfigurationStores } from '@dat/shared-models/configuration';
import { orderEffects } from '../../stores/order';
import { ImportIniText, Input, Label } from './styles';

export const ImportIniAlternative: FC<{ templateColor?: string; templateColorActive?: string }> = ({
    templateColor,
    templateColorActive
}) => {
    const { t } = useTranslation();
    const claimBuilders = useUnit(sharedConfigurationStores.claimBuilders);

    const handleInternalIniImport = async (event: ChangeEvent<HTMLInputElement>) => {
        event?.stopPropagation();
        const file = event?.target?.files?.[0];
        const templateId = claimBuilders[0].templateId;
        if (!file || !templateId) return;

        orderEffects.handleUploadIniFileFx({ file, templateId });
    };

    return (
        <Label>
            <ImportIniText $templateColor={templateColor} $templateColorActive={templateColorActive}>
                {t('importIniAlternativeInHeader')}
            </ImportIniText>
            <Input hidden type="file" accept=".txt,.ini,.svd" onChange={handleInternalIniImport} />
        </Label>
    );
};
