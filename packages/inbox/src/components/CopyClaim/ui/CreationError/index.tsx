import React, { <PERSON> } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Text } from '@wedat/ui-kit';
import { ActionButton, StatusContainer, StatusIcon, CrossCircleStyled } from '../../style';

interface Props {
    onClose: () => void;
}

export const CreationError: FC<Props> = ({ onClose }) => {
    const { t } = useTranslation();

    return (
        <StatusContainer>
            <Text>{t('copyClaim.ErrorCopying')}</Text>
            <StatusIcon isRed>
                <CrossCircleStyled />
            </StatusIcon>
            <Text>{t('copyClaim.TryAgain')}</Text>
            <ActionButton>
                <Button onClick={onClose}>{t('copyClaim.Close')}</Button>
            </ActionButton>
        </StatusContainer>
    );
};
