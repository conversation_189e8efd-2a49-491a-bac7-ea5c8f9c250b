import { use<PERSON>allback, FC } from 'react';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';

import { MenuItem } from './types';
import { Header as HeaderComponent } from '@dat/smart-components/Header';
import { Button, Item, StyledText, NotificationsStyled, LeftItem } from './styles';
import { contractsListEffects } from '../../stores/contractsList';
import { useMedia } from '@dat/core/hooks/useMedia';
import { sizes } from '@wedat/ui-kit/mediaQueries';
import { NewOrder } from '../NewOrder';
import { contractsListEuroSvStores, contractsListEuroSvEvents } from '../../stores/euroSv/contractsListEuroSv';
import { pluginStores } from '../../stores/plugin';
import { ImportIni } from '../ImportIni';
import { ImportIniAlternative } from '../ImportIniAlternative';
import { Invoice } from '../Invoice';
import { ProfileAddressBook } from '../ProfileAdressBook';
import { SchedulerMenu } from '@dat/claim-management/src/components/SchedulerMenu';
import { efficiencyEvents } from '../../stores/efficiency';
import { configurationStores } from '../../stores/configuration';
import { appointmentModalEvents } from '@dat/scheduler/src/stores/appointmentModal';
import { sharedConfigurationStores } from '@dat/shared-models/configuration';
import { sharedClaimManagementStores } from '@dat/shared-models/claim-management';
import { offlineStores } from '@dat/shared-models/offline';
import { EuroSvClaims, TooltipFloating, WedatClaims, ActivityClaims } from '@wedat/ui-kit';
import { LeftContentProps } from '@dat/smart-components/Header/types';
import { commonEvents, commonStores } from '../../stores/common';
import { InboxTabs } from '../../stores/common/types';
import { containedPluginsStores } from '@dat/claim-management/src/stores/containedPlugins';
import { contractsEuroSvDataStores } from '../../stores/euroSv/contractsEuroSvData';
import { ProfileFAQ } from '../ProfileFAQ';
import { sharedUserRoleAccessibilityStores } from '@dat/shared-models/configuration/pluginAccessibilityByUserRole';
import { sharedTemplateStores } from '@dat/shared-models/template';
import { colorPaletteStores } from '@dat/shared-models/colorPalette';
import { sharedActivityManagerStores } from '@dat/shared-models/activityManager';
import { parcellaClaimsStores } from '../../stores/parcellaClaims';
import { FleetViewButtons } from '../FleetViewButtons';
import { AssessorVehicleManager } from '../ActivityManager/AssessorVehicleManager';
import { activityManagerEvents, activityManagerStores } from '../../stores/activityManager';
import { INBOX_TABS_IDS } from '../../stores/common/constants';
import { sharedHideBlocksStores } from '@dat/shared-models/hide-blocks';
import { DeductibleComponent } from '../Deductible';
import { SpireContractComponent } from '../SpireContract';
import { ReimbursementComponent } from '../Reimbursement';

interface Props {
    fixed?: boolean;
}

export const Header: FC<Props> = props => {
    const isTabletSmall = useMedia(sizes.tabletSmall);
    const isOnline = useUnit(offlineStores.isOnline);
    const isMobile = useMedia(sizes.phoneBig);
    const importIni = useUnit(pluginStores.pluginOptions)?.settings?.importIni;
    const configuration = useUnit(configurationStores.configuration);
    const isInvoiceAvailable = useUnit(containedPluginsStores.invoiceOptions);
    const isEuroSvEnabled = useUnit(contractsEuroSvDataStores.isEuroSvEnabled);
    const isEuroSvListOpened = useUnit(contractsListEuroSvStores.isEuroSvListOpened);
    const isOpenedActivityManagerList = useUnit(activityManagerStores.isOpenedActivityManagerList);
    const claimManagementConfiguration = useUnit(sharedClaimManagementStores.configuration);
    const menuItemsSet: MenuItem[] = [];
    const roleOfUser = useUnit(sharedConfigurationStores.userRole);
    const rolesAccess = useUnit(sharedConfigurationStores.templateRoles)?.accessibility;
    const shouldNotVisibleNewOrderButton = roleOfUser && rolesAccess?.notEnableToCreateClaim?.includes(roleOfUser);
    const allowCreateNewClaim = claimManagementConfiguration.notAllowCreateNewClaim;
    const technicalSupport = claimManagementConfiguration.technicalSupport;
    const showParcellaInbox = useUnit(sharedConfigurationStores.userSettings.showParcellaInbox);
    const isHiddenInvoiceInInbox = useUnit(sharedUserRoleAccessibilityStores.isHiddenInvoiceInInbox);
    const isHiddenParcellaInInbox = useUnit(sharedUserRoleAccessibilityStores.isHiddenParcellaInInbox);
    const isVisibleProductivityInInbox = useUnit(sharedUserRoleAccessibilityStores.isVisibleProductivityInInbox);
    const templateIdForRoles = useUnit(sharedTemplateStores.templateIdForRoles);
    const claimBuilders = useUnit(sharedConfigurationStores.claimBuilders);
    const isSuperUser = useUnit(parcellaClaimsStores.isSuperUser);
    const isVisibleParcellaExpert = useUnit(sharedUserRoleAccessibilityStores.isVisibleParcellaForExpert);
    const isVisibleParcellaCollaborator = useUnit(sharedUserRoleAccessibilityStores.isVisibleParcellaForCollaborator);
    const activityManagerEnabled = useUnit(sharedActivityManagerStores.activityManagerEnabled);
    const hideBlocks = useUnit(sharedHideBlocksStores.hideBlocks);
    const isCalledSpireContractMBLI = claimManagementConfiguration.isCalledSpireContractMBLI;

    const isSchedulerhidden = hideBlocks?.header.includes('scheduler');
    const isNotificationhidden = hideBlocks?.header.includes('notification');

    const productsConfiguration = useUnit(sharedTemplateStores.productsConfiguration);

    const isFleetViewFromConfig = productsConfiguration?.['claim-management']?.fleetView;
    const isVisibleDeductible = productsConfiguration?.inbox?.settings?.isVisibleDeductible;
    const isVisibleReimbursement = productsConfiguration?.inbox?.settings?.isVisibleReimbursement;
    const defaultTemplateIdForRoles = useUnit(sharedConfigurationStores.userSettings?.defaultTemplateIdForRoles);
    const isValidTemplateId = defaultTemplateIdForRoles
        ? templateIdForRoles === defaultTemplateIdForRoles
        : !!templateIdForRoles;

    const inboxActiveTab = useUnit(commonStores.inboxActiveTab);

    const isNeedShowParcella = isSuperUser || isVisibleParcellaExpert || isVisibleParcellaCollaborator;
    const { t } = useTranslation();

    const menuItems = isOnline ? menuItemsSet : [];

    const templatePalette = useUnit(colorPaletteStores.templatePalette);

    const notificationButton = (
        <TooltipFloating content={t('header.notification')} placement="top-start">
            <NotificationsStyled templateBg={templatePalette?.colorBg} templateColor={templatePalette?.colorText} />
        </TooltipFloating>
    );

    if (importIni && !isMobile && claimBuilders.length <= 1) {
        menuItems.push({
            component: ImportIni,
            isText: true,
            props: {
                templateColor: templatePalette?.colorText,
                templateColorActive: templatePalette?.colorActive
            }
        });
    }

    if (!isMobile && claimBuilders.length === 1 && claimBuilders[0]?.layoutType === 'importIniAlternative') {
        menuItems.push({
            component: ImportIniAlternative,
            isText: true,
            props: {
                templateColor: templatePalette?.colorText,
                templateColorActive: templatePalette?.colorActive
            }
        });
    }

    if (isInvoiceAvailable) {
        menuItemsSet.push({
            component: Invoice,
            isText: true,
            props: {
                templateColor: templatePalette?.colorText
            }
        });
    }
    if (!isMobile && isFleetViewFromConfig) {
        menuItemsSet.push({
            component: FleetViewButtons,
            isText: true,
            props: {
                templateColor: templatePalette?.colorText,
                templateBg: templatePalette?.colorBg,
                withoutText: false,
                variant: 'palette'
            }
        });
    }
    if (
        !isMobile &&
        !allowCreateNewClaim &&
        !shouldNotVisibleNewOrderButton &&
        claimBuilders.length &&
        !(claimBuilders.length === 1 && claimBuilders[0].layoutType === 'importIniAlternative')
    ) {
        !isTabletSmall &&
            menuItemsSet.push({
                component: NewOrder,
                isText: true,
                props: {
                    templateColor: templatePalette?.colorText,
                    templateBg: templatePalette?.colorBg,
                    withoutText: false
                }
            });

        if (technicalSupport) {
            menuItemsSet.push({
                component: ProfileFAQ,
                isIcon: true,
                tooltipText: t('header.FAQ'),
                props: {
                    templateColor: templatePalette?.colorText
                }
            });
        }

        // NOTE: without this check, component returns null and an empty square appears in header
        if (configuration?.subjects) {
            menuItems.push({
                component: ProfileAddressBook,
                isIcon: true,
                tooltipText: t('header.ProfileAddressBook'),
                props: {
                    templateColor: templatePalette?.colorText
                }
            });
        }

        if (activityManagerEnabled) {
            menuItems.push({
                component: AssessorVehicleManager,
                isIcon: true,
                props: {
                    templateColor: templatePalette?.colorText
                }
            });
        }
    }
    if (isCalledSpireContractMBLI) {
        menuItemsSet.push({
            component: SpireContractComponent,
            isIcon: true,
            tooltipText: t('header.spire.contract')
        });
    }

    if (isVisibleDeductible) {
        menuItemsSet.push({
            component: DeductibleComponent,
            isIcon: true,
            tooltipText: t('header.deductible')
        });
    }

    if (isVisibleReimbursement) {
        menuItemsSet.push({
            component: ReimbursementComponent,
            isIcon: true,
            tooltipText: t('header.reimbursement')
        });
    }

    if (!isSchedulerhidden && !isMobile) {
        menuItemsSet.push({
            component: SchedulerMenu,
            isIcon: true,
            tooltipText: t('header.SchedulerMenuWrapper'),
            props: {
                isInInbox: true
            }
        });
    }

    const handleSchedulerClick = useCallback((item: MenuItem) => {
        item.component === SchedulerMenu && appointmentModalEvents.openSchedulerFromInbox();
    }, []);

    const handleClickOnReload = () => {
        if (isEuroSvListOpened) {
            contractsListEuroSvEvents.reloadContractsFn();
        }
        if (isOpenedActivityManagerList) {
            activityManagerEvents.reloadActivities();
        } else {
            contractsListEffects.reloadContractsFx();
            efficiencyEvents.reloadEfficiencyMetrics();
        }
    };

    const onTabClick = (tab: InboxTabs) => {
        commonEvents.setInboxActiveTab(tab);
    };

    const getLeftMenuItems = () => {
        const leftItems: LeftContentProps[] = [
            {
                element: (
                    <LeftItem
                        onClick={() => onTabClick(INBOX_TABS_IDS.INBOX)}
                        selected={inboxActiveTab === INBOX_TABS_IDS.INBOX}
                        $templateActive={templatePalette?.colorActive}
                        $templateColor={templatePalette?.colorText}
                    >
                        <WedatClaims /> Inbox
                    </LeftItem>
                ),
                dropdownProps: {
                    id: INBOX_TABS_IDS.INBOX,
                    title: 'Inbox',
                    onClick: () => onTabClick(INBOX_TABS_IDS.INBOX),
                    selected: inboxActiveTab === INBOX_TABS_IDS.INBOX
                }
            }
        ];

        if (isEuroSvEnabled) {
            leftItems.push({
                element: (
                    <LeftItem
                        onClick={() => {
                            contractsListEuroSvEvents.setIsEuroSvListOpened(true);
                            onTabClick(INBOX_TABS_IDS.EUROSV);
                        }}
                        selected={inboxActiveTab === INBOX_TABS_IDS.EUROSV}
                        $templateActive={templatePalette?.colorActive}
                        $templateColor={templatePalette?.colorText}
                    >
                        <EuroSvClaims /> Euro SV
                    </LeftItem>
                ),
                dropdownProps: {
                    id: INBOX_TABS_IDS.EUROSV,
                    title: 'Euro sv',
                    onClick: () => onTabClick(INBOX_TABS_IDS.EUROSV),
                    selected: inboxActiveTab === INBOX_TABS_IDS.EUROSV
                }
            });
        }

        if (activityManagerEnabled) {
            leftItems.push({
                element: (
                    <LeftItem
                        onClick={() => {
                            if (inboxActiveTab !== INBOX_TABS_IDS.ACTIVITY_MANAGER) {
                                activityManagerEvents.setIsOpenedActivityManagerList(true);
                                onTabClick(INBOX_TABS_IDS.ACTIVITY_MANAGER);
                            }
                        }}
                        selected={inboxActiveTab === INBOX_TABS_IDS.ACTIVITY_MANAGER}
                        $templateActive={templatePalette?.colorActive}
                        $templateColor={templatePalette?.colorText}
                    >
                        <ActivityClaims /> {t('header.tabs.activities', { defaultValue: 'Activities' })}
                    </LeftItem>
                ),
                dropdownProps: {
                    id: INBOX_TABS_IDS.ACTIVITY_MANAGER,
                    title: t('header.tabs.activities'),
                    onClick: () => onTabClick(INBOX_TABS_IDS.ACTIVITY_MANAGER),
                    selected: inboxActiveTab === INBOX_TABS_IDS.ACTIVITY_MANAGER
                }
            });
        }

        if (isValidTemplateId && isNeedShowParcella && showParcellaInbox) {
            if (!isHiddenParcellaInInbox) {
                leftItems.push({
                    element: (
                        <LeftItem
                            onClick={() => onTabClick(INBOX_TABS_IDS.PARCELLA)}
                            selected={inboxActiveTab === INBOX_TABS_IDS.PARCELLA}
                            $templateActive={templatePalette?.colorActive}
                            $templateColor={templatePalette?.colorText}
                        >
                            Parcella Claims
                        </LeftItem>
                    ),
                    dropdownProps: {
                        id: INBOX_TABS_IDS.PARCELLA,
                        title: 'Parcella claims',
                        onClick: () => onTabClick(INBOX_TABS_IDS.PARCELLA),
                        selected: inboxActiveTab === INBOX_TABS_IDS.PARCELLA
                    }
                });
            }
            if (!isHiddenInvoiceInInbox) {
                leftItems.push({
                    element: (
                        <LeftItem
                            onClick={() => onTabClick(INBOX_TABS_IDS.INVOICES)}
                            selected={inboxActiveTab === INBOX_TABS_IDS.INVOICES}
                            $templateActive={templatePalette?.colorActive}
                            $templateColor={templatePalette?.colorText}
                        >
                            Invoices
                        </LeftItem>
                    ),
                    dropdownProps: {
                        id: INBOX_TABS_IDS.INVOICES,
                        title: 'Invoices',
                        onClick: () => onTabClick(INBOX_TABS_IDS.INVOICES),
                        selected: inboxActiveTab === INBOX_TABS_IDS.INVOICES
                    }
                });
            }
            if (isVisibleProductivityInInbox) {
                leftItems.push({
                    element: (
                        <LeftItem
                            onClick={() => onTabClick(INBOX_TABS_IDS.PRODUCTIVITY)}
                            selected={inboxActiveTab === INBOX_TABS_IDS.PRODUCTIVITY}
                            $templateActive={templatePalette?.colorActive}
                            $templateColor={templatePalette?.colorText}
                        >
                            Productivity
                        </LeftItem>
                    ),
                    dropdownProps: {
                        id: INBOX_TABS_IDS.PRODUCTIVITY,
                        title: 'Productivity',
                        onClick: () => onTabClick(INBOX_TABS_IDS.PRODUCTIVITY),
                        selected: inboxActiveTab === INBOX_TABS_IDS.PRODUCTIVITY
                    }
                });
            }
        }

        return leftItems.length === 1 ? [] : leftItems;
    };

    return (
        <HeaderComponent
            withBoxShadow
            additionalButtonsChildren={!isNotificationhidden && notificationButton}
            withReloader={!isMobile}
            onReload={handleClickOnReload}
            leftContent={isOnline ? getLeftMenuItems() : []}
            rightContent={menuItems.map((item, index) => {
                if (item.component) {
                    const Component = item.component;
                    if (item.tooltipText) {
                        return (
                            <Item
                                key={index}
                                isIcon={item.isIcon}
                                isText={item.isText}
                                $templateColor={templatePalette?.colorText}
                                onClick={() => handleSchedulerClick(item)}
                            >
                                <TooltipFloating content={item.tooltipText} placement="top-start">
                                    <Component {...item.props} />
                                </TooltipFloating>
                            </Item>
                        );
                    } else {
                        return (
                            <Item
                                key={index}
                                isIcon={item.isIcon}
                                isText={item.isText}
                                $templateColor={templatePalette?.colorText}
                            >
                                <Component {...item.props} />
                            </Item>
                        );
                    }
                }
                if (!item?.text) return null;
                return (
                    <Item key={index} $templateColor={templatePalette?.colorText}>
                        <Button
                            type="button"
                            onClick={() => {
                                item?.onClick?.();
                            }}
                        >
                            <StyledText fontWeight={500} textTransform="capitalize">
                                {item.text}
                            </StyledText>
                        </Button>
                    </Item>
                );
            })}
            noHideContent
            templateBg={templatePalette?.colorBg}
            templateColor={templatePalette?.colorText}
            templateLogo={templatePalette?.companyLogo}
            templateLogoHeight={templatePalette?.companyLogoHeight}
            templateLogoBorder={templatePalette?.companyLogoBorder}
            templateLogoAlignLeft={templatePalette?.companyLogoAlignLeft}
            isLaptopContentExpanded
            {...props}
        />
    );
};
