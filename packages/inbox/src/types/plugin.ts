import { EventCallable } from 'effector';

export type PluginOptions = DAT2.Plugins.PluginBaseOptions & {
    onRedirectClick?: (redirectPath: string) => unknown;
    onContractClick?: (contract: DAT2.ContractFromListContracts) => unknown;
    onContractClickWithPressedCtrl?: (contract: DAT2.ContractFromListContracts) => unknown;
    onCreateContractClick?: () => any;
    newOrderLink?: string;
    getGalleryLink?: (contractId: number) => string;
    resetVSMStores?: EventCallable<void>;

    fleetView?: boolean;

    settings?: DAT2.InboxConfiguration['settings'];

    styles?: {
        fontSize?: {
            fieldsTitles?: number;
        };
        field?: {
            fontSize?: number;
            color?: string;
            backgroundColor?: string;
        };
        button?: {
            fontSize?: number;
            color?: string;
            backgroundColor?: string;
            backgroundColorHover?: string;
            height?: number;
        };
        form?: {
            borderColor?: string;
            borderWidth?: number;
            borderRadius?: number;
            backgroundColor?: string;
        };
        page?: {
            bgImage?: string;
        };
        icons?: {
            search?: string;
        };
        select?: {
            bgImage?: string;
            colorButton?: string;
        };
        header?: {
            backgroundColor?: string;
        };
    };
};
