import { getParsedArraySafe } from '@dat/api2/utils';
import { CONTRACT_ENTRIES_KEYS } from '@dat/core/constants/contract';

export const extractTiresParametersFromContractMemoField = (contract: DAT2.ContractFromGetContract | null): any => {
    if (contract === null) {
        return [];
    }

    const entries = getParsedArraySafe(contract?.customTemplateData?.entry);
    const tiresEntry = entries.find(entry => entry.key === CONTRACT_ENTRIES_KEYS.MEMO.manfredTires);
    const tiresJSON = tiresEntry?.value._text as string;

    if (!!tiresJSON) {
        try {
            return JSON.parse(tiresJSON);
        } catch (e) {
            console.error('a-error', e);
        }
    }

    return [];
};
