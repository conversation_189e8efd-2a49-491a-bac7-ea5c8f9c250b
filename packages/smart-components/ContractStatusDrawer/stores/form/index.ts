import { createEvent, createStore, restore } from 'effector';
import { INITIAL_FORM_VALUES } from './constants';
import { FormValues } from '../../types';

const initialFormValues = createStore<FormValues>(INITIAL_FORM_VALUES);

const setSubject = createEvent<string>();
const subjectData = restore(setSubject, '');

const existingStatusSubmitted = createStore<DAT2.API2DAT5.MyClaimExternalService_schema1.contractStatus | null>(null);

const setFormbuilderValues = createEvent<Record<string, DAT2.Field.Primitive>>();
const formbuilderValues = restore(setFormbuilderValues, null);

const formSubmitted = createEvent<FormValues>();
const submittedFormValues = restore(formSubmitted, INITIAL_FORM_VALUES);

const setStatusChangedInClaim = createEvent<boolean>();
const statusChangedInClaim = restore(setStatusChangedInClaim, false);

const statuses = createStore<DAT2.API2DAT5.MyClaimExternalService_schema1.contractStatus[]>([]);

const setShouldSendAppraisalResult_PRE = createEvent<boolean>();
const shouldSendAppraisalResult_PRE = restore(setShouldSendAppraisalResult_PRE, false);

const setShouldSendAppraisalResult_POST = createEvent<boolean>();
const shouldSendAppraisalResult_POST = restore(setShouldSendAppraisalResult_POST, false);

const returnMainContent = createEvent();

export const formEvents = {
    formSubmitted,
    setStatusChangedInClaim,
    setShouldSendAppraisalResult_PRE,
    setShouldSendAppraisalResult_POST,
    setFormbuilderValues,
    returnMainContent,
    setSubject
};

export const formStores = {
    initialFormValues,
    submittedFormValues,
    existingStatusSubmitted,
    statusChangedInClaim,
    statuses,
    shouldSendAppraisalResult_PRE,
    shouldSendAppraisalResult_POST,
    formbuilderValues,
    subjectData
};
