import { sample } from 'effector';
import { omitBy } from 'lodash-es';

import { sharedContractStatusEffects, sharedContractStatusStores } from '@dat/shared-models/contract/Status';
import { contractEvents, contractStores } from '@dat/shared-models/contract';
import { extractCalculationUserChoicesObjFromContract } from '@dat/shared-models/labour-rates/utils/extractMemoFieldsFromContract';
import { sharedItalianCalculationStores } from '@dat/shared-models/italian-calculation';
import { sharedConfigurationStores } from '@dat/shared-models/configuration';
import { ClaimPageGate, sharedClaimManagementStores } from '@dat/shared-models/claim-management';
import { sharedFleeViewStores } from '@dat/shared-models/fleetView';
import { NotificationParams, toastEffects } from '@dat/shared-models/smart-components/Toast';
import { FormValues } from '../../types';
import { statusesEffects, statusesStores } from '../statuses';
import { componentEffects, componentStores } from '../component';
import { findSelectedStatus, Result } from './utils/findSelectedStatus';
import { isDateValid } from '../../utils/mandatoryFieldsWithConditions';
import { formEvents, formStores } from './index';

import { commonFilterLogic } from '../../utils/fleetViewStatusCheck';

const { formSubmitted, setShouldSendAppraisalResult_PRE, returnMainContent } = formEvents;
const {
    statuses,
    initialFormValues,
    existingStatusSubmitted,
    shouldSendAppraisalResult_PRE,
    shouldSendAppraisalResult_POST,
    formbuilderValues
} = formStores;
const { possibleStatuses } = statusesStores;
const { changeStatusFx } = statusesEffects;
const { callOnStatusChangeFx, callOnStatusChangeFailedFx } = componentEffects;
const { props } = componentStores;
const { getPossibleContractStatusTransitionsFx } = sharedContractStatusEffects;
const { userRole, templateRoles } = sharedConfigurationStores;
const { configuration: claimManagementConfiguration } = sharedClaimManagementStores;

sample({
    source: {
        possibleStatuses,
        roles: templateRoles,
        userRole,
        config: claimManagementConfiguration
    },
    fn: ({ possibleStatuses, roles, userRole, config }) => {
        const hiddenStatuses = roles?.accessibility?.statusTransitionsVisibility;

        const genericVisibleStatuses = config.hiddenStatuses;

        if (genericVisibleStatuses) {
            return possibleStatuses.filter(
                option => option?.statusId && !genericVisibleStatuses?.includes(option.statusId)
            );
        }
        if (hiddenStatuses && userRole) {
            return possibleStatuses.filter(
                option => option?.statusId && !hiddenStatuses[userRole]?.includes(option?.statusId)
            );
        }

        return possibleStatuses;
    },
    target: statuses
});

sample({
    source: props,
    fn: (props): FormValues => ({ statusId: props.initialStatus?.id, note: '', comment: '' }),
    target: initialFormValues
});

sample({
    clock: formSubmitted,
    source: {
        possibleStatuses,
        customTemplateData: contractStores.customTemplateData,
        contract: contractStores.contract,
        fleetCheckInListBasedOnVin: sharedFleeViewStores.fleetCheckInListBasedOnVin,
        claimManagementConfiguration
    },
    filter: (sources, formValues) => {
        const { isListValid, checkInEvent } = commonFilterLogic(sources, formValues);

        return !(checkInEvent && !isListValid);
    },
    fn: ({ possibleStatuses }, formValues): Result => findSelectedStatus(possibleStatuses, formValues),
    target: existingStatusSubmitted
});

sample({
    clock: formSubmitted,
    source: {
        possibleStatuses,
        customTemplateData: contractStores.customTemplateData,
        contract: contractStores.contract,
        fleetCheckInListBasedOnVin: sharedFleeViewStores.fleetCheckInListBasedOnVin,
        claimManagementConfiguration
    },
    filter: (sources, formValues) => {
        const { isListValid, checkInEvent } = commonFilterLogic(sources, formValues);

        return checkInEvent && !isListValid;
    },
    fn: (): NotificationParams => ({
        message: {
            namespace: 'claim-management',
            key: 'error.failedStatusChangeForCheckOut'
        }
    }),
    target: toastEffects.showErrorToastFx
});

sample({
    clock: existingStatusSubmitted,
    filter: (
        existingStatusSubmitted
    ): existingStatusSubmitted is DAT2.API2DAT5.MyClaimExternalService_schema1.contractStatus =>
        !!existingStatusSubmitted,
    target: changeStatusFx
});

sample({
    clock: changeStatusFx.done,
    fn: ({ params }) => params,
    target: callOnStatusChangeFx
});

sample({
    clock: changeStatusFx.doneData,
    filter: ClaimPageGate.status,
    target: contractEvents.updateInitialContract
});

sample({
    clock: changeStatusFx.fail,
    target: callOnStatusChangeFailedFx
});

sample({
    source: { props },
    filter: ({ props }) => !!props.isOpen && !!props.contractId,
    fn: ({ props }) => props.contractId,
    target: getPossibleContractStatusTransitionsFx
});

sample({
    clock: setShouldSendAppraisalResult_PRE,
    source: {
        shouldSendAppraisalResult_PRE,
        contract: contractStores.contract,
        appraisalData: sharedItalianCalculationStores.doAppraisalResult
    },
    filter: ({ shouldSendAppraisalResult_PRE, contract, appraisalData }) => {
        const appraisalResult_PRE = extractCalculationUserChoicesObjFromContract(contract, 'AppraisalResult_PRE');
        const calculationUserChoicesObj =
            appraisalData?.data ?? extractCalculationUserChoicesObjFromContract(contract, 'CalculationUserChoicesObj');

        return !!shouldSendAppraisalResult_PRE && !appraisalResult_PRE && !!calculationUserChoicesObj;
    },
    fn: ({ contract, appraisalData }) => {
        const calculationUserChoicesObj =
            appraisalData?.data ?? extractCalculationUserChoicesObjFromContract(contract, 'CalculationUserChoicesObj');

        return {
            templateData: {
                entry: { key: 'AppraisalResult_PRE', value: JSON.stringify(calculationUserChoicesObj) }
            }
        };
    },
    target: componentEffects.updateCurrentContractFx
});

sample({
    clock: sharedContractStatusStores.statusName.updates,
    source: {
        shouldSendAppraisalResult_POST,
        contract: contractStores.contract,
        statusName: sharedContractStatusStores.statusName
    },
    filter: ({ shouldSendAppraisalResult_POST, statusName, contract }) => {
        const calculationUserChoicesObj = extractCalculationUserChoicesObjFromContract(
            contract,
            'CalculationUserChoicesObj'
        );
        return (
            !!shouldSendAppraisalResult_POST && statusName === 'N.A. - Perizia conclusa' && !!calculationUserChoicesObj
        );
    },
    fn: ({ contract }) => {
        const calculationUserChoicesObj = extractCalculationUserChoicesObjFromContract(
            contract,
            'CalculationUserChoicesObj'
        );
        return {
            templateData: {
                entry: { key: 'AppraisalResult_POST', value: JSON.stringify(calculationUserChoicesObj) }
            }
        };
    },
    target: componentEffects.updateCurrentContractFx
});

sample({
    clock: returnMainContent,
    source: { values: formbuilderValues },
    filter: ({ values }) => !!values,
    fn: ({ values }) => {
        //remove values for type of 'Date' fields
        const filteredValues = omitBy(values, value => {
            return typeof value === 'string' ? isDateValid(value) : false;
        });

        const templateData = {
            entry: filteredValues ? Object.entries(filteredValues).map(([key, value]) => ({ key, value })) : []
        };
        return { templateData };
    },
    target: componentEffects.updateCurrentContractFx
});
