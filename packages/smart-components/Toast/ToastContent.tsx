import { FC, ReactElement, useMemo } from 'react';
import { useUnit } from 'effector-react';

import { ErrorToast, SuccessToast, WarningToast } from '@wedat/ui-kit/assets/Icon';
import { Trans, useTranslation } from 'react-i18next';
import { i18nStores } from '@dat/shared-models/i18n/';
import { NotificationObject } from '@dat/shared-models/smart-components/Toast/types';

import { I18N_NAMESPACES } from '../../core/constants/i18n';
import { getCurrentErrorMessage, getTranslationsViaCurrentLocale } from './utils/';
import { Icon, ToastContentStyled, ToastInfo, ToastMessage, ToastTitle } from './styles';

export const icon: Record<NotificationObject['status'], ReactElement> = {
    success: <SuccessToast />,
    error: <ErrorToast />,
    info: <ErrorToast />,
    warn: <WarningToast />
};

export const ToastContent: FC<NotificationObject> = ({ title, message, status }) => {
    const toastNamespaces = useMemo(() => {
        const toastNamespaces = [];
        if (message) {
            toastNamespaces.push(I18N_NAMESPACES[message.namespace]);
        }
        if (title) {
            toastNamespaces.push(I18N_NAMESPACES[title.namespace]);
        }
        return toastNamespaces;
    }, [message, title]);

    const { t } = useTranslation(toastNamespaces);
    const translations = useUnit(i18nStores.translations);
    const locale = useUnit(i18nStores.locale);
    const currentLocaleTranslations = getTranslationsViaCurrentLocale(translations, locale);
    const currentError = getCurrentErrorMessage(currentLocaleTranslations, message.key);

    return (
        <ToastContentStyled>
            <Icon>{icon[status]}</Icon>
            <ToastInfo>
                {title && <ToastTitle>{t(title.key, { ns: `${toastNamespaces[1]}` })}</ToastTitle>}
                {message?.options?.isTransComponent ? (
                    <ToastMessage>
                        <Trans
                            i18nKey={message.key}
                            values={message.options}
                            t={t}
                            shouldUnescape
                            components={{
                                mailToVinabfrage: <a href="mailto:<EMAIL>" />,
                                mailToGlobalsales: <a href="mailto:<EMAIL>" />
                            }}
                        />
                    </ToastMessage>
                ) : currentError ? (
                    <ToastMessage>{t(currentError, message.options)}</ToastMessage>
                ) : (
                    <ToastMessage>{t(message.key, message.options)}</ToastMessage>
                )}
            </ToastInfo>
        </ToastContentStyled>
    );
};
