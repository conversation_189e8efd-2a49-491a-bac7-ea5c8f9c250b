import { RepairPositionWithRowKey } from '../types';
import { getManualRepairPositionForUpdate } from './getManualRepairPositionForUpdate';

describe('getManualRepairPositionForUpdate.test', () => {
    it('replace with WorkTime', () => {
        const manualPosition = {
            Description: 'Test description 1',
            RepairType: 'replace',
            SparePartAmount: 3,
            SparePartPrice: 1000,
            WorkTime: 3.5
        } as RepairPositionWithRowKey;

        const expected = {
            Description: 'Test description 1',
            RepairType: 'replace',
            SparePartAmount: 3,
            SparePartPrice: 1000,
            WorkTime: 3.5,
            PositionEntryType: 'manual',
            WorkManualInput: '№',
            PartDescription: 'Test description 1',
            WorkIndication: 1,
            SparePartUsed: false,
            WorkType: 'other',
            DentsWithFinishing: false,
            DentsWithSetupTime: false,
            DentsWithAddLightMetals: false,
            DentsOutOfReach: false,
            IsAdditionalLM: false,
            AlloyLM: false,
            LargeScale: false,
            AdhesiveTechnologyScale: false,
            AdditionLM: false,
            WorkCompleted: false,
            ContainMicroDents: false,
            AdhesiveMethod: false
        };

        expect(getManualRepairPositionForUpdate(manualPosition)).toStrictEqual(expected);
    });

    it('lacquer with LacquerPrice', () => {
        const manualPosition = {
            Description: 'Test description 2',
            RepairType: 'lacquer',
            LacquerPrice: 500
        } as RepairPositionWithRowKey;

        const expected = {
            Description: 'Test description 2',
            RepairType: 'lacquer',
            LacquerPrice: 500,
            PositionEntryType: 'manual',
            WorkManualInput: undefined,
            PartDescription: 'Test description 2',
            WorkIndication: 6,
            SparePartUsed: false,
            WorkType: 'other',
            DentsWithFinishing: false,
            DentsWithSetupTime: false,
            DentsWithAddLightMetals: false,
            DentsOutOfReach: false,
            IsAdditionalLM: false,
            AlloyLM: false,
            LargeScale: false,
            AdhesiveTechnologyScale: false,
            AdditionLM: false,
            WorkCompleted: false,
            ContainMicroDents: false,
            AdhesiveMethod: false
        };

        expect(getManualRepairPositionForUpdate(manualPosition)).toStrictEqual(expected);
    });
});
