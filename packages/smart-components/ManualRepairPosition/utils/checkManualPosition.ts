import { RepairPositionWithRowKey } from '../types';

export const checkManualPosition = (manualPosition: RepairPositionWithRowKey) => {
    const { Description, RepairType, SparePartAmount, SparePartPrice, WorkTime, WorkPrice, LacquerPrice } =
        manualPosition || {};

    // Predefined position with comment only
    // TODO: this is a temp solution. There is a need to add a special option for the comment
    if (RepairType === 'complete') return true;

    if (!RepairType || !Description) return false;

    if (RepairType === 'replace') {
        if (SparePartPrice !== undefined && SparePartAmount) return true;
    } else if (
        RepairType === 'overhaul' ||
        RepairType === 'fixing' ||
        RepairType === 'visual inspection' ||
        RepairType === 'dis- and reassemble' ||
        RepairType === 'adjust' ||
        RepairType === 'technical inspection'
    ) {
        if (WorkTime !== undefined || WorkPrice !== undefined) return true;
    } else if (RepairType === 'dis- and mounting') {
        if ((WorkTime !== undefined || WorkPrice !== undefined) && SparePartAmount) return true;
    } else if (RepairType === 'lacquer') {
        if (WorkTime !== undefined || LacquerPrice !== undefined) return true;
    } else if (RepairType === 'incidental costs') {
        if (SparePartPrice !== undefined && SparePartAmount) return true;
    }

    return false;
};
