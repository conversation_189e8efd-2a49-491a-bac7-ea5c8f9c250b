import { getParsedArraySafe } from '@dat/api2';
import { InspectionHistoryModalData } from '../types';

export const extractInspectionHistoryFromMemoField = (
    contract: DAT2.ContractFromGetContract | null
): InspectionHistoryModalData[] => {
    const dataFields = getParsedArraySafe(contract?.customTemplateData?.entry);
    const historyOfInspection = dataFields?.find(item => item.key === 'historyOfInspection');
    const historyValue = historyOfInspection?.value?._text as string | undefined;
    try {
        return historyValue ? JSON.parse(historyValue) : [];
    } catch {
        return [];
    }
};
