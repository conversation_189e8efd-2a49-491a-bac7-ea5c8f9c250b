export enum InspectionHistoryCondition {
    assembled = 'assembled',
    disassembled = 'disassembled',
    repaired = 'repaired',
    partlyRepaired = 'partly_repaired',
    notDefined = 'not_defined'
}

export enum InspectionHistoryInspection {
    onSite = 'onSite',
    driveIn = 'driveIn',
    video = 'video',
    fotoexpertise = 'Fotoexpertise'
}

export enum InspectionHistoryStatus {
    done = 'done',
    open = 'open',
    unsuccessful = 'unsuccessful'
}

export type InspectionHistoryModalData = Record<string, string> & {
    id?: number;
    inspectionHistoryName: string;
    inspectionHistoryDate?: string;
    repairer_id?: string;
    inspectionHistoryPlace?: string;
    inspectionHistoryPlaceName?: string;
    inspectionHistoryContact?: string;
    inspectionHistoryContactName?: string;
    alternativeContactField?: string;
    salutation?: string;
    inspectionHistoryStatus?: InspectionHistoryStatus | string | null;
    inspectionHistoryInspection?: InspectionHistoryInspection | string | null;
    inspectionHistoryCondition?: InspectionHistoryCondition | string | null;
    claimAssignedRepairerName?: string;
};
