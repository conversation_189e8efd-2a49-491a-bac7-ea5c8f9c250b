import { getFieldText, getParsedArraySafe } from '@dat/api2/utils';
import { useUnit } from 'effector-react';
import { contractStores } from '@dat/shared-models/contract';
import { apsToolStores } from '../stores';
import { extractDataFromContractCustomTemplateData } from '../helpers/extractDataFromContractCustomTemplateData';
import { ApsCallIds } from '../stores/types';

export const useCreateApsWreckageData = ({ apsCallId }: { apsCallId: ApsCallIds }) => {
    const contractId = useUnit(contractStores.contractId);
    const externalId = contractId.toString() + '_' + apsCallId;

    const {
        apsValuationKindActiveStore,
        apsFinalSalePriceStore,
        overrideEquipmentValueStore,
        priceOfAppraisalEquipment,
        apsListPriceEuroStore
    } = apsToolStores;
    const apsValuationKindActive = useUnit(apsValuationKindActiveStore);
    const isApsKindActive = apsValuationKindActive === 'APS';
    const isDatKindActive = apsValuationKindActive === 'DAT';
    const isManuellKindActive = apsValuationKindActive === 'Manuell';

    const apsFinalSalePrice = useUnit(apsFinalSalePriceStore);

    const overrideEquipmentValue = useUnit(overrideEquipmentValueStore);
    const priceOfAppraisalEquipmentValue = useUnit(priceOfAppraisalEquipment);
    const apsListPriceEuro = useUnit(apsListPriceEuroStore);

    const contract = useUnit(contractStores.contract);
    const vehicleIdentNumber = contract?.Dossier?.Vehicle?.VehicleIdentNumber;
    const licenseNumber = contract?.Dossier?.Vehicle?.RegistrationData?.LicenseNumber;

    const customTemplateData = getParsedArraySafe(contract?.customTemplateData?.entry);

    const manualValuationListPriceData = customTemplateData.find(
        item => item.key === 'manualValuationListPrice'
    )?.value;
    const manualValuationListPrice = Number(getFieldText(manualValuationListPriceData)) || 0;
    const manualValuationEquipmentData = customTemplateData.find(
        item => item.key === 'manualValuationEquipment'
    )?.value;
    const manualValuationEquipment = Number(getFieldText(manualValuationEquipmentData)) || 0;
    const sumOfManualValuationListPriceAndManualValuationEquipment =
        manualValuationListPrice + manualValuationEquipment;

    const manualValuationRvData = customTemplateData.find(item => item.key === 'manualValuationRV')?.value;
    const manualValuationRv = Number(getFieldText(manualValuationRvData)) || 0;

    const dateOfRegistration = extractDataFromContractCustomTemplateData(
        customTemplateData,
        'vehicleFirstRegistration'
    );
    const dateOfValuation =
        extractDataFromContractCustomTemplateData(customTemplateData, 'damageDate') || new Date().toISOString();

    const mileageOdometer = contract?.Dossier?.Vehicle?.MileageOdometer;

    const originalPriceGrossData = customTemplateData.find(item => item.key === 'valuationResult')?.value;
    const originalPriceGrossJson = getFieldText(originalPriceGrossData)?.toString() || null;
    const originalPriceGrossValue = originalPriceGrossJson && JSON.parse(originalPriceGrossJson);
    const originalPriceGross = originalPriceGrossValue?.Valuation?.OriginalPriceGross || 0;
    const salesPriceGross = originalPriceGrossValue?.Valuation?.SalesPriceGross || 0;

    const datECode = contract?.Dossier?.Vehicle?.DatECode;

    return {
        vehicleDefinition: {
            vin: vehicleIdentNumber || '',
            eurocode: datECode || '',
            dateOfRegistration
        },
        inputs: {
            dateOfValuation,
            dateOfRegistration,
            mileageKm: mileageOdometer || 0,
            replacementValue: 0,
            listPriceEuro: 0,

            ...(originalPriceGross && { listPriceEuro: originalPriceGross }),
            ...(isApsKindActive && { listPriceEuro: (apsListPriceEuro || 0) + (overrideEquipmentValue || 0) }),
            ...(isDatKindActive && {
                listPriceEuro: (apsListPriceEuro || 0) + (priceOfAppraisalEquipmentValue || 0)
            }),
            ...(isManuellKindActive &&
                sumOfManualValuationListPriceAndManualValuationEquipment && {
                    listPriceEuro: sumOfManualValuationListPriceAndManualValuationEquipment
                }),

            ...(salesPriceGross && { replacementValue: salesPriceGross }),
            ...(isApsKindActive && apsFinalSalePrice && { replacementValue: apsFinalSalePrice }),
            ...(isManuellKindActive && manualValuationRv && { replacementValue: manualValuationRv })
        },
        pdfInputs: {
            vin: vehicleIdentNumber || '',
            licensePlate: licenseNumber?.split(' ')?.join(''),
            printExplanation: false
        },
        externalId
    };
};
