import { useMount } from '@dat/core/hooks/useMount';
import { i18nEvents } from '@dat/shared-models/i18n';
import { setLocalStorageCountry } from '@dat/shared-models/template/utils/storageCountry';

// Set in LS locale and country when we have this params in options.
export const useSetStandAloneOptions = <TOptions extends DAT2.Plugins.PluginBaseOptions>(options: TOptions) => {
    const setParamsForStandAlone = () => {
        if (!options.isComponent) {
            if (options?.settings?.locale) {
                i18nEvents.localeChangedByOptions(options.settings.locale);
            }
            if (options?.settings?.country) {
                setLocalStorageCountry(options.settings.country);
            }
        }
    };

    useMount(() => {
        setParamsForStandAlone();
    });
};
