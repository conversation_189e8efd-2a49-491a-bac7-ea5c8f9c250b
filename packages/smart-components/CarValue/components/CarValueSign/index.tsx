import React from 'react';
import { carValueSignStores } from './stores';
import { useUnit } from 'effector-react';
import { DateFormat } from '../../../DateFormat';
import { Text } from '@wedat/kit';
import { useTranslation } from 'react-i18next';

export const CarValueSign = () => {
    const { t } = useTranslation('inbox');

    const carValueLog = useUnit(carValueSignStores.carValueLog);

    const lastCarValueLog = carValueLog && carValueLog.length > 0 && carValueLog[carValueLog.length - 1];

    return (
        <>
            {lastCarValueLog && (
                <Text color={lastCarValueLog.success ? 'var(--color-green-700)' : 'var(--color-red-700)'}>
                    {t('TransmissionAbout')}{' '}
                    {lastCarValueLog.success ? t('TransmissionSuccessful') : t('TransmissionError')}
                    {': '}
                    <DateFormat>{lastCarValueLog.time}</DateFormat>
                    {lastCarValueLog.success && (
                        <>
                            {', '}
                            {t('TransmissionId')}: {lastCarValueLog.id}
                        </>
                    )}
                </Text>
            )}
        </>
    );
};
