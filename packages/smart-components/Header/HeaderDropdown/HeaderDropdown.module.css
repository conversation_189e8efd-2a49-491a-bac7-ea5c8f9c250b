.dropdownWrapper {
    top: calc(100% - 18px);
}

.dropdown {
    padding: 0;
}

.dropdown::before {
    content: none;
}

.container {
    position: relative;
    display: flex;
    justify-content: center;
    margin-right: 32px;
    height: 100%;
}

.clickable {
    display: flex;
    align-items: center;
    height: 100%;
    border-bottom: 4px solid transparent;
    transition: opacity 150ms;
    cursor: pointer;
}

.clickable_selected {
    border-bottom: 4px solid var(--color-yellow-400);
    color: var(--color-yellow-400);
    transition: opacity 150ms;
}
@media (hover: hover) {
    .clickable:hover,
    .clickable_selected:hover {
        opacity: 0.5;
    }
}

.styledText {
    margin-right: 12px;
    color: var(--header-dropdown-text, var(--textPrimary));
}

.arrowExpandStyled {
    width: 18px;
    height: 100%;
    transform: rotate(180deg);
    transition: transform 0.3s;
    color: var(--header-dropdown-text, var(--textPrimary));
}

.arrowExpandStyled_opened {
    transform: rotate(0deg);
}

.content {
    border-radius: 12px;
}

.item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    min-height: 44px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    transition: background-color 150ms;
    color: var(--color-dust-blue-800);
    cursor: pointer;
    border-bottom: 1px solid var(--color-dust-blue-50);
}

.item:active {
    background-color: var(--color-dust-blue-100);
}

.item:first-of-type {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.item:last-of-type {
    min-height: 48px;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    border-bottom: none;
}

@media (hover: hover) {
    .item:hover {
        background-color: var(--color-blue-50);
    }
}

.item_disabled {
    pointer-events: none;
    color: var(--color-dust-blue-200);
    cursor: not-allowed;
}

.item_selected {
    border-bottom: 4px solid var(--color-yellow-400);
    color: var(--color-yellow-400);
}
