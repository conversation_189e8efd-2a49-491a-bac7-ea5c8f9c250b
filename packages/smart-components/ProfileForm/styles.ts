import { media } from '@wedat/ui-kit/mediaQueries';
import styled from 'styled-components/macro';

export const WhiteContainer = styled.div`
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    padding: 20px 20px;
    background-color: ${({ theme }) => theme.colors.white};
    border-radius: 5px;
`;

export const TitleWrapper = styled.div`
    ${media.laptopSmall`
        text-align: center
    `}
`;

export const FooterChildrenContainer = styled.footer`
    display: flex;
    padding: 10px;
    width: 100%;

    & > * {
        width: 100%;
    }
`;
