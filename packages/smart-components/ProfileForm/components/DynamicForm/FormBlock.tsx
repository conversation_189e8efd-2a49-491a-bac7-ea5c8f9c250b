import { FC, Fragment, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from 'styled-components';
import { ProfileForm } from '@dat/shared-models/profiles/types';

import { FieldsSubtitle, FormStyled } from '../form/styles';
import { getFieldsWithAsyncOptions, getFieldsWithoutAsyncOptions } from '../../utils';
import { useMount } from '@dat/core/hooks/useMount';
import { SelectOption } from '../../types';
import { InputField, SelectField } from '../../../FormikFields';

interface Props {
    form: ProfileForm;
    disabled?: boolean;
}

export const FormBlock: FC<Props> = ({ form, disabled }) => {
    const { t } = useTranslation();
    const {
        colors: { gray_300 }
    } = useTheme();

    const [fields, setFields] = useState(getFieldsWithoutAsyncOptions(form));

    useMount(() => {
        getFieldsWithAsyncOptions(form).then(setFields);
    });

    const fieldsComponent = useMemo(() => {
        return fields.map(field => {
            const label = t(`auth:profile.form.${field.name}`, field.label);
            const style = {
                ...(field.fullWidth && { gridColumn: 'span 2' })
            };
            const options: SelectOption[] =
                field.type === 'select' && Array.isArray(field.options)
                    ? field.options.map(o => ({
                          ...o,
                          key: String(o.key || o.label || ''),
                          value: String(o.value),
                          label: t(`auth:profile.form.${field.name}.options.${o.value}`, String(o.label))
                      }))
                    : [];

            const defaultProps = {
                style: style,
                key: field.name,
                name: field.name,
                label: label,
                disabled: disabled,
                placeholder: field.placeholder,
                required: field.required
            };

            switch (field.type) {
                case 'select':
                    return (
                        <SelectField
                            {...defaultProps}
                            options={options}
                            inputId={`smart-components__profile-form__select--${field.name}`}
                            aria-label={t(`auth:profile.form.${field.name}`)}
                        />
                    );
                case 'number':
                    return (
                        <InputField
                            {...defaultProps}
                            type={field.type}
                            min={field.min || (field.moreThan ? field.moreThan + (field.step || 1) : undefined)}
                            max={field.max || (field.lessThan ? field.lessThan - (field.step || 1) : undefined)}
                            step={field.step}
                            id={`smart-components__profile-form__select--${field.name}`}
                        />
                    );
                case 'text':
                    return (
                        <InputField
                            {...defaultProps}
                            type={field.type}
                            minLength={field.min}
                            maxLength={field.max}
                            size={field.length}
                            id={`smart-components__profile-form__select--${field.name}`}
                        />
                    );
                default:
                    return (
                        <InputField
                            {...defaultProps}
                            type={field.type}
                            id={`smart-components__profile-form__select--${field.name}`}
                        />
                    );
            }
        });
    }, [fields, t, disabled]);

    if (!fields.length) {
        return null;
    }

    return (
        <Fragment>
            <FieldsSubtitle asTag="h5" fontSize="16px" textTransform="capitalize" color={gray_300}>
                {t(`auth:profile.form.titles.${form.title}`, form.title)}
            </FieldsSubtitle>
            <FormStyled>{...fieldsComponent}</FormStyled>
        </Fragment>
    );
};
