import { FC, useMemo } from 'react';
import { useUnit } from 'effector-react';
import { useSortBy, useTable } from 'react-table';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useTranslation } from 'react-i18next';

import { Preloader } from '@wedat/ui-kit';
import { bidEffects, bidEvents, bidStores } from '@dat/shared-models/bid-management';
import { useFormattedPrice } from '@dat/shared-models/hooks/useFormattedPrice';
import { sharedTemplateStores } from '@dat/shared-models/template';
import {
    BID_PAGE_SIZE,
    BidTableTabs,
    DEFAULT_PAGE_NUMBER,
    ENCODED_QUERY_FOR_READONLY_CONTENT,
    tabs
} from '@dat/shared-models/bid-management/constants';
import { sharedUserStores } from '@dat/shared-models/user';
import { Tabs } from '@wedat/kit';

import { useMedia } from '@dat/core/hooks/useMedia';
import { sizes } from '@wedat/ui-kit/mediaQueries';
import { COLUMN_ORDER, getDynamicBidTableColumns, getStaticBidTableColumns } from './bidColumns';

import { ExpiredBidClaimDrawer } from '../BidComponents/components/ExpiredBidClaimDrawer';

import { NotFound } from '../NotFound';
import { InputWithSearch } from './InputWithSearch';
import {
    SortDownIcon,
    SortUpIcon,
    StyledTable,
    TableBody,
    TableCell,
    TableHeader,
    TableHeading,
    TableRow,
    TableContainer,
    StyledHeaderContainer,
    WrapperWithTab,
    StyledTabContainer,
    OfferContainer,
    TotalContainer,
    StyledTrophyIcon,
    StyledSecondPlaceMedal
} from './styles';
import { BidConfirmationModal } from '../BidComponents/components/ConfirmationModal';

export const BidClaimTable: FC = () => {
    const { t } = useTranslation('inbox');

    const formatPrice = useFormattedPrice();

    const isTabletSmall = useMedia(sizes.tabletSmall);

    const pageNumber = useUnit(bidStores.pageNumber);
    const activeTab = useUnit(bidStores.activeTab);
    const userEmail = useUnit(sharedUserStores.userProfile)?.email;
    const isLoading = useUnit(bidEffects.getBidsFx.pending);
    const bidList = useUnit(bidStores.bidList);
    const productsConfiguration = useUnit(sharedTemplateStores.productsConfiguration);
    const searchValue = useUnit(bidStores.inputValue);
    const selectedSearchField = useUnit(bidStores.selectedColumn);

    const overallWiningPrice = useUnit(bidStores.overallWiningPrice);

    const hasSearch = !!(selectedSearchField || searchValue);
    const IsNeedLoadMoreBids = pageNumber[activeTab].shouldLoadMore;

    const [isModalVisible, onConfirm, type] = useUnit([
        bidStores.isConfirmationModalVisible,
        bidStores.onConfirmAction,
        bidStores.confirmationType
    ]);

    const tabsValues = tabs(t);

    //insuranceCode ensures that we show list for insurance role with different functionality
    const insuranceCode = productsConfiguration?.inbox?.settings?.insuranceCode;

    const payload = insuranceCode ? {} : { email: userEmail };

    const claims = useMemo(() => bidList[activeTab] || [], [activeTab, bidList]);

    const loadMore = async () => {
        const listOfBids = await bidEffects.getGenericBidClaimListFx({
            ...payload,
            page: pageNumber[activeTab].page,
            loadMore: true
        });

        bidEvents.setPageNumber({
            ...pageNumber,
            [activeTab]: {
                page: pageNumber[activeTab].page + 1,
                shouldLoadMore: !(listOfBids?.data?.length < BID_PAGE_SIZE)
            }
        });
    };

    const handleRowClick = (row: DAT2.Bid.Response.Claim) => {
        const isLocalEnv = import.meta.env.VITE_ENV === 'local';
        const localDeeplinkStandaloneUrl = `http://localhost:${import.meta.env.VITE_LOCAL_DEEPLINK_PORT}/`;
        const baseUrl = isLocalEnv ? localDeeplinkStandaloneUrl : import.meta.env.VITE_PLUGINS_STANDALONE_URL;

        const emailForInsurance = row.participants?.[0]?.email;
        const base64Email = insuranceCode ? btoa(emailForInsurance) : userEmail ? btoa(userEmail) : '';
        const bidderUrl = `${baseUrl}deeplink/${row.deeplinkPath}?${base64Email}`;
        const urlForInsurance = `${baseUrl}deeplink/${row.deeplinkPath}?${base64Email}&extra=${ENCODED_QUERY_FOR_READONLY_CONTENT}`;

        if (activeTab === BidTableTabs.Expired) {
            const filteredBasedOnEmail = row.offerHistory?.length
                ? !insuranceCode
                    ? row.offerHistory.filter(offer => offer.userEmail === userEmail)
                    : row.offerHistory
                : [];

            const offerHistory = !insuranceCode ? filteredBasedOnEmail : row.offerHistory;

            offerHistory &&
                bidEvents.setBidClaimOfferHistory({
                    offerHistory,
                    winnerOfferId: row.winnerOfferId,
                    expiredClaimUrl: urlForInsurance
                });
            bidEvents.toggleExpiredClaimDrawerOpen();

            return;
        }

        if ((userEmail || insuranceCode) && row.deeplinkPath) {
            bidEvents.setBidClaimOfferHistory(null);
            const url = insuranceCode ? urlForInsurance : bidderUrl;
            window.open(url, '_blank', 'noopener,noreferrer');
        }
    };

    const tabsHandler = async (id?: string) => {
        id === BidTableTabs.Expired &&
            !overallWiningPrice &&
            !insuranceCode &&
            bidEffects.getOverallWiningPriceFx({ email: userEmail });

        bidEvents.setActiveTab(id as BidTableTabs);

        bidEvents.toggleExpiredClaimDrawerOpen(false);

        hasSearch &&
            bidEvents.setPageNumber({ ...pageNumber, [id as BidTableTabs]: { page: 2, shouldLoadMore: true } });

        bidEvents.setPageNumber(DEFAULT_PAGE_NUMBER);

        await bidEffects.getGenericBidClaimListFx({
            ...payload,
            status: id as BidTableTabs
        });
    };

    const columns = useMemo(() => {
        const combinedColumns = [
            ...getStaticBidTableColumns(t),
            ...getDynamicBidTableColumns(t, productsConfiguration, formatPrice, activeTab, userEmail, insuranceCode)
        ];

        // Sort columns based on columnOrder array
        return combinedColumns
            .filter(column => column.accessor) // Exclude columns with undefined accessor
            .sort((a, b) => COLUMN_ORDER.indexOf(a.accessor as string) - COLUMN_ORDER.indexOf(b.accessor as string));
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [productsConfiguration, t, activeTab]);

    const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } = useTable(
        {
            columns,
            data: claims
        },
        useSortBy
    );

    return (
        <>
            {isTabletSmall && <InputWithSearch />}
            <BidConfirmationModal
                isOpen={isModalVisible}
                handleDismiss={() => bidEvents.closeConfirmationModal()}
                onConfirm={() => {
                    onConfirm();
                    bidEvents.closeConfirmationModal();
                }}
                type={type}
            />

            <TableContainer>
                <WrapperWithTab>
                    <StyledTabContainer>
                        <Tabs
                            aria-label={t('deductible.filters')}
                            onTabChange={e => tabsHandler(e.value.id)}
                            activeIndex={tabsValues.findIndex(item => item.id === activeTab)}
                            model={tabsValues}
                        />
                    </StyledTabContainer>
                    {!isTabletSmall && <InputWithSearch />}
                </WrapperWithTab>
                <InfiniteScroll
                    dataLength={claims.length}
                    next={loadMore}
                    hasMore={IsNeedLoadMoreBids}
                    loader={<Preloader isLoading={isLoading} />}
                >
                    <StyledTable {...getTableProps()}>
                        <TableHeader>
                            {headerGroups.map(headerGroup => (
                                <TableRow {...headerGroup.getHeaderGroupProps()} key={headerGroup.id}>
                                    {headerGroup.headers.map(column => (
                                        <TableHeading
                                            {...column.getHeaderProps(column.getSortByToggleProps())}
                                            key={column.id}
                                            style={{
                                                cursor: column.canSort ? 'pointer' : 'default'
                                            }}
                                        >
                                            <StyledHeaderContainer>
                                                {column.render('Header')}
                                                {column.isSorted ? (
                                                    column.isSortedDesc ? (
                                                        <SortDownIcon />
                                                    ) : (
                                                        <SortUpIcon />
                                                    )
                                                ) : (
                                                    ''
                                                )}
                                            </StyledHeaderContainer>
                                        </TableHeading>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody {...getTableBodyProps()}>
                            {rows.map(row => {
                                prepareRow(row);
                                return (
                                    <TableRow
                                        onClick={() => handleRowClick(row.original)}
                                        {...row.getRowProps()}
                                        key={row.id}
                                    >
                                        {row.cells.map(cell => {
                                            return (
                                                <TableCell {...cell.getCellProps()} key={cell.column.id}>
                                                    <OfferContainer>
                                                        {cell.render('Cell')}
                                                        {activeTab === BidTableTabs.Expired &&
                                                            !insuranceCode &&
                                                            cell.column.id === 'offerColumn' &&
                                                            (row.original.finalRacePlace === 0 ? (
                                                                <StyledTrophyIcon />
                                                            ) : row.original.finalRacePlace === 1 ? (
                                                                <StyledSecondPlaceMedal />
                                                            ) : (
                                                                ''
                                                            ))}
                                                    </OfferContainer>
                                                </TableCell>
                                            );
                                        })}
                                    </TableRow>
                                );
                            })}

                            {/* Totals Row */}
                            {activeTab === BidTableTabs.Expired && !insuranceCode && !!claims.length && (
                                <TableRow>
                                    {headerGroups[0].headers.map((column, columnIndex) => (
                                        <TableCell isBold key={column.id}>
                                            {columnIndex === headerGroups[0].headers.length - 2 &&
                                            overallWiningPrice ? (
                                                <TotalContainer>
                                                    {`${t('total')} ${formatPrice(overallWiningPrice || 0)}`}
                                                </TotalContainer>
                                            ) : (
                                                ''
                                            )}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            )}
                        </TableBody>
                    </StyledTable>
                </InfiniteScroll>
                {!rows.length && <NotFound />}
            </TableContainer>
            <ExpiredBidClaimDrawer />
        </>
    );
};
