import { AvailableAssemblyGroup } from '../types/graphicTypes';
import { findGroupByDvns } from './findGroupByDvns';

export function deprecated_getSvgElementByDatId(
    svg: string | undefined,
    datid: string[],
    datIdDescriptionText?: { datId: string; description: string }[]
): { resultSvg: string; width: number; height: number } | undefined {
    if (!svg || !datid) return;

    const parser = new DOMParser();
    const svgObj = parser.parseFromString(svg, 'image/svg+xml');

    const gStandardebene = svgObj.getElementById('Standardebene');

    if (!gStandardebene) return;

    const gDatid = datid.map(datidItem => {
        // svgObj.getElementById(datidItem)
        const gDatidItem = svgObj.querySelector(`[id^="DATID_"][id$="${datidItem.slice(-3)}"]`);

        if (!!gDatidItem) {
            const textElements = gDatidItem?.querySelector(`text`);
            if (textElements) {
                // if text present
                const foundDescription = datIdDescriptionText?.find(item => item.datId === datidItem);
                if (foundDescription?.description) textElements.textContent = foundDescription?.description || '';
            }
        }

        return gDatidItem;
    });

    const isShowOverGroup = false;
    if (!isShowOverGroup) {
        gStandardebene.innerHTML = '';
        gDatid.forEach(gDatidItem => {
            if (!!gDatidItem) {
                gStandardebene.appendChild(gDatidItem);
            }
        });
    }

    const elemDiv = document.createElement('div');
    // elemDiv.style.display = "none";
    elemDiv.style.visibility = 'hidden';
    const svgElem = svgObj.children[0];
    elemDiv.appendChild(svgElem);
    document.body.appendChild(elemDiv);

    const divBounding = elemDiv.getBoundingClientRect();

    let left = Number.MAX_SAFE_INTEGER;
    let right = 0;
    let top = Number.MAX_SAFE_INTEGER;
    let bottom = 0;

    gDatid.forEach(gDatidItem => {
        if (gDatidItem) {
            const bounding = gDatidItem.getBoundingClientRect();
            if (left > bounding.left) left = bounding.left;
            if (right < bounding.right) right = bounding.right;
            if (top > bounding.top) top = bounding.top;
            if (bottom < bounding.bottom) bottom = bounding.bottom;
        }
    });

    const boundingWidth = right - left;
    const boundingHeight = bottom - top;

    const scaleX = svgElem.clientWidth / 83.873;
    const scaleY = svgElem.clientHeight / 137.336;

    const sideMargin = 10;

    const width = (boundingWidth + 2 * sideMargin) / scaleX;
    const height = (boundingHeight + 2 * sideMargin) / scaleY;

    gStandardebene.setAttribute(
        'transform',
        'translate(' +
            (divBounding.left - left + sideMargin) / scaleX +
            ',' +
            (divBounding.top - top + sideMargin) / scaleY +
            ')'
    );

    svgElem.setAttribute(
        'viewBox',
        // '0 0 83.873 137.336'
        '0 0 ' + width + ' ' + height
    );

    const oSerializer = new XMLSerializer();
    const resultSvg = oSerializer.serializeToString(svgElem);

    elemDiv.remove();

    return { resultSvg, width, height };
}

export function getSvgElementByDatId(
    svg: string | undefined,
    datid: string[],
    datIdDescriptionText?: { datId: string; description: string }[]
): { resultSvg: string; width: number; height: number } | undefined {
    if (!svg || !datid) return;

    const parser = new DOMParser();
    const svgObj = parser.parseFromString(svg, 'image/svg+xml');

    const gStandardebene = svgObj.getElementById('Standardebene');

    if (!gStandardebene) return;

    let width = 0;
    let height = 0;

    const addElements: Element[] = [];

    const elemDiv = document.createElement('div');
    // elemDiv.style.display = "none";
    elemDiv.style.visibility = 'hidden';
    const svgElem = svgObj.children[0];
    elemDiv.appendChild(svgElem);
    document.body.appendChild(elemDiv);

    const selectedPolygonsId = datid.map(datidItem => datidItem.slice(-3));

    try {
        const divBounding = elemDiv.getBoundingClientRect();

        let left = Number.MAX_SAFE_INTEGER;
        let right = Number.MIN_SAFE_INTEGER;
        let top = Number.MAX_SAFE_INTEGER;
        let bottom = Number.MIN_SAFE_INTEGER;

        datid.forEach(datidItem => {
            const gDatidItem = svgElem.querySelector(`[id^="DATID_"][id$="${datidItem.slice(-3)}_selection"]`);
            if (gDatidItem) {
                const bounding = gDatidItem.getBoundingClientRect();
                if (left > bounding.left) left = bounding.left;
                if (right < bounding.right) right = bounding.right;
                if (top > bounding.top) top = bounding.top;
                if (bottom < bounding.bottom) bottom = bounding.bottom;
            }
        });

        const deltaSize = 10;

        gStandardebene.childNodes.forEach(child => {
            const element = child as Element;
            if (element.getAttribute) {
                if (!element.getAttribute('id')) {
                    // if not has id check the size and add it
                    const bounding = element.getBoundingClientRect();
                    if (
                        left - deltaSize < bounding.left &&
                        bounding.right < right + deltaSize &&
                        top - deltaSize < bounding.top &&
                        bounding.bottom < bottom + deltaSize
                    ) {
                        addElements.push(element as Element);
                    }
                } else {
                    // if id is present check is it belong to polygons or not
                    const polId = element.getAttribute('id')?.slice(-3);
                    if (polId && selectedPolygonsId.includes(polId)) {
                        const textElements = element?.querySelector(`text`);
                        if (textElements) {
                            // if text present
                            const foundDescription = datIdDescriptionText?.find(
                                item => item.datId === element.getAttribute('id')
                            );
                            if (foundDescription?.description)
                                textElements.textContent = foundDescription?.description || '';
                        }
                        addElements.push(element as Element);
                    }
                }
            }
        });

        gStandardebene.innerHTML = '';

        addElements.forEach(gItem => {
            if (!!gItem) {
                gStandardebene.appendChild(gItem);
            }
        });

        const boundingWidth = right - left;
        const boundingHeight = bottom - top;

        const scaleX = svgElem.clientWidth / 83.873;
        const scaleY = svgElem.clientHeight / 137.336;

        const sideMargin = 10;

        width = (boundingWidth + 2 * sideMargin) / scaleX;
        height = (boundingHeight + 2 * sideMargin) / scaleY;

        gStandardebene.setAttribute(
            'transform',
            'translate(' +
                (divBounding.left - left + sideMargin) / scaleX +
                ',' +
                (divBounding.top - top + sideMargin) / scaleY +
                ')'
        );

        svgElem.setAttribute(
            'viewBox',
            // '0 0 83.873 137.336'
            '0 0 ' + width + ' ' + height
        );
    } finally {
        elemDiv.remove();
    }
    const oSerializer = new XMLSerializer();
    const resultSvg = oSerializer.serializeToString(svgElem);
    return { resultSvg, width, height };
}

export function getPartsGraphics(dvns: number[], availableAssemblyGroupsList: AvailableAssemblyGroup[]) {
    const allDvns = dvns.filter((value, index, self) => self.indexOf(value) === index);

    let result: {
        dvn: number;
        svg?: {
            resultSvg?: string;
            width?: number;
            height?: number;
        };
    }[] = [];

    result = allDvns.map(dvn => {
        const foundGroup = findGroupByDvns([dvn], availableAssemblyGroupsList);
        let svg = {};
        if (foundGroup?.currentAssemblyGroupObject?.datid)
            svg =
                getSvgElementByDatId(foundGroup?.currentAssemblyGroup?.svg, [
                    foundGroup?.currentAssemblyGroupObject?.datid
                ]) || {};

        return {
            dvn,
            svg
        };
    });

    return result;
}
