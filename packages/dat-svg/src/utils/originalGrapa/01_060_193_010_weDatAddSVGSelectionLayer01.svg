<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:dat="http://www.dat.de/sphinx/svg" width="100%" height="100%" viewBox="0 0 83.873 137.336" datversion="2.0"
  preserveAspectRatio="xMidYMid meet" zoomAndPan="magnify" version="1.0" contentScriptType="text/ecmascript"
  contentStyleType="text/css">
  <dat:constructionGroup>
    <dat:fza>1</dat:fza>
    <dat:hst>60</dat:hst>
    <dat:ht>193</dat:ht>
    <dat:constructionGroupId>10</dat:constructionGroupId>
    <dat:lkz>D</dat:lkz>
  </dat:constructionGroup>
  <g id="Hintergrund" fill-rule="evenodd" clip-rule="evenodd" stroke="#000000" stroke-linecap="round" fill="none">
    <g>
      <polygon stroke="#FFFFFF" stroke-width="0.01"
        points="0.005 0.005 83.868 0.005 83.868 137.331 0.005 137.331 0.005 0.005" />
    </g>
  </g>
  <g id="Standardebene" fill-rule="evenodd" clip-rule="evenodd" stroke="#000000" stroke-linecap="round" fill="none">
    <g id="DATID_072012" class="V2L2St2Sz359-S##">
      <g>
        <path stroke-width="0.12"
          d="M 25.538000106811523,79.11208935205079 25.538000106811523,79.00299835205078 25.570727406811525,79.02481655205078 25.570727406811525,79.09027115205078 Z" />
        <path stroke-width="0.12"
          d="M 25.581636506811524,79.11208935205079 25.598000156811526,79.00299835205078 25.614363806811525,79.11208935205079" />
        <path stroke-width="0.12"
          d="M 25.625272906811524,79.11208935205079 25.625272906811524,79.00299835205078 25.658000206811526,79.02481655205078 25.658000206811526,79.09027115205078" />
      </g>
    </g>
    <g id="_DATID_010048" class="DATID_010048_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M14.781 36.628C15.22 36.899 16.013 37.451 16.426 37.686C19.009 39.054 21.981 40.731 24.382 41.456C25.068 41.731 25.184 41.719 25.411 41.784C25.426 42.088 25.576 42.554 25.987 43.618C26.452 44.953 27.129 46.764 27.782 47.767C28.094 48.243 27.928 51.735 27.928 53.204C27.928 54.774 27.78 55.67 27.28 55.969C27.271 55.972 26.905 56.185 26.884 56.197C26.376 56.466 25.524 56.164 24.132 55.359C24.132 55.359 22.998 54.706 22.998 54.706C22.416 54.368 22.37 54.341 22.37 53.448C22.37 52.555 22.039 51.63 21.589 50.881C21.589 50.881 21.167 51.125 21.167 51.125C18.022 46.057 13.289 43.421 10.226 44.918C8.51 46.063 7.822 48.473 8.134 51.35C8.169 51.917 8.146 52.13 8.073 52.149C8.073 52.149 7.711 52.357 7.711 52.357C7.644 52.396 7.405 52.219 7.251 52.131C7.251 52.131 2.94 49.642 2.94 49.642C2.94 48.1 3.282 47.734 3.783 47.445C4.285 47.156 4.477 47.015 4.477 46.585C4.477 46.155 4.517 45.969 4.71 45.859C4.902 45.747 4.974 45.429 4.974 45.164C4.974 45.164 5.367 44.936 5.367 44.936C5.937 45.267 6.394 45.234 6.839 45.024C7.487 44.588 9.221 43.226 9.935 41.923C10.695 40.533 11.952 39.542 14.003 37.424C14.285 37.134 14.542 36.874 14.781 36.628"
            stroke-width="0.12" />
        </g>
      </g>
    </g>
    <g id="_DATID_009047" class="DATID_009047_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M14.781 36.628C16.28 35.094 16.934 34.292 16.649 32.067C16.318 29.486 16.152 27.618 15.955 26.329C15.865 25.745 15.643 25.051 15.294 24.371C15.294 23.417 15.634 23.242 16.966 23.865C19.027 25.246 20.908 26.525 21.905 27.265C22.125 27.428 22.326 27.586 22.515 27.746C25.356 29.923 26.216 30.981 26.533 31.437C26.615 31.644 26.786 31.902 26.955 32.308C27.336 33.223 29.623 37.378 30.501 38.75C31.333 40.478 31.986 41.24 32.588 41.954C32.807 42.213 32.59 42.484 32.421 42.485C31.496 42.442 30.661 42.372 30.158 42.304C29.318 42.189 27.146 41.771 26.46 41.503C26.079 41.356 25.757 41.255 25.521 41.265C25.51 41.286 25.501 41.307 25.492 41.331C25.437 41.484 25.402 41.606 25.411 41.784C25.184 41.719 25.068 41.731 24.382 41.456C21.981 40.731 19.009 39.054 16.426 37.686C16.013 37.451 15.22 36.899 14.781 36.628"
            stroke-width="0.12" />
        </g>
      </g>
    </g>
    <g id="DATID_072014" class="V2L2St2Sz3522-S##">
      <g>
        <path stroke-width="0.12"
          d="M 66.24600230407715,103.56609008447266 66.24600230407715,103.45699908447266 66.27872960407716,103.47881728447265 66.27872960407716,103.54427188447265 Z" />
        <path stroke-width="0.12"
          d="M 66.28963870407715,103.56609008447266 66.30600235407715,103.45699908447266 66.32236600407715,103.56609008447266" />
        <path stroke-width="0.12"
          d="M 66.33327510407716,103.56609008447266 66.33327510407716,103.45699908447266 66.36600240407715,103.47881728447265 66.36600240407715,103.54427188447265" />
      </g>
    </g>
    <g id="_DATID_008046" class="DATID_008046_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M16.653 32.104C17.128 32.249 17.538 32.475 17.981 32.811C18.525 33.223 19.653 34.155 20.428 34.194C21.204 34.232 22.181 34.298 22.875 34.811C23.57 35.325 24.826 36.499 25.62 36.597C26.414 36.697 27.803 36.845 28.25 37.143C28.602 37.378 29.676 38.243 30.454 38.676C30.472 38.707 30.486 38.728 30.501 38.75C31.333 40.478 31.986 41.24 32.588 41.954C32.807 42.213 32.59 42.484 32.421 42.485C31.496 42.442 30.661 42.372 30.158 42.304C29.318 42.189 27.146 41.771 26.46 41.503C26.079 41.356 25.757 41.255 25.521 41.265C25.51 41.286 25.501 41.307 25.492 41.331C25.339 41.75 25.339 41.941 25.987 43.618C26.452 44.953 27.129 46.764 27.782 47.767C28.094 48.243 27.928 51.735 27.928 53.204C27.928 54.774 27.78 55.67 27.28 55.969C27.271 55.972 26.905 56.185 26.884 56.197C26.376 56.466 25.524 56.164 24.132 55.359C24.132 55.359 22.998 54.706 22.998 54.706C22.416 54.368 22.37 54.341 22.37 53.448C22.37 52.555 22.039 51.63 21.589 50.881C21.589 50.881 21.167 51.125 21.167 51.125C18.022 46.057 13.289 43.421 10.226 44.918C8.51 46.063 7.822 48.473 8.134 51.35C8.169 51.917 8.146 52.13 8.073 52.149C8.073 52.149 7.711 52.357 7.711 52.357C7.644 52.396 7.405 52.219 7.251 52.131C7.251 52.131 2.94 49.642 2.94 49.642C2.94 48.1 3.282 47.734 3.783 47.445C4.285 47.156 4.477 47.015 4.477 46.585C4.477 46.155 4.517 45.969 4.71 45.859C4.902 45.747 4.974 45.429 4.974 45.164C4.974 45.164 5.367 44.936 5.367 44.936C5.937 45.267 6.394 45.234 6.839 45.024C7.487 44.588 9.221 43.226 9.935 41.923C10.695 40.533 11.952 39.542 14.003 37.424C16.053 35.307 16.984 34.685 16.653 32.104"
            stroke-width="0.12" />
        </g>
      </g>
    </g>
    <g id="_DATID_007045" class="DATID_007045_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M16.649 32.067C16.98 34.646 16.053 35.307 14.003 37.424C11.952 39.542 10.695 40.533 9.935 41.923C9.711 42.329 9.39 42.743 9.032 43.131C9.38 43.496 9.673 43.865 9.894 44.113C10.103 44.347 10.323 44.591 10.537 44.783C13.602 43.6 18.128 46.225 21.167 51.125C21.167 51.125 21.589 50.881 21.589 50.881C22.039 51.63 22.37 52.555 22.37 53.448C22.37 54.341 22.416 54.368 22.998 54.706C22.998 54.706 24.132 55.359 24.132 55.359C25.524 56.164 26.376 56.466 26.884 56.197C26.905 56.185 27.28 55.969 27.28 55.969C27.78 55.67 27.928 54.774 27.928 53.204C27.928 51.735 28.094 48.243 27.782 47.767C27.129 46.764 26.452 44.953 25.987 43.618C25.339 41.941 25.339 41.75 25.492 41.331C25.501 41.307 25.51 41.286 25.521 41.265C25.757 41.255 26.079 41.356 26.46 41.503C27.146 41.771 29.318 42.189 30.158 42.304C30.661 42.372 31.496 42.442 32.421 42.485C32.59 42.484 32.807 42.213 32.588 41.954C31.986 41.24 31.333 40.478 30.501 38.75C30.486 38.728 30.472 38.707 30.458 38.685C29.676 38.243 28.602 37.378 28.25 37.143C27.803 36.845 26.414 36.697 25.62 36.597C24.826 36.499 23.57 35.325 22.875 34.811C22.181 34.298 21.204 34.232 20.428 34.194C19.653 34.155 18.525 33.223 17.981 32.811C17.538 32.475 17.123 32.21 16.649 32.067"
            stroke-width="0.12" />
        </g>
      </g>
    </g>
    <g id="_DATID_006044" class="DATID_006044_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M26.533 31.437C26.533 31.437 26.533 31.437 26.533 31.437C26.615 31.644 26.786 31.902 26.955 32.308C27.336 33.223 29.623 37.378 30.501 38.75C31.333 40.478 31.986 41.24 32.588 41.954C32.807 42.213 32.59 42.484 32.421 42.485C31.496 42.442 30.661 42.372 30.158 42.304C29.318 42.189 27.146 41.771 26.46 41.503C26.079 41.356 25.757 41.255 25.521 41.265C25.51 41.286 25.501 41.307 25.492 41.331C25.339 41.75 25.339 41.941 25.987 43.618C26.452 44.953 27.129 46.764 27.782 47.767C28.094 48.243 27.928 51.735 27.928 53.204C27.928 54.774 27.78 55.67 27.28 55.969C27.28 55.969 26.905 56.185 26.884 56.197C26.376 56.466 25.524 56.164 24.132 55.359C24.132 55.359 22.998 54.704 22.998 54.704C22.416 54.368 22.37 54.341 22.37 53.448C22.37 52.555 22.039 51.63 21.589 50.881C21.589 50.881 21.167 51.125 21.167 51.125C18.022 46.057 13.289 43.421 10.226 44.918C8.51 46.063 7.822 48.473 8.134 51.35C8.169 51.917 8.146 52.13 8.073 52.149C8.073 52.149 7.711 52.357 7.711 52.357C7.644 52.396 7.405 52.219 7.251 52.131C7.251 52.131 2.94 49.642 2.94 49.642C2.94 48.1 3.282 47.734 3.783 47.445C4.285 47.156 4.477 47.015 4.477 46.585C4.477 46.155 4.517 45.969 4.71 45.859C4.902 45.747 4.974 45.429 4.974 45.164C4.974 45.164 5.367 44.936 5.367 44.936C5.937 45.267 6.394 45.234 6.839 45.024C7.487 44.588 9.221 43.226 9.935 41.923C10.695 40.533 11.952 39.542 14.003 37.424C16.053 35.307 16.98 34.646 16.649 32.067C16.318 29.486 16.152 27.618 15.955 26.329C15.865 25.745 15.643 25.051 15.294 24.371C15.294 23.417 15.634 23.242 16.966 23.865C19.027 25.246 20.908 26.525 21.905 27.265C22.125 27.428 22.326 27.586 22.515 27.746C25.356 29.923 26.216 30.981 26.533 31.437"
            stroke-width="0.12" />
        </g>
        <g>
          <g />
        </g>
      </g>
    </g>
    <g id="_DATID_055043" class="DATID_055043_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M65.664 19.289C65.664 19.289 63.725 20.621 63.725 20.621C62.371 21.824 57.643 24.844 55.661 25.957C53.49 27.176 49.982 28.893 48.383 29.655C47.082 30.274 45.881 30.692 44.741 31.01C44.173 31.138 44.152 31.481 43.847 31.748C43.689 31.887 43.368 32.218 43.012 32.585C43.012 32.585 43.012 32.588 43.012 32.588C43.012 32.588 40.344 34.057 40.344 34.057C40.079 33.6 40.265 33.92 39.99 33.442C39.658 32.857 38.807 31.286 38.939 31.022C38.817 30.899 38.686 30.771 38.543 30.637C38.658 28.748 38.917 27.908 39.725 27.088C40.597 26.204 45.756 22.768 50.116 20.221C50.116 20.221 50.497 20.001 50.497 20.001C50.497 20.001 50.514 19.992 50.53 19.983C50.646 19.914 50.762 19.847 50.878 19.78C55.328 17.211 58.292 15.827 59.396 15.541C60.503 15.255 61.112 15.179 62.045 15.368C62.488 15.442 64.677 17.669 65.037 18.03C65.394 18.385 65.177 18.522 65.662 19.291"
            stroke-width="0.12" />
        </g>
        <g />
      </g>
    </g>
    <g id="_DATID_052042" class="DATID_052042_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M75.472 34.954C75.385 34.479 75.31 33.743 75.31 32.741C75.31 30.907 75.334 30.501 75.549 30.216C75.748 29.951 75.765 29.861 75.638 29.606C75.194 28.987 74.144 27.914 73.179 26.85C73.179 26.85 73.036 27.004 73.036 27.004C73.12 27.481 72.941 28.036 72.352 28.809C71.248 30.257 67.43 33.911 65.111 35.25C59.868 38.277 56.707 40.148 48.951 42.11C46.996 42.604 46.651 42.939 45.991 42.064C45.991 42.064 44.96 42.28 44.96 42.28C45.695 43.627 45.735 43.758 45.759 44.159C45.777 44.47 45.887 44.707 46.103 45.414C46.103 45.414 48.256 45.414 48.256 45.414C48.677 45.427 49.106 45.435 49.523 45.435C51.085 45.435 51.505 45.435 51.886 46.159C52.267 46.884 52.42 47.14 52.533 47.408C52.606 47.578 52.655 47.823 52.662 48.06C54.21 47.332 61.299 44.701 64.921 42.529C68.542 40.356 73.457 36.889 74.983 35.363C74.983 35.363 75.471 34.954 75.472 34.954"
            stroke-width="0.12" />
        </g>
      </g>
    </g>
    <g id="_DATID_053041" class="DATID_053041_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M52.662 48.06C52.655 47.823 52.606 47.578 52.533 47.408C52.42 47.14 52.267 46.884 51.886 46.159C51.505 45.435 51.085 45.435 49.523 45.435C49.106 45.435 48.677 45.427 48.256 45.414C48.256 45.414 46.103 45.414 46.103 45.414C45.887 44.707 45.777 44.47 45.759 44.159C45.735 43.758 45.695 43.627 44.96 42.28C44.96 42.28 45.991 42.064 45.991 42.064C46.651 42.939 46.996 42.604 48.951 42.11C56.707 40.148 59.868 38.277 65.111 35.25C67.43 33.911 71.248 30.257 72.352 28.809C72.941 28.036 73.12 27.481 73.036 27.004C73.036 27.004 73.179 26.85 73.179 26.85C74.144 27.914 75.194 28.987 75.638 29.606C75.765 29.861 75.748 29.951 75.549 30.216C75.334 30.501 75.31 30.907 75.31 32.741C75.31 33.743 75.385 34.479 75.472 34.954C75.474 34.97 75.481 35.002 75.478 34.985C75.471 34.954 75.471 34.954 75.471 34.954C75.546 35.35 75.629 35.564 75.686 35.597C75.686 35.597 76.046 35.805 76.046 35.805C76.099 36.484 76.087 36.945 76.087 38.088C76.087 39.232 75.706 40.356 74.563 41.423C73.381 42.527 69.456 45.198 65.263 47.618C65.049 47.741 64.837 47.862 64.626 47.982C64.57 48.017 64.189 48.237 64.189 48.237C64.189 48.237 64.167 48.247 64.144 48.259C60.281 50.451 57.088 51.957 55.582 52.601C53.981 53.287 53.258 53.744 52.42 53.898C51.581 54.049 51.2 53.936 50.305 53.673C50.109 53.372 49.896 52.99 49.624 52.478C48.713 52.084 48.139 51.859 47.655 50.92C47.655 50.92 49.402 51.116 49.404 51.115C49.656 51.006 49.872 50.895 50.055 50.789C50.637 50.454 50.99 50.113 51.676 49.6C52.362 49.085 52.552 48.741 52.628 48.436C52.63 48.432 52.631 48.429 52.631 48.424C52.631 48.424 52.651 48.198 52.662 48.06"
            stroke-width="0.12" />
        </g>
      </g>
    </g>
    <g id="_DATID_054040" class="DATID_054040_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M47.655 50.92C47.655 50.92 49.402 51.116 49.404 51.115C49.656 51.006 49.872 50.895 50.055 50.789C50.637 50.454 50.99 50.113 51.676 49.6C52.362 49.085 52.552 48.741 52.628 48.436C52.63 48.432 52.631 48.429 52.631 48.424C52.631 48.424 52.662 48.06 52.662 48.06C54.21 47.332 61.299 44.701 64.921 42.529C68.542 40.356 73.457 36.889 74.983 35.363C74.983 35.363 75.471 34.954 75.471 34.954C75.546 35.35 75.629 35.564 75.686 35.597C75.686 35.597 76.046 35.805 76.046 35.805C76.099 36.484 76.087 36.945 76.087 38.088C76.087 39.232 75.706 40.356 74.563 41.423C73.381 42.527 69.456 45.198 65.263 47.618C65.049 47.741 64.837 47.862 64.626 47.982C64.57 48.017 64.189 48.237 64.189 48.237C64.189 48.237 64.167 48.247 64.144 48.259C60.281 50.451 57.088 51.957 55.582 52.601C53.981 53.287 53.258 53.744 52.42 53.898C51.581 54.049 51.2 53.936 50.305 53.673C50.109 53.372 49.896 52.99 49.624 52.478C48.713 52.084 48.139 51.859 47.655 50.92"
            stroke-width="0.12" />
        </g>
      </g>
    </g>
    <g id="_DATID_028039" class="DATID_028039_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M32.792 88.804C34.481 90.612 33.206 91.483 34.659 93.498C33.5 93.7 32.122 93.487 31.26 93.563C31.26 93.563 23.492 97.341 23.492 97.341C23.492 97.341 23.425 95.242 23.425 95.242C23.425 95.242 24.219 95.308 24.219 95.308C24.219 95.308 24.219 93.951 24.219 93.951C24.219 93.951 24.897 93.951 24.897 93.951C24.897 93.951 24.873 93.179 24.873 93.179C24.873 93.179 25.481 92.105 25.481 92.105C25.605 92.069 28.461 90.99 32.792 88.804"
            stroke-width="0.12" />
        </g>
      </g>
    </g>
    <g id="_DATID_026038" class="DATID_026038_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M24.115 83.802C24.121 83.831 24.138 83.859 24.167 83.89C26.182 85.971 25.565 86.978 25.843 88.501C25.621 88.599 23.339 89.618 23.339 89.618C23.339 89.618 19.722 91.706 19.722 91.706C19.722 91.706 19.364 91.454 19.364 91.454C19.364 91.454 16.536 93.087 16.536 93.087C16.536 93.087 15.419 91.177 15.419 91.177C15.419 91.177 15.419 86.141 15.419 86.141C15.419 86.141 15.923 85.852 15.923 85.852C15.923 85.852 15.923 84.069 15.923 84.069C15.923 84.069 16.754 83.289 16.754 83.289C16.754 83.289 16.754 82.161 16.754 82.161C16.754 82.161 18.175 82.522 18.175 82.522C18.515 82.465 18.928 82.32 19.413 82.04C20.37 81.487 21.96 80.701 23.28 80.066C23.448 81.307 22.836 82.004 24.115 83.802"
            stroke-width="0.12" />
        </g>
      </g>
    </g>
    <g id="_DATID_011037" class="DATID_011037_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M30.777 36.552L30.373 36.32C29.748 35.17 29.207 34.133 29.039 33.728 28.871 33.328 28.704 33.073 28.62 32.868 28.568 32.728 28.55 32.625 28.58 32.512 28.614 32.387 28.708 32.256 28.885 32.09 29.219 31.777 29.643 31.339 30.01 30.963L30 30.257C30.004 30.193 29.96 30.114 29.939 30.067L29.798 29.943 28.332 28.649C28.123 28.815 27.916 28.96 27.838 28.905 27.722 28.823 27.343 28.541 27.343 28.393 27.343 28.243 27.408 28.128 27.475 28.161 27.541 28.193 27.69 28.227 27.838 27.995 27.986 27.763 28.251 27.483 28.41 27.638 29.103 28.196 29.803 28.806 30.548 29.512 30.727 29.734 30.835 30.004 30.818 30.303L30.821 30.658C30.823 30.742 30.814 30.829 30.795 30.917 30.332 33.2 30.182 34.314 30.414 34.877 30.646 35.439 30.629 35.482 30.777 35.728 30.925 35.973 31.027 36.304 30.777 36.552z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M28.984 32.744C29.018 32.621 29.111 32.488 29.289 32.323C29.623 32.009 30.047 31.573 30.413 31.196" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M30.611 31.361C30.557 31.451 30.492 31.542 30.413 31.634C30.047 32.009 29.623 32.447 29.289 32.759C29.211 32.832 29.149 32.899 29.102 32.96" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M29.024 33.1C29.108 33.305 29.274 33.561 29.442 33.962C29.611 34.365 30.15 35.402 30.777 36.552" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M28.625 28.41C29.338 29.024 30.236 29.841 30.236 29.841C30.236 29.841 30.376 30.094 30.367 30.277C30.367 30.277 30.367 30.437 30.367 30.527" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M29.024 33.1C28.971 32.96 28.954 32.856 28.984 32.744" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M30.105 30.847L30.367 30.527" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M29.757 33.218C29.832 33.261 29.954 33.191 30.03 33.06C30.105 32.929 30.105 32.789 30.03 32.744C29.954 32.701 29.832 32.771 29.757 32.902C29.681 33.033 29.681 33.173 29.757 33.218" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M30.777 36.552C30.777 36.552 30.373 36.32 30.373 36.32C29.748 35.17 29.207 34.133 29.039 33.728C28.871 33.328 28.704 33.073 28.62 32.868C28.568 32.728 28.55 32.625 28.58 32.512C28.614 32.387 28.708 32.256 28.885 32.09C29.219 31.777 29.643 31.339 30.01 30.963C30.01 30.963 30 30.257 30 30.257C30.004 30.193 29.96 30.114 29.939 30.067C29.939 30.067 29.798 29.943 29.798 29.943C29.798 29.943 28.332 28.649 28.332 28.649C28.123 28.815 27.916 28.96 27.838 28.905C27.722 28.823 27.343 28.541 27.343 28.393C27.343 28.243 27.408 28.128 27.475 28.161C27.541 28.193 27.69 28.227 27.838 27.995C27.986 27.763 28.251 27.483 28.41 27.638C29.103 28.196 29.803 28.806 30.548 29.512C30.727 29.734 30.835 30.004 30.818 30.303C30.818 30.303 30.821 30.658 30.821 30.658C30.823 30.742 30.814 30.829 30.795 30.917C30.332 33.2 30.182 34.314 30.414 34.877C30.646 35.439 30.629 35.482 30.777 35.728C30.925 35.973 31.027 36.304 30.777 36.552"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M30.777 36.552L30.373 36.32C29.748 35.17 29.207 34.133 29.039 33.728C28.871 33.328 28.704 33.073 28.62 32.868C28.568 32.728 28.55 32.625 28.58 32.512C28.614 32.387 28.708 32.256 28.885 32.09C29.219 31.777 29.643 31.339 30.01 30.963L30 30.257C30.004 30.193 29.96 30.114 29.939 30.067L29.798 29.943L28.332 28.649C28.123 28.815 27.916 28.96 27.838 28.905C27.722 28.823 27.343 28.541 27.343 28.393C27.343 28.243 27.408 28.128 27.475 28.161C27.541 28.193 27.69 28.227 27.838 27.995C27.986 27.763 28.251 27.483 28.41 27.638C29.103 28.196 29.803 28.806 30.548 29.512C30.727 29.734 30.835 30.004 30.818 30.303L30.821 30.658C30.823 30.742 30.814 30.829 30.795 30.917C30.332 33.2 30.182 34.314 30.414 34.877C30.646 35.439 30.629 35.482 30.777 35.728C30.925 35.973 31.027 36.304 30.777 36.552" />
        </g>
      </g>
    </g>
    <g>
      <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
        d="M26.884 56.197C26.376 56.466 25.524 56.164 24.132 55.359L22.998 54.706C22.416 54.368 22.37 54.341 22.37 53.448 22.37 52.555 22.039 51.63 21.589 50.881L21.167 51.125C18.022 46.057 13.289 43.421 10.226 44.918 8.51 46.063 7.822 48.473 8.134 51.35 8.169 51.917 8.146 52.13 8.073 52.149L7.711 52.357C7.644 52.396 7.405 52.219 7.251 52.131L2.94 49.642C2.94 48.1 3.282 47.734 3.783 47.445 4.285 47.156 4.477 47.015 4.477 46.585 4.477 46.155 4.517 45.969 4.71 45.859 4.902 45.747 4.974 45.429 4.974 45.164L5.367 44.936C5.937 45.267 6.394 45.234 6.839 45.024 7.487 44.588 9.221 43.226 9.935 41.923 10.695 40.533 11.952 39.542 14.003 37.424 16.053 35.307 16.98 34.646 16.649 32.067 16.318 29.486 16.152 27.618 15.955 26.329 15.756 25.039 14.913 23.211 13.49 22.187 12.069 21.161 11.897 21.131 11.897 20.548 11.897 19.964 12.134 19.675 12.58 19.417 13.028 19.158 14.419 18.15 15.276 18.646 16.414 19.391 17.568 20.169 18.724 20.96 22.839 23.78 25.088 25.039 28.06 27.859 28.24 28.082 28.35 28.35 28.332 28.649L28.335 29.006C28.341 29.303 28.222 29.632 27.926 29.981 27.561 30.358 27.137 30.795 26.803 31.106 26.669 31.234 26.582 31.339 26.533 31.437 26.615 31.644 26.786 31.902 26.955 32.308 27.336 33.223 29.623 37.378 30.501 38.75 31.333 40.478 31.986 41.24 32.588 41.954 32.807 42.213 32.59 42.484 32.421 42.485 31.496 42.442 30.661 42.372 30.158 42.304 29.318 42.189 27.146 41.771 26.46 41.503 26.079 41.356 25.757 41.255 25.521 41.265 25.51 41.286 25.501 41.307 25.492 41.331 25.339 41.75 25.339 41.941 25.987 43.618 26.452 44.953 27.129 46.764 27.782 47.767 28.094 48.243 27.928 51.735 27.928 53.204 27.928 54.774 27.78 55.67 27.28 55.969 27.28 55.969 26.905 56.185 26.884 56.197z" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M13.449 43.148C13.496 43.156 13.544 43.167 13.592 43.176C16.576 43.807 20.013 46.43 22.464 50.417" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M9.794 45.167C8.08 46.311 7.392 48.722 7.703 51.598" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M7.722 52.154C7.727 52.033 7.721 51.853 7.703 51.598" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M6.825 48.374C7.024 47.341 7.384 46.457 7.871 45.743C8.317 45.088 8.871 44.569 9.513 44.192L9.519 44.189C10.672 43.524 12.035 43.359 13.487 43.667C16.359 44.273 19.647 46.811 22.013 50.637" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M14.35 42.128C17.48 42.987 21.016 45.74 23.536 49.871" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M6.825 48.374C6.679 49.481 6.259 50.054 4.315 48.871C3.964 48.658 3.562 48.423 3.133 48.18" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M13.185 43.609C13.502 42.85 13.903 42.872 14.15 42.506C14.396 42.142 14.949 40.716 15.102 40.106C15.255 39.496 15.788 38.962 16.321 37.895C16.856 36.828 17.388 35.912 17.542 35.075C17.693 34.235 17.617 34.332 17.274 31.664C16.931 28.995 16.664 27.547 16.359 25.946C16.054 24.346 16.132 23.716 14.378 22.268C13.731 21.734 12.929 21.195 11.903 20.55" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M29.737 38.542C27.832 35.189 27.527 34.35 26.231 32.064C24.936 29.777 24.25 28.405 22.192 26.879C20.338 25.507 15.461 22.283 12.003 19.986" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M26.498 31.092C26.532 30.968 26.626 30.836 26.803 30.67C27.137 30.358 27.561 29.92 27.926 29.544" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M17.593 34.762C20.114 35.816 23.85 37.53 25.946 38.331C27.94 39.093 28.628 39.725 30.971 39.664" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M16.426 37.686C19.009 39.054 21.981 40.731 24.382 41.456" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M25.621 43.829C24.974 42.152 24.974 41.96 25.126 41.542C25.17 41.424 25.239 41.348 25.332 41.307" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M27.416 47.978C26.763 46.975 26.086 45.164 25.621 43.829" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M27.414 47.975C25.335 48.887 22.91 50.229 22.013 50.637" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M4.974 45.164C5.62 45.536 6.121 45.445 6.613 45.165" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M4.71 45.859C5.955 46.578 6.759 46.387 8.679 44.402C10.596 42.418 17.431 36.121 17.585 33.978" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M4.339 47.043C5.476 47.7 6.577 47.683 7.075 47.396" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M27.926 29.544C28.074 29.37 28.178 29.199 28.243 29.036" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M27.414 47.975C27.728 48.454 27.562 51.947 27.562 53.415C27.562 54.518 27.489 55.289 27.272 55.753" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M22.464 50.417C23.003 51.311 23.359 52.38 23.359 53.448C23.359 53.512 23.373 53.719 23.559 53.826" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M26.472 48.405C26.635 48.963 26.567 50.737 26.539 52.118L26.521 53.415C26.521 54.375 26.625 54.862 26.396 55.189L26.338 55.213C25.943 55.253 25.516 54.957 24.652 54.457" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M24.652 54.457L23.559 53.826" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M23.957 53.692C24.039 53.643 24.039 53.49 23.957 53.347C23.876 53.206 23.742 53.128 23.661 53.176C23.577 53.223 23.577 53.378 23.661 53.52C23.742 53.661 23.876 53.738 23.957 53.692" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M25.201 52.869C25.285 52.822 25.285 52.664 25.201 52.52C25.117 52.375 24.981 52.295 24.897 52.344C24.814 52.393 24.814 52.549 24.897 52.695C24.981 52.84 25.117 52.918 25.201 52.869" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M25.682 49.637C25.759 49.593 25.759 49.451 25.682 49.32C25.606 49.189 25.484 49.118 25.408 49.161C25.332 49.204 25.332 49.347 25.408 49.478C25.484 49.609 25.606 49.68 25.682 49.637" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M25.699 50.331C25.774 50.287 25.774 50.146 25.699 50.015C25.623 49.883 25.501 49.813 25.425 49.856C25.349 49.899 25.349 50.042 25.425 50.173C25.501 50.304 25.623 50.375 25.699 50.331" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M25.748 51.125C25.824 51.081 25.824 50.939 25.748 50.807C25.673 50.676 25.55 50.606 25.475 50.649C25.399 50.694 25.399 50.835 25.475 50.966C25.55 51.097 25.673 51.168 25.748 51.125" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M23.466 50.761C23.542 50.718 23.542 50.576 23.466 50.445C23.39 50.313 23.268 50.241 23.193 50.286C23.117 50.329 23.117 50.472 23.193 50.603C23.268 50.734 23.39 50.804 23.466 50.761" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M18.763 34.807C19.472 35.112 21.189 35.854 21.189 35.854C23.004 36.649 24.844 37.456 26.097 37.935C26.576 38.118 26.981 38.292 27.352 38.454C27.853 38.671 28.288 38.86 28.766 39" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M29.088 38.35C28.873 37.954 25.557 31.844 25.557 31.844C24.455 29.891 23.76 28.64 21.905 27.265C20.908 26.525 19.027 25.246 16.966 23.865" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M18.088 33.982C18.071 33.853 18.048 33.695 18.021 33.499L17.978 33.134L17.823 32.176L17.771 31.6C17.425 28.917 17.236 28.097 16.929 26.487L16.852 25.856L16.83 25.628L16.762 25.361C16.698 24.992 16.638 24.673 16.556 24.376" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M29.088 38.35C29.298 38.732 29.298 39.177 28.766 39" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M18.763 34.807C18.269 34.631 18.155 34.622 18.088 33.982" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M16.556 24.376C16.437 23.963 16.585 23.609 16.966 23.865" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M24.591 31.786C24.855 31.719 24.756 31.454 24.425 30.826C24.094 30.198 23.961 29.9 23.632 29.9C23.3 29.9 22.937 30.198 23.333 30.893C23.731 31.586 23.798 32.036 24.591 31.786" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M24.268 31.586C24.343 31.545 24.343 31.407 24.268 31.278C24.196 31.15 24.076 31.082 24.001 31.123C23.928 31.167 23.928 31.304 24.001 31.432C24.076 31.561 24.196 31.629 24.268 31.586" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M23.881 30.794C23.954 30.751 23.954 30.612 23.881 30.484C23.806 30.356 23.687 30.288 23.614 30.33C23.539 30.373 23.539 30.51 23.614 30.638C23.687 30.766 23.806 30.835 23.881 30.794" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M25.856 33.468C25.856 33.121 25.504 33.094 24.978 32.789C24.454 32.487 24.152 32.691 24.152 33.021C24.152 33.353 24.222 33.369 24.879 33.75C25.536 34.128 25.856 34.162 25.856 33.468" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M25.161 33.588C25.236 33.545 25.236 33.406 25.161 33.28C25.088 33.152 24.969 33.082 24.894 33.125C24.821 33.167 24.821 33.305 24.894 33.433C24.969 33.561 25.088 33.631 25.161 33.588" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M28.17 37.701C28.17 37.353 27.82 37.326 27.294 37.024C26.768 36.719 26.466 36.924 26.466 37.255C26.466 37.585 26.538 37.603 27.195 37.982C27.852 38.362 28.17 38.396 28.17 37.701" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M27.478 37.822C27.55 37.779 27.55 37.64 27.478 37.512C27.403 37.384 27.283 37.316 27.21 37.359C27.137 37.401 27.137 37.539 27.21 37.667C27.283 37.795 27.403 37.863 27.478 37.822" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M22.896 35.485C22.896 35.139 22.544 35.11 22.018 34.807C21.493 34.505 21.192 34.709 21.192 35.039C21.192 35.369 21.263 35.387 21.92 35.767C22.576 36.146 22.896 36.179 22.896 35.485" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M22.202 35.606C22.275 35.563 22.275 35.424 22.202 35.296C22.129 35.168 22.009 35.1 21.934 35.143C21.861 35.185 21.861 35.323 21.934 35.451C22.009 35.579 22.129 35.648 22.202 35.606" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M20.498 34.411C20.498 34.064 20.146 34.036 19.62 33.732C19.096 33.429 18.795 33.634 18.795 33.963C18.795 34.295 18.864 34.313 19.521 34.692C20.178 35.07 20.498 35.104 20.498 34.411" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M19.804 34.53C19.878 34.487 19.878 34.35 19.804 34.222C19.731 34.094 19.611 34.024 19.536 34.067C19.463 34.11 19.463 34.249 19.536 34.375C19.611 34.503 19.731 34.573 19.804 34.53" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M21.225 27.961C21.225 27.614 20.873 27.586 20.349 27.283C19.824 26.98 19.521 27.184 19.521 27.515C19.521 27.846 19.593 27.864 20.25 28.242C20.907 28.621 21.225 28.655 21.225 27.961" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M20.532 28.082C20.606 28.039 20.606 27.9 20.532 27.772C20.457 27.644 20.338 27.576 20.265 27.618C20.19 27.661 20.19 27.798 20.265 27.926C20.338 28.054 20.457 28.123 20.532 28.082" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M17.274 24.89C17.347 24.847 17.347 24.708 17.274 24.58C17.201 24.454 17.08 24.385 17.007 24.426C16.934 24.469 16.934 24.608 17.007 24.736C17.08 24.864 17.201 24.932 17.274 24.89" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M18.303 24.763C18.303 25.076 17.135 25.803 16.83 25.628" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M24.981 36.729C25.053 36.688 25.053 36.548 24.981 36.421C24.907 36.293 24.786 36.225 24.713 36.267C24.64 36.31 24.64 36.448 24.713 36.576C24.786 36.704 24.907 36.773 24.981 36.729" />
      <g id="DATID_000000" class="V2L1St1Sz32-SK#">
        <path pointer-events="all"
          d="M26.884 56.197C26.376 56.466 25.524 56.164 24.132 55.359C24.132 55.359 22.998 54.706 22.998 54.706C22.416 54.368 22.37 54.341 22.37 53.448C22.37 52.555 22.039 51.63 21.589 50.881C21.589 50.881 21.167 51.125 21.167 51.125C18.022 46.057 13.289 43.421 10.226 44.918C8.51 46.063 7.822 48.473 8.134 51.35C8.169 51.917 8.146 52.13 8.073 52.149C8.073 52.149 7.711 52.357 7.711 52.357C7.644 52.396 7.405 52.219 7.251 52.131C7.251 52.131 2.94 49.642 2.94 49.642C2.94 48.1 3.282 47.734 3.783 47.445C4.285 47.156 4.477 47.015 4.477 46.585C4.477 46.155 4.517 45.969 4.71 45.859C4.902 45.747 4.974 45.429 4.974 45.164C4.974 45.164 5.367 44.936 5.367 44.936C5.937 45.267 6.394 45.234 6.839 45.024C7.487 44.588 9.221 43.226 9.935 41.923C10.695 40.533 11.952 39.542 14.003 37.424C16.053 35.307 16.98 34.646 16.649 32.067C16.318 29.486 16.152 27.618 15.955 26.329C15.756 25.039 14.913 23.211 13.49 22.187C12.069 21.161 11.897 21.131 11.897 20.548C11.897 19.964 12.134 19.675 12.58 19.417C13.028 19.158 14.419 18.15 15.276 18.646C16.414 19.391 17.568 20.169 18.724 20.96C22.839 23.78 25.088 25.039 28.06 27.859C28.24 28.082 28.35 28.35 28.332 28.649C28.332 28.649 28.335 29.006 28.335 29.006C28.341 29.303 28.222 29.632 27.926 29.981C27.561 30.358 27.137 30.795 26.803 31.106C26.669 31.234 26.582 31.339 26.533 31.437C26.615 31.644 26.786 31.902 26.955 32.308C27.336 33.223 29.623 37.378 30.501 38.75C31.333 40.478 31.986 41.24 32.588 41.954C32.807 42.213 32.59 42.484 32.421 42.485C31.496 42.442 30.661 42.372 30.158 42.304C29.318 42.189 27.146 41.771 26.46 41.503C26.079 41.356 25.757 41.255 25.521 41.265C25.51 41.286 25.501 41.307 25.492 41.331C25.339 41.75 25.339 41.941 25.987 43.618C26.452 44.953 27.129 46.764 27.782 47.767C28.094 48.243 27.928 51.735 27.928 53.204C27.928 54.774 27.78 55.67 27.28 55.969C27.271 55.972 26.905 56.185 26.884 56.197"
          stroke-width="0.25" />
        <path stroke-width="0.25" stroke-linejoin="round"
          d="M26.884 56.197C26.376 56.466 25.524 56.164 24.132 55.359L22.998 54.706C22.416 54.368 22.37 54.341 22.37 53.448C22.37 52.555 22.039 51.63 21.589 50.881L21.167 51.125C18.022 46.057 13.289 43.421 10.226 44.918C8.51 46.063 7.822 48.473 8.134 51.35C8.169 51.917 8.146 52.13 8.073 52.149L7.711 52.357C7.644 52.396 7.405 52.219 7.251 52.131L2.94 49.642C2.94 48.1 3.282 47.734 3.783 47.445C4.285 47.156 4.477 47.015 4.477 46.585C4.477 46.155 4.517 45.969 4.71 45.859C4.902 45.747 4.974 45.429 4.974 45.164L5.367 44.936C5.937 45.267 6.394 45.234 6.839 45.024C7.487 44.588 9.221 43.226 9.935 41.923C10.695 40.533 11.952 39.542 14.003 37.424C16.053 35.307 16.98 34.646 16.649 32.067C16.318 29.486 16.152 27.618 15.955 26.329C15.756 25.039 14.913 23.211 13.49 22.187C12.069 21.161 11.897 21.131 11.897 20.548C11.897 19.964 12.134 19.675 12.58 19.417C13.028 19.158 14.419 18.15 15.276 18.646C16.414 19.391 17.568 20.169 18.724 20.96C22.839 23.78 25.088 25.039 28.06 27.859C28.24 28.082 28.35 28.35 28.332 28.649L28.335 29.006C28.341 29.303 28.222 29.632 27.926 29.981C27.561 30.358 27.137 30.795 26.803 31.106C26.669 31.234 26.582 31.339 26.533 31.437C26.615 31.644 26.786 31.902 26.955 32.308C27.336 33.223 29.623 37.378 30.501 38.75C31.333 40.478 31.986 41.24 32.588 41.954C32.807 42.213 32.59 42.484 32.421 42.485C31.496 42.442 30.661 42.372 30.158 42.304C29.318 42.189 27.146 41.771 26.46 41.503C26.079 41.356 25.757 41.255 25.521 41.265C25.51 41.286 25.501 41.307 25.492 41.331C25.339 41.75 25.339 41.941 25.987 43.618C26.452 44.953 27.129 46.764 27.782 47.767C28.094 48.243 27.928 51.735 27.928 53.204C27.928 54.774 27.78 55.67 27.28 55.969C27.28 55.969 26.905 56.185 26.884 56.197" />
      </g>
    </g>
    <g id="_DATID_024036" class="DATID_024036_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M28.731 75.269L28.445 73.799C28.431 73.727 28.519 73.65 28.643 73.626 28.701 73.614 28.76 73.617 28.809 73.634L29.66 73.76C29.705 73.777 29.736 73.804 29.743 73.84L29.891 74.599 29.972 74.552 30.274 74.531 31.239 73.974 32.335 74.087 32.38 74.403 31.414 74.959 31.112 74.981 29.077 76.156 28.847 76.445 27.881 77.003 26.786 76.891 26.74 76.573 27.707 76.016 27.937 75.727 28.731 75.269z" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="27.881" y1="77.003" x2="27.835" y2="76.686" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="27.835" y1="76.686" x2="26.74" y2="76.573" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="27.835" y1="76.686" x2="28.801" y2="76.129" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="29.032" y1="75.84" x2="29.527" y2="75.557" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.368" y1="74.644" x2="32.335" y2="74.087" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="28.801" y1="76.129" x2="29.032" y2="75.84" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.068" y1="74.665" x2="31.368" y2="74.644" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="29.149" y1="75.028" x2="29.472" y2="74.84" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="28.801" y1="76.129" x2="27.707" y2="76.016" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="29.032" y1="75.84" x2="27.937" y2="75.727" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.068" y1="74.665" x2="29.972" y2="74.552" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="29.58" y1="75.397" x2="29.353" y2="74.219" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="30.03" y1="75.31" x2="29.891" y2="74.599" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="30.571" y1="74.948" x2="31.068" y2="74.665" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M29.527 75.557C29.527 75.557 29.888 75.641 30.041 75.477C30.193 75.314 30.283 75.088 30.362 75.037C30.442 74.986 30.571 74.948 30.571 74.948" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="29.179" y1="75.183" x2="28.981" y2="74.165" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="28.527" y1="73.879" x2="29.379" y2="74.006" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="28.591" y1="74.105" x2="29.442" y2="74.233" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M29.58 75.397C29.594 75.468 29.707 75.507 29.83 75.483C29.954 75.459 30.044 75.382 30.03 75.31" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M29.379 74.006C29.426 74.022 29.486 74.024 29.545 74.013C29.669 73.989 29.757 73.912 29.743 73.84" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M28.731 75.269C28.745 75.34 28.856 75.379 28.98 75.355C29.103 75.331 29.193 75.254 29.179 75.183" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M28.445 73.799C28.452 73.834 28.483 73.862 28.527 73.879" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M29.442 74.233C29.539 74.266 29.681 74.239 29.759 74.171" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M28.507 74.027C28.515 74.063 28.545 74.09 28.591 74.105" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M28.731 75.269C28.731 75.269 28.445 73.799 28.445 73.799C28.431 73.727 28.519 73.65 28.643 73.626C28.701 73.614 28.76 73.617 28.809 73.634C28.809 73.634 29.66 73.76 29.66 73.76C29.705 73.777 29.736 73.804 29.743 73.84C29.743 73.84 29.891 74.599 29.891 74.599C29.891 74.599 29.972 74.552 29.972 74.552C29.972 74.552 30.274 74.531 30.274 74.531C30.274 74.531 31.239 73.974 31.239 73.974C31.239 73.974 32.335 74.087 32.335 74.087C32.335 74.087 32.38 74.403 32.38 74.403C32.38 74.403 31.414 74.959 31.414 74.959C31.414 74.959 31.112 74.981 31.112 74.981C31.112 74.981 29.077 76.156 29.077 76.156C29.077 76.156 28.847 76.445 28.847 76.445C28.847 76.445 27.881 77.003 27.881 77.003C27.881 77.003 26.786 76.891 26.786 76.891C26.786 76.891 26.74 76.573 26.74 76.573C26.74 76.573 27.707 76.016 27.707 76.016C27.707 76.016 27.937 75.727 27.937 75.727C27.937 75.727 28.731 75.269 28.731 75.269"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M28.731 75.269L28.445 73.799C28.431 73.727 28.519 73.65 28.643 73.626C28.701 73.614 28.76 73.617 28.809 73.634L29.66 73.76C29.705 73.777 29.736 73.804 29.743 73.84L29.891 74.599L29.972 74.552L30.274 74.531L31.239 73.974L32.335 74.087L32.38 74.403L31.414 74.959L31.112 74.981L29.077 76.156L28.847 76.445L27.881 77.003L26.786 76.891L26.74 76.573L27.707 76.016L27.937 75.727L28.731 75.269" />
        </g>
      </g>
    </g>
    <g id="_DATID_027035" class="DATID_027035_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M50.825 81.31C51.03 81.109 51.215 80.909 51.372 80.706L51.868 78.936 52.265 78.671 52.265 77.861 51.527 77.429C51.527 77.429 51.13 77.155 51.179 77.135 38.334 87.248 25.743 92.03 25.481 92.105L24.873 93.179 24.897 93.951 24.219 93.951 24.219 95.308 23.425 95.242 23.492 97.341 31.26 93.563C32.341 93.468 34.229 93.826 35.487 93.253 36.765 92.668 41.774 89.968 44.543 88.046 45.359 87.48 46.023 86.234 46.726 85.156L50.826 82.31 50.825 81.31z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M25.295 92.736C25.295 92.736 32.365 91.257 35.728 89.326" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M35.728 89.326C35.728 89.326 44.651 84.468 47.412 82.099" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M47.412 82.099C47.412 82.099 50.228 79.046 51.116 77.965" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M25.675 93.796C25.675 93.796 31.637 92.367 40.07 87.224C40.07 87.224 48.073 82.727 51.485 79.087" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M25.262 92.57C25.524 92.495 38.68 87.543 51.527 77.429" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.884" y1="91.966" x2="44.476" y2="85.275" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="26.271" y1="95.108" x2="24.219" y2="95.308" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M26.271 95.108C26.271 95.108 27.954 94.783 31.26 93.563" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M46.726 85.156C47.088 84.605 47.46 84.097 47.866 83.748C48.799 82.95 49.978 82.138 50.825 81.31" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.884" y1="93.025" x2="44.476" y2="86.332" />
        <path stroke="none" fill="#FFFFFF"
          d="M32.884 93.025C32.884 93.025 32.439 93.248 32.439 92.653 32.439 92.058 32.884 91.966 32.884 91.966z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M32.884 93.025C32.884 93.025 32.439 93.248 32.439 92.653C32.439 92.058 32.884 91.966 32.884 91.966" />
        <path stroke="none" fill="#FFFFFF"
          d="M44.476 85.275C44.476 85.275 44.972 84.996 44.972 85.492 44.972 85.989 44.476 86.332 44.476 86.332z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M44.476 85.275C44.476 85.275 44.972 84.996 44.972 85.492C44.972 85.989 44.476 86.332 44.476 86.332" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M50.825 81.31C51.03 81.109 51.215 80.909 51.372 80.706C51.372 80.706 51.868 78.936 51.868 78.936C51.868 78.936 52.265 78.671 52.265 78.671C52.265 78.671 52.265 77.861 52.265 77.861C52.265 77.861 51.527 77.429 51.527 77.429C51.527 77.429 51.13 77.155 51.179 77.135C38.334 87.248 25.743 92.03 25.481 92.105C25.481 92.105 24.873 93.179 24.873 93.179C24.873 93.179 24.897 93.951 24.897 93.951C24.897 93.951 24.219 93.951 24.219 93.951C24.219 93.951 24.219 95.308 24.219 95.308C24.219 95.308 23.425 95.242 23.425 95.242C23.425 95.242 23.492 97.341 23.492 97.341C23.492 97.341 31.26 93.563 31.26 93.563C32.341 93.468 34.229 93.826 35.487 93.253C36.765 92.668 41.774 89.968 44.543 88.046C45.359 87.48 46.023 86.234 46.726 85.156C46.726 85.156 50.826 82.31 50.826 82.31C50.826 82.31 50.825 81.31 50.825 81.31"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M50.825 81.31C51.03 81.109 51.215 80.909 51.372 80.706L51.868 78.936L52.265 78.671L52.265 77.861L51.527 77.429C51.527 77.429 51.13 77.155 51.179 77.135C38.334 87.248 25.743 92.03 25.481 92.105L24.873 93.179L24.897 93.951L24.219 93.951L24.219 95.308L23.425 95.242L23.492 97.341L31.26 93.563C32.341 93.468 34.229 93.826 35.487 93.253C36.765 92.668 41.774 89.968 44.543 88.046C45.359 87.48 46.023 86.234 46.726 85.156L50.826 82.31L50.825 81.31" />
        </g>
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M39.28 90.644C39.46 90.748 39.753 90.578 39.935 90.266C40.115 89.953 40.115 89.614 39.935 89.51C39.753 89.406 39.46 89.575 39.28 89.888C39.1 90.2 39.1 90.54 39.28 90.644" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M43.299 88.09C43.368 88.129 43.479 88.064 43.548 87.945C43.616 87.826 43.616 87.697 43.548 87.658C43.479 87.618 43.368 87.683 43.299 87.802C43.231 87.921 43.231 88.049 43.299 88.09" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M35.667 92.495C35.735 92.536 35.847 92.471 35.915 92.352C35.984 92.233 35.984 92.105 35.915 92.064C35.847 92.025 35.735 92.09 35.667 92.209C35.597 92.328 35.597 92.456 35.667 92.495" />
      </g>
    </g>
    <g id="_DATID_025034" class="DATID_025034_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M25.538 79.003L28.832 77.1C28.832 77.1 29.091 77.049 29.242 77.103 29.393 77.159 29.696 77.313 29.986 77.146L29.701 76.625C29.701 76.625 29.753 76.575 29.826 76.533 29.9 76.489 29.969 76.469 29.969 76.469L30.251 76.984 32.761 75.536 32.478 75.021C32.478 75.021 32.53 74.971 32.604 74.929 32.679 74.885 32.747 74.865 32.747 74.865L33.011 75.347C33.024 75.34 33.204 75.29 33.335 74.856 33.466 74.423 33.75 74.233 33.75 74.233 33.75 74.233 37.314 72.016 39.398 70.323 41.481 68.631 43.064 66.938 43.064 66.938L44.484 67.301C44.484 67.301 44.171 67.635 43.64 68.156 43.628 68.168 43.64 68.12 43.64 68.12L43.64 69.792 44.246 69.792 44.246 74.739 43.28 77.647 40.331 79.35 40.331 79.805 40.338 79.805 36.719 81.893 33.915 83.871 26.085 88.393 23.339 89.618 19.722 91.706 19.364 91.454 16.536 93.087 15.419 91.177 15.419 86.141 15.923 85.852 15.923 84.069 16.754 83.289 16.754 82.161 18.175 82.522C18.515 82.465 18.928 82.32 19.413 82.04 21.292 80.956 25.608 78.971 25.538 79.003z" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M25.538 79.003C25.538 79.003 28.832 77.1 28.832 77.1C28.832 77.1 29.091 77.049 29.242 77.103C29.393 77.159 29.696 77.313 29.986 77.146C29.986 77.146 29.701 76.625 29.701 76.625C29.701 76.625 29.753 76.575 29.826 76.533C29.9 76.489 29.969 76.469 29.969 76.469C29.969 76.469 30.251 76.984 30.251 76.984C30.251 76.984 32.761 75.536 32.761 75.536C32.761 75.536 32.478 75.021 32.478 75.021C32.478 75.021 32.53 74.971 32.604 74.929C32.679 74.885 32.747 74.865 32.747 74.865C32.747 74.865 33.011 75.347 33.011 75.347C33.024 75.34 33.204 75.29 33.335 74.856C33.466 74.423 33.75 74.233 33.75 74.233C33.75 74.233 37.314 72.016 39.398 70.323C41.481 68.631 43.064 66.938 43.064 66.938C43.064 66.938 44.484 67.301 44.484 67.301C44.484 67.301 44.171 67.635 43.64 68.156C43.628 68.168 43.64 68.12 43.64 68.12C43.64 68.12 43.64 69.792 43.64 69.792C43.64 69.792 44.246 69.792 44.246 69.792C44.246 69.792 44.246 74.739 44.246 74.739C44.246 74.739 43.28 77.647 43.28 77.647C43.28 77.647 40.331 79.35 40.331 79.35C40.331 79.35 40.331 79.805 40.331 79.805C40.331 79.805 40.338 79.805 40.338 79.805C40.338 79.805 36.719 81.893 36.719 81.893C36.719 81.893 33.915 83.871 33.915 83.871C33.915 83.871 26.085 88.393 26.085 88.393C26.085 88.393 23.339 89.618 23.339 89.618C23.339 89.618 19.722 91.706 19.722 91.706C19.722 91.706 19.364 91.454 19.364 91.454C19.364 91.454 16.536 93.087 16.536 93.087C16.536 93.087 15.419 91.177 15.419 91.177C15.419 91.177 15.419 86.141 15.419 86.141C15.419 86.141 15.923 85.852 15.923 85.852C15.923 85.852 15.923 84.069 15.923 84.069C15.923 84.069 16.754 83.289 16.754 83.289C16.754 83.289 16.754 82.161 16.754 82.161C16.754 82.161 18.175 82.522 18.175 82.522C18.515 82.465 18.928 82.32 19.413 82.04C21.292 80.956 25.608 78.971 25.538 79.003"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M25.538 79.003L28.832 77.1C28.832 77.1 29.091 77.049 29.242 77.103C29.393 77.159 29.696 77.313 29.986 77.146L29.701 76.625C29.701 76.625 29.753 76.575 29.826 76.533C29.9 76.489 29.969 76.469 29.969 76.469L30.251 76.984L32.761 75.536L32.478 75.021C32.478 75.021 32.53 74.971 32.604 74.929C32.679 74.885 32.747 74.865 32.747 74.865L33.011 75.347C33.024 75.34 33.204 75.29 33.335 74.856C33.466 74.423 33.75 74.233 33.75 74.233C33.75 74.233 37.314 72.016 39.398 70.323C41.481 68.631 43.064 66.938 43.064 66.938L44.484 67.301C44.484 67.301 44.171 67.635 43.64 68.156C43.628 68.168 43.64 68.12 43.64 68.12L43.64 69.792L44.246 69.792L44.246 74.739L43.28 77.647L40.331 79.35L40.331 79.805L40.338 79.805L36.719 81.893L33.915 83.871L26.085 88.393L23.339 89.618L19.722 91.706L19.364 91.454L16.536 93.087L15.419 91.177L15.419 86.141L15.923 85.852L15.923 84.069L16.754 83.289L16.754 82.161L18.175 82.522C18.515 82.465 18.928 82.32 19.413 82.04C21.292 80.956 25.608 78.971 25.538 79.003" />
        </g>
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.203" y1="82.897" x2="29.972" y2="84.763" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M33.203 82.897C33.203 82.897 33.777 82.432 33.777 81.585C33.777 80.739 33.979 80.248 34.558 79.915" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="34.558" y1="79.915" x2="38.491" y2="77.644" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M38.491 77.644C38.491 77.644 38.789 77.441 38.789 78.004" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="38.789" y1="78.004" x2="38.789" y2="80.691" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="40.048" y1="79.971" x2="40.048" y2="76.736" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M40.048 76.736C40.048 76.736 40.04 76.245 39.398 76.617" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="39.398" y1="76.617" x2="33.848" y2="79.819" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M33.848 79.819C33.848 79.819 33.473 80.045 33.473 80.587" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.473" y1="80.587" x2="33.473" y2="81.686" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="35.774" y1="76.117" x2="24.367" y2="82.703" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="24.367" y1="82.703" x2="19.695" y2="84.602" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="19.695" y1="84.602" x2="17.524" y2="85.078" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="17.524" y1="85.078" x2="15.419" y2="86.814" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="42.716" y1="70.337" x2="44.246" y2="70.4" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="42.798" y1="77.924" x2="42.798" y2="74.641" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="42.798" y1="74.641" x2="44.246" y2="73.805" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="42.098" y1="77.234" x2="42.098" y2="75.26" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.098 75.26C42.098 75.26 42.009 74.944 41.633 75.162C41.255 75.381 40.677 75.748 40.677 75.748C40.677 75.748 40.417 76.009 40.417 76.161C40.417 76.313 40.439 76.594 40.439 76.594C40.439 76.594 40.472 76.852 40.655 76.746C40.84 76.641 40.96 76.617 40.96 76.834C40.96 77.051 40.866 77.191 40.7 77.289C40.531 77.385 40.439 77.441 40.439 77.57C40.439 77.701 40.36 78.415 40.96 78.069C41.558 77.724 41.899 77.555 41.899 77.555C41.899 77.555 42.098 77.447 42.098 77.234" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M44.246 73.15C44.246 73.15 43.317 73.192 41.475 74.257C39.634 75.319 39.549 75.799 38.709 76.286C37.866 76.772 35.722 78.04 35.722 78.04" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M44.246 72.65C44.246 72.65 43.317 72.694 41.475 73.757C39.634 74.82 39.582 75.463 38.74 75.948C38.091 76.325 36.368 77.343 35.621 77.787" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.023 70.218C42.197 70.317 42.478 70.155 42.652 69.855C42.826 69.554 42.826 69.227 42.652 69.128C42.645 69.123 42.637 69.12 42.628 69.117" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M41.914 69.759C41.862 69.977 41.908 70.15 42.023 70.218" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M42.92 69.478C42.96 69.24 42.906 69.058 42.792 68.966" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M41.75 69.902C41.74 69.98 41.74 70.051 41.749 70.108" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="41.749" y1="70.108" x2="41.871" y2="71.042" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="42.92" y1="69.478" x2="42.777" y2="70.34" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.023 72.409C42.197 72.51 42.478 72.346 42.652 72.046C42.738 71.896 42.783 71.739 42.783 71.608" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M41.893 72.12C41.893 72.263 41.942 72.362 42.023 72.409" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="42.783" y1="71.608" x2="42.783" y2="70.34" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="41.893" y1="72.12" x2="41.893" y2="71.042" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.134 72.215C42.246 72.28 42.429 72.174 42.54 71.981C42.652 71.787 42.652 71.578 42.54 71.513C42.429 71.448 42.246 71.554 42.134 71.748C42.023 71.941 42.023 72.15 42.134 72.215" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M40.634 71.504C40.746 71.569 40.926 71.463 41.039 71.269C41.151 71.076 41.151 70.867 41.039 70.802C40.926 70.737 40.746 70.843 40.634 71.036C40.521 71.23 40.521 71.439 40.634 71.504" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="42.716" y1="70.337" x2="35.774" y2="76.117" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M32.835 80.79C32.835 80.68 32.796 80.603 32.735 80.569C32.668 80.53 32.582 80.542 32.494 80.593" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M31.187 81.347C30.998 81.456 30.846 81.721 30.846 81.938" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M32.494 82.168C32.682 82.058 32.835 81.793 32.835 81.576" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M30.946 82.947C31.012 82.984 31.1 82.972 31.187 82.923" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M30.846 82.725C30.846 82.834 30.884 82.911 30.946 82.947" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.835" y1="81.576" x2="32.835" y2="80.79" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="30.846" y1="82.725" x2="30.846" y2="81.938" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.187" y1="81.347" x2="32.494" y2="80.593" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.494" y1="82.168" x2="31.187" y2="82.923" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M33.473 81.686C33.473 81.686 33.46 82.382 32.88 82.716" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.88" y1="82.716" x2="30.908" y2="83.855" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M30.908 83.855C30.908 83.855 30.102 84.278 30.102 83.498C30.102 82.716 30.102 82.587 30.102 82.587C30.102 82.587 29.963 82.034 29.343 82.391" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="29.343" y1="82.391" x2="20.082" y2="87.737" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M20.082 87.737C20.082 87.737 19.588 88.108 19.588 88.5" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="19.588" y1="88.5" x2="19.588" y2="91.629" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M29.972 84.763C29.972 84.763 29.007 85.18 29.007 84.42C29.007 83.661 28.984 83.573 28.984 83.573C28.984 83.573 28.826 83.287 28.442 83.509" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="28.442" y1="83.509" x2="21.371" y2="87.59" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M21.371 87.59C21.371 87.59 20.783 87.87 20.783 88.564" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="20.783" y1="88.564" x2="20.783" y2="89.942" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M20.783 89.942C20.783 89.942 20.76 90.528 21.454 90.126" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="21.454" y1="90.126" x2="23.053" y2="89.204" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.915" y1="83.221" x2="26.085" y2="87.742" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="26.085" y1="87.742" x2="23.053" y2="89.204" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.915" y1="83.221" x2="36.231" y2="81.62" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="36.231" y1="81.62" x2="38.798" y2="80.14" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M38.13 78.966C38.13 78.858 38.091 78.781 38.03 78.745C37.963 78.707 37.877 78.719 37.789 78.769" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.784 80.504C34.597 80.612 34.444 80.878 34.444 81.094" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M37.789 80.03C37.978 79.921 38.13 79.656 38.13 79.439" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.543 81.789C34.609 81.826 34.698 81.814 34.784 81.765" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.444 81.567C34.444 81.677 34.481 81.753 34.543 81.789" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="38.13" y1="79.439" x2="38.13" y2="78.966" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="34.444" y1="81.567" x2="34.444" y2="81.094" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="34.784" y1="80.504" x2="37.789" y2="78.769" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="37.789" y1="80.03" x2="34.784" y2="81.765" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M39.055 78.578C39.21 78.668 39.46 78.524 39.615 78.256C39.768 77.989 39.768 77.7 39.615 77.611C39.46 77.522 39.21 77.665 39.055 77.933C38.901 78.2 38.901 78.489 39.055 78.578" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M40.076 75.795C40.164 75.844 40.305 75.763 40.393 75.611C40.481 75.459 40.481 75.296 40.393 75.245C40.305 75.195 40.164 75.278 40.076 75.429C39.99 75.581 39.99 75.743 40.076 75.795" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.173 74.584C42.262 74.635 42.404 74.552 42.49 74.4C42.579 74.25 42.579 74.085 42.49 74.034C42.404 73.984 42.262 74.066 42.173 74.218C42.087 74.37 42.087 74.534 42.173 74.584" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M19.974 89.596C20.129 89.685 20.379 89.54 20.533 89.272C20.687 89.006 20.687 88.716 20.533 88.626C20.379 88.537 20.129 88.683 19.974 88.95C19.82 89.218 19.82 89.507 19.974 89.596" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="17.446" y1="91.468" x2="17.446" y2="89.493" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M17.446 89.493C17.446 89.493 17.535 89.075 17.911 88.858C18.289 88.64 18.867 88.34 18.867 88.34C18.867 88.34 19.128 88.301 19.128 88.453C19.128 88.605 19.106 88.912 19.106 88.912C19.106 88.912 19.073 89.207 18.888 89.313C18.704 89.42 18.585 89.534 18.585 89.751C18.585 89.968 18.678 90.004 18.846 89.906C19.013 89.81 19.106 89.757 19.106 89.888C19.106 90.018 19.184 90.643 18.585 90.989C17.986 91.334 17.646 91.557 17.646 91.557C17.646 91.557 17.446 91.68 17.446 91.468" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M17.198 88.977C17.239 89.001 17.292 88.993 17.346 88.962" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M17.556 88.599C17.556 88.533 17.532 88.486 17.493 88.465C17.411 88.417 17.28 88.493 17.198 88.635C17.115 88.777 17.115 88.93 17.198 88.977" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="16.871" y1="92.894" x2="16.871" y2="89.78" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="16.871" y1="89.78" x2="15.419" y2="90.618" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M40.088 72.527C40.201 72.46 37.079 75.513 37.079 75.513L35.093 77.108" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="35.093" y1="77.108" x2="24.135" y2="83.433" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="24.135" y1="83.433" x2="21.128" y2="84.019" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="35.494" y1="77.421" x2="24.067" y2="84.019" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="24.067" y1="84.019" x2="23.995" y2="83.46" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="35.494" y1="77.421" x2="35.494" y2="76.804" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="35.722" y1="78.04" x2="35.494" y2="77.421" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M38.371 75.263C38.371 75.171 38.338 75.106 38.286 75.076C38.229 75.045 38.155 75.055 38.082 75.096" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M36.978 75.733C36.899 75.78 36.826 75.858 36.774 75.95C36.716 76.051 36.688 76.156 36.689 76.243" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M38.093 76.218C38.173 76.173 38.244 76.093 38.296 76.003C38.372 75.871 38.398 75.733 38.371 75.635" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M36.784 76.876C36.841 76.909 36.915 76.899 36.99 76.855" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M36.701 76.697C36.703 76.786 36.734 76.846 36.784 76.876" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="38.371" y1="75.263" x2="38.371" y2="75.727" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="36.689" y1="76.243" x2="36.701" y2="76.697" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="36.978" y1="75.733" x2="38.082" y2="75.096" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="38.093" y1="76.218" x2="36.99" y2="76.855" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M34.207 75.266C33.61 75.7 32.985 76.155 32.1 76.665" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M32.1 76.665C32.1 76.665 27.629 79.263 26.829 79.724C26.027 80.188 25.754 79.917 24.658 80.549C23.562 81.182 20.687 82.74 20.687 82.74L16.754 83.289" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M40.462 71.802C40.668 71.921 41.003 71.727 41.21 71.37C41.417 71.01 41.417 70.623 41.21 70.504C41.171 70.48 41.127 70.469 41.082 70.468" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M40.432 70.992C40.253 71.34 40.268 71.689 40.462 71.802" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M41.097 72.814C41.21 72.879 41.39 72.774 41.503 72.581C41.615 72.387 41.615 72.177 41.503 72.112C41.39 72.048 41.21 72.153 41.097 72.346C40.985 72.54 40.985 72.75 41.097 72.814" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="38.02" y1="75.195" x2="38.02" y2="75.879" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="38.02" y1="75.879" x2="37.487" y2="76.186" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="37.487" y1="76.186" x2="37.487" y2="76.537" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="37.176" y1="76.748" x2="37.176" y2="76.009" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="37.176" y1="76.009" x2="37.707" y2="75.703" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="37.707" y1="75.703" x2="37.707" y2="75.313" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M23.106 84.078C23.106 83.986 23.073 83.921 23.021 83.891C22.964 83.858 22.89 83.867 22.817 83.911" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M21.713 84.548C21.634 84.593 21.561 84.673 21.509 84.763C21.451 84.864 21.422 84.969 21.423 85.058" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M22.826 85.033C22.907 84.986 22.978 84.908 23.032 84.816C23.106 84.686 23.132 84.546 23.106 84.448" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M21.518 85.689C21.576 85.722 21.649 85.712 21.724 85.67" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M21.434 85.512C21.437 85.599 21.469 85.661 21.518 85.689" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="23.106" y1="84.078" x2="23.106" y2="84.542" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="21.423" y1="85.058" x2="21.434" y2="85.512" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="21.713" y1="84.548" x2="22.817" y2="83.911" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="22.826" y1="85.033" x2="21.724" y2="85.67" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="22.754" y1="84.009" x2="22.754" y2="84.692" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="22.754" y1="84.692" x2="22.222" y2="85" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="22.222" y1="85" x2="22.222" y2="85.352" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="21.91" y1="85.561" x2="21.91" y2="84.822" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="21.91" y1="84.822" x2="22.44" y2="84.516" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="22.44" y1="84.516" x2="22.44" y2="84.128" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="23.952" y1="84.097" x2="23.801" y2="84.834" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="23.801" y1="84.834" x2="20.841" y2="86.545" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="20.841" y1="86.545" x2="19.635" y2="86.549" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="19.635" y1="86.549" x2="17.393" y2="87.843" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="17.393" y1="87.843" x2="15.419" y2="89.512" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="20.841" y1="86.132" x2="19.635" y2="86.137" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="19.635" y1="86.137" x2="17.393" y2="87.43" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="17.393" y1="87.43" x2="15.419" y2="88.659" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M18.702 84.167C18.815 84.23 18.995 84.126 19.108 83.933C19.219 83.739 19.219 83.528 19.108 83.465C18.995 83.4 18.815 83.504 18.702 83.698C18.591 83.891 18.591 84.102 18.702 84.167" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M18.53 84.465C18.736 84.584 19.073 84.39 19.28 84.031C19.486 83.673 19.486 83.286 19.28 83.167C19.073 83.046 18.736 83.24 18.53 83.599C18.324 83.957 18.324 84.344 18.53 84.465" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M18.178 86.046C18.289 86.111 18.472 86.007 18.583 85.813C18.695 85.618 18.695 85.409 18.583 85.344C18.472 85.281 18.289 85.385 18.178 85.578C18.065 85.772 18.065 85.983 18.178 86.046" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M17.07 84.625C17.242 84.724 17.524 84.563 17.699 84.262C17.871 83.96 17.871 83.635 17.699 83.534C17.524 83.435 17.242 83.597 17.07 83.897C16.896 84.198 16.896 84.525 17.07 84.625" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M17.966 83.885C18.009 83.626 17.943 83.433 17.804 83.35C17.571 83.216 17.196 83.435 16.963 83.837C16.826 84.073 16.769 84.322 16.795 84.516" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="17.966" y1="83.885" x2="17.823" y2="84.748" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M17.07 86.816C17.242 86.917 17.524 86.754 17.699 86.453C17.786 86.302 17.829 86.146 17.829 86.015" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M16.939 86.528C16.939 86.67 16.989 86.769 17.07 86.816" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="17.829" y1="86.015" x2="17.829" y2="84.748" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="16.939" y1="86.528" x2="16.939" y2="85.233" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M17.181 86.623C17.292 86.686 17.475 86.582 17.586 86.388C17.699 86.194 17.699 85.984 17.586 85.921C17.475 85.855 17.292 85.96 17.181 86.155C17.07 86.349 17.07 86.558 17.181 86.623" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="16.795" y1="84.516" x2="16.939" y2="85.233" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M34.716 75.819C34.786 75.861 34.878 75.847 34.97 75.795" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M35.331 75.173C35.331 75.058 35.289 74.977 35.225 74.939C35.085 74.858 34.856 74.989 34.716 75.233C34.576 75.475 34.576 75.739 34.716 75.819" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M24.454 81.745C24.524 81.786 24.617 81.772 24.708 81.719" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M25.068 81.097C25.068 80.983 25.027 80.902 24.963 80.864C24.823 80.784 24.594 80.915 24.454 81.158C24.315 81.4 24.315 81.664 24.454 81.745" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M18.175 82.522C18.175 82.522 18.347 82.736 18.722 82.849C19.147 82.977 19.835 82.98 20.832 82.403C22.713 81.317 27.028 79.332 27.028 79.332" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="26.958" y1="79.364" x2="34.734" y2="74.875" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="18.722" y1="82.849" x2="17.137" y2="82.477" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M17.137 82.477C17.137 82.477 16.827 82.364 16.754 82.161" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M28.832 77.1L29.004 77.712C29.004 77.712 29.036 77.909 29.416 77.689" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="29.416" y1="77.689" x2="34.57" y2="74.686" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.57 74.686C34.57 74.686 34.746 74.403 34.235 74.325C33.725 74.246 33.75 74.233 33.75 74.233" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.408 67.608C42.396 67.615 43.058 68.003 43.058 68.003C43.058 68.003 43.304 68.176 43.107 68.29C42.935 68.39 42.789 68.427 42.619 68.43C42.191 68.442 41.612 68.378 41.612 68.378" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M42.993 68.132C42.993 68.099 42.97 68.069 42.933 68.049" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.642 68.049C42.601 68.072 42.582 68.102 42.582 68.132C42.582 68.197 42.673 68.251 42.787 68.251C42.902 68.251 42.993 68.197 42.993 68.132" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M35.904 72.829C36.11 73.036 36.384 73.331 36.384 73.331C36.384 73.331 36.527 73.591 36.29 73.728C36.118 73.828 35.954 73.808 35.786 73.783C35.36 73.718 34.802 73.555 34.802 73.555" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M36.21 73.518C36.21 73.484 36.186 73.454 36.149 73.433" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M35.856 73.433C35.817 73.456 35.795 73.486 35.795 73.518C35.795 73.584 35.889 73.638 36.003 73.638C36.118 73.638 36.21 73.584 36.21 73.518" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M27.814 77.688C28.007 77.9 28.219 78.144 28.219 78.144C28.219 78.144 28.362 78.403 28.126 78.54C27.954 78.64 27.789 78.62 27.62 78.594C27.196 78.531 26.638 78.367 26.638 78.367" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M28.045 78.329C28.045 78.296 28.021 78.266 27.984 78.245" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M27.692 78.245C27.652 78.269 27.632 78.299 27.632 78.329C27.632 78.396 27.724 78.45 27.838 78.45C27.952 78.45 28.045 78.396 28.045 78.329" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M19.882 81.778C20.057 82.004 20.246 82.266 20.246 82.266C20.246 82.266 20.367 82.537 20.12 82.653C19.939 82.737 19.777 82.703 19.611 82.664C19.353 82.602 18.987 82.46 18.745 82.361" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M20.057 82.436C20.06 82.403 20.039 82.371 20.004 82.347" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M19.713 82.322C19.67 82.341 19.647 82.37 19.644 82.4C19.64 82.466 19.727 82.528 19.841 82.537C19.954 82.548 20.051 82.503 20.057 82.436" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M30.03 77.227C30.036 77.234 30.099 77.206 30.173 77.164C30.248 77.121 30.303 77.079 30.298 77.072" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M32.808 75.623C32.814 75.631 32.878 75.602 32.951 75.56C33.026 75.518 33.082 75.475 33.078 75.468" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.734 74.875C34.734 74.875 38.734 72.379 40.817 70.686C41.975 69.745 42.978 68.807 43.64 68.156" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.011" y1="75.347" x2="33.078" y2="75.468" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.761" y1="75.536" x2="32.808" y2="75.623" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="29.986" y1="77.146" x2="30.03" y2="77.227" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="30.251" y1="76.984" x2="30.298" y2="77.072" />
      </g>
    </g>
    <g>
      <path stroke="none" fill="#FFFFFF"
        d="M35.487 93.632C33.398 90.912 35.383 90.37 32.844 88.093 32.622 87.894 32.073 88.117 32.293 88.316 34.832 90.593 32.905 91.406 34.936 93.855 35.095 94.048 35.715 93.929 35.487 93.632z" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M34.936 93.855C34.402 93.191 34.142 92.652 34.002 92.174" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M33.878 91.599C33.828 91.262 33.811 90.947 33.755 90.625" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M33.607 90.061C33.424 89.56 33.066 89.009 32.293 88.316" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M35.487 93.632C34.953 92.969 34.692 92.43 34.552 91.951" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M34.429 91.378C34.378 91.04 34.36 90.724 34.305 90.402" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M34.158 89.838C33.975 89.337 33.616 88.786 32.844 88.093" />
    </g>
    <g>
      <path stroke="none" fill="#FFFFFF"
        d="M24.844 83.659C23.128 80.929 24.612 80.712 23.547 78.525 23.3 78.271 22.722 78.484 22.878 78.802 23.964 81.021 22.437 81.5 24.17 83.879 24.346 84.12 25.089 84.031 24.844 83.659L24.844 83.659z" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M24.17 83.879C23.931 83.537 23.82 83.427 23.577 82.888" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M23.321 82.054C23.152 81.221 23.585 80.352 22.943 78.962" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M24.844 83.659C24.606 83.296 24.582 83.152 24.297 82.606" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M24.015 81.754C23.783 81.146 24.266 79.9 23.597 78.656" />
      <path stroke="none" fill="#FFFFFF"
        d="M27.522 90.48C25.263 87.069 27.67 86.591 24.839 83.671 24.594 83.417 23.92 83.635 24.167 83.89 26.996 86.811 24.638 87.615 26.849 90.698 27.022 90.941 27.768 90.85 27.522 90.48L27.522 90.48z" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M26.849 90.698C26.213 89.811 25.734 88.762 25.763 87.62" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M25.734 87.049C25.725 86.575 25.667 86.368 25.513 85.897" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M25.164 85.144C24.817 84.632 24.76 84.504 24.167 83.89" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M27.522 90.48C26.942 89.647 26.429 88.457 26.411 87.554" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M26.396 86.768C26.378 86.323 26.301 85.933 26.173 85.578" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M25.791 84.849C25.475 84.394 25.335 84.165 24.839 83.671" />
    </g>
    <g>
      <path stroke-width="0.12" stroke-linejoin="round" fill="#FFFFFF"
        d="M22.333 86.438C22.333 85.615 21.667 84.947 20.843 84.947 20.019 84.947 19.353 85.615 19.353 86.438 19.353 87.262 20.019 87.929 20.843 87.929 21.667 87.929 22.333 87.262 22.333 86.438L22.333 86.438 22.333 86.438z" />
      <path stroke="none" fill="#000000"
        d="M20.792 86.042L20.385 86.042 20.385 85.875C20.542 85.859 20.652 85.834 20.713 85.799 20.774 85.763 20.821 85.679 20.852 85.545L21.022 85.545 21.022 87.263 20.792 87.263 20.792 86.042 20.792 86.042z" />
    </g>
    <g>
      <path stroke-width="0.12" stroke-linejoin="round" fill="#FFFFFF"
        d="M31.344 92.272C31.344 91.45 30.675 90.781 29.853 90.781 29.03 90.781 28.362 91.45 28.362 92.272 28.362 93.096 29.03 93.763 29.853 93.763 30.675 93.763 31.344 93.096 31.344 92.272L31.344 92.272 31.344 92.272z" />
      <path stroke="none" fill="#000000"
        d="M29.8 91.876L29.394 91.876 29.394 91.709C29.551 91.694 29.661 91.668 29.722 91.632 29.785 91.596 29.83 91.513 29.861 91.379L30.033 91.379 30.033 93.096 29.8 93.096 29.8 91.876 29.8 91.876z" />
    </g>
    <g id="_DATID_068033" class="DATID_068033_V2L3St2Sz1004-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M61.375 78.781L78.661 78.781 78.661 83.629 61.375 83.629 61.375 78.781z" />
        <g>
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M70.543 80.356C70.543 80.183 70.35 80.034 70.075 79.98" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M69.629 79.98C69.344 80.036 69.165 80.188 69.162 80.356C69.162 80.576 69.471 80.756 69.852 80.756C70.234 80.756 70.543 80.576 70.543 80.356" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M69.629 80.356C69.629 80.427 69.73 80.486 69.852 80.486C69.975 80.486 70.075 80.427 70.075 80.356" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="70.075" y1="79.29" x2="70.075" y2="80.356" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.629" y1="79.29" x2="69.629" y2="80.356" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M70.075 79.29C70.075 79.219 69.975 79.162 69.852 79.162C69.73 79.162 69.629 79.219 69.629 79.29" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.965" y1="80.75" x2="69.965" y2="82.846" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.74" y1="82.846" x2="69.852" y2="83.271" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.965" y1="82.846" x2="69.852" y2="83.271" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.74" y1="80.75" x2="69.74" y2="82.846" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.965" y1="80.75" x2="69.965" y2="82.846" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M69.629 79.29C69.629 79.361 69.73 79.418 69.852 79.418C69.975 79.418 70.075 79.361 70.075 79.29" />
        </g>
        <g>
          <g>
            <polygon stroke-width="0.12" stroke-linejoin="round"
              points="63.018 79.825 62.823 79.632 62.436 79.632 62.243 79.825 62.436 80.019 62.823 80.019 63.018 79.825" />
          </g>
          <line stroke-width="0.12" stroke-linejoin="round" x1="61.923" y1="79.828" x2="61.923" y2="80.561" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="63.337" y1="79.828" x2="63.337" y2="80.561" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M61.923 80.561C61.923 80.786 62.24 80.968 62.631 80.968C63.021 80.968 63.338 80.786 63.338 80.561" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M63.338 79.828C63.338 79.602 63.021 79.42 62.631 79.42C62.24 79.42 61.923 79.602 61.923 79.828C61.923 80.054 62.24 80.236 62.631 80.236C63.021 80.236 63.338 80.054 63.338 79.828" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="62.192" y1="81.811" x2="62.192" y2="80.881" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="63.069" y1="81.811" x2="63.069" y2="80.881" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="63.069" y1="81.813" x2="63.069" y2="82.697" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="62.192" y1="81.811" x2="62.192" y2="82.697" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M62.192 82.697C62.192 82.837 62.389 82.95 62.631 82.95C62.872 82.95 63.069 82.837 63.069 82.697" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M62.192 82.43C62.192 82.569 62.389 82.682 62.631 82.682C62.872 82.682 63.069 82.569 63.069 82.43" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M62.192 82.161C62.192 82.299 62.389 82.414 62.631 82.414C62.872 82.414 63.069 82.299 63.069 82.161" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M62.192 81.891C62.192 82.031 62.389 82.144 62.631 82.144C62.872 82.144 63.069 82.031 63.069 81.891" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M62.192 81.623C62.192 81.763 62.389 81.876 62.631 81.876C62.872 81.876 63.069 81.763 63.069 81.623" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M62.192 81.353C62.192 81.493 62.389 81.606 62.631 81.606C62.872 81.606 63.069 81.493 63.069 81.353" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M62.192 81.084C62.192 81.224 62.389 81.337 62.631 81.337C62.872 81.337 63.069 81.224 63.069 81.084" />
        </g>
        <g>
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M68.105 80.582C68.105 80.459 67.93 80.358 67.715 80.358C67.498 80.358 67.323 80.459 67.323 80.582C67.323 80.707 67.498 80.808 67.715 80.808C67.93 80.808 68.105 80.707 68.105 80.582" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M69.161 81.385C69.161 81.028 68.774 80.724 68.218 80.602" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M67.212 80.602C66.632 80.727 66.272 81.043 66.268 81.385C66.268 81.846 66.915 82.221 67.715 82.221C68.513 82.221 69.161 81.846 69.161 81.385" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M66.691 81.385C66.691 81.712 67.149 81.977 67.715 81.977C68.28 81.977 68.739 81.712 68.739 81.385" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M66.365 81.688C66.364 82.117 66.965 82.469 67.709 82.474C68.454 82.478 69.061 82.134 69.063 81.703" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M66.691 81.385C66.691 81.039 67.248 80.551 67.368 80.477" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M68.739 81.385C68.739 81.039 68.18 80.551 68.06 80.477" />
        </g>
        <g>
          <path stroke-width="0.12" stroke-linejoin="round" d="M72.4 80.921C72.4 80.784 72.266 80.667 72.069 80.606" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M71.509 80.606C71.299 80.668 71.179 80.792 71.177 80.921C71.177 81.114 71.451 81.272 71.789 81.272C72.126 81.272 72.4 81.114 72.4 80.921" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M72.069 79.983C72.067 79.894 71.942 79.822 71.789 79.822C71.634 79.823 71.509 79.896 71.509 79.984C71.509 80.073 71.635 80.147 71.789 80.146C71.944 80.146 72.069 80.072 72.069 79.983" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="71.509" y1="79.984" x2="71.509" y2="80.814" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="72.069" y1="79.983" x2="72.069" y2="80.814" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M71.509 80.814C71.509 80.903 71.634 80.975 71.789 80.975C71.942 80.975 72.069 80.903 72.069 80.814" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M71.789 82.718C71.846 82.724 71.903 82.721 71.957 82.713C72.186 82.68 72.358 82.548 72.344 82.415" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="71.23" y1="82.433" x2="71.245" y2="81.698" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="71.593" y1="82.719" x2="71.617" y2="81.78" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M71.23 82.433C71.222 82.56 71.382 82.682 71.593 82.719" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="72.346" y1="82.438" x2="72.331" y2="81.698" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M71.396 80.65C71.004 80.743 70.766 80.962 70.763 81.197C70.763 81.524 71.222 81.787 71.789 81.787C72.355 81.787 72.813 81.524 72.813 81.197" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M72.813 81.197C72.813 80.951 72.555 80.742 72.182 80.65" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="71.789" y1="82.718" x2="71.766" y2="81.787" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M71.23 82.153C71.222 82.28 71.382 82.402 71.593 82.439" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M71.789 82.438C71.846 82.444 71.903 82.441 71.957 82.433C72.186 82.4 72.358 82.268 72.344 82.135" />
        </g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <polygon points="61.375 78.781 78.661 78.781 78.661 83.629 61.375 83.629 61.375 78.781" stroke-width="0.12" />
          <g>
            <polygon stroke-width="0.25" stroke-linejoin="round"
              points="61.375 78.781 78.661 78.781 78.661 83.629 61.375 83.629 61.375 78.781" />
          </g>
        </g>
        <g>
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M77.983 81.301C77.983 80.98 77.534 80.721 76.98 80.721C76.427 80.721 75.977 80.98 75.977 81.301C75.977 81.62 76.427 81.879 76.98 81.879C77.534 81.879 77.983 81.62 77.983 81.301" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M75.977 81.546C75.977 81.865 76.427 82.125 76.98 82.125C77.534 82.125 77.983 81.865 77.983 81.546" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="75.977" y1="81.301" x2="75.977" y2="81.546" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="77.983" y1="81.301" x2="77.983" y2="81.546" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M77.531 81.301C77.531 81.125 77.284 80.983 76.98 80.983C76.677 80.983 76.43 81.125 76.43 81.301C76.43 81.475 76.677 81.617 76.98 81.617C77.284 81.617 77.531 81.475 77.531 81.301" />
        </g>
        <g>
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M75.399 81.501C75.399 81.34 75.29 81.195 75.115 81.088" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M73.606 81.088C73.417 81.204 73.322 81.353 73.322 81.501C73.322 81.831 73.787 82.099 74.361 82.099C74.935 82.099 75.399 81.831 75.399 81.501" />
          <g>
            <polygon stroke-width="0.12" stroke-linejoin="round"
              points="75.114 80.953 74.737 80.576 73.984 80.576 73.608 80.953 73.984 81.331 74.737 81.331 75.114 80.953" />
          </g>
          <polyline stroke-width="0.12" stroke-linejoin="round"
            points="73.608 81.501 73.984 81.878 74.737 81.878 75.114 81.501" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="73.608" y1="80.953" x2="73.608" y2="81.501" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="73.984" y1="81.331" x2="73.984" y2="81.878" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="74.737" y1="81.331" x2="74.737" y2="81.878" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="75.114" y1="80.953" x2="75.114" y2="81.501" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M74.751 80.968C74.756 80.843 74.584 80.736 74.368 80.728C74.153 80.721 73.975 80.816 73.971 80.939C73.968 81.003 74.01 81.06 74.079 81.103" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M74.632 81.123C74.709 81.081 74.748 81.025 74.751 80.968" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="73.322" y1="81.501" x2="73.322" y2="81.745" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="75.399" y1="81.501" x2="75.399" y2="81.745" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M73.322 81.745C73.322 82.076 73.787 82.344 74.361 82.344C74.935 82.344 75.399 82.076 75.399 81.745" />
        </g>
        <g>
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M66.078 80.456C66.078 80.295 65.972 80.147 65.802 80.034" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M64.132 80.034C63.948 80.156 63.856 80.308 63.856 80.456C63.856 80.811 64.353 81.097 64.968 81.097C65.581 81.097 66.078 80.811 66.078 80.456" />
          <g>
            <polygon stroke-width="0.12" stroke-linejoin="round"
              points="65.802 79.852 65.385 79.435 64.549 79.435 64.132 79.852 64.549 80.269 65.385 80.269 65.802 79.852" />
          </g>
          <polyline stroke-width="0.12" stroke-linejoin="round"
            points="64.132 80.456 64.549 80.875 65.385 80.875 65.802 80.456" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="64.132" y1="79.852" x2="64.132" y2="80.456" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="64.549" y1="80.269" x2="64.549" y2="80.875" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="65.385" y1="80.269" x2="65.385" y2="80.875" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="65.802" y1="79.852" x2="65.802" y2="80.456" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="64.525" y1="81.865" x2="64.525" y2="81.045" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="65.409" y1="81.865" x2="65.409" y2="81.045" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="65.409" y1="81.867" x2="65.409" y2="82.76" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="64.525" y1="81.865" x2="64.525" y2="82.76" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M64.525 82.76C64.525 82.9 64.724 83.015 64.968 83.015C65.212 83.015 65.409 82.9 65.409 82.76" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M64.525 82.489C64.525 82.629 64.724 82.743 64.968 82.743C65.212 82.743 65.409 82.629 65.409 82.489" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M64.525 82.218C64.525 82.358 64.724 82.472 64.968 82.472C65.212 82.472 65.409 82.358 65.409 82.218" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M64.525 81.947C64.525 82.087 64.724 82.201 64.968 82.201C65.212 82.201 65.409 82.087 65.409 81.947" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M64.525 81.676C64.525 81.816 64.724 81.93 64.968 81.93C65.212 81.93 65.409 81.816 65.409 81.676" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M64.525 81.405C64.525 81.546 64.724 81.659 64.968 81.659C65.212 81.659 65.409 81.546 65.409 81.405" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M64.525 81.134C64.525 81.274 64.724 81.388 64.968 81.388C65.212 81.388 65.409 81.274 65.409 81.134" />
        </g>
      </g>
    </g>
    <g id="_DATID_079032" class="DATID_079032_V2L2St2Sz680-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M60.24 113.457C60.277 113.503 60.317 113.537 60.359 113.561L62.378 114.725C62.412 114.837 62.472 114.914 62.546 114.953 62.853 115.131 63.396 114.763 63.759 114.135 64.123 113.504 64.17 112.85 63.862 112.674L63.46 112.441C63.453 112.37 63.426 112.298 63.389 112.233 63.289 112.061 63.128 111.968 63.029 112.025L62.771 112.174 61.563 111.477C61.231 111.284 60.692 111.596 60.359 112.171 60.253 112.355 60.18 112.545 60.143 112.718 59.625 112.718 59.447 113.319 59.447 113.447 59.447 113.575 59.493 113.769 59.493 113.769L60.265 115.106C60.344 115.242 60.561 115.263 60.75 115.155 60.939 115.045 61.027 114.846 60.95 114.712L60.24 113.457z" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.793" y1="113.271" x2="62.793" y2="112.87" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M63.734 113.323C63.716 113.313 63.695 113.307 63.677 113.304" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M63.218 113.579C63.173 113.635 63.131 113.695 63.098 113.753C62.908 114.081 62.884 114.424 63.045 114.516C63.206 114.609 63.49 114.418 63.679 114.087C63.869 113.759 63.893 113.417 63.734 113.323" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.218" y1="113.579" x2="63.218" y2="113.141" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.218" y1="113.141" x2="63.677" y2="112.876" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.677" y1="112.876" x2="63.677" y2="113.304" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.377 113.206C62.356 113.239 62.335 113.272 62.319 113.302C61.985 113.878 61.985 114.5 62.319 114.691" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.377" y1="113.207" x2="62.284" y2="113.153" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.359 112.412C62.308 112.439 62.284 112.501 62.284 112.575" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.793 112.87C62.793 112.789 62.765 112.698 62.719 112.62C62.619 112.447 62.459 112.353 62.359 112.412" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.284" y1="112.575" x2="62.284" y2="113.153" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.463" y1="112.483" x2="63.463" y2="112.68" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.014 113.906C61.972 113.882 61.904 113.921 61.86 113.993C61.819 114.066 61.819 114.146 61.86 114.17C61.902 114.194 61.972 114.155 62.014 114.081C62.055 114.009 62.055 113.93 62.014 113.906" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M61.828 113.856C61.804 113.884 61.784 113.912 61.768 113.941C61.676 114.102 61.676 114.277 61.768 114.329C61.773 114.332 61.777 114.334 61.78 114.335" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="61.826" y1="113.858" x2="62.377" y2="113.207" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="61.78" y1="114.335" x2="62.139" y2="114.489" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M61.735 113.804C61.713 113.831 61.692 113.859 61.676 113.887C61.582 114.049 61.582 114.222 61.676 114.277" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="61.734" y1="113.805" x2="62.284" y2="113.153" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="61.768" y1="114.329" x2="61.676" y2="114.277" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.746 112.188C62.405 112.123 61.959 112.441 61.674 112.93C61.343 113.506 61.343 114.128 61.674 114.319" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M60.95 114.712C60.871 114.576 60.655 114.555 60.466 114.665C60.277 114.774 60.188 114.971 60.265 115.106" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="61.439" y1="113.935" x2="60.869" y2="113.605" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="61.792" y1="112.748" x2="61.307" y2="112.468" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.188" y1="112.352" x2="61.987" y2="112.236" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="61.713" y1="112.865" x2="61.963" y2="113.01" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="61.439" y1="113.59" x2="61.734" y2="113.762" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.133" y1="113.933" x2="63.744" y2="113.579" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.134" y1="114.118" x2="63.746" y2="113.766" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.359" y1="112.412" x2="62.771" y2="112.174" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M63.463 112.483C63.463 112.468 63.462 112.454 63.46 112.441" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M63.862 112.674C63.753 112.609 63.612 112.617 63.463 112.68" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.793 113.271C62.432 113.772 62.268 114.373 62.378 114.725" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M60.143 112.718C60.073 113.037 60.116 113.299 60.24 113.457" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.468 112.599C62.429 112.623 62.429 112.695 62.468 112.765C62.506 112.832 62.57 112.868 62.609 112.846C62.649 112.823 62.649 112.75 62.609 112.682C62.57 112.614 62.506 112.576 62.468 112.599" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M60.24 113.457C60.277 113.503 60.317 113.537 60.359 113.561C60.359 113.561 62.378 114.725 62.378 114.725C62.412 114.837 62.472 114.914 62.546 114.953C62.853 115.131 63.396 114.763 63.759 114.135C64.123 113.504 64.17 112.85 63.862 112.674C63.862 112.674 63.46 112.441 63.46 112.441C63.453 112.37 63.426 112.298 63.389 112.233C63.289 112.061 63.128 111.968 63.029 112.025C63.029 112.025 62.771 112.174 62.771 112.174C62.771 112.174 61.563 111.477 61.563 111.477C61.231 111.284 60.692 111.596 60.359 112.171C60.253 112.355 60.18 112.545 60.143 112.718C59.625 112.718 59.447 113.319 59.447 113.447C59.447 113.575 59.493 113.769 59.493 113.769C59.493 113.769 60.265 115.106 60.265 115.106C60.344 115.242 60.561 115.263 60.75 115.155C60.939 115.045 61.027 114.846 60.95 114.712C60.95 114.712 60.24 113.457 60.24 113.457"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M60.24 113.457C60.277 113.503 60.317 113.537 60.359 113.561L62.378 114.725C62.412 114.837 62.472 114.914 62.546 114.953C62.853 115.131 63.396 114.763 63.759 114.135C64.123 113.504 64.17 112.85 63.862 112.674L63.46 112.441C63.453 112.37 63.426 112.298 63.389 112.233C63.289 112.061 63.128 111.968 63.029 112.025L62.771 112.174L61.563 111.477C61.231 111.284 60.692 111.596 60.359 112.171C60.253 112.355 60.18 112.545 60.143 112.718C59.625 112.718 59.447 113.319 59.447 113.447C59.447 113.575 59.493 113.769 59.493 113.769L60.265 115.106C60.344 115.242 60.561 115.263 60.75 115.155C60.939 115.045 61.027 114.846 60.95 114.712L60.24 113.457" />
        </g>
      </g>
    </g>
    <g id="_DATID_023031" class="DATID_023031_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M10.596 57.567L10.804 57.682 10.803 57.679C10.82 57.668 10.837 57.658 10.855 57.648 12.35 56.784 13.173 56.6 13.173 56.6 14.801 56.17 15.74 56.232 18.062 57.573 21.617 59.625 23.693 63.511 24.794 66.331 25.548 68.262 24.939 69.647 24.939 69.647 24.757 70.349 24.027 70.734 24.027 70.734L21.916 71.953 21.126 69.759 20.344 70.036C19.361 67.209 17.236 62.633 13.268 60.341 12.971 60.17 12.689 60.021 12.416 59.892 12.278 60.146 12.182 60.378 12.182 60.378L10.537 61.651 10.125 62.473 8.928 63.185 8.599 64.099 7.301 64.849 7.301 62.521C7.301 62.521 7.572 60.71 8.66 59.563 8.66 59.563 9.06 59.134 9.567 58.665L9.558 58.167C9.557 58.094 9.753 57.923 10.021 57.768 10.31 57.601 10.573 57.517 10.596 57.567z" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M12.416 59.892C10.551 59 9.18 59.033 8.66 59.563" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M21.649 69.281C21.155 67.856 20.368 65.986 19.202 64.188" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M18.66 63.396C18.126 62.662 17.525 61.954 16.85 61.308" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M16.577 61.055C16.06 60.588 15.502 60.159 14.899 59.783" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M13.763 59.158C13.75 59.151 13.737 59.143 13.723 59.137" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M13.723 59.137C12.993 58.789 12.346 58.536 11.781 58.39" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M11.445 58.314C11.157 58.259 10.894 58.235 10.656 58.244" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M19.062 63.142C18.532 62.412 17.934 61.71 17.263 61.069" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M16.992 60.817C16.477 60.351 15.919 59.923 15.32 59.548" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M13.964 58.817C13.311 58.515 12.711 58.298 12.178 58.161" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M11.845 58.085C11.516 58.018 11.22 57.984 10.96 57.987" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M13.756 57.524C13.647 57.49 13.542 57.46 13.439 57.433" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M13.102 57.357C12.381 57.219 11.477 57.305 11.006 57.561" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M18.65 59.859C18.513 59.735 18.373 59.615 18.23 59.496" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M14.156 57.293C14.047 57.261 13.941 57.231 13.836 57.204" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M13.502 57.127C12.74 56.972 12.163 56.973 11.778 57.155" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M18.236 60.1C18.099 59.975 17.958 59.853 17.815 59.735" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="16.561" y1="61.066" x2="16.388" y2="61.795" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="16.992" y1="60.817" x2="18.236" y2="60.098" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="18.65" y1="59.859" x2="19.176" y2="59.555" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="16.849" y1="61.307" x2="16.676" y2="62.037" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="17.263" y1="61.069" x2="18.922" y2="60.112" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="18.922" y1="60.112" x2="19.464" y2="59.798" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="11.446" y1="58.314" x2="11.275" y2="59.045" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="11.846" y1="58.085" x2="13.102" y2="57.359" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="13.503" y1="57.128" x2="14.063" y2="56.804" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="11.782" y1="58.39" x2="11.611" y2="59.121" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="12.179" y1="58.161" x2="13.439" y2="57.433" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="13.837" y1="57.204" x2="14.399" y2="56.881" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M14.063 56.804C14.063 56.804 14.183 56.726 14.297 56.765C14.409 56.801 14.399 56.881 14.399 56.881" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M19.176 59.555C19.176 59.555 19.24 59.503 19.371 59.579C19.504 59.655 19.518 59.767 19.464 59.798" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M8.928 63.185L9.714 60.121" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="9.714" y1="60.121" x2="11.025" y2="59.365" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M7.331 62.356C7.575 62.302 7.671 62.281 7.671 62.281L7.671 64.611" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M8.166 64.349L8.31 63.871C8.31 63.871 8.534 63.75 8.772 63.618" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M15.4 59.335C15.4 59.276 15.381 59.219 15.349 59.17" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M13.763 59.158C13.724 59.216 13.705 59.279 13.705 59.335C13.705 59.606 14.085 59.825 14.553 59.825C15.021 59.825 15.4 59.606 15.4 59.335" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M15.151 58.914C15.151 58.722 14.881 58.567 14.548 58.567C14.214 58.567 13.943 58.722 13.943 58.914C13.943 59.012 14.01 59.098 14.121 59.161" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M14.974 59.161C15.092 59.094 15.151 59.005 15.151 58.914" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="15.115" y1="58.796" x2="15.349" y2="59.17" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="13.753" y1="59.176" x2="13.977" y2="58.801" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M14.813 58.914C14.813 58.872 14.784 58.835 14.736 58.807" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M14.359 58.807C14.307 58.837 14.282 58.875 14.282 58.914C14.282 59 14.4 59.069 14.548 59.069C14.695 59.069 14.813 59 14.813 58.914" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M11.034 57.819C11.028 57.811 11.022 57.804 11.013 57.799" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M9.567 58.665C9.619 58.758 9.992 58.643 10.397 58.408C10.801 58.174 11.086 57.911 11.034 57.819" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M10.316 57.747C10.307 57.732 10.274 57.734 10.227 57.749" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M9.901 57.936C9.862 57.973 9.848 58 9.855 58.012C9.871 58.042 9.989 58.006 10.115 57.932C10.243 57.859 10.333 57.777 10.316 57.747" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M22.78 71.454C22.78 71.454 22.722 70.957 22.222 69.519" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M22.08 71.378C22.114 71.397 22.158 71.391 22.204 71.365" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M22.378 71.063C22.378 71.009 22.358 70.969 22.327 70.951C22.259 70.912 22.149 70.975 22.08 71.093C22.013 71.21 22.013 71.338 22.08 71.378" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="22.222" y1="69.519" x2="21.211" y2="69.528" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M9.558 58.167C9.56 58.213 9.638 58.212 9.76 58.173" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M10.5 57.746C10.59 57.664 10.625 57.606 10.609 57.578" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="10.804" y1="57.682" x2="11.013" y2="57.799" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M10.596 57.567C10.596 57.567 10.804 57.682 10.804 57.682C10.804 57.682 10.803 57.679 10.803 57.679C10.82 57.668 10.837 57.658 10.855 57.648C12.35 56.784 13.173 56.6 13.173 56.6C14.801 56.17 15.74 56.232 18.062 57.573C21.617 59.625 23.693 63.511 24.794 66.331C25.548 68.262 24.939 69.647 24.939 69.647C24.757 70.349 24.027 70.734 24.027 70.734C24.027 70.734 21.916 71.953 21.916 71.953C21.916 71.953 21.126 69.759 21.126 69.759C21.126 69.759 20.344 70.036 20.344 70.036C19.361 67.209 17.236 62.633 13.268 60.341C12.971 60.17 12.689 60.021 12.416 59.892C12.278 60.146 12.182 60.378 12.182 60.378C12.182 60.378 10.537 61.651 10.537 61.651C10.537 61.651 10.125 62.473 10.125 62.473C10.125 62.473 8.928 63.185 8.928 63.185C8.928 63.185 8.599 64.099 8.599 64.099C8.599 64.099 7.301 64.849 7.301 64.849C7.301 64.849 7.301 62.521 7.301 62.521C7.301 62.521 7.572 60.71 8.66 59.563C8.66 59.563 9.06 59.134 9.567 58.665C9.567 58.665 9.558 58.167 9.558 58.167C9.557 58.094 9.753 57.923 10.021 57.768C10.31 57.601 10.573 57.517 10.596 57.567"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M10.596 57.567L10.804 57.682L10.803 57.679C10.82 57.668 10.837 57.658 10.855 57.648C12.35 56.784 13.173 56.6 13.173 56.6C14.801 56.17 15.74 56.232 18.062 57.573C21.617 59.625 23.693 63.511 24.794 66.331C25.548 68.262 24.939 69.647 24.939 69.647C24.757 70.349 24.027 70.734 24.027 70.734L21.916 71.953L21.126 69.759L20.344 70.036C19.361 67.209 17.236 62.633 13.268 60.341C12.971 60.17 12.689 60.021 12.416 59.892C12.278 60.146 12.182 60.378 12.182 60.378L10.537 61.651L10.125 62.473L8.928 63.185L8.599 64.099L7.301 64.849L7.301 62.521C7.301 62.521 7.572 60.71 8.66 59.563C8.66 59.563 9.06 59.134 9.567 58.665L9.558 58.167C9.557 58.094 9.753 57.923 10.021 57.768C10.31 57.601 10.573 57.517 10.596 57.567" />
        </g>
        <line stroke-width="0.12" stroke-linejoin="round" x1="16.676" y1="62.037" x2="16.388" y2="61.795" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="11.611" y1="59.121" x2="11.275" y2="59.045" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="13.756" y1="57.524" x2="14.156" y2="57.293" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M17.815 59.735C17.815 59.735 17.664 59.524 17.846 59.42C18.027 59.316 18.23 59.496 18.23 59.496" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M11.446 58.314C11.446 58.314 11.577 58.104 11.644 58.143C11.714 58.183 11.782 58.39 11.782 58.39" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="11.644" y1="58.143" x2="11.846" y2="58.085" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M11.782 58.39C11.782 58.39 11.787 58.234 11.926 58.154C12.064 58.073 12.179 58.161 12.179 58.161" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M16.561 61.066C16.561 61.066 16.64 60.866 16.76 60.936C16.879 61.005 16.849 61.307 16.849 61.307" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="16.76" y1="60.936" x2="16.992" y2="60.817" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M16.849 61.307C16.849 61.307 16.911 60.799 17.263 61.069" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M18.236 60.098C18.236 60.098 18.475 60.097 18.603 60.024C18.731 59.95 18.65 59.859 18.65 59.859" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M13.102 57.359C13.102 57.359 13.343 57.356 13.47 57.283C13.598 57.21 13.503 57.128 13.503 57.128" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M13.439 57.433C13.439 57.433 13.467 57.32 13.561 57.265C13.657 57.21 13.837 57.204 13.837 57.204" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M19.062 63.142C19.062 63.142 19.343 63.39 19.099 63.53C18.855 63.671 18.658 63.395 18.658 63.395" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M24.397 70.088C23.902 68.662 22.765 63.606 19.585 60.164" />
      </g>
    </g>
    <g id="_DATID_002030" class="DATID_002030_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M7.884 2.61L16.292 2.61 16.292 7.464 7.884 7.464 7.884 2.61z" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <polygon points="7.884 2.61 16.292 2.61 16.292 7.464 7.884 7.464 7.884 2.61" stroke-width="0.12" />
          <g>
            <polygon stroke-width="0.25" stroke-linejoin="round"
              points="7.884 2.61 16.292 2.61 16.292 7.464 7.884 7.464 7.884 2.61" />
          </g>
        </g>
        <g>
          <path stroke-width="0.12" stroke-linejoin="round" d="M11.71 6.371C11.71 6.371 11.798 6.466 11.917 6.557" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M11.513 6.072C11.513 6.072 11.715 6.402 11.71 6.371" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M11.532 5.993C11.532 5.993 11.787 5.924 12.224 5.769" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M12.326 5.726C12.326 5.726 12.554 5.844 12.794 6.424" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M11.532 5.993C11.532 5.993 11.492 6.019 11.513 6.072" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M11.917 6.557C11.917 6.557 12.178 6.817 12.813 6.615" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M10.992 4.591C10.992 4.591 11.228 4.806 11.401 4.955" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M12.794 6.424C12.794 6.424 12.867 6.58 12.964 6.557" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="9.986" y1="3.383" x2="10.992" y2="4.591" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M9.714 3.527C9.714 3.527 10 3.902 10.201 4.492" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M10.201 4.492C10.201 4.492 10.279 4.703 10.307 4.844" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="10.284" y1="5.152" x2="9.163" y2="4.543" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="8.599" y1="6.758" x2="9.326" y2="7.176" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M11.166 4.673C11.166 4.673 11.949 4.767 13.122 4.033" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M15.25 3.849C15.25 3.849 15.516 4.213 15.56 4.801" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M15.234 4.354C15.234 4.354 15.204 5.027 15.127 5.222" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M15.071 5.413C15.071 5.413 15.122 5.267 15.127 5.222" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M15.09 5.418C15.09 5.418 15.618 5.035 15.551 4.716" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M14.083 2.879C14.137 2.93 14.189 2.98 14.239 3.028C14.239 3.028 15.192 3.802 15.25 3.849" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M11.401 4.955C11.401 4.955 12.201 5.602 12.326 5.726" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M11.385 7.104C11.294 6.775 11.166 6.512 11.003 6.272" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M9.326 7.176C9.326 7.176 9.448 7.206 9.442 7.096" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M13.122 4.033C13.448 3.833 14.307 3.277 14.108 2.903" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M12.326 5.726C12.326 5.726 13.012 5.496 14.028 4.929C15.092 4.278 15.217 4.006 15.217 4.006" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M12.774 6.626C12.774 6.626 13.458 6.397 14.475 5.83C14.73 5.669 14.932 5.533 15.09 5.418" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M9.319 6.049C9.291 6.134 9.246 6.264 9.247 6.434" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M15.275 3.887C15.275 3.887 15.298 4.027 15.234 4.354" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M9.406 6.936C9.406 6.936 9.869 6.807 10.096 6.506" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M10.332 5.663C10.257 5.631 10.179 5.604 10.092 5.58" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M9.475 2.879C9.638 3.008 9.82 3.177 9.986 3.383" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M9.125 2.879C9.125 2.879 9.551 3.243 9.721 3.532" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M9.865 3.242C9.865 3.242 10.25 3.161 10.875 2.879" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M10.092 5.58C9.931 5.538 9.762 5.569 9.632 5.644C9.503 5.719 9.371 5.889 9.319 6.049" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M10.307 4.844C10.307 4.844 10.337 4.908 10.367 5.06C10.378 5.129 10.335 5.237 10.262 5.277C10.196 5.312 10.118 5.371 10.067 5.395C10.067 5.395 9.97 5.463 9.843 5.569" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="9.247" y1="6.434" x2="8.614" y2="6.088" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M11.141 7.15C11.141 7.15 10.538 6.528 10.096 6.506" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M11.141 7.15C11.092 6.99 11.022 6.812 10.958 6.7C10.74 6.323 10.307 6.003 10.032 5.945C9.891 5.916 9.759 5.934 9.647 5.998C9.537 6.062 9.455 6.168 9.412 6.304C9.377 6.411 9.348 6.754 9.442 7.096" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="11.385" y1="7.104" x2="11.141" y2="7.15" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M11.385 7.104C11.643 7.001 12 6.788 12.028 6.626" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="11.512" y1="7.048" x2="11.949" y2="7.224" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="12.367" y1="7.07" x2="12.355" y2="6.687" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M12.367 7.07C12.384 7.121 12.723 7.152 12.843 7.115" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M15.336 5.198C15.303 5.373 15.307 5.689 15.198 5.87C15.087 6.049 13.194 7.057 12.843 7.115" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M13.425 6.56C13.39 6.54 13.333 6.572 13.298 6.633C13.263 6.692 13.263 6.758 13.298 6.778C13.316 6.788 13.339 6.785 13.362 6.772" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M13.451 6.618C13.451 6.589 13.441 6.569 13.425 6.56" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M14.756 5.859C14.721 5.839 14.663 5.871 14.63 5.932C14.595 5.992 14.595 6.057 14.63 6.076C14.647 6.086 14.669 6.083 14.692 6.07" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M14.782 5.916C14.782 5.887 14.772 5.868 14.756 5.859" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="12.367" y1="7.07" x2="11.949" y2="7.224" />
        </g>
      </g>
    </g>
    <g id="_DATID_044029" class="DATID_044029_V2L2St2Sz680-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M70.254 1.201L78.661 1.201 78.661 6.054 70.254 6.054 70.254 1.201z" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <polygon points="70.254 1.201 78.661 1.201 78.661 6.054 70.254 6.054 70.254 1.201" stroke-width="0.12" />
          <g>
            <polygon stroke-width="0.25" stroke-linejoin="round"
              points="70.254 1.201 78.661 1.201 78.661 6.054 70.254 6.054 70.254 1.201" />
          </g>
        </g>
        <g>
          <g>
            <path stroke-width="0.12" stroke-linejoin="round"
              d="M71.016 2.201C70.995 2.278 70.986 2.363 70.989 2.444C70.995 2.698 71.096 2.9 71.215 2.898C71.332 2.895 71.424 2.687 71.418 2.435C71.415 2.333 71.397 2.238 71.37 2.164" />
            <path stroke-width="0.12" stroke-linejoin="round" d="M71.37 2.164C71.313 2.028 70.978 1.453 71.415 1.635" />
            <path stroke-width="0.12" stroke-linejoin="round"
              d="M71.415 1.635C71.234 1.581 71.201 1.588 71.183 1.702C71.147 1.923 71.033 2.115 71.016 2.201" />
          </g>
          <line stroke-width="0.12" stroke-linejoin="round" x1="73.561" y1="2.57" x2="71.834" y2="1.694" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M71.834 1.694C71.728 1.644 71.68 1.647 71.641 1.712" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="73.344" y1="2.944" x2="71.722" y2="1.887" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M71.722 1.887C71.626 1.821 71.603 1.777 71.641 1.712" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M74.194 2.31C73.986 2.19 73.647 2.386 73.438 2.748C73.227 3.111 73.227 3.501 73.436 3.623" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="76.918" y1="5.633" x2="73.436" y2="3.623" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="77.673" y1="4.318" x2="74.194" y2="2.31" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M76.918 5.633C77.128 5.753 77.466 5.557 77.677 5.195C77.885 4.833 77.885 4.442 77.677 4.322C77.466 4.2 77.128 4.397 76.918 4.759C76.709 5.121 76.709 5.512 76.918 5.633" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M77.118 4C76.909 3.879 76.57 4.075 76.361 4.437C76.153 4.799 76.153 5.19 76.361 5.31" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M74.612 2.552C74.403 2.431 74.063 2.628 73.855 2.99C73.646 3.351 73.646 3.743 73.855 3.863" />
        </g>
      </g>
    </g>
    <g id="_DATID_004028" class="DATID_004028_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M7.884 8.557L16.292 8.557 16.292 13.412 7.884 13.412 7.884 8.557z" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <polygon points="7.884 8.557 16.291 8.557 16.291 13.412 7.884 13.412 7.884 8.557" stroke-width="0.12" />
          <g>
            <polygon stroke-width="0.25" stroke-linejoin="round"
              points="7.884 8.557 16.291 8.557 16.291 13.412 7.884 13.412 7.884 8.557" />
          </g>
        </g>
        <g>
          <g>
            <path stroke-width="0.12" stroke-linejoin="round"
              d="M8.647 9.56C8.627 9.637 8.618 9.721 8.619 9.803C8.625 10.055 8.727 10.257 8.845 10.255C8.964 10.253 9.055 10.045 9.05 9.792C9.047 9.689 9.028 9.596 9 9.521" />
            <path stroke-width="0.12" stroke-linejoin="round" d="M9 9.521C8.943 9.385 8.609 8.811 9.045 8.992" />
            <path stroke-width="0.12" stroke-linejoin="round"
              d="M9.045 8.992C8.865 8.939 8.833 8.945 8.814 9.058C8.778 9.281 8.663 9.473 8.647 9.56" />
          </g>
          <line stroke-width="0.12" stroke-linejoin="round" x1="11.192" y1="9.928" x2="9.465" y2="9.051" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M9.465 9.051C9.359 9 9.31 9.003 9.272 9.069" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="10.975" y1="10.301" x2="9.353" y2="9.246" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M9.353 9.246C9.256 9.179 9.234 9.134 9.272 9.069" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M11.826 9.669C11.617 9.548 11.276 9.743 11.067 10.105C10.859 10.468 10.859 10.859 11.067 10.98" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="14.55" y1="12.99" x2="11.067" y2="10.98" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="15.304" y1="11.676" x2="11.826" y2="9.669" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M14.55 12.99C14.759 13.111 15.098 12.916 15.307 12.553C15.516 12.191 15.516 11.8 15.307 11.679C15.098 11.557 14.759 11.753 14.55 12.115C14.34 12.477 14.34 12.869 14.55 12.99" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M14.75 11.356C14.541 11.237 14.201 11.432 13.993 11.794C13.784 12.157 13.784 12.548 13.993 12.669" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M12.243 9.91C12.034 9.789 11.695 9.984 11.486 10.346C11.276 10.71 11.276 11.101 11.486 11.221" />
        </g>
      </g>
    </g>
    <g id="_DATID_021027" class="DATID_021027_V2L2St2Sz680-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M50.137 68.457C50.503 68.652 50.84 68.623 51.298 68.671 52.021 68.748 52.441 68.519 53.774 68.328 55.109 68.138 56.031 67.739 56.613 67.403 57.195 67.067 57.548 66.727 58.234 66.213 58.92 65.698 59.11 65.355 59.186 65.051 59.262 64.745 59.206 64.289 59.091 64.022 58.978 63.755 58.825 63.497 58.444 62.774 58.304 62.511 58.161 62.343 57.96 62.235L57.652 62.057C57.456 61.939 57.204 61.884 56.84 61.862 55.781 61.25 55.7 61.039 55.716 60.709 55.732 60.378 55.633 60.311 55.435 60.362 55.237 60.411 55.154 60.493 54.972 60.643 54.789 60.792 54.456 60.631 54.045 60.395 53.648 60.164 53.401 59.932 53.17 60.015 52.822 59.932 52.093 59.796 51.085 60.378 50.377 60.786 50.006 61.207 50.006 61.341 49.161 61.173 48.268 60.968 47.871 60.814 47.185 60.546 46.689 60.432 46.536 60.851 46.384 61.271 46.384 61.462 47.031 63.139 47.554 64.643 48.347 66.75 49.07 67.626 49.305 67.909 49.508 68.09 49.7 68.206 49.71 68.212 49.719 68.218 49.719 68.218 49.719 68.218 50.078 68.426 50.137 68.457z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M47.39 63.345C46.743 61.668 46.743 61.478 46.896 61.058C47.048 60.64 47.542 60.753 48.228 61.021C48.915 61.287 51.088 61.707 51.926 61.82C52.765 61.935 54.518 62.049 56.081 62.049C56.746 62.049 57.204 62.049 57.54 62.106" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M49.6 68.028C49.545 67.969 49.488 67.905 49.429 67.834C48.706 66.956 47.914 64.849 47.39 63.345" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M58.673 63.774C58.5 64.346 58.176 65.108 57.338 65.795C56.5 66.48 55.546 66.746 54.118 67.09C52.689 67.433 52.441 67.605 51.926 67.167C51.411 66.727 50.915 66.022 50.896 65.051C50.878 64.078 51.125 63.203 52.079 62.973C53.031 62.744 55.414 62.43 56.442 62.25" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round"
          d="M50.935 63.43C49.887 62.764 48.686 62.307 47.6 61.582" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M50.935 63.43C49.887 62.764 48.686 62.307 47.6 61.582" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M52.975 65.046C52.898 64.361 53.387 64.165 54.231 64.112C54.994 64.066 56.061 63.847 56.786 63.731C57.509 63.618 58.215 63.607 58.292 64.313C58.341 64.769 57.777 64.914 56.576 65.114C55.284 65.328 54.823 65.59 54.06 65.704C53.298 65.819 53.127 65.867 52.975 65.046" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M50.28 61.395C50.795 61.493 51.267 61.573 51.567 61.613C52.405 61.728 54.159 61.843 55.722 61.843C56.036 61.843 56.302 61.843 56.533 61.848" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M54.822 61.831L53.243 60.118" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M53.055 60.49C52.859 60.442 52.511 60.375 52.039 60.5C51.826 60.557 51.588 60.652 51.329 60.801C50.905 61.046 50.618 61.281 50.5 61.435" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" d="M49.043 63.615L53.363 66.109" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M49.043 63.615L53.363 66.109" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" d="M50.432 66.591L51.136 66.998" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M50.432 66.591L51.136 66.998" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" d="M54.898 64.441L51.398 62.42" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M54.898 64.441L51.398 62.42" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" d="M56.484 66.691L53.771 65.125" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M56.484 66.691L53.771 65.125" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" d="M56.649 63.515L57.606 64.067" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M56.649 63.515L57.606 64.067" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M50.137 68.457C50.503 68.652 50.84 68.623 51.298 68.671C52.021 68.748 52.441 68.519 53.774 68.328C55.109 68.138 56.031 67.739 56.613 67.403C57.195 67.067 57.548 66.727 58.234 66.213C58.92 65.698 59.11 65.355 59.186 65.051C59.262 64.745 59.206 64.289 59.091 64.022C58.978 63.755 58.825 63.497 58.444 62.774C58.304 62.511 58.161 62.343 57.96 62.235C57.96 62.235 57.652 62.057 57.652 62.057C57.456 61.939 57.204 61.884 56.84 61.862C55.781 61.25 55.7 61.039 55.716 60.709C55.732 60.378 55.634 60.311 55.435 60.362C55.237 60.411 55.154 60.493 54.972 60.643C54.789 60.792 54.456 60.631 54.045 60.395C53.648 60.164 53.401 59.932 53.17 60.015C52.822 59.932 52.093 59.796 51.085 60.378C50.377 60.786 50.006 61.207 50.006 61.341C49.161 61.173 48.268 60.968 47.871 60.814C47.185 60.546 46.689 60.432 46.536 60.851C46.384 61.271 46.384 61.462 47.031 63.139C47.554 64.643 48.347 66.75 49.07 67.626C49.305 67.909 49.508 68.09 49.7 68.206C49.71 68.212 49.719 68.218 49.719 68.218C49.719 68.218 50.078 68.426 50.137 68.457"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M50.137 68.457C50.503 68.652 50.84 68.623 51.298 68.671C52.021 68.748 52.441 68.519 53.774 68.328C55.109 68.138 56.031 67.739 56.613 67.403C57.195 67.067 57.548 66.727 58.234 66.213C58.92 65.698 59.11 65.355 59.186 65.051C59.262 64.745 59.206 64.289 59.091 64.022C58.978 63.755 58.825 63.497 58.444 62.774C58.304 62.511 58.161 62.343 57.96 62.235L57.652 62.057C57.456 61.939 57.204 61.884 56.84 61.862C55.781 61.25 55.7 61.039 55.716 60.709C55.732 60.378 55.634 60.311 55.435 60.362C55.237 60.411 55.154 60.493 54.972 60.643C54.789 60.792 54.456 60.631 54.045 60.395C53.648 60.164 53.401 59.932 53.17 60.015C52.822 59.932 52.093 59.796 51.085 60.378C50.377 60.786 50.006 61.207 50.006 61.341C49.161 61.173 48.268 60.968 47.871 60.814C47.185 60.546 46.689 60.432 46.536 60.851C46.384 61.271 46.384 61.462 47.031 63.139C47.554 64.643 48.347 66.75 49.07 67.626C49.305 67.909 49.508 68.09 49.7 68.206C49.71 68.212 49.719 68.218 49.719 68.218C49.719 68.218 50.078 68.426 50.137 68.457" />
        </g>
      </g>
    </g>
    <g id="_DATID_062026" class="DATID_062026_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M53.021 59.292C53.021 59.384 53.015 59.442 53.079 59.512 53.204 59.712 53.436 59.844 53.698 59.994 53.698 60.289 53.731 60.381 53.902 60.478 54.073 60.578 54.1 60.514 54.347 60.369L54.625 60.53C54.962 60.725 55.146 60.749 55.435 60.768 56.161 60.808 56.944 60.853 57.704 60.853 58.423 60.853 58.883 60.845 59.207 60.926 59.298 60.948 59.375 60.978 59.441 61.024 59.542 61.095 59.615 61.204 59.692 61.35 59.692 61.35 59.822 61.591 59.896 61.732 59.801 61.787 59.713 61.777 59.598 61.71 59.484 61.645 59.357 61.715 59.234 61.786 59.112 61.856 59.088 61.95 59.375 62.115 59.664 62.283 59.737 62.456 60.154 62.215 60.228 62.354 60.28 62.46 60.326 62.567 60.351 62.627 60.374 62.698 60.39 62.777 59.807 63.112 59.895 63.252 60.161 63.405 60.427 63.56 60.645 63.643 60.768 63.572 61.271 63.28 61.216 63.018 61.133 61.859 61.049 60.701 59.609 59.741 59.198 59.503 58.786 59.265 56.966 58.478 56.594 58.478 56.222 58.478 56.04 58.509 55.643 58.28 55.243 58.049 55.171 58.313 55.171 58.445 55.171 58.576 55.063 58.648 54.899 58.743 54.734 58.838 54.542 58.859 54.262 58.585 53.981 58.313 53.85 58.337 53.476 58.552 53.103 58.768 53.021 58.776 53.021 58.875 53.021 58.902 53.021 58.926 53.021 58.948 53.021 58.948 53.021 59.281 53.021 59.292z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M55.435 60.362C56.152 60.402 56.951 60.429 57.704 60.429C59.268 60.429 59.686 60.429 60.069 61.152C60.45 61.877 60.601 62.134 60.716 62.401C60.831 62.667 60.887 63.125 60.811 63.429C60.799 63.475 60.786 63.523 60.768 63.572" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M55.435 60.362C55.146 60.343 54.962 60.317 54.625 60.124" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M54.625 60.124L53.698 59.588" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M53.698 59.588C53.436 59.436 53.204 59.304 53.079 59.106" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M54.667 59.942L56.892 58.656" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M59.463 60.387C58.993 60.116 58.073 59.429 57.643 59.429" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M59.932 61.799C59.99 61.908 60.008 61.941 60.008 61.941C60.042 62.006 60.075 62.067 60.103 62.121" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M60.417 62.948C60.43 63.082 60.427 63.218 60.399 63.326" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M54.232 60.304L53.826 60.069" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M53.698 59.994L53.698 59.994" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M53.021 59.292C53.021 59.384 53.015 59.442 53.079 59.512C53.204 59.712 53.436 59.844 53.698 59.994C53.698 60.289 53.731 60.381 53.902 60.478C54.073 60.578 54.1 60.514 54.347 60.369C54.347 60.369 54.625 60.53 54.625 60.53C54.962 60.725 55.146 60.749 55.435 60.768C56.161 60.808 56.944 60.853 57.704 60.853C58.423 60.853 58.883 60.845 59.207 60.926C59.298 60.948 59.375 60.978 59.441 61.024C59.542 61.095 59.615 61.204 59.692 61.35C59.692 61.35 59.822 61.591 59.896 61.732C59.801 61.787 59.713 61.777 59.598 61.71C59.484 61.645 59.357 61.715 59.234 61.786C59.112 61.856 59.088 61.95 59.375 62.115C59.664 62.283 59.737 62.456 60.154 62.215C60.228 62.354 60.28 62.46 60.326 62.567C60.351 62.627 60.374 62.698 60.39 62.777C59.807 63.112 59.895 63.252 60.161 63.405C60.427 63.56 60.645 63.643 60.768 63.572C61.271 63.28 61.216 63.018 61.133 61.859C61.049 60.701 59.609 59.741 59.198 59.503C58.786 59.265 56.966 58.478 56.594 58.478C56.222 58.478 56.04 58.509 55.643 58.28C55.243 58.049 55.171 58.313 55.171 58.445C55.171 58.576 55.063 58.648 54.899 58.743C54.734 58.838 54.542 58.859 54.262 58.585C53.981 58.313 53.85 58.337 53.476 58.552C53.103 58.768 53.021 58.776 53.021 58.875C53.021 58.902 53.021 58.926 53.021 58.948C53.021 58.948 53.021 59.281 53.021 59.292"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M53.021 59.292C53.021 59.384 53.015 59.442 53.079 59.512C53.204 59.712 53.436 59.844 53.698 59.994C53.698 60.289 53.731 60.381 53.902 60.478C54.073 60.578 54.1 60.514 54.347 60.369L54.625 60.53C54.962 60.725 55.146 60.749 55.435 60.768C56.161 60.808 56.944 60.853 57.704 60.853C58.423 60.853 58.883 60.845 59.207 60.926C59.298 60.948 59.375 60.978 59.441 61.024C59.542 61.095 59.615 61.204 59.692 61.35C59.692 61.35 59.822 61.591 59.896 61.732C59.801 61.787 59.713 61.777 59.598 61.71C59.484 61.645 59.357 61.715 59.234 61.786C59.112 61.856 59.088 61.95 59.375 62.115C59.664 62.283 59.737 62.456 60.154 62.215C60.228 62.354 60.28 62.46 60.326 62.567C60.351 62.627 60.374 62.698 60.39 62.777C59.807 63.112 59.895 63.252 60.161 63.405C60.427 63.56 60.645 63.643 60.768 63.572C61.271 63.28 61.216 63.018 61.133 61.859C61.049 60.701 59.609 59.741 59.198 59.503C58.786 59.265 56.966 58.478 56.594 58.478C56.222 58.478 56.04 58.509 55.643 58.28C55.243 58.049 55.171 58.313 55.171 58.445C55.171 58.576 55.063 58.648 54.899 58.743C54.734 58.838 54.542 58.859 54.262 58.585C53.981 58.313 53.85 58.337 53.476 58.552C53.103 58.768 53.021 58.776 53.021 58.875C53.021 58.902 53.021 58.926 53.021 58.948C53.021 58.948 53.021 59.281 53.021 59.292" />
        </g>
      </g>
    </g>
    <g id="_DATID_063025" class="DATID_063025_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M72.853 60.731L72.277 61.064 72.277 60.237 71.971 60.06 71.364 60.411 71.364 61.591 70.477 62.101 70.477 62.496 71.37 63.012 73.745 61.64 73.745 61.244 72.853 60.731z" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.37" y1="62.616" x2="73.745" y2="61.244" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.37" y1="62.616" x2="70.477" y2="62.101" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.49" y1="62.343" x2="71.885" y2="62.115" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.49" y1="62.343" x2="71.096" y2="62.115" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.49" y1="61.973" x2="71.879" y2="61.749" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.49" y1="61.973" x2="71.096" y2="61.746" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.096" y1="62.115" x2="71.096" y2="61.746" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.49" y1="62.343" x2="71.49" y2="61.973" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.885" y1="62.115" x2="71.885" y2="61.746" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.762 61.24C72.756 61.192 72.737 61.142 72.712 61.097C72.644 60.979 72.534 60.915 72.465 60.954" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.694" y1="61.393" x2="71.885" y2="61.86" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.697" y1="61.277" x2="73.078" y2="61.057" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="73.085" y1="61.421" x2="73.085" y2="61.052" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.697" y1="61.646" x2="73.085" y2="61.421" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.697" y1="61.646" x2="72.697" y2="61.277" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="73.085" y1="61.052" x2="72.691" y2="60.825" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.364" y1="60.411" x2="71.668" y2="60.588" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.668" y1="60.588" x2="72.277" y2="60.237" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.668" y1="60.588" x2="71.668" y2="61.414" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.364" y1="61.591" x2="72.277" y2="61.064" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M72.853 60.731C72.853 60.731 72.277 61.064 72.277 61.064C72.277 61.064 72.277 60.237 72.277 60.237C72.277 60.237 71.971 60.06 71.971 60.06C71.971 60.06 71.364 60.411 71.364 60.411C71.364 60.411 71.364 61.591 71.364 61.591C71.364 61.591 70.477 62.101 70.477 62.101C70.477 62.101 70.477 62.496 70.477 62.496C70.477 62.496 71.37 63.012 71.37 63.012C71.37 63.012 73.745 61.64 73.745 61.64C73.745 61.64 73.745 61.244 73.745 61.244C73.745 61.244 72.853 60.731 72.853 60.731"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M72.853 60.731L72.277 61.064L72.277 60.237L71.971 60.06L71.364 60.411L71.364 61.591L70.477 62.101L70.477 62.496L71.37 63.012L73.745 61.64L73.745 61.244L72.853 60.731" />
        </g>
      </g>
    </g>
    <g id="_DATID_066024" class="DATID_066024_V2L2St2Sz680-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M31.594 71.233C31.466 71.159 31.26 71.278 31.132 71.5 31.004 71.719 31.004 71.959 31.132 72.031L31.393 72.194 31.385 72.536 31.498 72.603 31.434 72.701C31.423 72.737 31.42 72.781 31.426 72.831L30.6 73.31C30.554 73.335 30.536 73.405 30.559 73.49 30.568 73.522 30.58 73.555 30.6 73.587 30.666 73.703 30.774 73.765 30.841 73.727L30.87 73.709 31.667 73.248C31.754 73.313 31.879 73.353 31.945 73.316L32.222 73.155 32.374 73.242 34.503 71.998 34.503 71.135 33.972 70.828 33.972 70.215 33.631 70.018 32.981 70.393 32.622 70.974 31.897 71.4 31.594 71.233z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M31.157 72.989C31.224 72.95 31.33 73.013 31.397 73.128C31.463 73.242 31.463 73.367 31.397 73.406" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M30.951 73.102C31.016 73.063 31.125 73.126 31.192 73.24C31.257 73.355 31.257 73.48 31.192 73.519" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M30.768 73.209C30.835 73.171 30.943 73.233 31.01 73.349C31.077 73.463 31.077 73.588 31.01 73.626" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M31.498 72.603L31.507 72.597C31.618 72.534 31.8 72.638 31.911 72.831C32.021 73.024 32.021 73.231 31.911 73.296" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M31.481 72.805C31.548 72.766 31.655 72.828 31.722 72.944C31.789 73.058 31.789 73.183 31.722 73.221" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.393" y1="71.933" x2="31.393" y2="72.194" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.393" y1="72.246" x2="31.951" y2="72.569" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.393" y1="71.933" x2="31.954" y2="72.256" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.954" y1="72.256" x2="32.246" y2="72.087" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.393" y1="71.933" x2="31.736" y2="71.734" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.736" y1="71.734" x2="31.736" y2="71.493" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.736" y1="71.493" x2="31.897" y2="71.4" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.736" y1="71.734" x2="32.295" y2="72.058" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.954" y1="72.256" x2="31.954" y2="72.534" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.951" y1="72.569" x2="32.243" y2="72.4" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.295" y1="71.816" x2="32.295" y2="72.058" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="31.736" y1="71.493" x2="32.295" y2="71.816" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.295" y1="71.816" x2="32.878" y2="71.48" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.878" y1="71.48" x2="32.673" y2="71.361" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.673" y1="71.361" x2="32.976" y2="71.186" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.976" y1="71.186" x2="33.323" y2="70.59" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.878" y1="71.48" x2="32.878" y2="71.849" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.243" y1="72.4" x2="32.359" y2="72.332" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.359" y1="72.332" x2="32.359" y2="72.141" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.365" y1="72.15" x2="32.884" y2="71.849" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.524" y1="72.212" x2="32.524" y2="73.117" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.524" y1="72.212" x2="34.143" y2="71.278" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="34.143" y1="71.278" x2="34.143" y2="72.206" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.524" y1="73.117" x2="34.143" y2="71.278" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.524" y1="72.212" x2="34.143" y2="72.206" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.347" y1="71.102" x2="33.473" y2="71.18" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.116" y1="71.557" x2="33.241" y2="71.635" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="34.182" y1="71.319" x2="34.503" y2="71.135" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.323" y1="70.59" x2="33.972" y2="70.215" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="34.143" y1="71.278" x2="33.686" y2="71.013" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.677" y1="70.998" x2="33.972" y2="70.828" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.527" y1="70.759" x2="33.527" y2="70.468" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.527" y1="70.468" x2="33.325" y2="70.352" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.325" y1="70.352" x2="33.566" y2="70.212" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.566" y1="70.212" x2="33.771" y2="70.331" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.771" y1="70.331" x2="33.771" y2="70.605" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.323" y1="70.59" x2="33.034" y2="70.415" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.518" y1="70.944" x2="33.527" y2="71.048" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="33.527" y1="71.048" x2="33.408" y2="71.102" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.878" y1="71.48" x2="33.03" y2="71.37" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M30.6 73.31C30.666 73.271 30.774 73.334 30.841 73.448C30.907 73.563 30.907 73.688 30.841 73.727" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.222" y1="73.155" x2="32.222" y2="72.64" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.241" y1="72.64" x2="32.374" y2="72.715" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="32.374" y1="72.715" x2="32.374" y2="73.242" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M31.8 71.344C31.673 71.271 31.466 71.391 31.338 71.611C31.211 71.831 31.211 72.07 31.338 72.144" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M33.241 71.635C33.314 71.671 33.424 71.599 33.488 71.474C33.552 71.349 33.545 71.216 33.473 71.18C33.401 71.143 33.29 71.215 33.226 71.34C33.162 71.465 33.17 71.597 33.241 71.635" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M33.347 71.102C33.276 71.064 33.164 71.137 33.1 71.263C33.036 71.388 33.043 71.519 33.116 71.557" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M31.594 71.233C31.466 71.159 31.26 71.278 31.132 71.5C31.004 71.719 31.004 71.959 31.132 72.031C31.132 72.031 31.393 72.194 31.393 72.194C31.393 72.194 31.385 72.536 31.385 72.536C31.385 72.536 31.498 72.603 31.498 72.603C31.498 72.603 31.434 72.701 31.434 72.701C31.423 72.737 31.42 72.781 31.426 72.831C31.426 72.831 30.6 73.31 30.6 73.31C30.554 73.335 30.536 73.405 30.559 73.49C30.568 73.522 30.58 73.555 30.6 73.587C30.666 73.703 30.774 73.765 30.841 73.727C30.841 73.727 30.87 73.709 30.87 73.709C30.87 73.709 31.667 73.248 31.667 73.248C31.754 73.313 31.879 73.353 31.945 73.316C31.945 73.316 32.222 73.155 32.222 73.155C32.222 73.155 32.374 73.242 32.374 73.242C32.374 73.242 34.503 71.998 34.503 71.998C34.503 71.998 34.503 71.135 34.503 71.135C34.503 71.135 33.972 70.828 33.972 70.828C33.972 70.828 33.972 70.215 33.972 70.215C33.972 70.215 33.631 70.018 33.631 70.018C33.631 70.018 32.981 70.393 32.981 70.393C32.981 70.393 32.622 70.974 32.622 70.974C32.622 70.974 31.897 71.4 31.897 71.4C31.897 71.4 31.594 71.233 31.594 71.233"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M31.594 71.233C31.466 71.159 31.26 71.278 31.132 71.5C31.004 71.719 31.004 71.959 31.132 72.031L31.393 72.194L31.385 72.536L31.498 72.603L31.434 72.701C31.423 72.737 31.42 72.781 31.426 72.831L30.6 73.31C30.554 73.335 30.536 73.405 30.559 73.49C30.568 73.522 30.58 73.555 30.6 73.587C30.666 73.703 30.774 73.765 30.841 73.727L30.87 73.709L31.667 73.248C31.754 73.313 31.879 73.353 31.945 73.316L32.222 73.155L32.374 73.242L34.503 71.998L34.503 71.135L33.972 70.828L33.972 70.215L33.631 70.018L32.981 70.393L32.622 70.974L31.897 71.4L31.594 71.233" />
        </g>
      </g>
    </g>
    <g id="_DATID_058023" class="DATID_058023_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M68.555 47.896C68.501 47.872 68.453 47.856 68.406 47.844 68.248 47.804 68.125 47.826 68.048 47.851 68.007 47.865 67.977 47.883 67.977 47.883L66.355 48.859C66.123 48.993 65.95 49.057 65.822 49.079 65.7 49.1 65.606 49.082 65.537 49.045 65.478 49.017 65.433 48.972 65.399 48.918 65.341 48.828 65.319 48.698 65.319 48.557 65.319 48.472 65.349 48.372 65.406 48.268 65.51 48.079 65.703 47.868 65.847 47.725 65.948 47.627 66.024 47.561 66.024 47.561 66.024 47.561 66.037 47.551 66.18 47.442 66.162 47.383 66.168 47.329 66.203 47.296L67.686 46.176C67.727 46.143 67.799 46.149 67.876 46.194L68.069 46.45C68.12 46.43 68.177 46.417 68.236 46.408 68.537 46.365 68.921 46.439 69.174 46.633 69.421 46.82 69.563 47.048 69.629 47.179 69.659 47.237 69.713 47.377 69.713 47.377L68.978 47.801C68.865 47.735 68.582 47.554 68.486 47.53 68.248 47.469 68.058 47.506 67.942 47.545 67.864 47.573 67.805 47.609 67.805 47.609L66.191 48.579C66.191 48.579 65.871 48.743 65.769 48.759 65.733 48.765 65.704 48.768 65.685 48.758L65.674 48.746C65.644 48.7 65.644 48.63 65.644 48.557 65.644 48.518 65.665 48.472 65.691 48.424 65.781 48.261 65.95 48.079 66.075 47.956 66.168 47.865 66.233 47.81 66.233 47.81 66.233 47.81 66.246 47.798 66.391 47.689 66.46 47.722 66.519 47.72 66.557 47.692L66.563 47.688 68.033 46.578C68.063 46.551 68.078 46.5 68.069 46.45L67.876 46.194C67.968 46.152 68.093 46.101 68.191 46.088 68.57 46.033 69.052 46.131 69.37 46.375 69.667 46.6 69.837 46.874 69.918 47.03 69.93 47.054 69.939 47.075 69.947 47.093L70.213 46.939 70.927 46.939 71.51 47.978 71.51 48.268 68.713 49.883 67.578 49.228 67.578 48.46 67.965 48.237 68.334 48.237 68.334 48.024 68.555 47.896z" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="67.578" y1="48.938" x2="68.713" y2="49.594" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="68.713" y1="49.594" x2="71.51" y2="47.978" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="67.578" y1="48.46" x2="68.29" y2="48.46" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="68.29" y1="48.46" x2="68.873" y2="49.5" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="68.677" y1="48.237" x2="69.26" y2="49.277" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.826" y1="47.643" x2="69.826" y2="47.161" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.826" y1="47.161" x2="70.54" y2="47.161" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.54" y1="47.161" x2="71.123" y2="48.203" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.54" y1="47.161" x2="70.927" y2="46.939" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.959" y1="48.296" x2="69.826" y2="47.643" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="68.334" y1="48.024" x2="69.046" y2="48.024" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.046" y1="48.024" x2="69.629" y2="49.064" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.433" y1="47.801" x2="70.016" y2="48.841" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.826" y1="47.643" x2="69.463" y2="47.853" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="68.334" y1="48.237" x2="68.677" y2="48.237" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="68.978" y1="47.801" x2="69.433" y2="47.801" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="68.721" y1="47.801" x2="68.978" y2="47.801" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.67" y1="48.131" x2="69.876" y2="48.588" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.393" y1="47.97" x2="69.739" y2="48.347" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M68.033 46.578C68.036 46.576 68.037 46.573 68.04 46.572" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M68.069 46.45C68.051 46.353 67.971 46.25 67.876 46.194" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.826" y1="47.161" x2="69.947" y2="47.093" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M66.563 47.688C66.626 47.63 66.596 47.496 66.495 47.387C66.396 47.28 66.265 47.24 66.203 47.296" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.826" y1="47.31" x2="69.713" y2="47.377" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M68.721 47.801L68.554 47.896" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M68.555 47.896C68.501 47.872 68.453 47.856 68.406 47.844C68.248 47.804 68.125 47.826 68.048 47.851C68.007 47.865 67.977 47.883 67.977 47.883C67.977 47.883 66.355 48.859 66.355 48.859C66.123 48.993 65.95 49.057 65.822 49.079C65.7 49.1 65.606 49.082 65.537 49.045C65.478 49.017 65.433 48.972 65.399 48.918C65.341 48.828 65.319 48.698 65.319 48.557C65.319 48.472 65.349 48.372 65.406 48.268C65.51 48.079 65.703 47.868 65.847 47.725C65.948 47.627 66.024 47.561 66.024 47.561C66.024 47.561 66.037 47.551 66.18 47.442C66.162 47.383 66.168 47.329 66.203 47.296C66.203 47.296 67.686 46.176 67.686 46.176C67.727 46.143 67.799 46.149 67.876 46.194C67.968 46.152 68.093 46.101 68.191 46.088C68.57 46.033 69.052 46.131 69.37 46.375C69.667 46.6 69.837 46.874 69.918 47.03C69.93 47.054 69.939 47.075 69.947 47.093C69.947 47.093 70.213 46.939 70.213 46.939C70.213 46.939 70.927 46.939 70.927 46.939C70.927 46.939 71.51 47.978 71.51 47.978C71.51 47.978 71.51 48.268 71.51 48.268C71.51 48.268 68.713 49.883 68.713 49.883C68.713 49.883 67.578 49.228 67.578 49.228C67.578 49.228 67.578 48.46 67.578 48.46C67.578 48.46 67.965 48.237 67.965 48.237C67.965 48.237 68.334 48.237 68.334 48.237C68.334 48.237 68.334 48.024 68.334 48.024C68.334 48.024 68.555 47.896 68.555 47.896"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M68.555 47.896C68.501 47.872 68.453 47.856 68.406 47.844C68.248 47.804 68.125 47.826 68.048 47.851C68.007 47.865 67.977 47.883 67.977 47.883L66.355 48.859C66.123 48.993 65.95 49.057 65.822 49.079C65.7 49.1 65.606 49.082 65.537 49.045C65.478 49.017 65.433 48.972 65.399 48.918C65.341 48.828 65.319 48.698 65.319 48.557C65.319 48.472 65.349 48.372 65.406 48.268C65.51 48.079 65.703 47.868 65.847 47.725C65.948 47.627 66.024 47.561 66.024 47.561C66.024 47.561 66.037 47.551 66.18 47.442C66.162 47.383 66.168 47.329 66.203 47.296L67.686 46.176C67.727 46.143 67.799 46.149 67.876 46.194C67.968 46.152 68.093 46.101 68.191 46.088C68.57 46.033 69.052 46.131 69.37 46.375C69.667 46.6 69.837 46.874 69.918 47.03C69.93 47.054 69.939 47.075 69.947 47.093L70.213 46.939L70.927 46.939L71.51 47.978L71.51 48.268L68.713 49.883L67.578 49.228L67.578 48.46L67.965 48.237L68.334 48.237L68.334 48.024L68.555 47.896" />
        </g>
        <path stroke-width="0.25" stroke-linejoin="round"
          d="M68.033 46.578C68.063 46.551 68.078 46.5 68.069 46.45C68.12 46.43 68.177 46.417 68.236 46.408C68.537 46.365 68.921 46.439 69.174 46.633C69.421 46.82 69.563 47.048 69.629 47.179C69.659 47.237 69.713 47.377 69.713 47.377L68.978 47.801C68.865 47.735 68.582 47.554 68.486 47.53C68.248 47.469 68.058 47.506 67.942 47.545C67.864 47.573 67.805 47.609 67.805 47.609L66.191 48.579C66.191 48.579 65.871 48.743 65.769 48.759C65.733 48.765 65.704 48.768 65.685 48.758L65.674 48.746C65.644 48.7 65.644 48.63 65.644 48.557C65.644 48.518 65.665 48.472 65.691 48.424C65.781 48.261 65.95 48.079 66.075 47.956C66.168 47.865 66.233 47.81 66.233 47.81C66.233 47.81 66.246 47.798 66.391 47.689C66.46 47.722 66.519 47.72 66.557 47.692L66.563 47.688L68.033 46.578" />
      </g>
    </g>
    <g id="_DATID_059022" class="DATID_059022_V2L2St2Sz680-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M80.293 48.363L80.293 46.213 75.867 48.768 75.867 50.918 80.293 48.363z" />
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M77.46 51.954C77.061 52.158 76.918 51.453 77.512 51.012 77.512 51.012 80.352 49.17 80.715 48.976 81.287 48.67 81.299 49.509 80.807 49.816L77.46 51.954z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M76.798 48.707L77.262 48.441L77.262 48.18L76.061 48.874L76.061 49.134L76.466 48.899L76.466 49.728L76.796 49.537L76.798 48.707" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M77.367 48.119L78.462 47.487L78.462 47.746L77.697 48.188L77.697 48.192L77.697 48.426L78.284 48.085L78.284 48.241L77.697 48.581L77.697 49.018L77.367 49.207C77.367 49.207 77.367 48.082 77.367 48.119" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M79.754 46.956L79.754 47.088L79.411 47.286C79.411 47.286 79.4 47.206 79.337 47.241L78.951 47.465C78.951 47.465 78.837 47.531 78.837 47.64C78.837 47.75 78.897 47.731 78.897 47.731L79.615 47.317C79.615 47.317 79.754 47.246 79.754 47.393L79.754 47.667C79.754 47.826 79.581 47.929 79.581 47.929L78.698 48.439C78.698 48.439 78.567 48.494 78.567 48.347L78.567 48.195L78.57 48.192L78.835 48.039C78.835 48.039 78.847 48.148 78.941 48.094L79.31 47.881C79.31 47.881 79.411 47.808 79.411 47.692C79.411 47.575 79.334 47.634 79.334 47.634L78.664 48.023C78.664 48.023 78.567 48.084 78.567 47.951L78.567 47.686C78.567 47.686 78.6 47.408 78.792 47.298L79.609 46.825L79.605 46.828C79.718 46.762 79.754 47.002 79.754 47.002L79.754 46.956" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M79.871 46.673L80.209 46.478L80.209 47.567L79.867 47.765C79.867 47.765 79.867 46.686 79.871 46.673" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M80.073 48.35L77.337 49.93C77.283 49.962 77.228 49.969 77.188 49.945C77.105 49.898 77.105 49.741 77.188 49.598C77.225 49.531 77.278 49.473 77.337 49.439L80.073 47.859C80.129 47.829 80.183 47.82 80.224 47.844C80.307 47.893 80.307 48.048 80.224 48.192C80.186 48.259 80.132 48.317 80.073 48.35" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M77.46 51.954C77.061 52.158 76.918 51.453 77.512 51.012C77.512 51.012 80.352 49.17 80.715 48.976C80.715 48.976 80.293 48.363 80.293 48.363C80.293 48.363 80.293 46.213 80.293 46.213C80.293 46.213 75.867 48.768 75.867 48.768C75.867 48.768 75.867 50.918 75.867 50.918C75.867 50.918 80.293 48.363 80.293 48.363C80.293 48.363 80.715 48.976 80.715 48.976C81.287 48.67 81.299 49.509 80.807 49.816C80.807 49.816 77.46 51.954 77.46 51.954"
            stroke-width="0.12" />
        </g>
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M80.796 49.603C80.843 49.572 80.881 49.529 80.906 49.47C80.933 49.414 80.944 49.348 80.941 49.276C80.938 49.204 80.921 49.156 80.891 49.134C80.861 49.112 80.822 49.116 80.775 49.148C80.727 49.177 80.691 49.222 80.664 49.28C80.638 49.337 80.626 49.402 80.629 49.475C80.632 49.548 80.65 49.594 80.68 49.615C80.709 49.637 80.748 49.633 80.796 49.603L80.796 49.603" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M80.204 49.529L80.066 49.618L80.073 49.792L80.209 49.704C80.236 49.688 80.257 49.671 80.269 49.655C80.293 49.628 80.304 49.597 80.302 49.56C80.301 49.521 80.287 49.502 80.263 49.503C80.251 49.503 80.23 49.512 80.204 49.529L80.204 49.529" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M78.626 50.573L78.564 50.874L78.71 50.78L78.626 50.573L78.626 50.573" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M77.469 51.723C77.483 51.715 77.496 51.704 77.51 51.692C77.51 51.692 77.525 51.676 77.536 51.664L77.474 51.64L77.534 51.527L77.594 51.552C77.602 51.526 77.608 51.503 77.611 51.484C77.615 51.457 77.617 51.429 77.615 51.396C77.612 51.325 77.596 51.277 77.569 51.253C77.54 51.231 77.501 51.235 77.45 51.268C77.403 51.298 77.365 51.341 77.338 51.398C77.313 51.454 77.301 51.52 77.305 51.595C77.308 51.683 77.331 51.734 77.373 51.746C77.402 51.755 77.433 51.746 77.469 51.723L77.469 51.723" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M80.769 49.033L80.769 49.033L80.769 49.033" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M81.061 49.2C81.066 49.316 81.04 49.423 80.986 49.521C80.944 49.6 80.884 49.664 80.802 49.716C80.719 49.768 80.656 49.783 80.609 49.762C80.549 49.737 80.516 49.667 80.51 49.551C80.504 49.432 80.53 49.325 80.584 49.228C80.626 49.151 80.688 49.085 80.769 49.033C80.852 48.981 80.915 48.966 80.962 48.988C81.022 49.011 81.055 49.081 81.061 49.2L81.061 49.2" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M80.224 49.401C80.265 49.377 80.296 49.362 80.319 49.359C80.34 49.354 80.359 49.357 80.376 49.368C80.388 49.377 80.4 49.389 80.408 49.404C80.415 49.418 80.421 49.439 80.421 49.465C80.423 49.496 80.418 49.53 80.406 49.569C80.393 49.606 80.37 49.643 80.34 49.674C80.367 49.67 80.387 49.674 80.399 49.689C80.411 49.704 80.418 49.734 80.42 49.78L80.421 49.823C80.424 49.853 80.427 49.881 80.427 49.881C80.432 49.896 80.439 49.904 80.451 49.905L80.453 49.92L80.319 50.006C80.316 49.993 80.311 49.975 80.311 49.975C80.307 49.96 80.304 49.942 80.302 49.923L80.299 49.863C80.296 49.823 80.289 49.799 80.275 49.793C80.262 49.789 80.237 49.796 80.203 49.82L80.078 49.899L80.09 50.152L79.972 50.226L79.942 49.581L80.224 49.401L80.224 49.401" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M79.405 49.923L79.87 49.628L79.876 49.741L79.703 49.851L79.727 50.383L79.608 50.46L79.582 49.929L79.409 50.039L79.405 49.923L79.405 49.923" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M78.917 50.234L79.382 49.939L79.388 50.052L79.215 50.161L79.24 50.694L79.12 50.771L79.094 50.24L78.923 50.35L78.917 50.234L78.917 50.234" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M78.62 50.424L78.62 50.424L78.62 50.424" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M78.379 51.243L78.552 50.466L78.688 50.381L78.923 50.896L78.792 50.979L78.748 50.871L78.537 51.006L78.504 51.164L78.379 51.243L78.379 51.243" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M78.043 50.792L78.043 50.792L78.043 50.792" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M77.831 51.337L77.813 50.939L77.935 50.862L77.953 51.259C77.954 51.304 77.96 51.332 77.971 51.347C77.987 51.374 78.019 51.371 78.069 51.341C78.117 51.31 78.149 51.271 78.161 51.225C78.17 51.2 78.173 51.164 78.171 51.119L78.152 50.722L78.274 50.645L78.293 51.042C78.296 51.11 78.289 51.17 78.272 51.22C78.239 51.314 78.174 51.392 78.075 51.454C77.974 51.518 77.906 51.527 77.867 51.478C77.846 51.453 77.834 51.405 77.831 51.337L77.831 51.337" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M77.736 51.31C77.737 51.368 77.733 51.42 77.724 51.466C77.715 51.511 77.701 51.551 77.683 51.588L77.753 51.615L77.692 51.728L77.62 51.7C77.599 51.728 77.581 51.749 77.566 51.765C77.542 51.79 77.512 51.816 77.475 51.838C77.4 51.886 77.337 51.899 77.287 51.881C77.224 51.859 77.191 51.789 77.185 51.671C77.18 51.554 77.206 51.445 77.263 51.345C77.311 51.265 77.371 51.201 77.445 51.154C77.519 51.106 77.584 51.093 77.635 51.112C77.697 51.136 77.73 51.201 77.736 51.31L77.736 51.31" />
      </g>
    </g>
    <g id="_DATID_064021" class="DATID_064021_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M78.688 62.804C78.688 63.009 78.542 63.212 78.39 63.299L70.137 68.061C69.915 68.188 69.567 68.045 69.567 67.694L69.567 65.26C69.567 65.082 69.694 64.858 69.873 64.762L78.04 60.045C78.397 59.84 78.688 60.003 78.688 60.372L78.688 62.804z" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M78.688 62.804C78.688 63.009 78.542 63.212 78.39 63.299C78.39 63.299 70.137 68.061 70.137 68.061C69.915 68.188 69.567 68.045 69.567 67.694C69.567 67.694 69.567 65.26 69.567 65.26C69.567 65.082 69.694 64.858 69.873 64.762C69.873 64.762 78.04 60.045 78.04 60.045C78.397 59.84 78.688 60.003 78.688 60.372C78.688 60.372 78.688 62.804 78.688 62.804"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round" fill="#FFFFFF"
            d="M78.688 62.804C78.688 63.009 78.542 63.212 78.39 63.299L70.137 68.061C69.915 68.188 69.567 68.045 69.567 67.694L69.567 65.26C69.567 65.082 69.694 64.858 69.873 64.762L78.04 60.045C78.397 59.84 78.688 60.003 78.688 60.372L78.688 62.804z" />
        </g>
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M78.391 60.537C78.39 60.444 78.359 60.284 78.135 60.412L70.126 65.049C69.948 65.152 69.953 65.314 69.953 65.314L69.953 67.621C69.953 67.828 70.144 67.769 70.144 67.769L70.149 67.766" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.362 65.492C70.328 65.513 70.296 65.515 70.274 65.501C70.224 65.474 70.224 65.382 70.274 65.298C70.322 65.212 70.402 65.168 70.45 65.195C70.474 65.209 70.487 65.239 70.487 65.277" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.35 67.521C70.316 67.54 70.284 67.543 70.263 67.528C70.213 67.501 70.213 67.409 70.263 67.325C70.311 67.24 70.391 67.194 70.438 67.222C70.463 67.236 70.475 67.266 70.475 67.304" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M78.087 61.023C78.052 61.042 78.022 61.045 77.998 61.031C77.95 61.003 77.95 60.911 77.998 60.826C78.048 60.743 78.126 60.697 78.176 60.725C78.2 60.738 78.212 60.768 78.212 60.805" />
      </g>
    </g>
    <g id="_DATID_015020" class="DATID_015020_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M51.398 44.832L51.398 43.357C55.097 41.165 61.844 37.171 61.844 37.171 63.14 36.423 64.087 35.527 64.706 34.536 65.307 33.575 65.594 32.529 65.594 31.463 65.594 29.225 65.37 28 65.058 27.217 64.944 26.932 64.787 26.699 64.635 26.469 64.317 25.986 64.021 25.506 63.926 24.818 63.811 23.989 63.651 22.954 63.09 22.393 62.648 21.928 61.945 21.265 61.09 20.457L58.783 18.25C57.165 16.557 56.7 16.079 56.475 15.874 55.84 15.226 55.258 14.721 54.71 14.359 54.222 14.121 53.756 14.002 53.295 13.999 52.844 13.996 52.406 14.109 51.965 14.321 50.881 14.843 45.368 18.028 40.777 20.71L34.459 24.431C32.743 25.471 32.006 26.31 31.507 27.469 31.574 27.899 32.226 29.074 32.488 29.533L37.194 37.887C37.64 38.762 38.503 40.378 39.405 41.256 39.469 41.319 39.531 41.384 39.593 41.447 39.668 41.448 40.618 41.457 40.618 41.457L40.966 41.472 41.246 41.506C41.908 41.505 42.075 41.476 42.396 41.555 42.533 41.588 42.612 41.613 42.713 41.676L43.331 42.033C43.347 42.042 43.381 42.063 43.412 42.085 43.572 42.198 43.773 42.478 43.893 42.707 44.103 43.171 44.409 43.847 44.409 45.116 44.409 45.624 44.491 46.435 44.823 46.99 44.835 47.009 44.845 47.027 44.856 47.045 44.883 47.043 44.909 47.04 44.936 47.036 45.143 47.006 45.289 46.959 45.554 46.805L45.633 46.765C45.633 46.765 47.567 45.621 51.232 43.451L51.232 44.93C49.124 46.179 47.36 47.222 46.578 47.676 45.771 48.142 45.185 48.125 44.765 47.856L44.256 47.561C43.371 47.113 43.151 45.594 43.151 44.759 43.151 43.539 42.832 42.936 42.648 42.518 42.35 42.485 41.936 42.448 41.393 42.448L40.118 42.426C39.914 42.423 39.704 42.347 39.49 42.219L38.951 41.908C37.793 41.283 36.521 39.015 35.941 37.878 35.682 37.408 35.362 36.826 34.963 36.093 33.011 32.521 32.25 31.306 31.242 29.536 30.916 28.964 30.091 27.448 30.181 27.135 30.728 25.722 31.495 24.74 33.512 23.518 35.53 22.294 49.28 14.243 51.067 13.382 52.237 12.818 53.396 12.85 54.712 13.608L54.726 13.617 55.344 13.974 55.356 13.98C56.039 14.378 56.764 14.971 57.554 15.778 57.783 15.987 58.24 16.434 59.878 18.149 61.746 19.968 63.386 21.454 64.186 22.292L64.186 22.292C64.837 22.943 65.048 24.128 65.18 25.086 65.311 26.045 65.941 26.509 66.272 27.335 66.602 28.163 66.855 29.455 66.855 31.821 66.855 34.185 65.581 36.469 62.783 38.084 60.948 39.143 55.548 42.362 51.398 44.832z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.969 42.549C43 42.563 43.027 42.576 43.054 42.594C43.14 42.656 43.2 42.75 43.265 42.875C43.265 42.875 43.265 42.875 43.277 42.896" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M30.8 27.492C30.71 27.803 31.533 29.321 31.859 29.893C32.868 31.663 33.628 32.878 35.579 36.45C35.979 37.182 36.301 37.765 36.558 38.235" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M30.8 27.492C31.347 26.079 32.113 25.097 34.131 23.873C36.148 22.649 49.899 14.599 51.685 13.74C52.668 13.266 53.645 13.212 54.713 13.657" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M36.558 38.235C37.005 39.109 37.859 40.649 38.756 41.6" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M43.265 42.875C43.451 43.292 43.768 43.896 43.768 45.116C43.768 45.759 43.899 46.808 44.365 47.462" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M45.066 47.283C45.106 47.314 45.148 47.338 45.194 47.359C45.296 47.402 45.417 47.414 45.554 47.393C45.761 47.363 45.906 47.316 46.173 47.162L46.25 47.122L62.463 37.529C63.758 36.78 64.704 35.883 65.323 34.893C65.924 33.932 66.212 32.886 66.212 31.821C66.212 29.582 65.987 28.356 65.674 27.574C65.561 27.289 65.403 27.056 65.253 26.826C64.935 26.341 64.638 25.862 64.543 25.175C64.43 24.347 64.269 23.31 63.707 22.748" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M40.128 41.802L41.237 41.813L41.584 41.829L41.865 41.862C42.298 41.862 42.518 41.85 42.71 41.863" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M39.838 41.676C39.935 41.75 40.03 41.801 40.128 41.802" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.625 42.476C42.633 42.49 42.64 42.503 42.648 42.518C42.648 42.518 42.648 42.52 42.658 42.539" />
        <g id="DATID_000000" class="V2L1St1Sz32-S#D">
          <path
            d="M43.331 42.033C43.331 42.033 42.713 41.676 42.713 41.676C42.612 41.613 42.533 41.588 42.396 41.555C42.075 41.476 41.908 41.505 41.246 41.506C41.246 41.506 40.966 41.472 40.966 41.472C40.966 41.472 40.618 41.457 40.618 41.457C40.618 41.457 39.668 41.448 39.593 41.447C39.531 41.384 39.469 41.319 39.405 41.256C38.503 40.378 37.64 38.762 37.194 37.887C37.194 37.887 32.488 29.533 32.488 29.533C32.226 29.074 31.574 27.899 31.507 27.469C32.006 26.31 32.743 25.471 34.459 24.431C34.459 24.431 40.777 20.71 40.777 20.71C45.368 18.028 50.881 14.843 51.965 14.321C52.406 14.109 52.844 13.996 53.295 13.999C53.756 14.002 54.222 14.121 54.71 14.359C55.258 14.721 55.84 15.226 56.475 15.874C56.7 16.079 57.165 16.557 58.783 18.251C58.783 18.251 61.09 20.457 61.09 20.457C61.945 21.265 62.648 21.928 63.09 22.393C63.651 22.954 63.811 23.989 63.926 24.818C64.021 25.506 64.317 25.986 64.635 26.469C64.787 26.699 64.944 26.932 65.058 27.217C65.37 28 65.594 29.225 65.594 31.463C65.594 32.529 65.307 33.575 64.706 34.536C64.087 35.527 63.14 36.423 61.844 37.171C61.844 37.171 45.633 46.765 45.633 46.765C45.633 46.765 45.554 46.805 45.554 46.805C45.289 46.959 45.143 47.006 44.936 47.036C44.909 47.04 44.883 47.043 44.856 47.045C44.845 47.027 44.835 47.009 44.823 46.99C44.491 46.435 44.409 45.624 44.409 45.116C44.409 43.847 44.103 43.171 43.893 42.707C43.773 42.478 43.572 42.198 43.412 42.085C43.381 42.063 43.347 42.042 43.331 42.033"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M43.331 42.033L42.713 41.676C42.612 41.613 42.533 41.588 42.396 41.555C42.075 41.476 41.908 41.505 41.246 41.506L40.966 41.472L40.618 41.457C40.618 41.457 39.668 41.448 39.593 41.447C39.531 41.384 39.469 41.319 39.405 41.256C38.503 40.378 37.64 38.762 37.194 37.887L32.488 29.533C32.226 29.074 31.574 27.899 31.507 27.469C32.006 26.31 32.743 25.471 34.459 24.431L40.777 20.71C45.368 18.028 50.881 14.843 51.965 14.321C52.406 14.109 52.844 13.996 53.295 13.999C53.756 14.002 54.222 14.121 54.71 14.359C55.258 14.721 55.84 15.226 56.475 15.874C56.7 16.079 57.165 16.557 58.783 18.251L61.09 20.457C61.945 21.265 62.648 21.928 63.09 22.393C63.651 22.954 63.811 23.989 63.926 24.818C64.021 25.506 64.317 25.986 64.635 26.469C64.787 26.699 64.944 26.932 65.058 27.217C65.37 28 65.594 29.225 65.594 31.463C65.594 32.529 65.307 33.575 64.706 34.536C64.087 35.527 63.14 36.423 61.844 37.171L45.633 46.765L45.554 46.805C45.289 46.959 45.143 47.006 44.936 47.036C44.909 47.04 44.883 47.043 44.856 47.045C44.845 47.027 44.835 47.009 44.823 46.99C44.491 46.435 44.409 45.624 44.409 45.116C44.409 43.847 44.103 43.171 43.893 42.707C43.773 42.478 43.572 42.198 43.412 42.085C43.381 42.063 43.347 42.042 43.331 42.033" />
        </g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M42.648 42.518C42.832 42.936 43.151 43.539 43.151 44.759C43.151 45.594 43.371 47.113 44.256 47.561C44.256 47.561 44.771 47.859 44.765 47.856C45.185 48.125 45.771 48.142 46.578 47.676C48.915 46.326 59.987 39.698 62.783 38.084C65.581 36.469 66.853 34.185 66.853 31.821C66.853 29.455 66.602 28.163 66.271 27.335C65.941 26.509 65.311 26.045 65.18 25.086C65.048 24.128 64.837 22.943 64.186 22.292C64.186 22.292 64.186 22.292 64.186 22.292C63.386 21.454 61.746 19.968 59.878 18.149C58.24 16.434 57.783 15.987 57.554 15.778C56.764 14.971 56.039 14.378 55.356 13.98C55.35 13.977 55.344 13.974 55.344 13.974C55.344 13.974 54.726 13.617 54.726 13.617C54.726 13.617 54.719 13.612 54.712 13.608C53.396 12.85 52.237 12.818 51.067 13.382C49.28 14.243 35.53 22.294 33.512 23.518C31.495 24.74 30.728 25.722 30.181 27.135C30.091 27.448 30.916 28.964 31.242 29.536C32.25 31.306 33.011 32.521 34.963 36.093C35.362 36.826 35.682 37.408 35.941 37.878C36.521 39.015 37.793 41.283 38.951 41.908C38.951 41.908 39.49 42.219 39.49 42.219C39.704 42.347 39.914 42.423 40.118 42.426C40.118 42.426 41.393 42.448 41.393 42.448C41.936 42.448 42.35 42.485 42.648 42.518"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M42.648 42.518C42.832 42.936 43.151 43.539 43.151 44.759C43.151 45.594 43.371 47.113 44.256 47.561L44.765 47.856C45.185 48.125 45.771 48.142 46.578 47.676C48.915 46.326 59.987 39.698 62.783 38.084C65.581 36.469 66.853 34.185 66.853 31.821C66.853 29.455 66.602 28.163 66.271 27.335C65.941 26.509 65.311 26.045 65.18 25.086C65.048 24.128 64.837 22.943 64.186 22.292L64.186 22.292C63.386 21.454 61.746 19.968 59.878 18.149C58.24 16.434 57.783 15.987 57.554 15.778C56.764 14.971 56.039 14.378 55.356 13.98L55.344 13.974L54.726 13.617L54.712 13.608C53.396 12.85 52.237 12.818 51.067 13.382C49.28 14.243 35.53 22.294 33.512 23.518C31.495 24.74 30.728 25.722 30.181 27.135C30.091 27.448 30.916 28.964 31.242 29.536C32.25 31.306 33.011 32.521 34.963 36.093C35.362 36.826 35.682 37.408 35.941 37.878C36.521 39.015 37.793 41.283 38.951 41.908L39.49 42.219C39.704 42.347 39.914 42.423 40.118 42.426L41.393 42.448C41.936 42.448 42.35 42.485 42.648 42.518" />
        </g>
      </g>
    </g>
    <g id="_DATID_051019" class="DATID_051019_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M44.368 41.195L46.784 39.799C46.92 40.042 47.048 40.253 47.158 40.435 47.222 40.551 47.359 40.598 47.572 40.54 47.619 40.529 48.49 40.292 48.49 40.292 55.11 38.618 58.28 37.006 62.408 34.643 62.423 34.636 62.436 34.628 62.436 34.628L62.819 34.408C63.156 34.213 63.47 34.033 63.792 33.847 65.858 32.654 69.209 29.469 70.362 28.042 70.596 27.699 70.518 27.277 70.2 26.969 69.787 26.559 69.245 26.047 68.621 25.457L66.331 23.268C64.653 21.542 64.239 20.945 63.822 20.533L63.722 20.617C62.368 21.818 57.64 24.839 55.658 25.952 53.485 27.172 49.979 28.887 48.378 29.649 47.079 30.268 45.878 30.687 44.738 31.006 44.17 31.134 44.149 31.477 43.844 31.743 43.686 31.881 43.365 32.212 43.009 32.579 43.085 32.92 43.295 33.164 43.685 34.03 43.753 34.165 43.825 34.317 43.896 34.49L43.896 34.49C44.195 35.207 45.723 38.005 46.725 39.712L44.314 41.098C44.137 40.777 43.936 40.409 43.709 39.993 41.758 36.42 40.996 35.206 39.987 33.435 39.655 32.851 38.802 31.282 38.936 31.018 38.814 30.893 38.682 30.765 38.54 30.632 38.654 28.743 38.914 27.902 39.722 27.082 40.594 26.199 45.753 22.763 50.112 20.216L50.494 19.995C50.494 19.995 50.511 19.986 50.527 19.978 50.643 19.91 50.759 19.843 50.875 19.775 55.325 17.207 58.289 15.82 59.393 15.535 60.5 15.25 61.109 15.173 62.042 15.364 62.485 15.436 64.673 17.664 65.034 18.024 65.396 18.385 65.168 18.521 65.683 19.321 65.912 19.53 66.368 19.978 68.007 21.693 69.875 23.51 71.513 24.996 72.314 25.835 72.504 26.035 72.668 26.225 72.793 26.42 73.847 27.614 75.135 28.903 75.638 29.606 75.765 29.861 75.748 29.951 75.549 30.216 75.334 30.501 75.31 30.907 75.31 32.741 75.31 34.578 75.56 35.524 75.686 35.597L76.046 35.805C76.099 36.484 76.087 36.945 76.087 38.088 76.087 39.232 75.706 40.356 74.563 41.423 73.381 42.527 69.456 45.198 65.263 47.618 65.049 47.741 64.837 47.862 64.626 47.982 64.57 48.017 64.189 48.237 64.189 48.237 64.189 48.237 64.167 48.247 64.144 48.259 60.281 50.451 57.088 51.957 55.582 52.601 53.981 53.287 53.258 53.744 52.42 53.898 51.581 54.049 51.2 53.936 50.305 53.673 50.109 53.372 49.896 52.99 49.624 52.478 48.173 51.85 47.578 51.651 46.85 48.344 46.122 45.036 45.792 44.722 45.759 44.159 45.731 43.67 45.676 43.582 44.368 41.195z" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M63.926 20.44C64.01 20.361 64.075 20.292 64.12 20.236" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M73.036 27.004C73.12 27.481 72.941 28.036 72.352 28.809C71.248 30.257 67.43 33.911 65.111 35.25C59.868 38.277 56.707 40.148 48.951 42.11C46.996 42.604 46.651 42.939 45.991 42.064" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M45.71 41.652C44.835 40.28 42.548 36.127 42.167 35.212C41.969 34.74 41.773 34.47 41.713 34.241" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M46.182 42.567C46.301 42.564 46.423 42.56 46.551 42.554" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M38.921 30.411C39.027 28.655 39.259 27.804 39.942 27.033" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M48.256 45.414C48.677 45.427 49.106 45.435 49.523 45.435C51.085 45.435 51.505 45.435 51.886 46.159C52.267 46.884 52.42 47.14 52.533 47.408C52.648 47.674 52.704 48.131 52.628 48.436C52.552 48.741 52.362 49.085 51.676 49.6C50.99 50.113 50.637 50.454 50.055 50.789C49.872 50.895 49.656 51.006 49.404 51.115" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M50.597 45.451C53.014 44.789 55.82 43.633 57.24 43.043C58.804 42.396 61.966 40.909 64.025 39.652C66.082 38.393 71.609 35.058 73.134 33.649C74.659 32.238 75.076 31.78 75.04 31.172C75.001 30.542 75.254 30.082 75.638 29.606" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M48.256 45.414C47.521 44.472 46.759 43.826 45.71 41.652" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M76.046 35.805C75.719 36.012 75.37 36.07 75.269 35.593C75.115 34.868 75.045 32.895 75.025 31.432" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M52.662 48.06C54.21 47.332 61.299 44.701 64.921 42.529C68.542 40.356 73.459 36.889 74.983 35.363" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M67.185 41.454C67.328 41.551 67.388 41.695 67.513 42.072C67.703 42.643 67.551 42.987 66.445 43.634C65.34 44.283 63.32 45.426 62.29 45.96C61.262 46.493 61.109 46.569 60.994 45.883C60.935 45.521 60.856 45.203 60.835 44.963" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M60.97 44.543L60.975 44.542" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M49.402 51.116C50 52.253 50.365 52.962 50.686 53.454" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M50.686 53.451C51.301 53.633 51.673 53.743 52.118 53.746" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M49.469 51.243C50.956 50.975 52.191 50.399 53.792 49.484C55.393 48.57 56.097 48.265 56.917 47.226C57.301 46.74 57.427 46.584 58.823 45.941C60.308 45.253 63.283 43.746 64.787 42.89C66.597 41.862 68.617 40.557 69.819 39.737C71.019 38.917 71.343 38.518 71.99 38.536C72.638 38.555 73.516 38.345 74.03 37.832C74.545 37.317 75.269 36.85 75.498 36.564C75.725 36.279 75.911 36.301 76.075 36.319" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M41.713 34.241C40.762 32.645 40.393 31.8 38.921 30.411" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M66.712 23.047L69.003 25.237C69.626 25.827 70.168 26.338 70.582 26.748" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M70.743 27.823C70.977 27.48 70.9 27.057 70.582 26.748" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M66.712 23.047C64.924 21.207 64.572 20.652 64.12 20.236" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M48.256 45.414C47.991 45.881 48.225 46.871 48.454 48.162C48.884 50.576 48.847 50.832 49.402 51.116" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M45.329 41.872C44.453 40.5 42.167 36.347 41.784 35.432C41.588 34.96 41.392 34.691 41.332 34.462" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M47.875 45.634C47.14 44.692 46.378 44.045 45.329 41.872" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M49.021 51.337C49.21 51.697 49.377 52.014 49.526 52.295" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M41.332 34.462C40.493 33.055 40.109 32.232 39.024 31.108" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M47.875 45.634C47.61 46.1 47.844 47.091 48.073 48.383C48.503 50.796 48.465 51.051 49.021 51.337" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M48.246 45.969L49.523 45.991C50.225 45.991 50.676 45.982 50.993 46.06C51.067 46.079 51.13 46.1 51.182 46.139C51.268 46.2 51.328 46.293 51.393 46.418L51.709 47.009C51.863 47.299 51.951 47.46 52.023 47.627C52.097 47.801 52.14 48.101 52.09 48.301L52.09 48.301C52.031 48.531 51.859 48.767 51.343 49.155L50.749 49.615C50.414 49.881 50.146 50.095 49.777 50.307C49.61 50.404 49.412 50.506 49.182 50.606" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M63.45 42.042C63.447 41.789 63.383 41.558 63.279 41.378" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.927 41.023C62.838 40.973 62.744 40.947 62.658 40.941C62.165 40.896 61.719 41.387 61.661 42.039C61.603 42.689 61.954 43.252 62.447 43.296C62.853 43.332 63.228 43.005 63.381 42.515" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M62.862 41.521C62.868 41.756 62.927 41.97 63.021 42.14" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M63.378 42.514C63.469 42.564 63.564 42.593 63.654 42.598C64.06 42.636 64.435 42.307 64.588 41.817" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M64.656 41.334C64.653 41.09 64.591 40.868 64.492 40.692" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M64.138 40.326C64.048 40.277 63.953 40.25 63.865 40.243C63.46 40.207 63.087 40.533 62.932 41.021" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M64.069 40.799C64.072 41.054 64.135 41.283 64.239 41.463" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M64.587 41.819C64.679 41.868 64.774 41.896 64.861 41.902C65.268 41.939 65.643 41.609 65.796 41.121" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.864 40.642C65.861 40.393 65.799 40.168 65.697 39.991" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.305 39.609C65.227 39.573 65.146 39.551 65.072 39.545C64.667 39.509 64.292 39.838 64.138 40.328" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.277 40.113C65.281 40.359 65.343 40.581 65.441 40.756" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.793 41.119C65.885 41.17 65.98 41.198 66.069 41.206C66.561 41.25 67.007 40.758 67.064 40.107C67.123 39.457 66.772 38.893 66.28 38.85C65.885 38.814 65.521 39.121 65.358 39.588" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.619 41.365C62.57 41.362 62.523 41.371 62.476 41.392C62.417 41.418 62.362 41.463 62.314 41.515C62.189 41.652 62.106 41.851 62.087 42.076C62.067 42.301 62.112 42.512 62.21 42.668C62.247 42.729 62.295 42.783 62.348 42.82C62.39 42.847 62.435 42.866 62.484 42.871C62.534 42.875 62.582 42.865 62.628 42.844C62.688 42.817 62.743 42.774 62.792 42.72C62.915 42.584 62.997 42.384 63.018 42.159L63.02 42.137" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.862 41.521C62.832 41.479 62.795 41.445 62.756 41.417C62.715 41.389 62.67 41.369 62.619 41.365" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M63.829 40.654C63.777 40.649 63.725 40.661 63.679 40.682C63.616 40.709 63.56 40.753 63.509 40.808C63.384 40.948 63.299 41.151 63.28 41.378L63.28 41.38" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M63.45 42.042C63.478 42.079 63.512 42.109 63.548 42.134C63.591 42.164 63.639 42.183 63.691 42.188C63.743 42.192 63.793 42.182 63.841 42.161C63.902 42.133 63.96 42.088 64.01 42.033C64.135 41.893 64.218 41.692 64.239 41.465L64.239 41.463" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M64.069 40.801C64.04 40.764 64.007 40.732 63.97 40.707C63.927 40.679 63.881 40.658 63.829 40.654" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.034 39.963C64.983 39.96 64.935 39.969 64.888 39.99C64.828 40.018 64.772 40.063 64.721 40.116C64.596 40.255 64.513 40.456 64.493 40.682L64.492 40.692" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M64.656 41.334C64.686 41.372 64.721 41.405 64.76 41.432C64.801 41.46 64.847 41.479 64.899 41.484C64.95 41.488 64.998 41.478 65.045 41.457C65.106 41.43 65.162 41.386 65.212 41.332C65.337 41.194 65.42 40.993 65.439 40.767L65.441 40.756" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.277 40.115C65.246 40.076 65.212 40.042 65.174 40.017C65.132 39.987 65.085 39.969 65.034 39.963" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M66.242 39.265C66.191 39.259 66.141 39.271 66.094 39.292C66.033 39.319 65.977 39.362 65.927 39.417C65.802 39.557 65.718 39.758 65.698 39.984L65.697 39.991" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.861 40.637C65.891 40.677 65.927 40.71 65.965 40.737C66.007 40.765 66.055 40.786 66.105 40.789C66.156 40.795 66.206 40.783 66.254 40.764C66.314 40.735 66.37 40.692 66.421 40.637C66.546 40.497 66.629 40.296 66.65 40.07C66.671 39.843 66.625 39.631 66.525 39.472C66.486 39.408 66.438 39.356 66.382 39.317C66.34 39.289 66.293 39.268 66.242 39.265" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M49.216 48.435C49.216 47.475 49.356 47.219 49.942 46.881C50.529 46.542 51.1 46.814 51.1 47.542C51.1 48.27 50.95 48.634 50.207 49.064C49.465 49.491 49.216 49.183 49.216 48.435" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M49.719 47.023C50.25 46.805 50.728 47.091 50.728 47.756C50.728 48.408 50.607 48.77 50.048 49.146" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M48.902 46.875C48.975 46.917 49.091 46.85 49.164 46.725C49.237 46.6 49.237 46.463 49.164 46.421C49.091 46.38 48.975 46.448 48.902 46.573C48.829 46.698 48.829 46.835 48.902 46.875" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M50.737 46.71C50.811 46.753 50.927 46.685 51 46.56C51.073 46.433 51.073 46.298 51 46.256C50.927 46.215 50.811 46.283 50.737 46.408C50.665 46.533 50.665 46.668 50.737 46.71" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M51.383 48.496C51.454 48.539 51.573 48.47 51.645 48.344C51.718 48.219 51.718 48.085 51.645 48.042C51.573 48 51.454 48.069 51.383 48.194C51.31 48.32 51.31 48.454 51.383 48.496" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M49.414 50.1C49.487 50.143 49.604 50.075 49.676 49.95C49.75 49.823 49.75 49.689 49.676 49.646C49.604 49.604 49.487 49.673 49.414 49.798C49.343 49.923 49.343 50.058 49.414 50.1" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M67.603 22.115L69.89 24.3C70.745 25.111 71.448 25.774 71.891 26.239C72.185 26.545 72.424 26.806 72.465 27.138C72.48 27.254 72.465 27.379 72.424 27.516C72.341 27.786 72.167 28.089 71.888 28.454C70.808 29.867 67.082 33.436 64.819 34.743C59.625 37.743 56.493 39.598 48.807 41.543L47.868 41.796C47.387 41.932 47.078 42.037 46.831 42.021L46.732 41.994C46.637 41.942 46.558 41.845 46.459 41.713" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M47.284 41.399C47.359 41.356 47.359 41.215 47.284 41.085C47.209 40.954 47.088 40.884 47.012 40.926C46.936 40.97 46.936 41.112 47.012 41.241C47.088 41.371 47.209 41.442 47.284 41.399" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.405 32.551C42.481 32.509 42.481 32.368 42.405 32.237C42.331 32.106 42.209 32.036 42.134 32.079C42.058 32.124 42.058 32.265 42.134 32.395C42.209 32.524 42.331 32.594 42.405 32.551" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M44.357 30.402C44.432 30.359 44.432 30.217 44.357 30.088C44.283 29.957 44.161 29.887 44.085 29.931C44.009 29.974 44.009 30.114 44.085 30.245C44.161 30.375 44.283 30.446 44.357 30.402" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M40.323 30.038C40.398 29.995 40.398 29.853 40.323 29.724C40.247 29.594 40.125 29.524 40.051 29.567C39.975 29.609 39.975 29.751 40.051 29.881C40.125 30.01 40.247 30.082 40.323 30.038" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M41.033 28.086C41.109 28.044 41.109 27.902 41.033 27.772C40.959 27.643 40.837 27.571 40.762 27.615C40.686 27.658 40.686 27.8 40.762 27.929C40.837 28.059 40.959 28.131 41.033 28.086" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M64.251 19.571C64.325 19.527 64.325 19.387 64.251 19.257C64.176 19.126 64.054 19.056 63.978 19.099C63.904 19.143 63.904 19.283 63.978 19.414C64.054 19.544 64.176 19.614 64.251 19.571" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.481 16.908C62.557 16.864 62.557 16.724 62.481 16.594C62.405 16.463 62.284 16.393 62.209 16.437C62.134 16.48 62.134 16.62 62.209 16.751C62.284 16.882 62.405 16.951 62.481 16.908" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M60.597 16.742C60.671 16.699 60.671 16.559 60.597 16.428C60.521 16.298 60.399 16.228 60.323 16.271C60.249 16.315 60.249 16.455 60.323 16.585C60.399 16.716 60.521 16.786 60.597 16.742" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.59 20.364C65.665 20.321 65.665 20.179 65.59 20.05C65.515 19.92 65.393 19.85 65.317 19.893C65.242 19.936 65.242 20.077 65.317 20.207C65.393 20.338 65.515 20.408 65.59 20.364" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M71.676 27.077C71.751 27.035 71.751 26.893 71.676 26.763C71.6 26.634 71.478 26.562 71.403 26.606C71.328 26.649 71.328 26.791 71.403 26.92C71.478 27.05 71.6 27.121 71.676 27.077" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M45.875 30.434C45.411 28.352 45.213 27.987 46.304 27.326C47.396 26.664 47.521 26.96 49.359 29.178" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M59.701 23.455C59.07 22.068 58.74 21.439 59.633 20.811C60.526 20.182 60.633 20.557 62.002 21.908" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M40.411 27.291C41.749 26.164 46.429 23.082 50.424 20.748" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M50.654 20.646L50.935 20.446L51.183 20.309C55.555 17.786 58.463 16.414 59.548 16.132C60.537 15.877 61.084 15.798 61.92 15.968" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M44.59 30.472C44.984 30.362 45.387 30.239 45.799 30.1" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M48.996 28.739C50.774 27.879 53.557 26.498 55.387 25.469C56.328 24.943 57.926 23.96 59.473 22.954" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M61.618 21.518C62.655 20.798 63.454 20.189 63.685 19.894" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M46.424 28.385C46.5 28.341 46.5 28.201 46.424 28.07C46.35 27.94 46.228 27.87 46.152 27.913C46.076 27.957 46.076 28.097 46.152 28.228C46.228 28.358 46.35 28.428 46.424 28.385" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M47.366 27.888C47.442 27.844 47.442 27.704 47.366 27.574C47.292 27.443 47.17 27.373 47.094 27.417C47.02 27.46 47.02 27.6 47.094 27.731C47.17 27.861 47.292 27.931 47.366 27.888" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M59.802 21.885C59.878 21.843 59.878 21.701 59.802 21.571C59.728 21.44 59.606 21.37 59.53 21.414C59.454 21.457 59.454 21.599 59.53 21.728C59.606 21.859 59.728 21.928 59.802 21.885" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M60.563 21.291C60.639 21.246 60.639 21.106 60.563 20.975C60.487 20.846 60.365 20.775 60.29 20.818C60.216 20.862 60.216 21.003 60.29 21.132C60.365 21.263 60.487 21.333 60.563 21.291" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.329 45.156C62.402 45.198 62.52 45.131 62.591 45.005C62.664 44.88 62.664 44.743 62.591 44.701C62.52 44.661 62.402 44.728 62.329 44.853C62.258 44.978 62.258 45.115 62.329 45.156" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.951 43.155C66.024 43.197 66.141 43.13 66.213 43.003C66.286 42.878 66.286 42.743 66.213 42.701C66.141 42.659 66.024 42.726 65.951 42.853C65.879 42.978 65.879 43.113 65.951 43.155" />
        <g id="DATID_000000" class="V2L1St1Sz32-S#D">
          <path
            d="M62.819 34.408C62.819 34.408 62.436 34.628 62.436 34.628C62.436 34.628 62.423 34.636 62.408 34.643C58.28 37.006 55.11 38.618 48.49 40.292C48.49 40.292 47.619 40.529 47.572 40.54C47.359 40.598 47.222 40.551 47.158 40.435C46.243 38.954 44.243 35.323 43.896 34.49C43.896 34.49 43.896 34.49 43.896 34.49C43.825 34.317 43.753 34.165 43.685 34.03C43.295 33.164 43.085 32.92 43.009 32.579C43.365 32.212 43.686 31.881 43.844 31.743C44.149 31.477 44.17 31.134 44.738 31.006C45.878 30.687 47.079 30.268 48.378 29.649C49.979 28.887 53.485 27.172 55.658 25.952C57.64 24.839 62.368 21.818 63.722 20.617C63.726 20.611 63.817 20.53 63.822 20.533C64.239 20.945 64.653 21.542 66.331 23.268C66.331 23.268 68.621 25.457 68.621 25.457C69.245 26.047 69.787 26.559 70.2 26.969C70.518 27.277 70.596 27.699 70.362 28.042C69.209 29.469 65.858 32.654 63.792 33.847C63.47 34.033 63.156 34.213 62.819 34.408"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M62.819 34.408L62.436 34.628C62.436 34.628 62.423 34.636 62.408 34.643C58.28 37.006 55.11 38.618 48.49 40.292C48.49 40.292 47.619 40.529 47.572 40.54C47.359 40.598 47.222 40.551 47.158 40.435C46.243 38.954 44.243 35.323 43.896 34.49L43.896 34.49C43.825 34.317 43.753 34.165 43.685 34.03C43.295 33.164 43.085 32.92 43.009 32.579C43.365 32.212 43.686 31.881 43.844 31.743C44.149 31.477 44.17 31.134 44.738 31.006C45.878 30.687 47.079 30.268 48.378 29.649C49.979 28.887 53.485 27.172 55.658 25.952C57.64 24.839 62.368 21.818 63.722 20.617L63.822 20.533C64.239 20.945 64.653 21.542 66.331 23.268L68.621 25.457C69.245 26.047 69.787 26.559 70.2 26.969C70.518 27.277 70.596 27.699 70.362 28.042C69.209 29.469 65.858 32.654 63.792 33.847C63.47 34.033 63.156 34.213 62.819 34.408" />
        </g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M64.626 47.982C64.837 47.862 65.049 47.741 65.263 47.618C69.456 45.198 73.381 42.527 74.563 41.423C75.706 40.356 76.087 39.232 76.087 38.088C76.087 36.945 76.099 36.484 76.046 35.805C76.046 35.805 75.686 35.597 75.686 35.597C75.56 35.524 75.31 34.578 75.31 32.741C75.31 30.907 75.334 30.501 75.549 30.216C75.748 29.951 75.765 29.861 75.638 29.606C75.135 28.903 73.847 27.614 72.793 26.42C72.668 26.225 72.504 26.035 72.314 25.835C71.513 24.996 69.875 23.51 68.007 21.693C66.368 19.978 65.912 19.53 65.683 19.321C65.168 18.521 65.396 18.385 65.034 18.024C64.673 17.664 62.485 15.436 62.042 15.364C61.109 15.173 60.5 15.25 59.393 15.535C58.289 15.82 55.325 17.207 50.875 19.775C50.759 19.843 50.643 19.91 50.527 19.978C50.511 19.986 50.494 19.995 50.494 19.995C50.494 19.995 50.112 20.216 50.112 20.216C45.753 22.763 40.594 26.199 39.722 27.082C38.914 27.902 38.654 28.743 38.54 30.632C38.682 30.765 38.814 30.893 38.936 31.018C38.802 31.282 39.655 32.851 39.987 33.435C40.996 35.206 41.758 36.42 43.709 39.993C45.659 43.564 45.725 43.597 45.759 44.159C45.792 44.722 46.122 45.036 46.85 48.344C47.578 51.651 48.173 51.85 49.624 52.478C49.896 52.99 50.109 53.372 50.305 53.673C51.2 53.936 51.581 54.049 52.42 53.898C53.258 53.744 53.981 53.287 55.582 52.601C57.088 51.957 60.281 50.451 64.144 48.259C64.167 48.247 64.189 48.237 64.189 48.237C64.189 48.237 64.57 48.017 64.626 47.982"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M64.626 47.982C64.837 47.862 65.049 47.741 65.263 47.618C69.456 45.198 73.381 42.527 74.563 41.423C75.706 40.356 76.087 39.232 76.087 38.088C76.087 36.945 76.099 36.484 76.046 35.805L75.686 35.597C75.56 35.524 75.31 34.578 75.31 32.741C75.31 30.907 75.334 30.501 75.549 30.216C75.748 29.951 75.765 29.861 75.638 29.606C75.135 28.903 73.847 27.614 72.793 26.42C72.668 26.225 72.504 26.035 72.314 25.835C71.513 24.996 69.875 23.51 68.007 21.693C66.368 19.978 65.912 19.53 65.683 19.321C65.168 18.521 65.396 18.385 65.034 18.024C64.673 17.664 62.485 15.436 62.042 15.364C61.109 15.173 60.5 15.25 59.393 15.535C58.289 15.82 55.325 17.207 50.875 19.775C50.759 19.843 50.643 19.91 50.527 19.978C50.511 19.986 50.494 19.995 50.494 19.995L50.112 20.216C45.753 22.763 40.594 26.199 39.722 27.082C38.914 27.902 38.654 28.743 38.54 30.632C38.682 30.765 38.814 30.893 38.936 31.018C38.802 31.282 39.655 32.851 39.987 33.435C40.996 35.206 41.758 36.42 43.709 39.993C45.659 43.564 45.725 43.597 45.759 44.159C45.792 44.722 46.122 45.036 46.85 48.344C47.578 51.651 48.173 51.85 49.624 52.478C49.896 52.99 50.109 53.372 50.305 53.673C51.2 53.936 51.581 54.049 52.42 53.898C53.258 53.744 53.981 53.287 55.582 52.601C57.088 51.957 60.281 50.451 64.144 48.259C64.167 48.247 64.189 48.237 64.189 48.237C64.189 48.237 64.57 48.017 64.626 47.982" />
        </g>
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M75.751 39.323C74.924 40.381 72.079 42.979 64.837 47.161C57.595 51.343 53.03 53.082 51.707 53.478" />
      </g>
    </g>
    <g>
      <g>
        <path stroke="none" fill="#FFFFFF"
          d="M20.932 34.03C17.571 33.347 18.713 31.637 15.304 31.582 15.006 31.579 14.737 32.107 15.034 32.112 18.445 32.165 17.524 34.042 20.663 34.558 20.908 34.6 21.298 34.104 20.932 34.03L20.932 34.03z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M20.663 34.558C19.824 34.408 19.274 34.171 18.855 33.904" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M18.385 33.551C18.125 33.329 17.905 33.103 17.652 32.896" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M17.172 32.567C16.704 32.308 16.071 32.128 15.034 32.112" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M20.932 34.03C20.094 33.88 19.544 33.643 19.125 33.374" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M18.655 33.021C18.394 32.799 18.175 32.573 17.922 32.366" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M17.44 32.039C16.974 31.78 16.341 31.6 15.304 31.582" />
      </g>
      <g>
        <path stroke="none" fill="#FFFFFF"
          d="M26.373 36.45C23.01 35.768 24.153 34.057 20.743 34.003 20.446 33.997 20.176 34.527 20.474 34.531 23.885 34.585 22.963 36.463 26.103 36.978 26.349 37.02 26.739 36.524 26.373 36.45L26.373 36.45z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M26.103 36.978C25.265 36.829 24.713 36.591 24.295 36.323" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M23.824 35.97C23.565 35.749 23.344 35.521 23.091 35.316" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M22.611 34.987C22.143 34.728 21.512 34.548 20.474 34.531" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M26.373 36.45C25.533 36.299 24.984 36.063 24.565 35.793" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M24.094 35.442C23.835 35.219 23.614 34.993 23.361 34.786" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M22.881 34.457C22.414 34.2 21.78 34.018 20.743 34.003" />
      </g>
      <g>
        <path stroke="none" fill="#FFFFFF"
          d="M32.06 38.908C28.699 38.225 29.843 36.515 26.431 36.46 26.135 36.456 25.865 36.984 26.163 36.99 29.573 37.043 28.652 38.92 31.791 39.436 32.037 39.476 32.426 38.981 32.06 38.908L32.06 38.908z" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M31.791 39.436C30.952 39.286 30.402 39.049 29.984 38.78" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M29.513 38.429C29.253 38.207 29.033 37.979 28.78 37.774" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M28.3 37.445C27.832 37.185 27.199 37.006 26.163 36.99" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M32.06 38.908C31.222 38.758 30.672 38.521 30.253 38.252" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M29.783 37.899C29.522 37.677 29.303 37.451 29.05 37.244" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M28.57 36.915C28.102 36.656 27.469 36.476 26.431 36.46" />
      </g>
    </g>
    <g>
      <path stroke="none" fill="#FFFFFF"
        d="M7.184 42.241C10.255 43.771 9.529 45.484 12.81 46.417 13.095 46.5 13.394 45.865 13.108 45.783 9.827 44.848 10.48 43.112 7.581 41.801 7.355 41.698 6.851 42.075 7.184 42.241L7.184 42.241z" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M7.581 41.801C8.352 42.162 9.044 42.652 9.378 43.021" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M9.893 43.588C10.16 43.872 10.532 44.262 10.725 44.509" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M11.451 45.057C11.946 45.354 12.432 45.631 13.144 45.801" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M7.184 42.241C7.955 42.603 8.605 43.106 8.981 43.473" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M9.372 43.911C9.621 44.191 9.734 44.317 9.931 44.554" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M10.226 44.918C11.103 45.823 11.858 46.139 12.855 46.423" />
    </g>
    <g>
      <path stroke-width="0.12" stroke-linejoin="round" fill="#FFFFFF"
        d="M8.102 47.281C8.102 46.479 7.451 45.829 6.649 45.829 5.846 45.829 5.195 46.479 5.195 47.281 5.195 48.085 5.846 48.734 6.649 48.734 7.451 48.734 8.102 48.085 8.102 47.281L8.102 47.281 8.102 47.281z" />
      <path stroke="none" fill="#000000"
        d="M6.012 48.103C6.02 47.954 6.051 47.826 6.105 47.716 6.158 47.606 6.262 47.506 6.418 47.415L6.649 47.281C6.753 47.222 6.825 47.17 6.867 47.128 6.932 47.06 6.966 46.984 6.966 46.899 6.966 46.798 6.936 46.719 6.876 46.659 6.815 46.6 6.735 46.57 6.634 46.57 6.485 46.57 6.383 46.628 6.325 46.74 6.296 46.799 6.278 46.884 6.275 46.99L6.054 46.99C6.057 46.84 6.086 46.718 6.139 46.622 6.232 46.456 6.397 46.372 6.636 46.372 6.833 46.372 6.977 46.426 7.068 46.533 7.16 46.64 7.206 46.759 7.206 46.889 7.206 47.027 7.157 47.146 7.059 47.243 7.003 47.301 6.903 47.369 6.758 47.451L6.593 47.542C6.514 47.585 6.451 47.627 6.407 47.667 6.326 47.735 6.275 47.814 6.254 47.899L7.197 47.899 7.197 48.103 6.012 48.103 6.012 48.103z" />
    </g>
    <g>
      <path stroke-width="0.12" stroke-linejoin="round" fill="#FFFFFF"
        d="M21.971 40.051C21.971 39.249 21.32 38.597 20.518 38.597 19.716 38.597 19.065 39.249 19.065 40.051 19.065 40.853 19.716 41.503 20.518 41.503 21.32 41.503 21.971 40.853 21.971 40.051L21.971 40.051 21.971 40.051z" />
      <path stroke="none" fill="#000000"
        d="M20.431 39.676L20.025 39.676 20.025 39.509C20.182 39.493 20.292 39.469 20.353 39.432 20.416 39.396 20.461 39.313 20.492 39.179L20.664 39.179 20.664 40.896 20.431 40.896 20.431 39.676 20.431 39.676z" />
    </g>
    <g>
      <path stroke-width="0.12" stroke-linejoin="round" fill="#FFFFFF"
        d="M31.644 40.752C31.644 39.929 30.978 39.262 30.153 39.262 29.33 39.262 28.664 39.929 28.664 40.752 28.664 41.575 29.33 42.243 30.153 42.243 30.978 42.243 31.644 41.575 31.644 40.752L31.644 40.752 31.644 40.752z" />
      <path stroke="none" fill="#000000"
        d="M30.137 41.643C29.932 41.643 29.785 41.587 29.693 41.475 29.602 41.362 29.556 41.226 29.556 41.066L29.782 41.066C29.792 41.177 29.812 41.259 29.844 41.31 29.9 41.399 30.001 41.445 30.149 41.445 30.263 41.445 30.355 41.414 30.423 41.353 30.492 41.292 30.527 41.213 30.527 41.118 30.527 40.999 30.49 40.915 30.419 40.868 30.346 40.82 30.245 40.798 30.115 40.798 30.1 40.798 30.086 40.798 30.071 40.798 30.056 40.798 30.042 40.798 30.027 40.799L30.027 40.607C30.048 40.61 30.068 40.612 30.082 40.612 30.097 40.613 30.114 40.615 30.132 40.615 30.213 40.615 30.28 40.601 30.332 40.576 30.423 40.53 30.469 40.451 30.469 40.335 30.469 40.249 30.439 40.182 30.378 40.136 30.317 40.09 30.245 40.066 30.164 40.066 30.021 40.066 29.92 40.115 29.864 40.21 29.835 40.264 29.817 40.338 29.812 40.436L29.599 40.436C29.599 40.308 29.625 40.2 29.675 40.109 29.763 39.948 29.92 39.868 30.141 39.868 30.318 39.868 30.454 39.908 30.55 39.985 30.646 40.064 30.695 40.177 30.695 40.326 30.695 40.432 30.667 40.518 30.609 40.584 30.574 40.625 30.528 40.658 30.472 40.68 30.562 40.706 30.634 40.753 30.684 40.825 30.736 40.896 30.76 40.982 30.76 41.085 30.76 41.25 30.707 41.384 30.599 41.487 30.489 41.591 30.336 41.643 30.137 41.643L30.137 41.643z" />
    </g>
    <g>
      <path stroke-width="0.12" stroke-linejoin="round" fill="#FFFFFF"
        d="M31.414 46.134C31.414 45.332 30.765 44.68 29.961 44.68 29.16 44.68 28.509 45.332 28.509 46.134 28.509 46.936 29.16 47.587 29.961 47.587 30.765 47.587 31.414 46.936 31.414 46.134L31.414 46.134 31.414 46.134z" />
      <path stroke-width="0.12" stroke-linejoin="round" fill="#FFFFFF"
        d="M31.414 46.134C31.414 45.332 30.765 44.68 29.961 44.68 29.16 44.68 28.509 45.332 28.509 46.134 28.509 46.936 29.16 47.587 29.961 47.587 30.765 47.587 31.414 46.936 31.414 46.134L31.414 46.134 31.414 46.134z" />
      <g>
        <path stroke="none" fill="#000000"
          d="M30.231 46.972L30.013 46.972 30.013 46.549 29.257 46.549 29.257 46.338 30.047 45.241 30.231 45.241 30.231 46.36 30.484 46.36 30.484 46.549 30.231 46.549 30.231 46.972 30.231 46.972z" />
        <path stroke="none" fill="#FFFFFF" d="M29.457 46.36L30.01 46.36 30.01 45.579 29.457 46.36 29.457 46.36z" />
      </g>
    </g>
    <g id="_DATID_017018" class="DATID_017018_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M42.57 52.213L43.037 52.484C43.429 52.688 43.584 53.25 43.584 53.996 43.584 54.899 43.131 55.487 42.408 55.758 41.686 56.029 41.64 56.073 41.822 57.473 42.003 58.874 41.798 59.213 41.369 59.46 40.942 59.707 40.423 59.896 40.219 60.014 40.07 60.1 39.909 60.253 39.728 60.131L39.238 59.848C39.159 59.811 39.082 59.726 38.993 59.564 38.103 58.194 37.179 55.72 36.545 53.896 35.659 51.606 35.659 51.345 35.868 50.774 35.88 50.741 35.893 50.712 35.906 50.685 36.231 50.67 36.67 50.807 37.189 51.009 38.127 51.375 41.094 51.947 42.238 52.103 42.365 52.115 42.475 52.152 42.57 52.213z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.759 52.404C41.613 52.247 38.648 51.674 37.71 51.31C37.191 51.109 36.75 50.97 36.426 50.984C36.412 51.012 36.399 51.042 36.389 51.073C36.179 51.646 36.179 51.906 37.064 54.197C37.7 56.021 38.622 58.493 39.515 59.863" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M39.584 59.978C39.561 59.945 39.537 59.908 39.515 59.863" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M37.277 54.798C37.914 55.429 38.502 56.243 41.325 54.843C43.252 53.887 43.423 53.895 43.57 53.646" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M40.695 54.149C40.93 54.284 41.313 54.064 41.548 53.655C41.784 53.246 41.784 52.804 41.548 52.668C41.313 52.531 40.93 52.753 40.695 53.161C40.459 53.57 40.459 54.012 40.695 54.149" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M41.548 52.668L40.345 51.973" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M40.695 54.149L39.683 53.564" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M39.683 53.564C39.24 53.308 39.134 53.097 39.134 52.68C39.134 52.262 39.259 51.912 39.371 51.912" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M40.923 56.593C41.094 56.6 41.207 56.722 41.207 57.055C41.207 57.722 41.301 58.293 40.765 58.603C39.926 59.088 39.652 59.197 39.564 58.826" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M38.834 54.942C38.935 55 39.097 54.906 39.197 54.732C39.298 54.56 39.298 54.371 39.197 54.314C39.097 54.256 38.935 54.35 38.834 54.524C38.734 54.697 38.734 54.884 38.834 54.942" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.232 55.338C42.332 55.396 42.496 55.301 42.595 55.128C42.697 54.954 42.697 54.767 42.595 54.709C42.496 54.651 42.332 54.744 42.232 54.918C42.133 55.091 42.133 55.28 42.232 55.338" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M40.63 56.094C40.729 56.152 40.893 56.058 40.993 55.884C41.093 55.71 41.093 55.524 40.993 55.466C40.893 55.408 40.729 55.502 40.63 55.674C40.53 55.848 40.53 56.036 40.63 56.094" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M42.57 52.213C42.57 52.213 43.037 52.484 43.037 52.484C43.429 52.688 43.584 53.249 43.584 53.996C43.584 54.899 43.131 55.487 42.408 55.758C41.686 56.029 41.64 56.073 41.822 57.473C42.003 58.874 41.798 59.213 41.369 59.46C40.942 59.707 40.423 59.896 40.219 60.014C40.07 60.1 39.909 60.253 39.728 60.131C39.728 60.131 39.238 59.848 39.238 59.848C39.159 59.811 39.082 59.726 38.993 59.564C38.103 58.194 37.179 55.72 36.545 53.896C35.659 51.606 35.659 51.345 35.868 50.774C35.88 50.741 35.893 50.712 35.906 50.685C36.231 50.67 36.67 50.807 37.189 51.009C38.127 51.375 41.094 51.947 42.238 52.103C42.365 52.115 42.475 52.152 42.57 52.213"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M42.57 52.213L43.037 52.484C43.429 52.688 43.584 53.249 43.584 53.996C43.584 54.899 43.131 55.487 42.408 55.758C41.686 56.029 41.64 56.073 41.822 57.473C42.003 58.874 41.798 59.213 41.369 59.46C40.942 59.707 40.423 59.896 40.219 60.014C40.07 60.1 39.909 60.253 39.728 60.131L39.238 59.848C39.159 59.811 39.082 59.726 38.993 59.564C38.103 58.194 37.179 55.72 36.545 53.896C35.659 51.606 35.659 51.345 35.868 50.774C35.88 50.741 35.893 50.712 35.906 50.685C36.231 50.67 36.67 50.807 37.189 51.009C38.127 51.375 41.094 51.947 42.238 52.103C42.365 52.115 42.475 52.152 42.57 52.213" />
        </g>
        <g id="DATID_000000" class="V2L1St1Sz32-S#D">
          <path
            d="M39.539 58.676C39.713 58.603 39.942 58.478 40.246 58.302C40.781 57.993 40.686 57.421 40.686 56.756C40.686 56.713 40.685 56.673 40.68 56.637C40.561 56.682 40.426 56.752 40.292 56.829C39.643 57.204 39.524 57.371 39.524 58.116C39.524 58.348 39.527 58.534 39.539 58.676"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M39.539 58.676C39.713 58.603 39.942 58.478 40.246 58.302C40.781 57.993 40.686 57.421 40.686 56.756C40.686 56.713 40.685 56.673 40.68 56.637C40.561 56.682 40.426 56.752 40.292 56.829C39.643 57.204 39.524 57.371 39.524 58.116C39.524 58.348 39.527 58.534 39.539 58.676" />
        </g>
      </g>
    </g>
    <g id="_DATID_056017" class="DATID_056017_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M70.418 37.561C70.418 37.561 70.322 37.499 70.048 37.664 69.774 37.829 69.277 38.133 69.277 38.133L69.299 39.125 68.792 40.774 69.623 41.222C69.623 41.222 69.894 41.082 70.105 40.796 70.316 40.512 70.524 40.189 70.524 40.189L70.734 39.67C70.734 39.67 70.798 39.561 70.945 39.515 71.094 39.47 71.316 39.39 71.316 39.39 71.316 39.39 71.65 39.719 71.941 39.77 71.941 39.77 71.876 39.984 71.984 40.171 72.093 40.36 72.427 41.003 72.427 41.003L73.582 40.335 73.058 39.426C73.405 39.31 73.629 39.003 73.703 38.612L74.838 38.427 74.691 37.692 76.79 37.35C77.042 37.31 77.195 36.948 77.155 36.515L77.29 36.493C77.322 36.274 77.215 36.076 77.215 36.076L77.054 36.103C76.923 35.787 76.707 35.618 76.512 35.646L73.74 36.098C73.734 36.063 73.722 36.029 73.706 35.999 73.641 35.877 73.513 35.817 73.424 35.865L72.644 36.29C72.597 36.316 72.569 36.365 72.563 36.423L72.501 36.457C72.397 36.356 72.271 36.332 72.177 36.381L72.084 36.429 71.427 36.476 70.587 36.962C70.406 37.06 70.34 37.305 70.418 37.561z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.177 36.381C72.109 36.417 72.069 36.485 72.057 36.569" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.305 37.033C72.387 37.07 72.465 37.07 72.528 37.036C72.614 36.99 72.658 36.889 72.65 36.773" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.003 36.473C71.87 36.545 71.84 36.75 71.936 36.932C72.034 37.112 72.221 37.201 72.355 37.13" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M71.829 36.567C71.695 36.639 71.665 36.844 71.763 37.024C71.859 37.206 72.046 37.295 72.18 37.222" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M71.656 36.661C71.522 36.731 71.492 36.936 71.588 37.118C71.686 37.298 71.873 37.387 72.007 37.316" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M71.481 36.753C71.349 36.825 71.319 37.03 71.415 37.21C71.513 37.392 71.7 37.481 71.834 37.408" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M71.308 36.847C71.174 36.918 71.144 37.122 71.242 37.304C71.338 37.485 71.525 37.573 71.659 37.502" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M71.135 36.939C71.001 37.012 70.971 37.216 71.067 37.398C71.165 37.579 71.352 37.667 71.486 37.594" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.96 37.033C70.828 37.104 70.798 37.31 70.894 37.49C70.992 37.671 71.179 37.759 71.313 37.689" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.849 36.822C70.629 36.939 70.579 37.277 70.739 37.573C70.897 37.871 71.206 38.017 71.426 37.899C71.554 37.829 71.623 37.686 71.629 37.518" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M71.277 36.863C71.125 36.768 70.968 36.759 70.849 36.822" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.313" y1="37.689" x2="72.528" y2="37.036" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.867" y1="36.74" x2="73.677" y2="36.298" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.355 36.536C72.293 36.57 72.28 36.665 72.325 36.75C72.37 36.835 72.459 36.878 72.522 36.844" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.522" y1="36.844" x2="72.73" y2="36.729" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.549 37.627C72.424 37.695 72.396 37.887 72.486 38.057C72.578 38.228 72.754 38.311 72.879 38.243C73.006 38.176 73.034 37.984 72.942 37.814C72.852 37.643 72.676 37.56 72.549 37.627" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M77.155 36.515C77.141 36.365 77.103 36.222 77.054 36.103" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="77.054" y1="36.103" x2="73.337" y2="36.709" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M73.424 35.865C73.379 35.89 73.35 35.936 73.343 35.993" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M73.51 36.305C73.566 36.332 73.62 36.331 73.662 36.308C73.727 36.273 73.756 36.189 73.74 36.098" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.563 36.423C72.555 36.481 72.57 36.545 72.599 36.6C72.665 36.722 72.792 36.783 72.882 36.734" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.355" y1="36.536" x2="72.501" y2="36.457" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.084" y1="36.429" x2="70.96" y2="37.033" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M70.415 37.56C70.492 37.82 70.695 38.024 70.912 38.07" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M71.737 38.99C72.149 39.411 72.652 39.56 73.058 39.426" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M73.703 38.612C73.826 37.939 73.483 37.148 72.909 36.718" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M76.575 37.386C76.828 37.344 76.981 36.982 76.941 36.549" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M68.792 40.774L69.143 40.563L69.713 39.753" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.102" y1="38.771" x2="70.049" y2="37.664" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M69.078 40.768L69.289 40.637L69.858 39.826L70.246 38.845" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.246" y1="38.845" x2="70.194" y2="37.737" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.299" y1="39.125" x2="70.1" y2="38.746" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.194" y1="37.737" x2="70.43" y2="37.607" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.171" y1="37.929" x2="72.734" y2="38.262" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.912" y1="38.07" x2="72.153" y2="38.331" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.087" y1="37.273" x2="72.59" y2="37.61" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.623" y1="37.588" x2="72.436" y2="37.896" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.153" y1="38.331" x2="72.451" y2="38.201" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.153" y1="38.331" x2="71.737" y2="38.99" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.834" y1="39.084" x2="72.734" y2="38.262" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.881" y1="39.466" x2="72.629" y2="38.357" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.99" y1="39.447" x2="72.99" y2="38.213" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="73.647" y1="38.82" x2="72.99" y2="38.411" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.992" y1="38.067" x2="73.676" y2="38.729" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.99" y1="37.953" x2="73.662" y2="37.844" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.945" y1="37.82" x2="73.147" y2="36.929" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="73.043" y1="36.828" x2="72.698" y2="37.457" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.698" y1="37.457" x2="72.516" y2="37.081" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M73.031 37.445C73.031 37.445 73.221 37.368 73.221 37.915L73.221 37.915" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M73.216 38.034C73.21 38.118 73.2 38.191 73.185 38.253" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M73.097 38.478C73.043 38.572 72.99 38.603 72.99 38.603" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.078" y1="40.768" x2="69.599" y2="41.054" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M69.295 40.628C69.304 40.652 69.313 40.676 69.325 40.695C69.433 40.898 69.643 40.996 69.793 40.914C69.944 40.834 69.978 40.604 69.87 40.402C69.808 40.287 69.713 40.207 69.615 40.173" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M69.504 40.377C69.433 40.414 69.418 40.523 69.468 40.618C69.519 40.713 69.618 40.759 69.689 40.722C69.725 40.703 69.748 40.667 69.754 40.622" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M69.623 40.378C69.579 40.357 69.537 40.359 69.504 40.377" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M69.209 40.686C69.218 40.707 69.227 40.726 69.236 40.743C69.344 40.945 69.555 41.043 69.704 40.963C69.721 40.954 69.734 40.944 69.746 40.933" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.087" y1="38.459" x2="70.228" y2="38.459" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.069" y1="38.07" x2="70.21" y2="38.07" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.894" y1="38.549" x2="70.087" y2="38.459" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.876" y1="38.161" x2="70.069" y2="38.07" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.876" y1="38.161" x2="69.894" y2="38.549" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M74.802 38.243L73.93 38.235C73.93 38.235 73.727 38.21 73.718 38.139" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M74.777 38.116L73.968 38.091C73.968 38.091 73.819 38.021 73.819 37.896C73.819 37.771 73.808 37.503 73.808 37.503" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="73.686" y1="37.966" x2="73.668" y2="37.42" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M73.668 37.42C73.668 37.42 73.668 37.314 73.908 37.314C74.147 37.314 74.513 37.348 74.513 37.348" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M74.513 37.348C74.513 37.348 74.686 37.359 74.686 37.661" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M76.84 36.137C76.709 35.823 76.495 35.652 76.298 35.682" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M73.945 37.314C73.984 36.941 73.858 36.533 73.649 36.314" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M73.789 37.326C73.795 37.237 73.792 37.146 73.781 37.064" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M73.67 36.655C73.617 36.537 73.554 36.442 73.487 36.375" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="77.155" y1="36.515" x2="73.775" y2="37.066" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M73.775 37.066L73.566 37.192L73.355 37.185" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M73.281 37.082C73.281 37.082 73.201 36.709 73.337 36.709C73.471 36.709 73.582 36.814 73.582 36.929C73.582 37.043 73.566 37.192 73.566 37.192" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="73.382" y1="36.713" x2="73.382" y2="36.46" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="73.266" y1="36.78" x2="73.043" y2="36.828" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M74.688 38.029C74.688 37.975 74.65 37.927 74.59 37.893" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M74.12 37.893C74.028 37.947 74.001 38.024 74.043 38.093" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M74.656 38.112C74.677 38.085 74.688 38.055 74.688 38.029" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M74.691 37.692L74.067 37.7C74.067 37.7 73.927 37.731 73.897 37.837C73.865 37.942 73.875 38.021 73.875 38.021" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M73.03 39.801C73.03 39.801 72.914 39.683 72.701 39.793C72.509 39.893 72.501 40.027 72.564 40.186C72.626 40.347 72.864 40.75 72.864 40.75" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.718" y1="40.835" x2="72.093" y2="39.637" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.941" y1="39.77" x2="72.093" y2="39.637" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.093" y1="39.637" x2="72.228" y2="39.558" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.228" y1="39.558" x2="72.415" y2="39.896" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.415 39.896C72.415 39.896 72.427 39.779 72.609 39.673C72.793 39.569 72.953 39.628 72.953 39.628L72.881 39.466" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M72.322 39.728C72.322 39.728 72.42 39.487 72.682 39.472" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.686" y1="39.387" x2="70.433" y2="39.527" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.433" y1="39.527" x2="70.411" y2="39.648" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.411 39.648C70.411 39.648 70.411 39.807 70.268 39.807C70.125 39.807 70.046 39.807 70.046 39.807L69.89 40.444" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.046" y1="39.807" x2="69.894" y2="39.807" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="69.929" y1="39.628" x2="70.414" y2="39.628" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.433 39.527C70.433 39.527 70.23 39.548 70.23 39.389C70.23 39.228 70.236 38.96 70.236 38.96L70.596 38.954C70.596 38.954 70.578 39.159 70.587 39.271C70.594 39.372 70.686 39.387 70.686 39.387" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.596 38.954C70.596 38.954 70.602 38.835 70.441 38.835" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.686" y1="39.387" x2="71.316" y2="39.387" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M71.316 39.387C71.316 39.387 71.403 39.386 71.403 39.225C71.403 39.066 71.391 39.009 71.391 39.009L71.737 38.99" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.546 38.862C70.546 38.862 70.695 38.923 70.695 39.066C70.695 39.209 70.695 39.271 70.763 39.271C70.832 39.271 70.849 39.215 71.027 39.215C71.203 39.215 71.278 39.271 71.278 39.271L71.266 38.991C71.266 38.991 71.26 38.889 71.489 38.889" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M70.734 39.67L70.843 39.387" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M72.85 40.506C72.896 40.482 72.924 40.441 72.936 40.39" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.796 40.122C72.703 40.085 72.609 40.125 72.576 40.216" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M72.727 40.515C72.772 40.529 72.816 40.524 72.85 40.506" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.938 40.674C73.106 40.588 73.159 40.356 73.057 40.158C72.953 39.96 72.733 39.868 72.566 39.956C72.555 39.962 72.546 39.966 72.539 39.972" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.834 40.706C72.873 40.701 72.909 40.691 72.938 40.674" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M70.418 37.561C70.418 37.561 70.322 37.499 70.048 37.664C69.774 37.829 69.277 38.133 69.277 38.133C69.277 38.133 69.299 39.125 69.299 39.125C69.299 39.125 68.792 40.774 68.792 40.774C68.792 40.774 69.623 41.222 69.623 41.222C69.623 41.222 69.894 41.082 70.105 40.796C70.316 40.512 70.524 40.189 70.524 40.189C70.524 40.189 70.734 39.67 70.734 39.67C70.734 39.67 70.798 39.561 70.945 39.515C71.094 39.47 71.316 39.39 71.316 39.39C71.316 39.39 71.65 39.719 71.941 39.77C71.941 39.77 71.876 39.984 71.984 40.171C72.093 40.36 72.427 41.003 72.427 41.003C72.427 41.003 73.582 40.335 73.582 40.335C73.582 40.335 73.058 39.426 73.058 39.426C73.405 39.31 73.629 39.003 73.703 38.612C73.703 38.612 74.838 38.427 74.838 38.427C74.838 38.427 74.691 37.692 74.691 37.692C74.691 37.692 76.79 37.35 76.79 37.35C77.042 37.31 77.195 36.948 77.155 36.515C77.155 36.515 77.29 36.493 77.29 36.493C77.322 36.274 77.215 36.076 77.215 36.076C77.215 36.076 77.054 36.103 77.054 36.103C76.923 35.787 76.707 35.618 76.512 35.646C76.512 35.646 73.74 36.098 73.74 36.098C73.734 36.063 73.722 36.029 73.706 35.999C73.641 35.877 73.513 35.817 73.424 35.865C73.424 35.865 72.644 36.29 72.644 36.29C72.597 36.316 72.569 36.365 72.563 36.423C72.563 36.423 72.501 36.457 72.501 36.457C72.397 36.356 72.271 36.332 72.177 36.381C72.177 36.381 72.084 36.429 72.084 36.429C72.084 36.429 71.427 36.476 71.427 36.476C71.427 36.476 70.587 36.962 70.587 36.962C70.406 37.06 70.34 37.305 70.418 37.561"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M70.418 37.561C70.418 37.561 70.322 37.499 70.048 37.664C69.774 37.829 69.277 38.133 69.277 38.133L69.299 39.125L68.792 40.774L69.623 41.222C69.623 41.222 69.894 41.082 70.105 40.796C70.316 40.512 70.524 40.189 70.524 40.189L70.734 39.67C70.734 39.67 70.798 39.561 70.945 39.515C71.094 39.47 71.316 39.39 71.316 39.39C71.316 39.39 71.65 39.719 71.941 39.77C71.941 39.77 71.876 39.984 71.984 40.171C72.093 40.36 72.427 41.003 72.427 41.003L73.582 40.335L73.058 39.426C73.405 39.31 73.629 39.003 73.703 38.612L74.838 38.427L74.691 37.692L76.79 37.35C77.042 37.31 77.195 36.948 77.155 36.515L77.29 36.493C77.322 36.274 77.215 36.076 77.215 36.076L77.054 36.103C76.923 35.787 76.707 35.618 76.512 35.646L73.74 36.098C73.734 36.063 73.722 36.029 73.706 35.999C73.641 35.877 73.513 35.817 73.424 35.865L72.644 36.29C72.597 36.316 72.569 36.365 72.563 36.423L72.501 36.457C72.397 36.356 72.271 36.332 72.177 36.381L72.084 36.429L71.427 36.476L70.587 36.962C70.406 37.06 70.34 37.305 70.418 37.561" />
        </g>
      </g>
    </g>
    <g id="_DATID_057016" class="DATID_057016_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M70.462 43.756C70.873 43.899 71.382 43.506 71.596 42.881 71.811 42.256 71.653 41.634 71.242 41.493 70.915 41.381 70.527 41.604 70.269 42.023 69.938 41.909 69.546 42.139 69.289 42.564 68.95 42.473 68.561 42.726 68.316 43.164 67.908 43.063 67.43 43.456 67.224 44.052 67.009 44.677 67.168 45.299 67.579 45.441 67.923 45.558 68.331 45.307 68.587 44.851 68.912 44.932 69.28 44.701 69.524 44.298 69.846 44.384 70.215 44.158 70.462 43.756z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.112 42.348C69.932 42.857 69.998 43.369 70.256 43.628" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.617 43.43C70.903 43.494 71.231 43.219 71.375 42.805C71.525 42.366 71.415 41.929 71.126 41.831C70.909 41.756 70.655 41.89 70.474 42.152" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.319 42.473C70.213 42.804 70.257 43.127 70.412 43.305" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M70.617 43.43C70.799 42.923 70.731 42.409 70.474 42.152" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M69.135 42.895C68.953 43.417 69.034 43.938 69.308 44.182" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M69.677 43.966C69.881 43.993 70.097 43.856 70.256 43.628" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.412 43.305C70.516 42.975 70.474 42.652 70.319 42.473" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.112 42.348C69.903 42.302 69.671 42.438 69.503 42.679" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M69.347 42.999C69.233 43.348 69.287 43.692 69.465 43.863" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M69.677 43.966C69.858 43.445 69.778 42.924 69.503 42.679" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M68.173 43.497C68.01 44.012 68.102 44.509 68.373 44.741" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M68.73 44.518C68.933 44.545 69.15 44.411 69.308 44.182" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M69.465 43.863C69.578 43.512 69.525 43.17 69.347 42.999" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M69.135 42.895C68.92 42.868 68.689 43.021 68.53 43.274" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M68.387 43.603C68.295 43.938 68.352 44.25 68.516 44.412" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M68.73 44.518C68.893 44.003 68.802 43.506 68.53 43.274" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M67.695 45.103C67.923 45.182 68.191 45.029 68.373 44.741" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M68.516 44.412C68.608 44.079 68.551 43.767 68.387 43.603" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M68.173 43.497C67.891 43.46 67.584 43.734 67.447 44.128C67.296 44.567 67.406 45.005 67.695 45.103" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M70.462 43.756C70.873 43.899 71.382 43.506 71.596 42.881C71.811 42.256 71.653 41.634 71.242 41.493C70.915 41.381 70.527 41.604 70.269 42.023C69.938 41.909 69.546 42.139 69.289 42.564C68.95 42.473 68.561 42.726 68.316 43.164C67.908 43.063 67.43 43.456 67.224 44.052C67.009 44.677 67.168 45.299 67.579 45.441C67.923 45.558 68.331 45.307 68.587 44.851C68.912 44.932 69.28 44.701 69.524 44.298C69.846 44.384 70.215 44.158 70.462 43.756"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M70.462 43.756C70.873 43.899 71.382 43.506 71.596 42.881C71.811 42.256 71.653 41.634 71.242 41.493C70.915 41.381 70.527 41.604 70.269 42.023C69.938 41.909 69.546 42.139 69.289 42.564C68.95 42.473 68.561 42.726 68.316 43.164C67.908 43.063 67.43 43.456 67.224 44.052C67.009 44.677 67.168 45.299 67.579 45.441C67.923 45.558 68.331 45.307 68.587 44.851C68.912 44.932 69.28 44.701 69.524 44.298C69.846 44.384 70.215 44.158 70.462 43.756" />
        </g>
      </g>
    </g>
    <g id="_DATID_018015" class="DATID_018015_V2L2St2Sz680-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M9.887 51.362C9.275 51.735 8.747 52.24 8.319 52.868 7.831 53.582 7.471 54.466 7.274 55.5 7.128 56.488 7.161 57.051 7.161 57.448 7.161 57.54 7.16 57.615 7.152 57.671 7.131 57.671 7.11 57.671 7.088 57.671 6.945 57.671 6.844 57.688 6.769 57.731L6.424 57.93C6.291 58 6.259 58.151 6.259 58.426L7.698 59.256C7.934 59.392 8.054 59.539 8.135 59.496L8.521 59.274C8.593 59.256 8.617 59.042 8.582 58.475 8.269 55.6 8.958 53.188 10.673 52.043 13.868 50.472 18.841 53.429 21.937 58.789 22.58 59.889 23.155 61.152 23.579 62.463L24.602 62.462 24.957 62.256C24.542 60.889 23.937 59.478 23.138 58.095 20.757 53.969 17.297 51.22 14.292 50.587 12.839 50.28 11.475 50.444 10.323 51.109L10.317 51.112C10.307 51.118 10.297 51.125 10.287 51.131 10.275 51.137 10.265 51.143 10.265 51.143 10.265 51.143 9.909 51.35 9.887 51.362z" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M10.455 52.161C8.596 53.234 7.828 55.718 8.151 58.725" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M8.16 59.408C8.176 59.292 8.173 59.081 8.151 58.723" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M11.43 50.758C12.215 50.594 13.06 50.606 13.936 50.792C16.94 51.426 20.4 54.176 22.783 58.301C23.582 59.683 24.187 61.094 24.602 62.469" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M7.088 57.819C7.027 57.869 6.921 57.878 6.732 57.878C6.708 57.878 6.687 57.878 6.666 57.878" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M8.076 57.417L7.193 56.906" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M9.887 51.362C9.275 51.735 8.747 52.24 8.319 52.868C7.831 53.582 7.471 54.466 7.274 55.5C7.128 56.488 7.161 57.051 7.161 57.448C7.161 57.54 7.16 57.615 7.152 57.671C7.131 57.671 7.11 57.671 7.088 57.671C6.945 57.671 6.844 57.689 6.769 57.731C6.769 57.731 6.424 57.93 6.424 57.93C6.291 58 6.259 58.151 6.259 58.426C6.259 58.424 7.698 59.256 7.698 59.256C7.934 59.392 8.054 59.539 8.135 59.496C8.135 59.496 8.521 59.274 8.521 59.274C8.593 59.256 8.617 59.042 8.582 58.475C8.269 55.6 8.958 53.188 10.673 52.043C13.868 50.472 18.841 53.429 21.937 58.789C22.58 59.889 23.155 61.152 23.579 62.463C23.579 62.462 24.602 62.462 24.602 62.462C24.602 62.462 24.957 62.256 24.957 62.256C24.542 60.889 23.937 59.478 23.138 58.095C20.757 53.969 17.297 51.22 14.292 50.587C12.839 50.28 11.475 50.444 10.323 51.109C10.323 51.109 10.317 51.112 10.317 51.112C10.307 51.118 10.297 51.125 10.287 51.131C10.275 51.137 10.265 51.143 10.265 51.143C10.265 51.143 9.909 51.35 9.887 51.362"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M9.887 51.362C9.275 51.735 8.747 52.24 8.319 52.868C7.831 53.582 7.471 54.466 7.274 55.5C7.128 56.488 7.161 57.051 7.161 57.448C7.161 57.54 7.16 57.615 7.152 57.671C7.131 57.671 7.11 57.671 7.088 57.671C6.945 57.671 6.844 57.689 6.769 57.731L6.424 57.93C6.291 58 6.259 58.151 6.259 58.426L7.698 59.256C7.934 59.392 8.054 59.539 8.135 59.496L8.521 59.274C8.593 59.256 8.617 59.042 8.582 58.475C8.269 55.6 8.958 53.188 10.673 52.043C13.868 50.472 18.841 53.429 21.937 58.789C22.58 59.889 23.155 61.152 23.579 62.463L24.602 62.462L24.957 62.256C24.542 60.889 23.937 59.478 23.138 58.095C20.757 53.969 17.297 51.22 14.292 50.587C12.839 50.28 11.475 50.444 10.323 51.109L10.317 51.112C10.307 51.118 10.297 51.125 10.287 51.131C10.275 51.137 10.265 51.143 10.265 51.143C10.265 51.143 9.909 51.35 9.887 51.362" />
        </g>
      </g>
    </g>
    <g id="_DATID_005014" class="DATID_005014_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M34.359 18.62C34.192 18.463 34.076 18.3 34.039 18.21 33.93 17.949 34.42 18.129 33.621 17.644 33.481 17.561 33.18 17.439 33.18 17.015 33.18 16.797 33.213 16.678 33.442 16.602 33.67 16.525 34.137 16.268 34.256 16.199 34.448 16.088 34.572 16.091 34.68 16.265 34.738 16.359 35.103 16.754 35.179 16.829 35.256 16.905 35.234 17.286 35.32 17.437 35.304 17.458 35.277 17.489 35.223 17.542 35.198 18.106 35.515 18.318 35.865 18.343 35.905 18.099 35.908 17.824 35.865 17.634 35.777 17.254 35.408 16.655 35.246 16.46 34.959 16.117 34.223 15.308 34.223 15.102 34.223 14.896 34.731 14.582 34.865 14.503 35.112 14.362 35.451 14.178 35.561 14.124 35.668 14.07 36.252 14.022 36.322 14.134 36.494 14.416 36.441 14.56 36.429 14.734 36.418 14.912 36.356 15.07 35.343 15.656 35.164 15.759 36.17 16.922 36.223 17.21 36.353 17.917 36.978 17.388 37.017 17.579 37.093 17.96 36.939 18.271 35.853 18.899 35.654 19.257 35.176 19.228 34.953 19.175 34.787 19.135 34.621 19.07 34.487 18.963 34.353 18.856 34.42 18.763 34.359 18.62z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M35.851 18.42C35.808 18.637 35.734 18.82 35.637 18.884C35.223 19.152 34.792 18.972 34.478 18.724" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M36.418 14.33C36.35 14.217 35.908 14.243 35.799 14.298C35.691 14.352 35.35 14.535 35.103 14.678C34.996 14.74 34.649 14.954 34.517 15.143" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.515 15.141C34.71 15.464 35.231 16.042 35.463 16.318C35.625 16.515 35.996 17.112 36.082 17.492C36.158 17.829 36.03 18.699 35.881 18.824" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.664 17.841C34.49 17.422 33.478 16.952 33.43 16.905C33.338 16.814 33.405 16.737 33.634 16.661C33.862 16.585 34.289 16.356 34.408 16.288" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.408 16.288C34.468 16.382 34.93 16.807 35.006 16.884C35.082 16.96 35.051 17.376 35.223 17.542" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.441 17.894C34.268 17.477 33.231 16.951 33.185 16.905" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.353 18.614L34.353 18.612C34.259 18.521 34.49 18.019 34.441 17.894" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M33.795 17.111C34.036 17.004 34.334 16.841 34.429 16.786" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.429 16.786C34.457 16.868 35.003 17.471 35.223 17.542" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M36.338 17.519C36.432 17.809 36.844 17.634 37.027 17.646" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M33.709 16.975C33.75 16.998 33.817 16.96 33.857 16.888C33.899 16.818 33.899 16.74 33.857 16.716C33.817 16.693 33.75 16.731 33.709 16.803C33.667 16.875 33.667 16.951 33.709 16.975" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.162 16.739C34.204 16.763 34.271 16.724 34.311 16.652C34.353 16.582 34.353 16.504 34.311 16.481C34.271 16.457 34.204 16.495 34.162 16.567C34.121 16.638 34.121 16.714 34.162 16.739" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M35.027 18.509C35.027 18.557 35.095 18.597 35.179 18.597C35.264 18.597 35.332 18.557 35.332 18.509C35.332 18.46 35.264 18.42 35.179 18.42C35.095 18.42 35.027 18.46 35.027 18.509" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M35.003 15.272C35.055 15.303 35.142 15.253 35.194 15.163C35.246 15.07 35.246 14.973 35.194 14.942C35.142 14.912 35.055 14.961 35.003 15.053C34.951 15.144 34.951 15.242 35.003 15.272" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M35.947 14.792C36 14.823 36.088 14.774 36.142 14.679C36.195 14.586 36.195 14.486 36.142 14.455C36.088 14.423 36 14.474 35.947 14.567C35.893 14.66 35.893 14.762 35.947 14.792" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M35.304 14.821L35.773 14.551" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M35.773 14.551L35.773 14.926" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M35.773 14.926L35.316 15.189" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M35.304 14.865L35.304 15.115" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M34.405 16.289C34.454 16.439 34.357 16.826 34.429 16.786" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M34.359 18.62C34.192 18.463 34.076 18.3 34.039 18.21C33.93 17.949 34.42 18.129 33.621 17.644C33.481 17.561 33.18 17.439 33.18 17.015C33.18 16.797 33.213 16.678 33.442 16.602C33.67 16.525 34.137 16.268 34.256 16.199C34.448 16.088 34.572 16.091 34.68 16.265C34.738 16.359 35.103 16.754 35.179 16.829C35.256 16.905 35.234 17.286 35.32 17.437C35.304 17.458 35.277 17.489 35.223 17.542C35.198 18.106 35.515 18.318 35.865 18.343C35.905 18.099 35.908 17.824 35.865 17.634C35.777 17.254 35.408 16.655 35.246 16.46C34.959 16.117 34.223 15.308 34.223 15.102C34.223 14.896 34.731 14.582 34.865 14.503C35.112 14.362 35.451 14.178 35.561 14.124C35.668 14.07 36.252 14.022 36.322 14.134C36.494 14.417 36.441 14.56 36.429 14.734C36.418 14.912 36.356 15.07 35.343 15.656C35.164 15.759 36.17 16.922 36.223 17.21C36.353 17.917 36.978 17.388 37.017 17.579C37.093 17.96 36.939 18.271 35.853 18.899C35.654 19.257 35.176 19.228 34.953 19.175C34.787 19.135 34.621 19.07 34.487 18.963C34.353 18.856 34.42 18.763 34.359 18.62"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M34.359 18.62C34.192 18.463 34.076 18.3 34.039 18.21C33.93 17.949 34.42 18.129 33.621 17.644C33.481 17.561 33.18 17.439 33.18 17.015C33.18 16.797 33.213 16.678 33.442 16.602C33.67 16.525 34.137 16.268 34.256 16.199C34.448 16.088 34.572 16.091 34.68 16.265C34.738 16.359 35.103 16.754 35.179 16.829C35.256 16.905 35.234 17.286 35.32 17.437C35.304 17.458 35.277 17.489 35.223 17.542C35.198 18.106 35.515 18.318 35.865 18.343C35.905 18.099 35.908 17.824 35.865 17.634C35.777 17.254 35.408 16.655 35.246 16.46C34.959 16.117 34.223 15.308 34.223 15.102C34.223 14.896 34.731 14.582 34.865 14.503C35.112 14.362 35.451 14.178 35.561 14.124C35.668 14.07 36.252 14.022 36.322 14.134C36.494 14.417 36.441 14.56 36.429 14.734C36.418 14.912 36.356 15.07 35.343 15.656C35.164 15.759 36.17 16.922 36.223 17.21C36.353 17.917 36.978 17.388 37.017 17.579C37.093 17.96 36.939 18.271 35.853 18.899C35.654 19.257 35.176 19.228 34.953 19.175C34.787 19.135 34.621 19.07 34.487 18.963C34.353 18.856 34.42 18.763 34.359 18.62" />
        </g>
      </g>
    </g>
    <g id="_DATID_014013" class="DATID_014013_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M3.016 40.825C2.835 40.47 2.508 40.265 2.198 40.338 1.811 40.429 1.593 40.917 1.713 41.429 1.833 41.942 2.243 42.284 2.632 42.195 2.702 42.179 2.774 42.146 2.842 42.095 3.016 42.469 3.352 42.689 3.673 42.615 3.742 42.6 3.815 42.567 3.882 42.518 4.059 42.89 4.394 43.11 4.714 43.036 4.785 43.021 4.857 42.987 4.924 42.938 5.099 43.311 5.437 43.531 5.756 43.457 6.144 43.366 6.362 42.878 6.242 42.365 6.123 41.853 5.711 41.511 5.323 41.6 5.254 41.615 5.184 41.648 5.118 41.697 4.936 41.323 4.601 41.106 4.282 41.179 4.213 41.195 4.141 41.228 4.073 41.277 3.895 40.905 3.56 40.685 3.24 40.759 3.181 40.771 3.121 40.798 3.016 40.825z" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M4.815 42.569C4.788 42.365 4.814 42.176 4.876 42.02" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M5.224 42.088C5.246 42.277 5.224 42.454 5.168 42.6" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M3.773 42.139C3.749 41.944 3.773 41.759 3.831 41.609" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M4.181 41.648C4.208 41.85 4.182 42.04 4.12 42.195" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M2.732 41.722C2.706 41.524 2.73 41.337 2.791 41.185" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M3.141 41.234C3.165 41.43 3.141 41.615 3.081 41.765" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M5.402 41.936C5.44 41.926 5.48 41.929 5.519 41.941C5.57 41.956 5.618 41.984 5.663 42.021C5.778 42.116 5.865 42.267 5.907 42.444C5.948 42.621 5.936 42.793 5.875 42.929C5.851 42.982 5.82 43.03 5.78 43.066C5.751 43.093 5.716 43.112 5.677 43.121C5.639 43.131 5.599 43.128 5.562 43.118C5.509 43.103 5.461 43.073 5.416 43.036C5.301 42.941 5.214 42.792 5.173 42.615L5.168 42.597" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M5.224 42.088C5.246 42.051 5.269 42.018 5.299 41.991C5.328 41.965 5.362 41.945 5.402 41.936" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M4.357 41.503C4.399 41.493 4.44 41.496 4.48 41.509C4.533 41.524 4.583 41.552 4.628 41.591C4.746 41.689 4.835 41.841 4.876 42.02L4.876 42.021" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M4.815 42.569C4.796 42.6 4.774 42.63 4.746 42.654C4.716 42.682 4.679 42.703 4.639 42.713C4.597 42.722 4.557 42.719 4.517 42.707C4.464 42.692 4.413 42.662 4.368 42.625C4.251 42.527 4.161 42.375 4.12 42.197L4.12 42.195" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M4.181 41.648C4.2 41.615 4.222 41.587 4.25 41.563C4.28 41.533 4.317 41.514 4.357 41.503" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M3.317 41.088C3.357 41.079 3.398 41.082 3.436 41.093C3.488 41.107 3.538 41.137 3.584 41.174C3.7 41.271 3.788 41.421 3.83 41.6L3.831 41.609" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M3.773 42.139C3.753 42.173 3.729 42.204 3.702 42.228C3.672 42.256 3.637 42.277 3.597 42.286C3.556 42.295 3.516 42.292 3.476 42.281C3.424 42.267 3.375 42.237 3.33 42.2C3.213 42.103 3.125 41.953 3.084 41.774L3.081 41.765" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M3.141 41.235C3.161 41.201 3.184 41.17 3.212 41.146C3.242 41.118 3.277 41.097 3.317 41.088" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M2.274 40.665C2.315 40.655 2.357 40.658 2.395 40.67C2.447 40.686 2.498 40.715 2.544 40.753C2.66 40.848 2.749 41 2.79 41.179L2.791 41.185" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M2.733 41.719C2.713 41.753 2.69 41.784 2.661 41.81C2.631 41.838 2.596 41.857 2.556 41.868C2.515 41.877 2.474 41.874 2.435 41.862C2.382 41.847 2.333 41.819 2.286 41.78C2.17 41.683 2.081 41.533 2.04 41.353C1.999 41.174 2.011 41 2.072 40.862C2.096 40.807 2.129 40.759 2.169 40.723C2.198 40.695 2.234 40.674 2.274 40.665" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M3.016 40.825C2.835 40.47 2.508 40.265 2.198 40.338C1.811 40.429 1.593 40.917 1.713 41.429C1.833 41.942 2.243 42.284 2.632 42.195C2.702 42.179 2.774 42.146 2.842 42.095C3.016 42.469 3.352 42.689 3.673 42.615C3.742 42.6 3.815 42.567 3.882 42.518C4.059 42.89 4.394 43.11 4.714 43.036C4.785 43.021 4.857 42.987 4.924 42.938C5.099 43.311 5.437 43.531 5.756 43.457C6.144 43.366 6.362 42.878 6.242 42.365C6.123 41.853 5.711 41.511 5.323 41.6C5.254 41.615 5.184 41.648 5.118 41.697C4.936 41.323 4.601 41.106 4.282 41.179C4.213 41.195 4.141 41.228 4.073 41.277C3.895 40.905 3.56 40.685 3.24 40.759C3.181 40.771 3.121 40.798 3.016 40.825"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M3.016 40.825C2.835 40.47 2.508 40.265 2.198 40.338C1.811 40.429 1.593 40.917 1.713 41.429C1.833 41.942 2.243 42.284 2.632 42.195C2.702 42.179 2.774 42.146 2.842 42.095C3.016 42.469 3.352 42.689 3.673 42.615C3.742 42.6 3.815 42.567 3.882 42.518C4.059 42.89 4.394 43.11 4.714 43.036C4.785 43.021 4.857 42.987 4.924 42.938C5.099 43.311 5.437 43.531 5.756 43.457C6.144 43.366 6.362 42.878 6.242 42.365C6.123 41.853 5.711 41.511 5.323 41.6C5.254 41.615 5.184 41.648 5.118 41.697C4.936 41.323 4.601 41.106 4.282 41.179C4.213 41.195 4.141 41.228 4.073 41.277C3.895 40.905 3.56 40.685 3.24 40.759C3.181 40.771 3.121 40.798 3.016 40.825" />
        </g>
      </g>
    </g>
    <g id="_DATID_020012" class="DATID_020012_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M43.167 58.926L43.482 59.109 43.591 59.046C43.682 59.006 43.725 58.999 43.866 59.081L44.012 59.164C44.192 59.268 44.326 59.697 44.603 60.478 45 61.601 45.054 61.697 44.359 62.097L42.957 62.908C42.601 63.115 42.487 63.164 42.353 63.085L42.146 62.966C41.862 62.802 41.844 62.561 41.874 61.304 41.887 60.698 41.884 60.363 41.938 60.173L41.938 59.64 43.167 58.926z" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="43.892" y1="59.216" x2="42.447" y2="60.051" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.447 60.051C42.31 60.133 42.24 60.176 42.197 61.453C42.167 62.314 42.179 62.776 42.387 63.052" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M43.892 59.216C43.924 59.203 43.956 59.194 43.984 59.189" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="43.429" y1="59.14" x2="42.145" y2="59.881" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M42.145 59.881C42.082 59.918 42.018 59.951 41.962 60.1" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="41.97" y1="59.659" x2="42.25" y2="59.82" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="42.222" y1="60.887" x2="44.238" y2="59.723" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="42.191" y1="61.732" x2="44.527" y2="60.384" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="42.759" y1="60.121" x2="42.759" y2="62.978" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="43.362" y1="59.758" x2="43.362" y2="62.624" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="43.975" y1="59.484" x2="43.975" y2="62.32" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M43.167 58.926C43.167 58.926 43.482 59.109 43.482 59.109C43.482 59.109 43.591 59.046 43.591 59.046C43.682 59.006 43.725 58.999 43.866 59.081C43.866 59.081 44.012 59.164 44.012 59.164C44.192 59.268 44.326 59.697 44.603 60.478C45 61.601 45.054 61.697 44.359 62.097C44.359 62.097 42.957 62.908 42.957 62.908C42.601 63.115 42.487 63.164 42.353 63.085C42.353 63.085 42.146 62.966 42.146 62.966C41.862 62.802 41.844 62.561 41.874 61.304C41.887 60.698 41.884 60.363 41.938 60.173C41.938 60.173 41.938 59.64 41.938 59.64C41.938 59.64 43.17 58.929 43.167 58.926"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M43.167 58.926L43.482 59.109L43.591 59.046C43.682 59.006 43.725 58.999 43.866 59.081L44.012 59.164C44.192 59.268 44.326 59.697 44.603 60.478C45 61.601 45.054 61.697 44.359 62.097L42.957 62.908C42.601 63.115 42.487 63.164 42.353 63.085L42.146 62.966C41.862 62.802 41.844 62.561 41.874 61.304C41.887 60.698 41.884 60.363 41.938 60.173L41.938 59.64L43.167 58.926" />
        </g>
      </g>
    </g>
    <g>
      <path stroke-width="0.12" stroke-linejoin="round" fill="#FFFFFF"
        d="M41.36 31.445C41.36 30.643 40.71 29.992 39.908 29.992 39.106 29.992 38.454 30.643 38.454 31.445 38.454 32.247 39.106 32.898 39.908 32.898 40.71 32.898 41.36 32.247 41.36 31.445 41.36 31.445 41.36 31.445 41.36 31.445L41.36 31.445z" />
      <path stroke="none" fill="#000000"
        d="M39.884 32.338C39.68 32.338 39.533 32.281 39.441 32.17 39.348 32.057 39.302 31.922 39.302 31.76L39.529 31.76C39.539 31.873 39.56 31.954 39.591 32.005 39.648 32.095 39.75 32.14 39.896 32.14 40.011 32.14 40.101 32.109 40.17 32.048 40.24 31.987 40.274 31.91 40.274 31.812 40.274 31.693 40.238 31.611 40.165 31.564 40.093 31.515 39.991 31.492 39.862 31.492 39.848 31.492 39.834 31.492 39.819 31.492 39.804 31.493 39.789 31.493 39.774 31.495L39.774 31.303C39.795 31.306 39.814 31.307 39.831 31.307 39.845 31.309 39.862 31.309 39.88 31.309 39.96 31.309 40.027 31.297 40.079 31.271 40.171 31.225 40.216 31.146 40.216 31.03 40.216 30.943 40.186 30.878 40.125 30.83 40.064 30.785 39.993 30.762 39.911 30.762 39.768 30.762 39.667 30.809 39.612 30.905 39.582 30.958 39.564 31.035 39.561 31.132L39.345 31.132C39.345 31.004 39.371 30.894 39.423 30.804 39.512 30.643 39.667 30.564 39.89 30.564 40.064 30.564 40.201 30.603 40.298 30.681 40.393 30.76 40.442 30.873 40.442 31.021 40.442 31.128 40.414 31.213 40.356 31.28 40.322 31.321 40.276 31.353 40.219 31.376 40.31 31.4 40.381 31.449 40.432 31.519 40.482 31.591 40.509 31.678 40.509 31.78 40.509 31.945 40.454 32.079 40.345 32.182 40.237 32.286 40.084 32.338 39.884 32.338z" />
    </g>
    <g>
      <path stroke-width="0.12" stroke-linejoin="round" fill="#FFFFFF"
        d="M49.296 46.665C49.296 45.843 48.63 45.176 47.807 45.176 46.984 45.176 46.316 45.843 46.316 46.665 46.316 47.488 46.984 48.155 47.807 48.155 48.63 48.155 49.296 47.488 49.296 46.665L49.296 46.665 49.296 46.665z" />
      <path stroke="none" fill="#000000"
        d="M47.755 46.268L47.348 46.268 47.348 46.103C47.505 46.087 47.615 46.061 47.676 46.026 47.737 45.99 47.784 45.905 47.814 45.773L47.985 45.773 47.985 47.49 47.755 47.49 47.755 46.268 47.755 46.268z" />
    </g>
    <g>
      <path stroke-width="0.12" stroke-linejoin="round" fill="#FFFFFF"
        d="M49.729 53.201C49.729 52.378 49.063 51.712 48.238 51.712 47.415 51.712 46.749 52.378 46.749 53.201 46.749 54.024 47.415 54.692 48.238 54.692 49.063 54.692 49.729 54.024 49.729 53.201L49.729 53.201 49.729 53.201z" />
      <path stroke="none" fill="#000000"
        d="M47.622 53.966C47.631 53.817 47.661 53.688 47.715 53.578 47.768 53.468 47.871 53.368 48.027 53.277L48.259 53.143C48.362 53.084 48.435 53.033 48.476 52.99 48.542 52.923 48.576 52.847 48.576 52.761 48.576 52.661 48.545 52.581 48.484 52.521 48.424 52.463 48.344 52.433 48.243 52.433 48.095 52.433 47.993 52.49 47.936 52.601 47.905 52.662 47.887 52.746 47.884 52.853L47.664 52.853C47.667 52.703 47.695 52.579 47.747 52.485 47.841 52.317 48.008 52.234 48.244 52.234 48.442 52.234 48.587 52.287 48.679 52.395 48.77 52.502 48.814 52.621 48.814 52.752 48.814 52.89 48.767 53.008 48.67 53.106 48.612 53.162 48.512 53.231 48.366 53.313L48.201 53.405C48.122 53.448 48.061 53.488 48.017 53.529 47.936 53.598 47.886 53.676 47.865 53.761L48.807 53.761 48.807 53.966 47.622 53.966 47.622 53.966z" />
    </g>
    <g id="_DATID_046011" class="DATID_046011_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M47.348 21.015C47.176 21.106 47.067 21.237 46.957 21.361 46.871 21.457 46.787 21.55 46.692 21.632 46.551 21.757 46.273 22.044 45.956 22.368 45.597 22.736 45.192 23.152 44.871 23.454 44.771 23.548 44.706 23.591 44.674 23.664L44.328 23.864C43.375 22.268 43.009 21.42 41.536 20.033 41.649 18.146 41.909 17.303 42.719 16.484 43.618 15.571 49.04 11.965 53.49 9.397 57.939 6.828 60.904 5.442 62.009 5.157 63.113 4.871 63.723 4.794 64.658 4.985 65.1 5.059 67.287 7.285 67.65 7.647 68.01 8.007 67.781 8.144 68.298 8.942 68.418 9.053 68.602 9.228 68.987 9.621L68.641 9.823 67.703 9.278C67.629 9.333 67.552 9.416 67.489 9.522 67.448 9.571 67.406 9.621 67.364 9.673L67.266 9.791 67.022 10.092 66.903 10.217C66.459 10.784 64.048 12.43 61.786 13.871 61.722 14.25 61.503 14.378 60.789 14.791 59.84 15.339 57.597 16.647 56.688 17.172 55.941 17.605 55.884 17.763 55.393 17.519 53.758 18.349 52.113 19.149 51.152 19.606 49.795 20.253 48.536 20.689 47.348 21.015z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M42.576 21.132C43.859 21.314 45.268 21.16 46.573 20.833C47.945 20.49 49.393 20.033 50.993 19.271C52.594 18.509 56.1 16.794 58.274 15.573C60.445 14.355 66.049 10.734 66.734 9.856C67.421 8.98 68.069 8.484 67.65 7.647" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M44.326 23.864C44.271 23.649 44.334 23.471 44.628 23.195C45.238 22.623 46.152 21.632 46.457 21.364C46.762 21.097 46.933 20.713 47.502 20.585" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M68.121 8.836C67.971 8.778 67.778 8.778 67.584 8.887" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M45.887 20.384C44.564 19.129 43.384 18.182 42.119 17.271" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M67.76 7.953C65.896 6.719 65.516 6.549 64.371 5.729C64.09 5.528 63.713 5.245 63.265 4.905" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M61.814 13.403C61.814 13.525 61.811 13.63 61.804 13.721" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M55.213 17.425C55.149 17.388 55.078 17.347 54.999 17.301" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M68.641 9.823C68.262 9.438 68.147 9.343 68.028 9.236L67.871 9.198C67.85 9.202 67.828 9.21 67.805 9.22" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M47.348 21.015C47.176 21.106 47.067 21.237 46.957 21.361C46.871 21.457 46.787 21.55 46.692 21.632C46.551 21.757 46.273 22.044 45.956 22.368C45.597 22.736 45.192 23.152 44.871 23.454C44.771 23.548 44.706 23.591 44.674 23.664C44.674 23.664 44.328 23.864 44.328 23.864C43.375 22.268 43.009 21.42 41.536 20.033C41.649 18.146 41.909 17.303 42.719 16.484C43.618 15.571 49.04 11.965 53.49 9.397C57.939 6.828 60.904 5.442 62.009 5.157C63.113 4.871 63.723 4.794 64.658 4.985C65.1 5.059 67.287 7.285 67.65 7.647C68.01 8.007 67.781 8.144 68.298 8.942C68.418 9.053 68.602 9.228 68.987 9.621C68.987 9.621 68.641 9.823 68.641 9.823C68.641 9.823 67.703 9.278 67.703 9.278C67.629 9.333 67.552 9.416 67.489 9.522C67.448 9.571 67.406 9.621 67.364 9.673C67.364 9.673 67.266 9.791 67.266 9.791C67.266 9.791 67.022 10.092 67.022 10.092C67.022 10.092 66.903 10.217 66.903 10.217C66.459 10.784 64.048 12.43 61.786 13.871C61.722 14.25 61.503 14.378 60.789 14.791C59.84 15.339 57.597 16.647 56.688 17.172C55.941 17.605 55.884 17.763 55.393 17.519C53.758 18.349 52.113 19.149 51.152 19.606C49.795 20.253 48.536 20.689 47.348 21.015"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M47.348 21.015C47.176 21.106 47.067 21.237 46.957 21.361C46.871 21.457 46.787 21.55 46.692 21.632C46.551 21.757 46.273 22.044 45.956 22.368C45.597 22.736 45.192 23.152 44.871 23.454C44.771 23.548 44.706 23.591 44.674 23.664L44.328 23.864C43.375 22.268 43.009 21.42 41.536 20.033C41.649 18.146 41.909 17.303 42.719 16.484C43.618 15.571 49.04 11.965 53.49 9.397C57.939 6.828 60.904 5.442 62.009 5.157C63.113 4.871 63.723 4.794 64.658 4.985C65.1 5.059 67.287 7.285 67.65 7.647C68.01 8.007 67.781 8.144 68.298 8.942C68.418 9.053 68.602 9.228 68.987 9.621L68.641 9.823L67.703 9.278C67.629 9.333 67.552 9.416 67.489 9.522C67.448 9.571 67.406 9.621 67.364 9.673L67.266 9.791L67.022 10.092L66.903 10.217C66.459 10.784 64.048 12.43 61.786 13.871C61.722 14.25 61.503 14.378 60.789 14.791C59.84 15.339 57.597 16.647 56.688 17.172C55.941 17.605 55.884 17.763 55.393 17.519C53.758 18.349 52.113 19.149 51.152 19.606C49.795 20.253 48.536 20.689 47.348 21.015" />
        </g>
      </g>
    </g>
    <g id="_DATID_048010" class="DATID_048010_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M59.576 16.868C59.557 16.879 59.539 16.888 59.523 16.899 59.347 16.998 59.162 17.1 58.97 17.204 58.792 17.1 58.488 17.181 58.488 17.463 58.304 17.562 58.113 17.664 57.92 17.765 57.417 17.475 57.317 17.472 57.1 17.597 56.884 17.724 56.837 17.681 56.837 17.913 56.837 18.143 56.811 18.21 56.993 18.315 56.877 18.466 56.744 18.64 56.679 18.739 56.597 18.864 56.619 18.841 57.106 19.123 58 19.638 57.886 19.518 58.795 18.993 59.704 18.469 61.948 17.16 62.896 16.612 63.844 16.065 63.92 16.016 63.92 15.224L63.063 14.727C63.006 14.763 62.951 14.798 62.898 14.833 62.396 14.542 62.295 14.541 62.079 14.666 61.862 14.791 61.814 14.75 61.814 14.98 61.814 15.212 61.834 15.291 62.014 15.396 61.478 15.733 60.968 16.047 60.514 16.32 60.012 16.028 59.911 16.027 59.695 16.152 59.478 16.277 59.43 16.234 59.43 16.466 59.43 16.698 59.396 16.765 59.576 16.868z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M57.939 18.698C58.82 18.243 59.671 17.792 60.381 17.394C61.213 16.928 62.548 16.108 63.918 15.224" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M57.936 19.449L57.936 18.698" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M57.936 18.698L57.079 18.202" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M57.079 18.202C57.331 18.073 57.579 17.943 57.823 17.817" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M59.673 16.814C59.896 16.687 60.146 16.539 60.42 16.376" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M62.063 15.365C62.314 15.207 62.57 15.044 62.828 14.878" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M57.063 18.224L57.079 18.202" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M56.936 17.78L57.301 17.992" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M59.53 16.333L59.896 16.545" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M61.912 14.849L62.279 15.06" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M58.423 18.64L58.813 18.864" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M59.448 18.343L59.307 18.26" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M61.384 16.969L61.746 17.178" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M61.317 17.382L61.133 17.277" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M62.902 15.871L63.353 16.132" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M62.987 16.408L62.786 16.291" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M59.576 16.868C59.557 16.879 59.539 16.888 59.523 16.899C59.347 16.998 59.162 17.1 58.97 17.204C58.792 17.1 58.488 17.181 58.488 17.463C58.304 17.562 58.113 17.664 57.92 17.765C57.417 17.475 57.317 17.472 57.1 17.597C56.884 17.724 56.837 17.681 56.837 17.913C56.837 18.143 56.811 18.21 56.993 18.315C56.877 18.466 56.744 18.64 56.679 18.739C56.597 18.864 56.619 18.841 57.106 19.123C58 19.638 57.886 19.518 58.795 18.993C59.704 18.469 61.948 17.16 62.896 16.612C63.844 16.065 63.92 16.016 63.92 15.224C63.92 15.224 63.063 14.727 63.063 14.727C63.006 14.763 62.951 14.798 62.898 14.833C62.396 14.542 62.295 14.541 62.079 14.666C61.862 14.791 61.814 14.75 61.814 14.98C61.814 15.212 61.834 15.291 62.014 15.396C61.478 15.733 60.968 16.047 60.514 16.32C60.012 16.028 59.911 16.027 59.695 16.152C59.478 16.277 59.43 16.234 59.43 16.466C59.43 16.698 59.396 16.765 59.576 16.868"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M59.576 16.868C59.557 16.879 59.539 16.888 59.523 16.899C59.347 16.998 59.162 17.1 58.97 17.204C58.792 17.1 58.488 17.181 58.488 17.463C58.304 17.562 58.113 17.664 57.92 17.765C57.417 17.475 57.317 17.472 57.1 17.597C56.884 17.724 56.837 17.681 56.837 17.913C56.837 18.143 56.811 18.21 56.993 18.315C56.877 18.466 56.744 18.64 56.679 18.739C56.597 18.864 56.619 18.841 57.106 19.123C58 19.638 57.886 19.518 58.795 18.993C59.704 18.469 61.948 17.16 62.896 16.612C63.844 16.065 63.92 16.016 63.92 15.224L63.063 14.727C63.006 14.763 62.951 14.798 62.898 14.833C62.396 14.542 62.295 14.541 62.079 14.666C61.862 14.791 61.814 14.75 61.814 14.98C61.814 15.212 61.834 15.291 62.014 15.396C61.478 15.733 60.968 16.047 60.514 16.32C60.012 16.028 59.911 16.027 59.695 16.152C59.478 16.277 59.43 16.234 59.43 16.466C59.43 16.698 59.396 16.765 59.576 16.868" />
        </g>
      </g>
    </g>
    <g id="_DATID_013009" class="DATID_013009_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M18.243 41.892L18.548 41.715C18.832 41.551 18.687 41.274 18.553 41.031 18.338 40.636 15.022 34.527 15.022 34.527 13.922 32.573 13.227 31.321 11.371 29.948 10.374 29.207 8.493 27.928 6.432 26.548 6.264 26.434 6.192 26.445 6.038 26.533L5.785 26.679C5.649 26.748 5.615 26.99 5.69 27.257 5.774 27.553 5.831 27.873 5.897 28.24L5.966 28.509 5.987 28.737 6.065 29.367C6.371 30.978 6.561 31.798 6.905 34.479L6.958 35.057 7.113 36.014 7.155 36.38C7.184 36.575 7.206 36.732 7.222 36.862 7.29 37.503 7.403 37.512 7.897 37.686 8.606 37.993 10.323 38.734 10.323 38.734 12.138 39.53 13.978 40.337 15.231 40.814 15.711 40.997 16.117 41.173 16.486 41.335 16.987 41.551 17.422 41.74 17.902 41.88 18.064 41.933 18.176 41.93 18.243 41.892z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M18.222 41.231C18.007 40.835 14.692 34.725 14.692 34.725C13.59 32.771 12.896 31.519 11.041 30.146C10.042 29.405 8.163 28.126 6.102 26.745" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M18.222 41.231C18.324 41.414 18.376 41.61 18.346 41.746" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M5.921 26.664C5.974 26.675 6.033 26.701 6.102 26.745" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M18.243 41.892C18.243 41.892 18.548 41.715 18.548 41.715C18.832 41.551 18.687 41.274 18.553 41.031C18.338 40.636 15.022 34.527 15.022 34.527C13.922 32.573 13.227 31.321 11.371 29.948C10.374 29.207 8.493 27.928 6.432 26.548C6.264 26.434 6.192 26.445 6.038 26.533C6.038 26.533 5.785 26.679 5.785 26.679C5.649 26.748 5.615 26.99 5.69 27.257C5.774 27.553 5.831 27.873 5.897 28.24C5.897 28.24 5.966 28.509 5.966 28.509C5.966 28.509 5.987 28.737 5.987 28.737C5.987 28.737 6.065 29.367 6.065 29.367C6.371 30.978 6.561 31.798 6.905 34.479C6.905 34.479 6.958 35.057 6.958 35.057C6.958 35.057 7.113 36.014 7.113 36.014C7.113 36.014 7.155 36.38 7.155 36.38C7.184 36.575 7.206 36.732 7.222 36.862C7.29 37.503 7.403 37.512 7.897 37.686C8.606 37.993 10.323 38.734 10.323 38.734C12.138 39.53 13.978 40.337 15.231 40.814C15.711 40.997 16.117 41.173 16.486 41.335C16.987 41.551 17.422 41.74 17.902 41.88C18.064 41.933 18.176 41.93 18.243 41.892"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M18.243 41.892L18.548 41.715C18.832 41.551 18.687 41.274 18.553 41.031C18.338 40.636 15.022 34.527 15.022 34.527C13.922 32.573 13.227 31.321 11.371 29.948C10.374 29.207 8.493 27.928 6.432 26.548C6.264 26.434 6.192 26.445 6.038 26.533L5.785 26.679C5.649 26.748 5.615 26.99 5.69 27.257C5.774 27.553 5.831 27.873 5.897 28.24L5.966 28.509L5.987 28.737L6.065 29.367C6.371 30.978 6.561 31.798 6.905 34.479L6.958 35.057L7.113 36.014L7.155 36.38C7.184 36.575 7.206 36.732 7.222 36.862C7.29 37.503 7.403 37.512 7.897 37.686C8.606 37.993 10.323 38.734 10.323 38.734C12.138 39.53 13.978 40.337 15.231 40.814C15.711 40.997 16.117 41.173 16.486 41.335C16.987 41.551 17.422 41.74 17.902 41.88C18.064 41.933 18.176 41.93 18.243 41.892" />
        </g>
      </g>
    </g>
    <g id="_DATID_075008" class="DATID_075008_V2L2St2Sz680-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M66.246 103.457L66.246 103.349 65.855 103.114 65.865 102.664C65.865 102.664 65.9 102.584 65.769 102.507 65.635 102.43 65.435 102.319 65.435 102.319 65.435 102.319 65.299 102.257 65.087 102.381L63.738 103.159C63.738 103.159 63.509 103.233 63.509 103.68L61.493 104.843C61.493 104.843 61.335 104.968 61.335 105.388L60.77 105.713C60.77 105.713 60.646 105.757 60.646 106.034 60.646 106.308 60.841 106.257 60.841 106.257L60.515 106.445 56.484 107.897 55.475 107.581C55.475 107.581 54.841 107.509 54.765 107.552L54.52 107.411 53.438 107.79 48.167 104.724 47.365 105.186 47.365 105.421 47.822 105.683 47.822 106.272 47.365 106.536 47.365 106.769 53.085 110.072 53.085 110.156C53.085 110.156 53.09 110.29 53.201 110.355 53.314 110.42 53.317 110.412 53.317 110.412 53.317 110.412 53.43 110.463 53.542 110.399L53.862 110.215 56.363 110.215 56.363 110.376 56.914 110.376C56.914 110.376 57.64 110.093 57.64 109.659 57.64 109.224 57.655 109.224 57.655 109.224L59.198 108.662 59.198 108.98 58.46 109.406 58.46 109.902 58.734 110.06 60.604 108.98 60.604 108.734 62.1 107.602 62.527 107.397 62.517 108.852C62.517 108.852 62.499 109.016 62.615 109.082 62.729 109.149 62.78 109.197 63.027 109.054L63.609 108.718C63.609 108.718 63.716 108.665 63.716 108.506L63.716 107.853 63.898 107.957 63.898 108.26C63.898 108.26 64.102 108.962 64.912 109.43 65.406 109.715 65.751 109.935 65.947 110.063 65.968 110.078 65.959 110.085 65.971 110.108 66.15 110.418 66.441 110.585 66.62 110.483 66.62 110.483 66.789 110.552 66.856 110.561L67.329 110.618C67.329 110.618 68.64 110.626 68.64 109.507L68.64 109.1C68.795 109.016 68.871 108.903 68.871 108.789L68.871 108.057C68.871 107.819 68.537 107.625 68.126 107.625 67.712 107.625 67.379 107.819 67.379 108.057L67.379 108.64C67.177 108.496 66.986 108.471 66.852 108.549L66.615 108.685C66.615 108.685 66.15 108.65 65.852 108.412 65.555 108.173 64.974 107.621 64.974 107.621L65.465 107.337 65.465 105.733 68.703 103.862C68.703 103.862 68.923 103.817 69.387 103.382 69.85 102.947 71.837 101.135 71.837 101.135L72.069 101.403C72.069 101.403 72.26 101.649 72.498 101.522 72.56 101.489 72.625 101.396 72.625 101.179 72.625 100.96 72.625 100.022 72.625 100.022L72.73 99.564 72.751 97.477 72.275 97.201C72.275 97.201 72.293 97.15 72.207 97.099 72.121 97.049 72.081 97.037 72.081 97.037L71.816 97.266 66.566 94.234C66.566 94.234 66.436 94.18 66.364 94.221L65.79 94.552C65.561 94.685 65.51 94.933 65.51 94.933L65.51 96.091 71.316 99.444 68.394 102.108C68.394 102.108 67.944 102.489 67.524 102.731 67.102 102.974 66.246 103.457 66.246 103.457z" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="47.365" y1="105.186" x2="53.085" y2="108.489" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.085" y1="108.912" x2="52.715" y2="108.697" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="52.128" y1="108.358" x2="50.933" y2="107.67" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="50.347" y1="107.331" x2="48.408" y2="106.212" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="47.365" y1="106.536" x2="53.085" y2="109.837" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.085" y1="110.072" x2="53.085" y2="108.722" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M48.307 105.144C48.307 105.084 48.222 105.034 48.115 105.034C48.009 105.034 47.923 105.084 47.923 105.144C47.923 105.206 48.009 105.256 48.115 105.256C48.222 105.256 48.307 105.206 48.307 105.144" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M47.822 106.537C47.822 106.631 47.954 106.706 48.115 106.706C48.277 106.706 48.408 106.631 48.408 106.537" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="48.408" y1="106.537" x2="48.408" y2="106.022" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="47.822" y1="105.683" x2="53.085" y2="108.722" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="47.822" y1="106.537" x2="47.822" y2="106.272" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M56.914 110.376L56.914 110.376" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.898" y1="107.957" x2="63.898" y2="108.26" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M66.945 110.295C67.125 110.191 67.125 109.855 66.945 109.546C66.766 109.234 66.475 109.066 66.296 109.171" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="65.971" y1="109.358" x2="66.296" y2="109.171" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="66.62" y1="110.483" x2="66.945" y2="110.295" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M67.596 108.361C67.742 108.445 67.933 108.487 68.126 108.487C68.331 108.487 68.519 108.439 68.653 108.361" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M50.832 106.602C50.832 106.542 50.746 106.492 50.64 106.492C50.533 106.492 50.448 106.542 50.448 106.602C50.448 106.664 50.533 106.713 50.64 106.713C50.746 106.713 50.832 106.664 50.832 106.602" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M50.347 107.995C50.347 108.088 50.478 108.165 50.64 108.165C50.802 108.165 50.933 108.088 50.933 107.995" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="50.933" y1="107.995" x2="50.933" y2="107.48" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="50.347" y1="107.73" x2="50.347" y2="107.141" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="50.347" y1="107.995" x2="50.347" y2="107.73" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M52.613 107.631C52.613 107.569 52.527 107.519 52.421 107.519C52.314 107.519 52.228 107.569 52.228 107.631C52.228 107.692 52.314 107.742 52.421 107.742C52.527 107.742 52.613 107.692 52.613 107.631" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M52.128 109.024C52.128 109.117 52.259 109.192 52.421 109.192C52.584 109.192 52.715 109.117 52.715 109.024" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="52.715" y1="109.024" x2="52.715" y2="108.509" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="52.128" y1="108.759" x2="52.128" y2="108.17" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="52.128" y1="109.024" x2="52.128" y2="108.759" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="66.165" y1="108.944" x2="66.615" y2="108.685" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="67.379" y1="108.789" x2="67.379" y2="108.64" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M67.611 109.123C67.611 109.287 67.84 109.42 68.126 109.42C68.409 109.42 68.64 109.287 68.64 109.123" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="67.611" y1="109.123" x2="67.611" y2="109.1" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M67.379 108.789C67.379 109.027 67.712 109.219 68.126 109.219C68.325 109.219 68.507 109.174 68.64 109.1" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M66.62 110.483C66.71 110.43 66.756 110.32 66.756 110.186" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M66.296 109.39C66.17 109.316 66.055 109.31 65.971 109.358C65.802 109.456 65.793 109.763 65.947 110.063" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M66.856 110.561C66.942 110.57 67.018 110.555 67.075 110.521" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M67.075 110.521C67.328 110.376 67.328 109.906 67.075 109.469C66.825 109.034 66.418 108.799 66.165 108.944C66.042 109.016 65.978 109.168 65.977 109.355" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M66.246 103.457C66.246 103.457 66.246 103.349 66.246 103.349C66.246 103.349 65.855 103.114 65.855 103.114C65.855 103.114 65.865 102.664 65.865 102.664C65.865 102.664 65.9 102.584 65.769 102.507C65.635 102.43 65.435 102.319 65.435 102.319C65.435 102.319 65.299 102.257 65.087 102.381C65.087 102.381 63.738 103.159 63.738 103.159C63.738 103.159 63.509 103.233 63.509 103.68C63.509 103.68 61.493 104.843 61.493 104.843C61.493 104.843 61.335 104.968 61.335 105.388C61.335 105.388 60.77 105.713 60.77 105.713C60.77 105.713 60.646 105.757 60.646 106.034C60.646 106.308 60.841 106.257 60.841 106.257C60.841 106.257 60.515 106.445 60.515 106.445C60.515 106.445 56.484 107.897 56.484 107.897C56.484 107.897 55.475 107.581 55.475 107.581C55.475 107.581 54.841 107.509 54.765 107.552C54.765 107.552 54.52 107.411 54.52 107.411C54.52 107.411 53.438 107.79 53.438 107.79C53.438 107.79 48.167 104.724 48.167 104.724C48.167 104.724 47.365 105.186 47.365 105.186C47.365 105.186 47.365 105.421 47.365 105.421C47.365 105.421 47.822 105.683 47.822 105.683C47.822 105.683 47.822 106.272 47.822 106.272C47.822 106.272 47.365 106.536 47.365 106.536C47.365 106.536 47.365 106.769 47.365 106.769C47.365 106.769 53.085 110.072 53.085 110.072C53.085 110.072 53.085 110.156 53.085 110.156C53.085 110.156 53.09 110.29 53.201 110.355C53.314 110.42 53.317 110.412 53.317 110.412C53.317 110.412 53.43 110.463 53.542 110.399C53.542 110.399 53.862 110.215 53.862 110.215C53.862 110.215 56.363 110.215 56.363 110.215C56.363 110.215 56.363 110.376 56.363 110.376C56.363 110.376 56.914 110.376 56.914 110.376C56.914 110.376 57.64 110.093 57.64 109.659C57.64 109.224 57.655 109.224 57.655 109.224C57.655 109.224 59.198 108.662 59.198 108.662C59.198 108.662 59.198 108.98 59.198 108.98C59.198 108.98 58.46 109.406 58.46 109.406C58.46 109.406 58.46 109.902 58.46 109.902C58.46 109.902 58.734 110.06 58.734 110.06C58.734 110.06 60.604 108.98 60.604 108.98C60.604 108.98 60.604 108.734 60.604 108.734C60.604 108.734 62.1 107.602 62.1 107.602C62.1 107.602 62.527 107.397 62.527 107.397C62.527 107.397 62.517 108.852 62.517 108.852C62.517 108.852 62.499 109.016 62.615 109.082C62.729 109.149 62.78 109.197 63.027 109.054C63.027 109.054 63.609 108.718 63.609 108.718C63.609 108.718 63.716 108.665 63.716 108.506C63.716 108.506 63.716 107.853 63.716 107.853C63.716 107.853 63.898 107.957 63.898 107.957C63.898 107.957 63.898 108.26 63.898 108.26C63.898 108.26 64.102 108.962 64.912 109.43C65.406 109.715 65.751 109.935 65.947 110.063C65.968 110.078 65.959 110.085 65.971 110.108C66.15 110.418 66.441 110.585 66.62 110.483C66.62 110.483 66.789 110.552 66.856 110.561C66.856 110.561 67.329 110.618 67.329 110.618C67.329 110.618 68.64 110.626 68.64 109.507C68.64 109.507 68.64 109.1 68.64 109.1C68.795 109.016 68.871 108.903 68.871 108.789C68.871 108.789 68.871 108.057 68.871 108.057C68.871 107.819 68.537 107.625 68.126 107.625C67.712 107.625 67.379 107.819 67.379 108.057C67.379 108.057 67.379 108.64 67.379 108.64C67.177 108.496 66.986 108.471 66.852 108.549C66.852 108.549 66.615 108.685 66.615 108.685C66.615 108.685 66.15 108.65 65.852 108.412C65.555 108.173 64.974 107.621 64.974 107.621C64.974 107.621 65.465 107.337 65.465 107.337C65.465 107.337 65.465 105.733 65.465 105.733C65.465 105.733 68.703 103.862 68.703 103.862C68.703 103.862 68.923 103.817 69.387 103.382C69.85 102.947 71.837 101.135 71.837 101.135C71.837 101.135 72.069 101.403 72.069 101.403C72.069 101.403 72.26 101.649 72.498 101.522C72.56 101.489 72.625 101.396 72.625 101.179C72.625 100.96 72.625 100.022 72.625 100.022C72.625 100.022 72.73 99.564 72.73 99.564C72.73 99.564 72.751 97.477 72.751 97.477C72.751 97.477 72.275 97.201 72.275 97.201C72.275 97.201 72.293 97.15 72.207 97.099C72.121 97.049 72.081 97.037 72.081 97.037C72.081 97.037 71.816 97.266 71.816 97.266C71.816 97.266 66.566 94.234 66.566 94.234C66.566 94.234 66.436 94.18 66.364 94.221C66.364 94.221 65.79 94.552 65.79 94.552C65.561 94.685 65.51 94.933 65.51 94.933C65.51 94.933 65.51 96.091 65.51 96.091C65.51 96.091 71.316 99.444 71.316 99.444C71.316 99.444 68.394 102.108 68.394 102.108C68.394 102.108 67.944 102.489 67.524 102.731C67.102 102.974 66.246 103.457 66.246 103.457"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M66.246 103.457L66.246 103.349L65.855 103.114L65.865 102.664C65.865 102.664 65.9 102.584 65.769 102.507C65.635 102.43 65.435 102.319 65.435 102.319C65.435 102.319 65.299 102.257 65.087 102.381L63.738 103.159C63.738 103.159 63.509 103.233 63.509 103.68L61.493 104.843C61.493 104.843 61.335 104.968 61.335 105.388L60.77 105.713C60.77 105.713 60.646 105.757 60.646 106.034C60.646 106.308 60.841 106.257 60.841 106.257L60.515 106.445L56.484 107.897L55.475 107.581C55.475 107.581 54.841 107.509 54.765 107.552L54.52 107.411L53.438 107.79L48.167 104.724L47.365 105.186L47.365 105.421L47.822 105.683L47.822 106.272L47.365 106.536L47.365 106.769L53.085 110.072L53.085 110.156C53.085 110.156 53.09 110.29 53.201 110.355C53.314 110.42 53.317 110.412 53.317 110.412C53.317 110.412 53.43 110.463 53.542 110.399L53.862 110.215L56.363 110.215L56.363 110.376L56.914 110.376C56.914 110.376 57.64 110.093 57.64 109.659C57.64 109.224 57.655 109.224 57.655 109.224L59.198 108.662L59.198 108.98L58.46 109.406L58.46 109.902L58.734 110.06L60.604 108.98L60.604 108.734L62.1 107.602L62.527 107.397L62.517 108.852C62.517 108.852 62.499 109.016 62.615 109.082C62.729 109.149 62.78 109.197 63.027 109.054L63.609 108.718C63.609 108.718 63.716 108.665 63.716 108.506L63.716 107.853L63.898 107.957L63.898 108.26C63.898 108.26 64.102 108.962 64.912 109.43C65.406 109.715 65.751 109.935 65.947 110.063C65.968 110.078 65.959 110.085 65.971 110.108C66.15 110.418 66.441 110.585 66.62 110.483C66.62 110.483 66.789 110.552 66.856 110.561L67.329 110.618C67.329 110.618 68.64 110.626 68.64 109.507L68.64 109.1C68.795 109.016 68.871 108.903 68.871 108.789L68.871 108.057C68.871 107.819 68.537 107.625 68.126 107.625C67.712 107.625 67.379 107.819 67.379 108.057L67.379 108.64C67.177 108.496 66.986 108.471 66.852 108.549L66.615 108.685C66.615 108.685 66.15 108.65 65.852 108.412C65.555 108.173 64.974 107.621 64.974 107.621L65.465 107.337L65.465 105.733L68.703 103.862C68.703 103.862 68.923 103.817 69.387 103.382C69.85 102.947 71.837 101.135 71.837 101.135L72.069 101.403C72.069 101.403 72.26 101.649 72.498 101.522C72.56 101.489 72.625 101.396 72.625 101.179C72.625 100.96 72.625 100.022 72.625 100.022L72.73 99.564L72.751 97.477L72.275 97.201C72.275 97.201 72.293 97.15 72.207 97.099C72.121 97.049 72.081 97.037 72.081 97.037L71.816 97.266L66.566 94.234C66.566 94.234 66.436 94.18 66.364 94.221L65.79 94.552C65.561 94.685 65.51 94.933 65.51 94.933L65.51 96.091L71.316 99.444L68.394 102.108C68.394 102.108 67.944 102.489 67.524 102.731C67.102 102.974 66.246 103.457 66.246 103.457" />
        </g>
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.527" y1="107.397" x2="65.465" y2="105.733" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.974" y1="106.01" x2="64.974" y2="107.621" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.819" y1="106.099" x2="64.819" y2="107.709" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.034" y1="106.543" x2="64.034" y2="107.927" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M63.917 107.966C63.917 107.966 64.123 108.07 64.31 107.962C64.495 107.855 64.819 107.709 64.819 107.709L64.974 107.621" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.716" y1="107.853" x2="63.716" y2="106.722" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.453" y1="108.623" x2="63.453" y2="106.873" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.838" y1="108.971" x2="62.838" y2="107.221" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="58.46" y1="109.406" x2="58.734" y2="109.566" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="58.734" y1="110.06" x2="58.734" y2="109.566" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M59.472 109.138L58.734 109.566" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M59.472 108.819L59.472 109.138" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="59.472" y1="108.819" x2="59.198" y2="108.662" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="59.198" y1="108.662" x2="62.1" y2="107.602" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="59.472" y1="108.819" x2="59.776" y2="108.646" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M59.776 108.646L59.776 108.965" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="59.776" y1="108.965" x2="60.604" y2="108.486" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="60.604" y1="108.734" x2="60.604" y2="108.486" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="59.472" y1="109.138" x2="59.198" y2="108.98" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="59.776" y1="108.646" x2="59.57" y2="108.527" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="59.776" y1="108.647" x2="60.331" y2="108.328" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="60.604" y1="108.486" x2="60.331" y2="108.328" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="60.567" y1="108.465" x2="61.341" y2="107.881" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.28" y1="110.132" x2="53.28" y2="108.57" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M53.317 110.412C53.317 110.412 53.28 110.356 53.28 110.132" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M53.28 108.57C53.28 108.57 53.274 108.375 53.673 108.2C54.073 108.027 54.899 107.757 54.899 107.757C54.899 107.757 55.204 107.728 55.536 107.823C55.871 107.918 56.487 108.132 56.487 108.132C56.487 108.132 57.552 108.513 57.552 109.259C57.552 110.006 57.362 110.117 57.362 110.117" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M57.655 109.224C57.655 109.224 57.734 108.311 56.484 107.897" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M53.085 108.722C53.085 108.722 53.085 108.543 53.085 108.489C53.085 108.435 53.079 108.325 53.408 108.137C53.63 108.01 54.869 107.612 54.869 107.612L54.765 107.552" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="54.682" y1="107.516" x2="53.35" y2="107.992" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.35" y1="107.992" x2="53.185" y2="107.897" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.185" y1="107.897" x2="53.438" y2="107.79" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.185" y1="107.897" x2="53.185" y2="108.293" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.35" y1="107.992" x2="53.35" y2="108.171" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="54.862" y1="107.935" x2="53.862" y2="108.346" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="55.356" y1="107.95" x2="56.393" y2="108.263" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="56.393" y1="108.263" x2="56.393" y2="108.753" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.862" y1="108.346" x2="56.393" y2="108.263" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M54.862 107.935C54.862 107.935 55.061 107.862 55.356 107.95" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.862" y1="108.753" x2="56.393" y2="108.753" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="56.363" y1="110.215" x2="56.363" y2="109.902" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="56.363" y1="109.902" x2="56.722" y2="109.902" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="56.722" y1="109.902" x2="56.722" y2="108.817" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="56.722" y1="108.817" x2="56.393" y2="108.817" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="56.393" y1="108.753" x2="56.393" y2="108.817" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.862" y1="108.346" x2="53.862" y2="108.822" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.743" y1="110.058" x2="56.363" y2="110.058" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.743" y1="110.284" x2="53.743" y2="109.903" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.743" y1="109.903" x2="53.466" y2="109.903" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.466" y1="109.903" x2="53.466" y2="108.822" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.466" y1="108.822" x2="53.862" y2="108.822" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M54.286 109.349C54.286 109.24 54.191 109.153 54.073 109.153C53.954 109.153 53.859 109.24 53.859 109.349C53.859 109.403 53.883 109.451 53.92 109.487" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M54.223 109.487C54.265 109.45 54.286 109.399 54.286 109.349" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M56.368 109.349C56.368 109.24 56.271 109.153 56.154 109.153C56.034 109.153 55.939 109.24 55.939 109.349C55.939 109.403 55.963 109.451 56.002 109.487" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M56.304 109.487C56.347 109.45 56.368 109.399 56.368 109.349" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="60.841" y1="106.257" x2="63.374" y2="104.796" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.326" y1="104.823" x2="63.326" y2="104.237" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.344" y1="104.227" x2="64.036" y2="104.626" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.036" y1="104.626" x2="64.036" y2="105.013" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.036" y1="105.013" x2="63.863" y2="104.912" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M63.863 104.912C63.863 104.912 63.783 104.876 63.554 105.009C63.323 105.141 62.292 105.745 62.292 105.745C62.292 105.745 61.866 105.974 61.435 106.144C61.003 106.307 60.558 106.412 60.558 106.412" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="61.335" y1="105.388" x2="63.509" y2="104.132" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.509" y1="103.68" x2="63.509" y2="104.132" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.509" y1="104.132" x2="63.896" y2="104.355" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.896" y1="104.355" x2="63.896" y2="103.703" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M63.896 103.703C63.896 103.703 63.878 103.552 64.105 103.423" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.105" y1="103.423" x2="65.65" y2="102.531" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.65 102.531C65.65 102.531 65.718 102.48 65.793 102.522" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="63.896" y1="104.28" x2="64.266" y2="104.493" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.036" y1="104.626" x2="64.266" y2="104.493" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.036" y1="105.013" x2="64.266" y2="104.879" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.266" y1="104.493" x2="64.266" y2="104.879" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.266" y1="104.597" x2="65.415" y2="103.935" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="65.572" y1="103.311" x2="65.944" y2="103.525" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="65.944" y1="103.525" x2="65.944" y2="103.912" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="65.944" y1="103.525" x2="66.246" y2="103.349" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.415 103.935C65.415 103.935 65.677 103.757 65.944 103.912" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="65.944" y1="103.912" x2="66.246" y2="103.736" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="66.246" y1="103.736" x2="66.246" y2="103.457" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.033" y1="104.358" x2="64.706" y2="103.793" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M64.706 103.793C64.706 103.793 64.778 103.664 65.331 103.983" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="65.162" y1="103.748" x2="65.162" y2="103.191" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="65.572" y1="103.311" x2="65.296" y2="103.152" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.033" y1="104.358" x2="64.033" y2="103.826" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M64.033 103.826C64.033 103.826 64.037 103.656 64.263 103.525" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="64.263" y1="103.525" x2="65.512" y2="102.805" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M65.512 102.805C65.512 102.805 65.662 102.718 65.662 102.888" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="65.662" y1="102.888" x2="65.662" y2="103.362" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="65.662" y1="103.362" x2="65.855" y2="103.114" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M61.052 106.135C61.095 106.111 61.095 106.03 61.052 105.954C61.009 105.879 60.939 105.84 60.896 105.864C60.853 105.89 60.853 105.971 60.896 106.046C60.939 106.12 61.009 106.161 61.052 106.135" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M61.716 105.754C61.759 105.728 61.759 105.647 61.716 105.572C61.673 105.496 61.601 105.456 61.558 105.481C61.515 105.507 61.515 105.587 61.558 105.662C61.601 105.737 61.673 105.778 61.716 105.754" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.055 104.971C62.055 104.971 61.933 104.826 62.259 104.638C62.585 104.45 62.661 104.623 62.661 104.623" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.661" y1="104.623" x2="62.661" y2="104.349" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.661" y1="104.349" x2="63.509" y2="103.858" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.661" y1="104.349" x2="62.506" y2="104.26" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.221" y1="99.388" x2="71.221" y2="98.4" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M71.221 98.4C71.221 98.4 71.019 97.95 71.337 97.682C71.656 97.412 71.816 97.266 71.816 97.266" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M71.316 99.444L71.46 99.281" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.161 100.004C72.418 100.46 72.099 100.912 72.099 100.912L71.837 101.135" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M71.46 99.281C71.46 99.281 71.632 99.132 72.161 100.004" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.418" y1="101.42" x2="72.418" y2="100.266" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.418 100.266C72.418 100.266 72.403 99.834 72.048 99.429C71.692 99.022 71.221 98.4 71.221 98.4" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.625 100.022C72.625 100.022 72.552 99.772 72.183 99.293C71.813 98.814 71.45 98.35 71.45 98.35" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M71.45 98.35C71.45 98.35 71.262 98.093 71.558 97.817C71.855 97.542 72.275 97.201 72.275 97.201" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.45" y1="98.35" x2="72.531" y2="97.364" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.563" y1="98.495" x2="72.365" y2="97.762" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.365" y1="97.762" x2="72.2" y2="97.665" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.365" y1="97.762" x2="72.746" y2="97.981" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.365" y1="97.762" x2="72.115" y2="99.206" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.611" y1="97.903" x2="72.335" y2="99.503" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="65.816" y1="94.713" x2="71.021" y2="97.718" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="70.935" y1="98.021" x2="65.721" y2="95.01" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.715 98.62C70.778 98.584 70.778 98.465 70.715 98.355C70.652 98.245 70.549 98.186 70.486 98.224C70.453 98.242 70.438 98.281 70.438 98.328" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M70.599 98.608C70.644 98.635 70.686 98.638 70.715 98.62" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M66.447 96.156C66.512 96.12 66.512 96.001 66.447 95.893C66.385 95.783 66.281 95.724 66.218 95.76C66.186 95.778 66.171 95.816 66.171 95.864" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M66.334 96.146C66.378 96.171 66.418 96.174 66.447 96.156" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M66.548 94.674C66.548 94.638 66.522 94.605 66.481 94.581" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M66.165 94.581C66.12 94.608 66.099 94.641 66.099 94.674C66.099 94.745 66.198 94.804 66.322 94.804C66.447 94.804 66.548 94.745 66.548 94.674" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M69.147 96.174C69.147 96.14 69.123 96.106 69.081 96.084" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M68.765 96.084C68.721 96.108 68.698 96.141 68.698 96.174C68.698 96.246 68.799 96.305 68.924 96.305C69.048 96.305 69.147 96.246 69.147 96.174" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M70.897 97.186C70.897 97.15 70.873 97.117 70.831 97.093" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M70.513 97.093C70.469 97.118 70.447 97.152 70.447 97.186C70.447 97.257 70.549 97.314 70.673 97.314C70.796 97.314 70.897 97.257 70.897 97.186" />
      </g>
    </g>
    <g>
      <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
        d="M9.016 35.871C8.329 36.393 7.706 36.86 7.272 37.173 7.251 37.09 7.235 36.985 7.222 36.856 7.205 36.725 7.182 36.567 7.155 36.372L7.112 36.006 6.956 35.049C6.956 35.049 6.952 34.996 6.94 34.872 8.74 33.729 11.362 31.836 12.236 31.157 12.679 31.597 13.042 32.046 13.378 32.531 12.806 32.96 11.557 33.926 10.287 34.901 10.346 34.963 10.4 35.036 10.449 35.122 10.705 35.046 11.012 35.22 11.205 35.573L11.226 35.567C11.541 35.46 11.926 35.756 12.086 36.228 12.227 36.652 12.141 37.087 11.874 37.24 11.852 37.253 11.827 37.265 11.8 37.274 11.542 37.359 11.228 37.183 11.032 36.826L11.013 36.834C10.762 36.915 10.455 36.75 10.257 36.405 10.005 36.481 9.698 36.308 9.506 35.96L9.484 35.968C9.335 36.018 9.167 35.978 9.016 35.871z" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M11.079 36.497C11.194 36.359 11.22 36.128 11.16 35.902" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M11.064 35.667C10.929 35.433 10.727 35.317 10.553 35.35" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M10.403 35.429C10.259 35.567 10.224 35.837 10.304 36.097" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M10.407 36.323C10.553 36.552 10.762 36.655 10.935 36.598" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M11.175 36.734C11.319 36.981 11.539 37.098 11.721 37.039C11.949 36.963 12.042 36.624 11.928 36.28C11.813 35.939 11.534 35.723 11.304 35.801C11.089 35.872 10.994 36.176 11.079 36.497" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M9.593 35.566C9.673 35.42 9.682 35.222 9.63 35.029" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M9.647 35.866C9.848 35.67 9.891 35.29 9.772 34.929" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M9.673 34.701C9.458 34.31 9.099 34.143 8.836 34.292" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M9.362 35.558C9.372 35.621 9.388 35.68 9.407 35.732" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M10.407 36.323C10.628 36.134 10.681 35.734 10.553 35.35" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M10.198 34.82C10.028 34.683 9.837 34.637 9.673 34.701" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M9.069 34.478C9.039 34.481 9.012 34.485 8.99 34.493C8.76 34.57 8.667 34.911 8.782 35.253C8.897 35.594 9.176 35.81 9.406 35.732" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M11.175 36.734C11.375 36.539 11.419 36.162 11.304 35.801" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M11.064 35.667C10.864 35.86 10.82 36.237 10.935 36.598" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M8.942 35.808C8.808 35.688 8.695 35.514 8.625 35.305C8.484 34.881 8.57 34.448 8.836 34.292" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M9.069 34.478C9.083 34.478 9.099 34.478 9.115 34.479C9.263 34.493 9.42 34.606 9.532 34.795C9.364 34.959 9.307 35.253 9.362 35.558" />
      <path stroke-width="0.12" stroke-linejoin="round"
        d="M10.057 34.965C9.955 34.909 9.856 34.899 9.774 34.929C9.557 35.002 9.461 35.307 9.55 35.633" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M9.647 35.866C9.782 36.095 9.983 36.21 10.156 36.176" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M10.304 36.097C10.449 35.96 10.484 35.691 10.403 35.429" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M10.301 35.204C10.23 35.094 10.144 35.012 10.057 34.965" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M10.102 35.966C10.115 36.042 10.134 36.113 10.156 36.176" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M10.301 35.204C10.119 35.359 10.051 35.654 10.102 35.966" />
      <path stroke-width="0.12" stroke-linejoin="round" d="M9.55 35.633C9.567 35.613 9.583 35.587 9.593 35.566" />
      <path stroke-width="0.25" stroke-linejoin="round"
        d="M9.016 35.871C8.329 36.393 7.706 36.86 7.272 37.173C7.251 37.09 7.235 36.985 7.222 36.856C7.205 36.725 7.182 36.567 7.155 36.372L7.112 36.006L6.956 35.049C6.956 35.049 6.952 34.996 6.94 34.872C8.74 33.729 11.362 31.836 12.236 31.157C12.679 31.597 13.042 32.046 13.378 32.531C12.806 32.96 11.557 33.926 10.287 34.901C10.346 34.963 10.4 35.036 10.449 35.122C10.705 35.046 11.012 35.22 11.205 35.573L11.226 35.567C11.541 35.46 11.926 35.756 12.086 36.228C12.227 36.652 12.141 37.087 11.874 37.24C11.852 37.253 11.827 37.265 11.8 37.274C11.542 37.359 11.228 37.183 11.032 36.826L11.013 36.834C10.762 36.915 10.455 36.75 10.257 36.405C10.005 36.481 9.698 36.308 9.506 35.96L9.484 35.968C9.335 36.018 9.167 35.978 9.016 35.871" />
    </g>
    <g>
      <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
        d="M6.645 32.6C6.724 33.118 6.809 33.722 6.905 34.472L6.905 34.472C8.704 33.329 11.114 31.6 11.989 30.92 11.708 30.661 11.396 30.402 11.041 30.138 10.923 30.051 10.794 29.957 10.653 29.856 9.776 30.536 8.445 31.46 6.645 32.6z" />
      <path stroke-width="0.25" stroke-linejoin="round"
        d="M6.645 32.6C6.724 33.118 6.809 33.722 6.905 34.472L6.905 34.472C8.704 33.329 11.114 31.6 11.989 30.92C11.708 30.661 11.396 30.402 11.041 30.138C10.923 30.051 10.794 29.957 10.653 29.856C9.776 30.536 8.445 31.46 6.645 32.6" />
    </g>
    <g id="_DATID_012007" class="DATID_012007_V2L1St2Sz356-S##">

      <g>
        <g id="DATID_000000" class="DATID_000000_V2L1St1Sz32-SK#">
          <path
            d="M12.311 33.344C12.759 33.002 13.131 32.716 13.378 32.531C13.042 32.046 12.679 31.597 12.236 31.157C11.75 31.535 10.724 32.286 9.602 33.076C9.602 33.076 9.602 32.656 9.602 32.656C10.618 31.945 11.522 31.283 11.989 30.92C11.708 30.661 11.396 30.402 11.041 30.138C10.923 30.051 10.794 29.957 10.653 29.856C9.776 30.536 8.445 31.46 6.645 32.6C6.724 33.118 6.809 33.722 6.905 34.472C6.905 34.472 6.905 34.472 6.905 34.472C7.727 33.951 8.676 33.305 9.548 32.695C9.548 32.695 9.548 33.113 9.548 33.113C8.664 33.734 7.724 34.375 6.94 34.872C6.952 34.996 6.956 35.049 6.956 35.049C6.956 35.049 7.112 36.006 7.112 36.006C7.112 36.006 7.155 36.372 7.155 36.372C7.182 36.567 7.205 36.725 7.222 36.856C7.235 36.985 7.251 37.09 7.272 37.173C7.706 36.86 8.329 36.393 9.016 35.871C9.167 35.978 9.335 36.018 9.484 35.968C9.492 35.965 9.5 35.963 9.506 35.96C9.698 36.308 10.005 36.481 10.257 36.405C10.455 36.75 10.762 36.915 11.013 36.834C11.021 36.832 11.028 36.829 11.032 36.826C11.228 37.183 11.542 37.359 11.8 37.274C11.827 37.265 11.852 37.253 11.874 37.24C12.141 37.087 12.227 36.652 12.086 36.228C11.926 35.756 11.541 35.46 11.226 35.567C11.218 35.569 11.211 35.57 11.205 35.573C11.012 35.22 10.705 35.046 10.449 35.122C10.4 35.036 10.346 34.963 10.287 34.901C10.949 34.392 11.753 33.774 12.311 33.344"
            stroke-width="0.12" />
        </g>
      </g>
    </g>
    <polygon stroke="none" fill="#FFFFFF" points="32.933 38.45 31.963 42.073 33.905 43.194" />
    <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" d="M23.824 40.942L32.933 43.86L32.933 42.274" />
    <polygon stroke="none" fill="#000000" points="32.933 38.914 32.098 42.03 33.768 42.996" />
    <path stroke-width="0.12" stroke-linejoin="round" d="M23.824 40.942L32.933 43.86L32.933 42.274" />
    <polygon stroke="none" fill="#FFFFFF" points="32.933 50.076 33.905 46.454 31.963 45.332" />
    <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" d="M23.824 41.594L32.933 44.514L32.933 46.253" />
    <polygon stroke="none" fill="#000000" points="32.933 49.613 33.768 46.496 32.098 45.53" />
    <path stroke-width="0.12" stroke-linejoin="round" d="M23.824 41.594L32.933 44.514L32.933 46.253" />
    <polygon stroke="none" fill="#FFFFFF" points="45.271 45.03 44.301 49.774 46.241 48.654" />
    <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" d="M52.959 47.633L45.271 51.084L45.271 48.854" />
    <polygon stroke="none" fill="#000000" points="45.271 45.494 44.435 49.576 46.106 48.612" />
    <path stroke-width="0.12" stroke-linejoin="round" d="M52.959 47.633L45.271 51.084L45.271 48.854" />
    <polygon stroke="none" fill="#FFFFFF" points="45.271 57.219 46.241 52.475 44.301 53.595" />
    <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" d="M52.959 48.204L45.271 51.655L45.271 53.395" />
    <polygon stroke="none" fill="#000000" points="45.271 56.755 46.106 52.673 44.435 53.637" />
    <path stroke-width="0.12" stroke-linejoin="round" d="M52.959 48.204L45.271 51.655L45.271 53.395" />
    <polygon stroke="none" fill="#FFFFFF" points="37.362 29.725 36.392 34.469 38.334 33.348" />
    <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" d="M42.429 32.853L37.362 35.777L37.362 33.548" />
    <polygon stroke="none" fill="#000000" points="37.362 30.189 36.527 34.271 38.198 33.307" />
    <path stroke-width="0.12" stroke-linejoin="round" d="M42.429 32.853L37.362 35.777L37.362 33.548" />
    <g id="_DATID_065006" class="DATID_065006_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" fill="#FFFFFF"
          d="M80.072 64.042C80.072 64.231 79.936 64.42 79.796 64.501L72.12 68.93C71.915 69.049 71.59 68.915 71.59 68.588L71.59 66.325C71.59 66.159 71.707 65.95 71.875 65.861L79.471 61.475C79.802 61.284 80.072 61.436 80.072 61.78L80.072 64.042z" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M80.072 64.042C80.072 64.231 79.936 64.42 79.796 64.501C79.796 64.501 72.12 68.93 72.12 68.93C71.915 69.049 71.59 68.915 71.59 68.588C71.59 68.588 71.59 66.325 71.59 66.325C71.59 66.159 71.707 65.95 71.875 65.861C71.875 65.861 79.471 61.475 79.471 61.475C79.802 61.284 80.072 61.436 80.072 61.78C80.072 61.78 80.072 64.042 80.072 64.042"
            stroke-width="0.25" />
          <path stroke-width="0.25" fill="#FFFFFF"
            d="M80.072 64.042C80.072 64.231 79.936 64.42 79.796 64.501L72.12 68.93C71.915 69.049 71.59 68.915 71.59 68.588L71.59 66.325C71.59 66.159 71.707 65.95 71.875 65.861L79.471 61.475C79.802 61.284 80.072 61.436 80.072 61.78L80.072 64.042z" />
        </g>
        <path stroke-width="0.12"
          d="M79.796 61.932C79.796 61.845 79.768 61.698 79.558 61.817L72.109 66.129C71.944 66.224 71.948 66.376 71.948 66.376L71.948 68.522C71.948 68.713 72.129 68.658 72.129 68.658L72.132 68.656" />
        <line stroke-width="0.12" x1="72.736" y1="65.765" x2="72.754" y2="68.248" />
        <path stroke-width="0.12" stroke-dasharray="0.01 0.847"
          d="M72.174 66.923C72.227 66.953 72.296 66.944 72.364 66.905C72.512 66.819 72.631 66.611 72.631 66.441C72.631 66.356 72.6 66.296 72.552 66.269C72.501 66.239 72.432 66.248 72.364 66.287C72.215 66.373 72.096 66.579 72.096 66.751C72.096 66.835 72.126 66.896 72.174 66.923" />
        <line stroke-width="0.12" x1="72.429" y1="67.34" x2="72.171" y2="67.489" />
        <line stroke-width="0.12" x1="72.429" y1="67.367" x2="72.429" y2="67.76" />
        <path stroke="none" fill="#000000"
          d="M76.019 66.007L75.995 64.587 75.602 64.817 75.597 64.53 76.65 63.911 76.656 64.2 76.265 64.43 76.287 65.849 76.019 66.007z" />
        <path stroke="none" fill="#000000"
          d="M77.233 64.698L77.233 64.364 76.766 64.634 76.766 64.968 77.233 64.698z" />
        <path stroke="none" fill="#000000"
          d="M73.045 65.992L73.534 65.706C73.644 65.641 73.727 65.603 73.786 65.59 73.864 65.575 73.932 65.588 73.989 65.632 74.045 65.674 74.088 65.742 74.12 65.834 74.15 65.926 74.167 66.052 74.17 66.212 74.171 66.352 74.159 66.481 74.135 66.599 74.103 66.742 74.058 66.87 73.998 66.983L73.804 66.893C73.831 66.843 73.853 66.774 73.87 66.686 73.888 66.599 73.894 66.489 73.893 66.358 73.891 66.227 73.879 66.131 73.861 66.069 73.841 66.01 73.816 65.969 73.783 65.95 73.75 65.929 73.707 65.926 73.658 65.941 73.62 65.953 73.546 65.989 73.438 66.054L73.319 66.123 73.337 67.254 73.536 67.138C73.611 67.093 73.665 67.057 73.697 67.027 73.739 66.986 73.775 66.942 73.804 66.893L73.998 66.983C73.953 67.069 73.891 67.152 73.811 67.23 73.754 67.29 73.674 67.347 73.575 67.406L73.072 67.7 73.045 65.992z" />
        <path stroke="none" fill="#000000"
          d="M75.712 66.197L75.423 66.367 75.302 66.046 75.209 65.808 75.016 65.284 74.846 66.022 75.209 65.808 75.302 66.046 74.769 66.358 74.665 66.811 74.382 66.978 74.868 64.968 75.153 64.802 75.712 66.197z" />
        <path stroke="none" fill="#000000"
          d="M78.533 64.233L78.533 64.233 78.533 64.527 77.57 65.081C77.581 64.965 77.612 64.841 77.665 64.712 77.715 64.581 77.819 64.39 77.972 64.137 78.096 63.932 78.174 63.798 78.2 63.734 78.237 63.648 78.257 63.572 78.257 63.509 78.257 63.438 78.24 63.392 78.207 63.372 78.174 63.354 78.129 63.362 78.072 63.395 78.015 63.427 77.968 63.475 77.936 63.534 77.9 63.594 77.882 63.671 77.876 63.767L77.602 63.893C77.618 63.709 77.671 63.554 77.756 63.427 77.841 63.302 77.95 63.203 78.078 63.128 78.219 63.046 78.331 63.026 78.412 63.067 78.492 63.109 78.533 63.195 78.533 63.326 78.533 63.401 78.522 63.478 78.498 63.56 78.474 63.64 78.438 63.731 78.388 63.835 78.355 63.902 78.295 64.007 78.209 64.15 78.121 64.29 78.066 64.382 78.043 64.427 78.022 64.469 78.001 64.51 77.987 64.548L78.533 64.233z" />
        <path stroke="none" fill="#000000"
          d="M79.421 64.013L79.421 64.013 79.146 64.173 79.146 62.978C79.046 63.143 78.927 63.292 78.793 63.423L78.793 63.134C78.864 63.066 78.941 62.97 79.025 62.847 79.108 62.722 79.167 62.6 79.198 62.481L79.421 62.353 79.421 64.013z" />
      </g>
    </g>
    <g id="_DATID_061005" class="DATID_061005_V2L1St2Sz356-S##">

      <g>
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M62.7 56.688L62.128 56.615L62.531 56.381L62.762 56.411L62.762 56.031C62.762 55.945 62.703 55.911 62.628 55.954L61.701 56.488C61.628 56.531 61.567 56.636 61.567 56.722L61.567 57.21C61.567 57.295 61.628 57.331 61.701 57.287L62.628 56.753C62.655 56.737 62.679 56.713 62.7 56.688" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M63.061 56.448L63.482 56.503L63.079 56.735L62.975 56.722C62.933 56.789 62.878 56.845 62.817 56.881L61.527 57.627C61.392 57.704 61.283 57.642 61.283 57.485L61.283 56.759C61.283 56.604 61.392 56.415 61.527 56.338L62.817 55.591C62.953 55.515 63.061 55.578 63.061 55.732C63.061 55.732 63.061 56.459 63.061 56.448" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <polygon points="61.063 58.164 65.061 55.856 65.061 53.942 61.063 56.25 61.063 58.164" stroke-width="0.12" />
          <g />
        </g>
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M64.801 55.762L63.884 56.295C63.784 56.351 63.828 56.143 63.862 56.087L64.45 55.128C64.513 55.051 64.459 55.012 64.459 55.012L64.111 55.215C63.947 55.31 63.957 55.435 63.957 55.435L63.859 55.487L63.744 55.542C63.744 55.542 63.718 55.206 63.985 55.051L64.501 54.752C64.501 54.752 64.641 54.701 64.722 54.884C64.722 55.115 64.652 55.198 64.219 55.89L64.801 55.543L64.801 55.762" />
      </g>
    </g>
    <g id="_DATID_016004" class="DATID_016004_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M39.237 49.512L38.877 49.304C38.823 49.29 38.807 49.219 38.802 49.085L38.802 48.674 38.795 48.689C38.77 48.84 38.701 48.962 38.588 49.027 38.296 49.195 37.825 48.923 37.533 48.418 37.429 48.24 37.354 48.039 37.326 47.841 37.298 47.494 37.381 47.326 37.57 47.179L40.712 45.365C40.917 45.25 41.222 45.341 41.505 45.628L41.945 45.372 42.307 45.581 42.322 45.606 42.322 49.847C42.322 50.249 42.168 50.42 42.026 50.503 41.942 50.503 41.878 50.487 41.829 50.448L41.521 50.271C41.404 50.222 41.35 50.094 41.35 49.814L41.35 49.557 40.953 49.777C40.841 49.841 40.777 49.856 40.74 49.817 40.728 49.804 40.771 49.835 40.771 49.835L40.411 49.628C40.357 49.613 40.341 49.542 40.341 49.408L40.341 48.92 39.417 49.453C39.307 49.517 39.243 49.533 39.237 49.512z" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="42.307" y1="45.581" x2="39.167" y2="47.393" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="39.167" y1="47.393" x2="39.167" y2="49.293" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="40.701" y1="48.712" x2="40.701" y2="49.618" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="41.71" y1="49.348" x2="41.71" y2="50.024" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="38.805" y1="47.185" x2="39.167" y2="47.393" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="38.805" y1="47.185" x2="38.805" y2="48.676" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="40.701" y1="48.712" x2="40.341" y2="48.92" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="41.71" y1="49.348" x2="41.35" y2="49.557" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M41.829 50.448C41.749 50.384 41.712 50.255 41.712 50.024" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M40.701 49.618C40.701 49.75 40.719 49.822 40.771 49.835" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="41.505" y1="45.628" x2="38.805" y2="47.185" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M39.167 49.293C39.167 49.427 39.183 49.499 39.237 49.512" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M38.781 48.314C38.746 48.139 38.677 47.963 38.588 47.81C38.356 47.409 38.012 47.155 37.728 47.152" />
        <g id="DATID_000000" class="DATID_000000_V2L1St1Sz32-SK#">
          <path
            d="M39.237 49.512C39.237 49.512 38.877 49.304 38.877 49.304C38.823 49.29 38.807 49.219 38.802 49.085C38.802 49.085 38.802 48.674 38.802 48.674C38.802 48.674 38.795 48.68 38.795 48.689C38.77 48.84 38.701 48.962 38.588 49.027C38.296 49.195 37.825 48.923 37.533 48.418C37.429 48.24 37.354 48.039 37.326 47.841C37.298 47.494 37.381 47.326 37.57 47.179C37.57 47.179 40.712 45.365 40.712 45.365C40.917 45.25 41.222 45.341 41.505 45.628C41.505 45.628 41.945 45.372 41.945 45.372C41.945 45.372 42.307 45.581 42.307 45.581C42.307 45.581 42.322 45.606 42.322 45.606C42.322 45.606 42.322 49.847 42.322 49.847C42.322 50.249 42.168 50.42 42.026 50.503C41.942 50.503 41.878 50.487 41.829 50.448C41.829 50.448 41.521 50.271 41.521 50.271C41.404 50.222 41.35 50.094 41.35 49.814C41.35 49.814 41.35 49.557 41.35 49.557C41.345 49.549 40.953 49.777 40.953 49.777C40.841 49.841 40.777 49.856 40.74 49.817C40.728 49.804 40.771 49.835 40.771 49.835C40.771 49.835 40.411 49.628 40.411 49.628C40.357 49.613 40.341 49.542 40.341 49.408C40.341 49.408 40.341 48.92 40.341 48.92C40.341 48.92 39.417 49.453 39.417 49.453C39.307 49.517 39.243 49.533 39.237 49.512"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M39.237 49.512L38.877 49.304C38.823 49.29 38.807 49.219 38.802 49.085L38.802 48.674L38.795 48.689C38.77 48.84 38.701 48.962 38.588 49.027C38.296 49.195 37.825 48.923 37.533 48.418C37.429 48.24 37.354 48.039 37.326 47.841C37.298 47.494 37.381 47.326 37.57 47.179L40.712 45.365C40.917 45.25 41.222 45.341 41.505 45.628L41.945 45.372L42.307 45.581L42.322 45.606L42.322 49.847C42.322 50.249 42.168 50.42 42.026 50.503C41.942 50.503 41.878 50.487 41.829 50.448L41.521 50.271C41.404 50.222 41.35 50.094 41.35 49.814L41.35 49.557L40.953 49.777C40.841 49.841 40.777 49.856 40.74 49.817C40.728 49.804 40.771 49.835 40.771 49.835L40.411 49.628C40.357 49.613 40.341 49.542 40.341 49.408L40.341 48.92L39.417 49.453C39.307 49.517 39.243 49.533 39.237 49.512" />
        </g>
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M39.417 47.784C39.484 47.823 39.594 47.761 39.662 47.643C39.731 47.524 39.731 47.398 39.662 47.357C39.594 47.319 39.484 47.383 39.417 47.5C39.348 47.618 39.348 47.746 39.417 47.784" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M40.926 49.481C40.994 49.521 41.106 49.457 41.173 49.34C41.241 49.222 41.241 49.094 41.173 49.054C41.106 49.015 40.994 49.079 40.926 49.197C40.859 49.314 40.859 49.442 40.926 49.481" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M41.847 46.387C41.915 46.427 42.026 46.363 42.094 46.246C42.162 46.128 42.162 46 42.094 45.96C42.026 45.921 41.915 45.985 41.847 46.103C41.78 46.222 41.78 46.348 41.847 46.387" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M41.948 50.152C41.991 50.176 42.063 50.136 42.106 50.06C42.151 49.982 42.151 49.901 42.106 49.875C42.063 49.85 41.991 49.89 41.948 49.968C41.904 50.043 41.904 50.125 41.948 50.152" />
      </g>
    </g>
    <g id="_DATID_049003" class="DATID_049003_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round"
          d="M66.608 35.539C67.423 35.079 68.263 34.593 69.165 34.072C71.486 32.732 75.302 29.079 76.408 27.631C77.513 26.181 77.17 25.495 76.37 24.658C75.569 23.818 73.93 22.332 72.063 20.515C70.424 18.8 69.966 18.352 69.737 18.143C68.486 16.879 67.581 16.762 66.509 17.362C66.498 17.368 66.487 17.375 66.487 17.375L66.128 17.582C66.128 17.582 66.114 17.589 66.102 17.597C65.87 17.731 65.632 17.896 65.381 18.091C63.768 19.338 60.735 21.217 57.469 23.103C57.454 23.111 57.438 23.12 57.438 23.12L57.076 23.329C57.076 23.329 57.06 23.335 57.046 23.344C53.505 25.364 49.799 27.335 47.399 28.474C47.159 28.586 46.929 28.71 46.712 28.841L46.694 28.855L46.334 29.062L46.317 29.068C44.936 29.899 44.033 30.968 44.155 31.201C44.573 31.809 44.911 32.439 45.408 33.273C45.468 33.5 45.664 33.77 45.86 34.241C46.243 35.156 48.529 39.31 49.405 40.682C50.283 42.054 50.435 41.7 52.646 41.14C58.993 39.533 62.264 37.988 66.117 35.82C66.15 35.802 66.183 35.787 66.183 35.787C66.183 35.787 66.545 35.579 66.608 35.539" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round"
          d="M74.909 25.966C74.974 26.102 75.066 26.164 74.873 26.408C74.865 26.419 74.858 26.429 74.849 26.44C73.831 27.774 70.32 31.14 68.186 32.374C68.04 32.457 67.896 32.54 67.753 32.624C67.319 32.874 66.59 32.149 65.268 32.765C63.78 33.46 64.275 34.585 63.704 34.893C60.682 36.484 57.631 37.738 52.524 39.03C52.524 39.03 51.893 39.201 51.741 39.243C51.493 39.304 51.307 39.246 51.176 39.03C50.222 37.457 48.363 34.078 48.031 33.28L48.031 33.28C47.911 32.987 47.79 32.756 47.689 32.563C47.661 32.509 47.634 32.463 47.615 32.42C47.078 31.288 47.49 30.926 48.365 30.512C53.289 28.176 63.567 22.414 67.12 19.669C67.274 19.55 67.382 19.411 67.496 19.332C67.563 19.28 67.625 19.3 67.683 19.327C67.825 19.422 67.968 19.562 68.135 19.731C68.353 19.946 69.766 21.099 70.707 21.932L72.998 24.121C73.807 24.887 74.474 25.516 74.909 25.966" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M74.909 25.966C74.974 26.102 75.066 26.164 74.873 26.408C74.865 26.419 74.858 26.429 74.849 26.44C73.831 27.774 70.32 31.14 68.186 32.374C68.04 32.457 67.896 32.54 67.753 32.624C67.319 32.874 66.59 32.149 65.268 32.765C63.78 33.46 64.275 34.585 63.704 34.893C60.682 36.484 57.631 37.738 52.524 39.03C52.524 39.03 51.893 39.201 51.741 39.243C51.493 39.304 51.307 39.246 51.176 39.03C50.222 37.457 48.363 34.078 48.031 33.28L48.031 33.28C47.911 32.987 47.79 32.756 47.689 32.563C47.661 32.509 47.634 32.463 47.615 32.42C47.078 31.288 47.49 30.926 48.365 30.512C53.289 28.176 63.567 22.414 67.12 19.669C67.274 19.55 67.382 19.411 67.496 19.332C67.563 19.28 67.625 19.3 67.683 19.327C67.825 19.422 67.968 19.562 68.135 19.731C68.353 19.946 69.766 21.099 70.707 21.932L72.998 24.121C73.807 24.887 74.474 25.516 74.909 25.966" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round"
          d="M44.787 30.428C44.57 30.696 44.469 30.905 44.515 30.992C44.933 31.602 45.271 32.231 45.768 33.064C45.828 33.292 46.024 33.561 46.222 34.033C46.603 34.948 48.89 39.103 49.765 40.475C50.371 41.42 50.631 41.545 51.459 41.348" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M44.787 30.428C44.57 30.696 44.469 30.905 44.515 30.992C44.933 31.602 45.271 32.231 45.768 33.064C45.828 33.292 46.024 33.561 46.222 34.033C46.603 34.948 48.89 39.103 49.765 40.475C50.371 41.42 50.631 41.545 51.459 41.348" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round"
          d="M48.554 31.6C52.753 29.913 64.528 23.332 67.802 20.586" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M48.554 31.6C52.753 29.913 64.528 23.332 67.802 20.586" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round"
          d="M49.314 32.658C53.515 30.972 65.289 24.39 68.563 21.644" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M49.314 32.658C53.515 30.972 65.289 24.39 68.563 21.644" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round"
          d="M50.142 33.848C54.341 32.161 66.115 25.58 69.39 22.835" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M50.142 33.848C54.341 32.161 66.115 25.58 69.39 22.835" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round"
          d="M51.1 35.006C55.301 33.32 67.075 26.737 70.349 23.992" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M51.1 35.006C55.301 33.32 67.075 26.737 70.349 23.992" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round"
          d="M51.96 36.229C56.161 34.542 67.935 27.961 71.209 25.216" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M51.96 36.229C56.161 34.542 67.935 27.961 71.209 25.216" />
        <path stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round"
          d="M53.018 37.552C57.219 35.865 68.992 29.285 72.266 26.539" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M53.018 37.552C57.219 35.865 68.992 29.285 72.266 26.539" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M74.909 25.966C74.974 26.102 75.066 26.164 74.873 26.408C74.865 26.419 74.858 26.429 74.849 26.44C73.831 27.774 70.32 31.14 68.186 32.374C68.04 32.457 67.896 32.54 67.753 32.624C67.319 32.874 66.59 32.149 65.268 32.765C63.78 33.46 64.275 34.585 63.704 34.893C60.682 36.484 57.631 37.738 52.524 39.03C52.524 39.03 51.893 39.201 51.741 39.243C51.493 39.304 51.307 39.246 51.176 39.03C50.222 37.457 48.363 34.078 48.031 33.28L48.031 33.28C47.911 32.987 47.79 32.756 47.689 32.563C47.661 32.509 47.634 32.463 47.615 32.42C47.078 31.288 47.49 30.926 48.365 30.512C53.289 28.176 63.567 22.414 67.12 19.669C67.274 19.55 67.382 19.411 67.496 19.332C67.563 19.28 67.625 19.3 67.683 19.327C67.825 19.422 67.968 19.562 68.135 19.731C68.353 19.946 69.766 21.099 70.707 21.932L72.998 24.121C73.807 24.887 74.474 25.516 74.909 25.966" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M44.787 30.428C44.57 30.696 44.469 30.905 44.515 30.992C44.933 31.602 45.271 32.231 45.768 33.064C45.828 33.292 46.024 33.561 46.222 34.033C46.603 34.948 48.89 39.103 49.765 40.475C50.371 41.42 50.631 41.545 51.459 41.348" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M48.554 31.6C52.753 29.913 64.528 23.332 67.802 20.586" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M49.314 32.658C53.515 30.972 65.289 24.39 68.563 21.644" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M50.142 33.848C54.341 32.161 66.115 25.58 69.39 22.835" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M51.1 35.006C55.301 33.32 67.075 26.737 70.349 23.992" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M51.96 36.229C56.161 34.542 67.935 27.961 71.209 25.216" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M53.018 37.552C57.219 35.865 68.992 29.285 72.266 26.539" />
        <line stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" x1="59.237" y1="30.012" x2="65.897"
          y2="33.857" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="59.237" y1="30.012" x2="65.897" y2="33.857" />
        <line stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" x1="51.622" y1="27.585" x2="64.918"
          y2="35.262" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="51.622" y1="27.585" x2="64.918" y2="35.262" />
        <line stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" x1="65.718" y1="19.032" x2="71.123"
          y2="22.152" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="65.718" y1="19.032" x2="71.123" y2="22.152" />
        <line stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" x1="74.516" y1="25.117" x2="69.822"
          y2="22.407" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="74.516" y1="25.117" x2="69.822" y2="22.407" />
        <line stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" x1="66.182" y1="27.763" x2="71.778"
          y2="30.995" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="66.182" y1="27.763" x2="71.778" y2="30.995" />
        <line stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" x1="62.146" y1="26.836" x2="66.507"
          y2="29.355" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.146" y1="26.836" x2="66.507" y2="29.355" />
        <line stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" x1="60.295" y1="34.643" x2="57.17"
          y2="32.838" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="60.295" y1="34.643" x2="57.17" y2="32.838" />
        <line stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" x1="54.011" y1="35.966" x2="56.228"
          y2="37.246" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="54.011" y1="35.966" x2="56.228" y2="37.246" />
        <line stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" x1="53.668" y1="37.645" x2="48.329"
          y2="34.551" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="53.668" y1="37.645" x2="48.329" y2="34.551" />
        <g id="DATID_000000" class="DATID_000000_V2L1St1Sz32-SK#">
          <path
            d="M66.608 35.539C67.423 35.079 68.263 34.593 69.165 34.072C71.486 32.732 75.302 29.079 76.408 27.631C77.513 26.181 77.17 25.495 76.37 24.658C75.569 23.818 73.93 22.332 72.063 20.515C70.424 18.8 69.966 18.352 69.737 18.143C68.486 16.879 67.581 16.762 66.509 17.362C66.498 17.368 66.487 17.375 66.487 17.375C66.487 17.375 66.128 17.582 66.128 17.582C66.128 17.582 66.114 17.589 66.102 17.597C65.87 17.731 65.632 17.896 65.381 18.091C63.768 19.338 60.735 21.217 57.469 23.103C57.454 23.111 57.438 23.12 57.438 23.12C57.438 23.12 57.076 23.329 57.076 23.329C57.076 23.329 57.06 23.335 57.046 23.344C53.505 25.364 49.799 27.335 47.399 28.474C47.159 28.586 46.929 28.71 46.712 28.841C46.712 28.841 46.694 28.855 46.694 28.855C46.694 28.855 46.334 29.062 46.334 29.062C46.334 29.062 46.317 29.068 46.317 29.068C44.936 29.899 44.033 30.968 44.155 31.201C44.573 31.809 44.911 32.439 45.408 33.273C45.468 33.5 45.664 33.77 45.86 34.241C46.243 35.156 48.529 39.31 49.405 40.682C50.283 42.054 50.435 41.7 52.646 41.14C58.993 39.533 62.264 37.988 66.117 35.82C66.15 35.802 66.183 35.787 66.183 35.787C66.183 35.787 66.545 35.579 66.608 35.539"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M66.608 35.539C67.423 35.079 68.263 34.593 69.165 34.072C71.486 32.732 75.302 29.079 76.408 27.631C77.513 26.181 77.17 25.495 76.37 24.658C75.569 23.818 73.93 22.332 72.063 20.515C70.424 18.8 69.966 18.352 69.737 18.143C68.486 16.879 67.581 16.762 66.509 17.362C66.498 17.368 66.487 17.375 66.487 17.375L66.128 17.582C66.128 17.582 66.114 17.589 66.102 17.597C65.87 17.731 65.632 17.896 65.381 18.091C63.768 19.338 60.735 21.217 57.469 23.103C57.454 23.111 57.438 23.12 57.438 23.12L57.076 23.329C57.076 23.329 57.06 23.335 57.046 23.344C53.505 25.364 49.799 27.335 47.399 28.474C47.159 28.586 46.929 28.71 46.712 28.841L46.694 28.855L46.334 29.062L46.317 29.068C44.936 29.899 44.033 30.968 44.155 31.201C44.573 31.809 44.911 32.439 45.408 33.273C45.468 33.5 45.664 33.77 45.86 34.241C46.243 35.156 48.529 39.31 49.405 40.682C50.283 42.054 50.435 41.7 52.646 41.14C58.993 39.533 62.264 37.988 66.117 35.82C66.15 35.802 66.183 35.787 66.183 35.787C66.183 35.787 66.545 35.579 66.608 35.539" />
        </g>
        <line stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" x1="46.668" y1="30.673" x2="52.82"
          y2="34.225" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="46.668" y1="30.673" x2="52.82" y2="34.225" />
        <line stroke="#FFFFFF" stroke-width="0.36" stroke-linejoin="round" x1="62.991" y1="20.472" x2="71.209"
          y2="25.216" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="62.991" y1="20.472" x2="71.209" y2="25.216" />
      </g>
    </g>
    <g id="_DATID_047002" class="DATID_047002_V2L1St2Sz356-S##">

      <g>
        <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
          d="M71.972 12.489C71.915 12.647 71.933 12.778 72.01 12.849L77.486 17.951C77.582 18.042 77.756 18.024 77.93 17.922L80.977 20.762C80.887 20.963 80.908 21.149 81.016 21.254L81.766 21.951C81.974 22.146 82.326 22.106 82.552 21.864 82.778 21.623 82.792 21.268 82.584 21.074L81.834 20.376C81.718 20.268 81.527 20.272 81.341 20.371L78.293 17.532C78.39 17.341 78.384 17.173 78.292 17.086L72.816 11.984C72.734 11.907 72.599 11.909 72.454 11.971L72.373 11.897C72.43 11.737 72.412 11.608 72.334 11.535L71.371 10.637C71.155 10.435 70.799 10.465 70.575 10.705 70.353 10.943 70.349 11.301 70.566 11.503L71.528 12.4C71.611 12.477 71.746 12.477 71.891 12.413L71.972 12.489z" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M81.819 21.632C81.906 21.57 81.888 21.399 81.778 21.248C81.671 21.1 81.513 21.028 81.426 21.091C81.382 21.123 81.365 21.181 81.373 21.251" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M81.652 21.634C81.719 21.664 81.78 21.661 81.819 21.632" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M77.287 16.149C77.149 16.021 76.855 16.111 76.634 16.35C76.411 16.588 76.341 16.885 76.48 17.015" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.173 11.71C72.09 11.632 71.915 11.686 71.783 11.829C71.65 11.971 71.608 12.15 71.691 12.227" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M71.189 11.556C71.275 11.493 71.257 11.32 71.149 11.172C71.04 11.022 70.882 10.952 70.796 11.015C70.753 11.045 70.736 11.105 70.743 11.173" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M71.022 11.557C71.09 11.586 71.15 11.585 71.189 11.556" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M78.07 17.324C78.009 17.266 77.876 17.306 77.777 17.414C77.676 17.521 77.644 17.657 77.707 17.714" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M72.454 11.971C72.242 12.064 72.049 12.276 71.971 12.489" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="72.373" y1="11.897" x2="72.173" y2="11.71" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="71.891" y1="12.413" x2="71.691" y2="12.227" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M72.334 11.535C72.266 11.471 72.159 11.461 72.04 11.497" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M71.471 12.109C71.441 12.24 71.465 12.34 71.528 12.4" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="77.707" y1="17.714" x2="77.93" y2="17.922" />
        <line stroke-width="0.12" stroke-linejoin="round" x1="78.07" y1="17.324" x2="78.293" y2="17.532" />
        <path stroke-width="0.12" stroke-linejoin="round" d="M81.341 20.371C81.18 20.46 81.049 20.606 80.977 20.763" />
        <path stroke-width="0.12" stroke-linejoin="round"
          d="M78.292 17.086C78.155 16.957 77.861 17.047 77.64 17.286C77.417 17.525 77.347 17.824 77.486 17.951" />
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M71.972 12.489C71.915 12.647 71.933 12.778 72.01 12.849C72.01 12.849 77.486 17.951 77.486 17.951C77.582 18.042 77.756 18.024 77.93 17.922C77.93 17.922 80.977 20.762 80.977 20.762C80.887 20.963 80.908 21.149 81.016 21.254C81.016 21.254 81.766 21.951 81.766 21.951C81.974 22.146 82.326 22.106 82.552 21.864C82.778 21.623 82.792 21.268 82.584 21.074C82.584 21.074 81.834 20.376 81.834 20.376C81.718 20.268 81.527 20.272 81.341 20.371C81.341 20.371 78.293 17.532 78.293 17.532C78.39 17.341 78.384 17.173 78.292 17.086C78.292 17.086 72.816 11.984 72.816 11.984C72.734 11.907 72.599 11.909 72.454 11.971C72.454 11.971 72.373 11.897 72.373 11.897C72.43 11.737 72.412 11.608 72.334 11.535C72.334 11.535 71.371 10.637 71.371 10.637C71.155 10.435 70.799 10.465 70.575 10.705C70.353 10.943 70.349 11.301 70.566 11.503C70.566 11.503 71.528 12.4 71.528 12.4C71.611 12.477 71.746 12.477 71.891 12.413C71.891 12.413 71.972 12.489 71.972 12.489"
            stroke-width="0.25" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M71.972 12.489C71.915 12.647 71.933 12.778 72.01 12.849L77.486 17.951C77.582 18.042 77.756 18.024 77.93 17.922L80.977 20.762C80.887 20.963 80.908 21.149 81.016 21.254L81.766 21.951C81.974 22.146 82.326 22.106 82.552 21.864C82.778 21.623 82.792 21.268 82.584 21.074L81.834 20.376C81.718 20.268 81.527 20.272 81.341 20.371L78.293 17.532C78.39 17.341 78.384 17.173 78.292 17.086L72.816 11.984C72.734 11.907 72.599 11.909 72.454 11.971L72.373 11.897C72.43 11.737 72.412 11.608 72.334 11.535L71.371 10.637C71.155 10.435 70.799 10.465 70.575 10.705C70.353 10.943 70.349 11.301 70.566 11.503L71.528 12.4C71.611 12.477 71.746 12.477 71.891 12.413L71.972 12.489" />
        </g>
      </g>
    </g>
    <g id="_DATID_050001" class="DATID_050001_V2L1St2Sz356-S##">

      <g>
        <g>
          <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
            d="M73.629 19.282L74.021 18.818 73.816 18.615 64.914 28.254 65.203 28.411 65.593 28.022C65.921 28.146 66.212 27.899 66.212 27.899L69.469 24.626 69.796 24.539 70.248 23.954 70.259 23.777 73.331 20.28C73.331 20.28 73.772 19.754 73.629 19.282z" />
          <g id="DATID_000000" class="V2L1St1Sz32-SK#">
            <path
              d="M73.629 19.282C73.629 19.282 74.021 18.818 74.021 18.818C74.021 18.818 73.816 18.615 73.816 18.615C73.816 18.615 64.914 28.254 64.914 28.254C64.914 28.254 65.203 28.411 65.203 28.411C65.203 28.411 65.593 28.022 65.593 28.022C65.921 28.146 66.212 27.899 66.212 27.899C66.212 27.899 69.469 24.626 69.469 24.626C69.469 24.626 69.796 24.539 69.796 24.539C69.796 24.539 70.248 23.954 70.248 23.954C70.248 23.954 70.259 23.777 70.259 23.777C70.259 23.777 73.331 20.28 73.331 20.28C73.331 20.28 73.772 19.754 73.629 19.282"
              stroke-width="0.25" />
            <path stroke-width="0.25" stroke-linejoin="round"
              d="M73.629 19.282L74.021 18.818L73.816 18.615L64.914 28.254L65.203 28.411L65.593 28.022C65.921 28.146 66.212 27.899 66.212 27.899L69.469 24.626L69.796 24.539L70.248 23.954L70.259 23.777L73.331 20.28C73.331 20.28 73.772 19.754 73.629 19.282" />
          </g>
          <path stroke-width="0.12" stroke-linejoin="round" d="M69.492 24.367L69.265 24.419" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M69.93 23.841L69.938 23.638" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M69.93 23.841L69.492 24.367" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M73.219 19.739L65.795 27.789" />
        </g>
        <g>
          <path stroke="#FFFFFF" stroke-width="0.8" stroke-linejoin="round" fill="#FFFFFF"
            d="M69.864 29.413C69.864 29.413 70.594 30.346 70.004 30.687 69.414 31.027 69.406 31.039 69.406 31.039 69.406 31.039 68.873 31.356 68.45 30.579 68.025 29.801 68.356 29.425 68.356 29.425 68.356 29.425 69.135 28.35 70.975 26.088 72.894 23.89 76.795 19.414 76.795 19.414 76.795 19.414 77.001 19.169 77.317 19.169 77.635 19.169 77.87 19.39 77.691 19.617 77.513 19.844 69.864 29.413 69.864 29.413z" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M69.861 30.769C69.861 30.769 69.329 31.086 68.905 30.309C68.48 29.532 68.855 29.143 68.855 29.143" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="68.855" y1="29.143" x2="77.164" y2="19.189" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="70.195" y1="28.996" x2="69.518" y2="28.336" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.534" y1="28.332" x2="69.09" y2="28.457" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.182" y1="29.199" x2="69.73" y2="28.542" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M69.39 29.829C69.283 29.891 69.263 30.059 69.347 30.204C69.432 30.347 69.587 30.414 69.694 30.35C69.802 30.288 69.822 30.12 69.737 29.975C69.653 29.832 69.498 29.765 69.39 29.829" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M69.149 30.222C69.161 30.248 69.174 30.274 69.188 30.297C69.34 30.559 69.623 30.678 69.819 30.564C70.016 30.449 70.051 30.143 69.897 29.882C69.873 29.839 69.844 29.8 69.816 29.768" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.149" y1="30.222" x2="68.849" y2="29.539" />
          <path stroke-width="0.12" stroke-linejoin="round"
            d="M68.849 29.539C68.849 29.539 68.781 29.317 68.885 29.108" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.864" y1="29.413" x2="69.667" y2="29.64" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.667" y1="29.64" x2="69.182" y2="29.199" />
          <line stroke-width="0.12" stroke-linejoin="round" x1="69.484" y1="29.475" x2="70.033" y2="28.818" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M69.484 29.475L69.121 29.838L69.182 29.199" />
          <path stroke-width="0.12" stroke-linejoin="round" d="M69.121 29.838L69.667 29.64" />
          <path stroke-width="0.25" stroke-linejoin="round"
            d="M69.864 29.413C69.864 29.413 70.594 30.346 70.004 30.687C69.414 31.027 69.406 31.039 69.406 31.039C69.406 31.039 68.873 31.356 68.45 30.579C68.025 29.801 68.356 29.425 68.356 29.425C68.356 29.425 69.135 28.35 70.975 26.088C72.894 23.89 76.795 19.414 76.795 19.414C76.795 19.414 77.001 19.169 77.317 19.169C77.635 19.169 77.87 19.39 77.691 19.617C77.513 19.844 69.864 29.413 69.864 29.413" />
        </g>
        <g id="DATID_000000" class="V2L1St1Sz32-SK#">
          <path
            d="M73.629 19.282C73.629 19.282 74.021 18.818 74.021 18.818C74.021 18.818 73.816 18.615 73.816 18.615C73.816 18.615 64.914 28.254 64.914 28.254C64.914 28.254 65.203 28.411 65.203 28.411C65.203 28.411 65.593 28.022 65.593 28.022C65.921 28.146 66.212 27.899 66.212 27.899C66.212 27.899 69.469 24.626 69.469 24.626C69.469 24.626 69.796 24.539 69.796 24.539C69.796 24.539 70.975 26.088 70.975 26.088C72.894 23.89 76.795 19.414 76.795 19.414C76.795 19.414 77.001 19.169 77.317 19.169C77.635 19.169 77.87 19.39 77.691 19.617C77.513 19.844 69.864 29.413 69.864 29.413C69.864 29.413 70.594 30.346 70.004 30.687C69.414 31.027 69.406 31.039 69.406 31.039C69.406 31.039 68.873 31.356 68.45 30.579C68.025 29.801 68.356 29.425 68.356 29.425C68.356 29.425 69.135 28.35 70.975 26.088C70.975 26.088 69.796 24.539 69.796 24.539C69.796 24.539 70.248 23.954 70.248 23.954C70.248 23.954 70.259 23.777 70.259 23.777C70.259 23.777 73.331 20.28 73.331 20.28C73.331 20.28 73.772 19.754 73.629 19.282"
            stroke-width="0.12" />
        </g>
      </g>
    </g>
    <g id="dat-logo">
      <path
        d="M 78.00923316381837,129.07848313867188 L 81.87301102734375,129.07848313867188 L 81.87301102734375,135.33602228515628 L 78.00923316381837,135.33602228515628 L 78.00923316381837,129.07848313867188 Z"
        id="path2914" style="fill: rgb(40, 87, 155); fill-opacity: 1; fill-rule: nonzero; stroke: none;" />
      <path
        d="M 78.09506816381837,129.1643181386719 L 81.78047855029297,129.1643181386719 L 81.78047855029297,132.80715050927736 L 78.09506816381837,132.80715050927736 L 78.09506816381837,129.1643181386719 Z"
        id="path2916" style="fill: rgb(238, 195, 39); fill-opacity: 1; fill-rule: nonzero; stroke: none;" />
      <path
        d="M 80.11459219970703,131.53508251513674 C 80.04438185205078,131.5246046098633 79.99390768676759,131.4571436645508 79.99390768676759,131.37645541162112 L 79.99390768676759,131.01646811572266 C 79.99390768676759,130.94505071337892 80.05760077734377,130.8657371616211 80.11459219970703,130.86110173632812 L 80.11459219970703,130.73321261572266 C 80.11201882617189,130.73321261572266 80.10926942382812,130.73321261572266 80.10669605029298,130.7338915839844 C 79.9971726020508,130.74711050927738 79.90670217675782,130.84925751220703 79.90670217675782,130.9836764633789 L 79.90670217675782,132.07017656396488 L 80.11459219970703,132.7959936357422 L 80.11459219970703,132.41437156103518 L 80.00369405029298,132.0427076875 L 79.99854311206056,131.5955106904297 C 80.024809125,131.62365853515627 80.06807030029297,131.64203258984378 80.11459219970703,131.6459806645508 L 80.11459219970703,131.53508251513674 Z M 80.11459219970703,132.7959936357422 L 80.11459219970703,132.41437156103518 L 80.23647957617189,132.82483721337894 L 80.12437856323243,132.83015998925782 L 80.11459219970703,132.7959936357422 Z M 80.11854027441407,130.86110173632812 C 80.1762232385254,130.86041438574222 80.22806791381836,130.91157171044924 80.22927077734376,131.02178250927736 L 80.23458936206055,131.3817781875 C 80.23579222558595,131.4571436645508 80.20506262500001,131.53713618457033 80.13364941381838,131.53713618457033 C 80.12695193676758,131.53713618457033 80.12043048852539,131.5364488339844 80.11459219970703,131.53508251513674 L 80.11459219970703,131.6459806645508 C 80.1224883491211,131.64666801513675 80.13038868969727,131.64735536572266 80.13811300146486,131.64735536572266 C 80.20901069970704,131.6459806645508 80.2792252385254,131.61181431103518 80.31064218969726,131.52924003515625 C 80.32248641381837,131.60014611572268 80.3971603491211,131.62950101513673 80.47578655029298,131.6361901098633 L 80.47578655029298,131.53713618457033 C 80.40110842382813,131.53576986572267 80.35201315087892,131.46177908984376 80.35063844970703,131.35224725927736 L 80.34531567382814,130.84411076513675 C 80.34463251440431,130.75364033984377 80.40763406323242,130.69458686572267 80.47578655029298,130.69063040869142 L 80.47578655029298,130.5785335869141 C 80.36969566381836,130.57527286279299 80.27853788793945,130.68805703515625 80.2792252385254,130.79363240869142 C 80.23579222558595,130.7464231586914 80.17038494970703,130.72805748632814 80.11459219970703,130.73321261572266 L 80.11459219970703,130.86110173632812 L 80.11854027441407,130.86110173632812 Z M 80.47578655029298,131.6361901098633 L 80.47578655029298,131.53713618457033 L 80.47853176147461,131.53713618457033 L 80.4829953491211,131.53713618457033 C 80.55973552734375,131.53713618457033 80.60762793676759,131.45250823925784 80.60625742675782,131.3680479404297 L 80.6011064885254,130.82831008398438 C 80.60041913793947,130.7543276904297 80.56041868676758,130.68668233398438 80.47853176147461,130.69063040869142 L 80.47578655029298,130.69063040869142 L 80.47578655029298,130.5785335869141 C 80.51578700146484,130.57990828808596 80.5571579626465,130.59689925927736 80.59715841381836,130.6362123598633 L 80.59715841381836,129.65546366162113 C 80.58926226440431,129.35898923632814 81.037662125,129.3433687751465 81.03645926147462,129.65546366162113 C 81.03834947558595,129.73546456396485 81.03440140087892,129.64756751220705 81.03645926147462,129.7275684145508 C 81.102038375,129.53993008691407 81.40297638793946,129.6331499145508 81.40160168676758,129.8789783369141 L 81.4004030144043,130.7038493339844 C 81.42323227441406,130.6971602392578 81.47645165087891,130.6971602392578 81.52743713793946,130.78178818457033 L 81.52417641381837,132.06880186279298 L 81.2685574367676,132.83668143750003 L 81.16624278735353,132.83410806396486 L 81.43645119970704,132.03995828515627 L 81.4369667126465,130.83621461572267 C 81.37276230029298,130.75483901220704 81.23044719970704,130.8650498110352 81.21276468676758,131.055948862793 L 81.21276468676758,131.6243458857422 C 80.9571499008789,131.7522433886719 80.84436153735352,131.8215987392578 80.8220435991211,132.15206348925784 C 80.81998573852539,132.2420142104492 80.73552543969727,132.24390861572266 80.73346338793945,132.15738626513672 C 80.7362085991211,131.94434111279298 80.79509442675783,131.7489826645508 81.12161110205079,131.55945831396485 L 81.12161110205079,131.04530331103518 C 81.1504504885254,130.85131956396486 81.21276468676758,130.7929534404297 81.31439617675781,130.7208486875 L 81.31439617675781,129.90456119042972 C 81.31645403735352,129.6987248369141 81.0580898491211,129.69340206103516 81.04882318969727,129.9097079375 L 81.04830348559571,131.0492513857422 C 81.0429849008789,131.09388726220703 80.95113977441406,131.09251256103516 80.94530567675781,131.05972090869142 L 80.94461832617188,129.66988125927736 C 80.94272811206055,129.5236180839844 80.72694193969727,129.46199123632815 80.69552498852539,129.66782758984377 L 80.69552498852539,131.38761228515625 C 80.70668186206055,131.53903058984378 80.61157601147463,131.63808451513674 80.50600063793945,131.6373971645508 C 80.49621427441406,131.6373971645508 80.48574475146485,131.63670981396484 80.47578655029298,131.6361901098633"
        id="path2918" style="fill: rgb(40, 87, 155); fill-opacity: 1; fill-rule: nonzero; stroke: none;" />
      <path
        d="M 79.00852356323243,130.52806361279298 L 79.00852356323243,130.81921526220705 C 78.96320452734375,130.8315791904297 78.91204301147462,130.84479811572265 78.8629477385254,130.85595498925784 C 78.797368625,130.87037258691407 78.61247969970704,130.9129464116211 78.61247969970704,130.9129464116211 C 78.61247969970704,130.9129464116211 78.77899876147461,130.9536342133789 78.88526567675783,130.97989603515626 C 78.93110022558594,130.99225996337893 78.97041332617188,131.00272948632812 79.00852356323243,131.01319900927734 L 79.00852356323243,131.28855835986332 L 78.30107216381836,131.05714753515628 L 78.30107216381836,130.75037961572266 L 79.00852356323243,130.52806361279298 Z M 79.00852356323243,129.76876753808597 L 79.00852356323243,130.0580331645508 L 78.54106648852539,130.0580331645508 L 78.54106648852539,130.40411418457035 L 78.3050202385254,130.40411418457035 L 78.3050202385254,129.3838805480957 L 78.54106648852539,129.43109398925782 L 78.54106648852539,129.76876753808597 L 79.00852356323243,129.76876753808597 Z M 78.3050202385254,132.59806181396488 L 79.00852356323243,132.59806181396488 L 79.00852356323243,132.30879618750004 L 78.5292222644043,132.30879618750004 L 78.5292222644043,132.17901266162113 C 78.5292222644043,132.06691583984377 78.569905875,132.0020198857422 78.647157375,131.9548106357422 C 78.73367972558594,131.9022786098633 78.85899966381837,131.88597498925782 79.00852356323243,131.8839129375 L 79.00852356323243,131.56409373925783 C 78.758742875,131.56597976220706 78.58037958911133,131.62898131103518 78.45385678735352,131.75739013574218 C 78.36784994970704,131.8434011645508 78.32339010205078,131.94691448632813 78.31274455029298,132.06691583984377 C 78.30896831323243,132.12390726220704 78.3050202385254,132.23205600927736 78.3050202385254,132.31274426220705 L 78.3050202385254,132.59806181396488 Z M 79.00852356323243,129.76876753808597 L 79.71992303735352,129.76876753808597 L 79.71992303735352,130.0578571357422 L 79.00852356323243,130.0578571357422 L 79.00852356323243,129.76876753808597 Z M 79.00852356323243,131.28855835986332 L 79.00852356323243,131.01319900927734 C 79.05367076147462,131.0257305839844 79.09641642382813,131.03878186279297 79.14740610205078,131.05526151220704 L 79.14740610205078,130.77852746044925 C 79.14740610205078,130.77852746044925 79.0871497644043,130.79758886572267 79.00852356323243,130.81921526220705 L 79.00852356323243,130.52806361279298 L 79.71992303735352,130.30454893750002 L 79.71992303735352,130.6094308339844 L 79.3834523520508,130.7090044633789 L 79.3834523520508,131.12478450927736 L 79.71992303735352,131.2243581386719 L 79.71992303735352,131.52203123632813 L 79.00852356323243,131.28855835986332 Z M 79.00852356323243,132.59806181396488 L 79.71992303735352,132.59806181396488 L 79.71992303735352,132.25369078808595 C 79.71992303735352,132.17111651220705 79.7159749626465,132.10364718457032 79.71133953735352,132.0504361904297 C 79.70155317382813,131.91875825927735 79.62413402734376,131.79224383984376 79.50808493969727,131.7069201616211 C 79.37967192382813,131.6113029892578 79.21109500146486,131.56409373925783 79.01762257617187,131.56409373925783 L 79.00852356323243,131.56409373925783 L 79.00852356323243,131.8839129375 L 79.04388858911133,131.8839129375 C 79.34293638793946,131.8839129375 79.48782486206055,131.96923661572265 79.48782486206055,132.15669891455082 L 79.48782486206055,132.30879618750004 L 79.00852356323243,132.30879618750004 L 79.00852356323243,132.59806181396488 Z"
        id="path2920" style="fill: rgb(40, 87, 155); fill-opacity: 1; fill-rule: nonzero; stroke: none;" />
      <path
        d="M 78.97830947558595,133.9781109086914 C 78.89178712500001,133.97879825927737 78.805952125,133.97879825927737 78.72063263793946,133.97879825927737 C 78.62621413793946,133.98138001513672 78.58106693969728,134.03253733984377 78.58295296264649,134.15647838574222 C 78.58157826147462,134.28506323925782 78.63668785205078,134.33278381103517 78.73436707617188,134.33415851220704 C 78.84389052441406,134.3334711616211 78.91273036206056,134.3334711616211 78.97830947558595,134.3334711616211 L 78.97830947558595,134.23253121337893 C 78.89109977441407,134.23253121337893 78.805952125,134.23184386279297 78.72595541381835,134.23184386279297 C 78.69436662500001,134.22137433984375 78.68921568676758,134.18137388867189 78.68784517675782,134.15459236279298 C 78.68921568676758,134.13021656396486 78.69110590087891,134.08952876220707 78.72647092675783,134.0811129086914 C 78.83153078735353,134.0811129086914 78.90037062500001,134.08180025927737 78.97830947558595,134.08180025927737 L 78.97830947558595,133.9781109086914 Z M 78.97315853735353,133.14019701513672 L 78.97830947558595,133.14019701513672 L 78.97830947558595,133.2327211098633 L 78.96646525146485,133.2327211098633 C 78.91667843676758,133.2425116645508 78.9036313491211,133.25881528515626 78.87530747558594,133.31598273632812 C 78.79015982617187,133.49435021337894 78.76715453735352,133.54482018750002 78.64990677734376,133.7755436616211 C 78.7599457385254,133.77623101220706 78.86946918676759,133.77623101220706 78.97830947558595,133.77623101220706 L 78.97830947558595,133.87579625927737 L 78.45574700146486,133.87579625927737 C 78.39480331323243,133.87717096044923 78.39342861206055,133.8764836098633 78.39411596264648,133.92970298632815 C 78.39549066381836,134.14463416162113 78.39222993969727,134.2613664086914 78.39274126147461,134.4816203598633 C 78.39411596264648,134.5557871645508 78.39411596264648,134.55836053808594 78.46690387500001,134.5611099404297 L 78.97830947558595,134.5611099404297 L 78.97830947558595,134.6502056645508 L 78.59153646264649,134.6502056645508 C 78.59222381323244,134.76298983691407 78.59222381323244,134.81603318457033 78.59153646264649,134.92367060986328 C 78.59153646264649,134.96624443457034 78.59222381323244,134.96693178515625 78.62947486206056,134.9688261904297 C 78.68595496264649,134.96813883984376 78.89642255029298,134.9688261904297 78.97830947558595,134.9688261904297 L 78.97830947558595,135.06719276513672 C 78.88921375146485,135.06650541455082 78.702950125,135.06529835986328 78.59273932617188,135.06650541455082 C 78.53780576440431,135.06461100927737 78.49248253735352,135.0225568886719 78.49248253735352,134.96745148925783 C 78.49506010205079,134.8992948110352 78.49437275146485,134.80161558691407 78.4938572385254,134.65415373925782 C 78.44922136206056,134.65484108984379 78.43222619970703,134.65672711279296 78.39480331323243,134.65484108984379 C 78.34175577441407,134.65603976220706 78.29901011206056,134.61020521337892 78.29712408911134,134.55441246337892 C 78.29901011206056,134.32437633984375 78.29574938793945,134.0351107133789 78.29712408911134,133.87785831103517 C 78.29643673852539,133.82412761279298 78.33780769970704,133.77742968457034 78.39480331323243,133.77485631103517 C 78.43943918969727,133.77623101220706 78.45505965087891,133.77623101220706 78.53505636206054,133.77297028808596 C 78.65436617382812,133.5513416357422 78.73900250146485,133.3853380869141 78.8176245114746,133.23787623925784 C 78.86552111206055,133.14603111279297 78.9015734885254,133.14088436572268 78.97315853735353,133.14019701513672 M 78.97830947558595,134.3334711616211 L 78.97830947558595,134.23253121337893 C 79.05968088793946,134.23184386279297 79.14225516381836,134.23184386279297 79.22362657617188,134.23184386279297 C 79.25366882617189,134.22789578808596 79.26482569970705,134.19064473925783 79.26482569970705,134.15579103515626 C 79.26482569970705,134.12695583984376 79.25572668676759,134.08764273925783 79.22809016381836,134.08180025927737 L 78.97830947558595,134.08180025927737 L 78.97830947558595,133.9781109086914 C 79.06294161206056,133.9781109086914 79.14689058911134,133.97879825927737 79.22946067382813,133.97879825927737 C 79.32508203735352,133.9781109086914 79.37297863793947,134.0234341357422 79.37435333911134,134.15647838574222 C 79.3736659885254,134.28694926220703 79.32508203735352,134.33227248925783 79.230835375,134.3334711616211 C 79.11804701147462,134.3334711616211 79.05041003735352,134.33278381103517 78.97830947558595,134.3334711616211 M 79.91785905029298,133.77691836279297 L 79.91785905029298,133.8752849375 C 79.62275932617189,133.8752849375 79.30551350146484,133.87579625927737 78.97830947558595,133.87579625927737 L 78.97830947558595,133.77623101220706 C 79.29435243676758,133.77691836279297 79.60645151440431,133.77691836279297 79.91785905029298,133.77691836279297 M 79.91785905029298,134.65158036572268 C 79.70224052441407,134.65158036572268 79.49692387500001,134.65209168750002 79.1257755144043,134.6534663886719 C 79.12645867382813,134.80092823632816 79.12526000146485,134.78256256396486 79.12645867382813,134.96693178515625 C 79.1257755144043,135.01414103515629 79.08783292382813,135.06907878808596 79.02877944970703,135.0678801157227 C 79.02414402441407,135.0678801157227 79.00577835205078,135.0678801157227 78.97830947558595,135.06719276513672 L 78.97830947558595,134.9688261904297 L 78.98809583911134,134.9688261904297 C 79.01830992675781,134.9688261904297 79.02414402441407,134.96813883984376 79.02551872558594,134.94324333691407 C 79.02551872558594,134.83766796337892 79.02414402441407,134.8153458339844 79.02551872558594,134.6502056645508 L 78.97830947558595,134.6502056645508 L 78.97830947558595,134.5611099404297 C 79.2727218491211,134.5611099404297 79.59597780029297,134.56042258984377 79.91785905029298,134.55973523925783 L 79.91785905029298,134.65158036572268 Z M 78.97830947558595,133.14019701513672 C 79.22740281323243,133.14088436572268 79.58155601147462,133.14156333398438 79.91785905029298,133.14225068457034 L 79.91785905029298,133.23392816455078 C 79.58550827734376,133.23392816455078 79.26345518969727,133.23392816455078 78.97830947558595,133.2327211098633 L 78.97830947558595,133.14019701513672 Z M 81.06530283911133,133.9794856098633 L 81.06530283911133,134.08180025927737 C 80.87509113793945,134.08180025927737 80.76110410205078,134.0804339404297 80.57947590087892,134.0804339404297 C 80.54788711206055,134.09296551513674 80.54462638793946,134.1223204145508 80.544110875,134.15459236279298 C 80.54600108911134,134.18927003808597 80.5506365144043,134.22600976513672 80.58204927441406,134.23184386279297 C 80.60230935205078,134.23184386279297 80.96504605029298,134.23115651220706 81.06530283911133,134.23184386279297 L 81.06530283911133,134.3334711616211 C 80.92693581323243,134.3334711616211 80.70994677734376,134.3302104375 80.57947590087892,134.3334711616211 C 80.48179667675782,134.33227248925783 80.43853550146486,134.28557456103516 80.4390510144043,134.15716573632812 C 80.43716080029297,134.03253733984377 80.4829953491211,133.98138001513672 80.5774138491211,133.9794856098633 C 80.73741146264649,133.97879825927737 80.9034150114746,133.9781109086914 81.06530283911133,133.9794856098633 M 81.06530283911133,133.77623101220706 L 81.06530283911133,133.87391023632813 C 80.75783918676758,133.8745975869141 80.36179951440431,133.8745975869141 79.91785905029298,133.8752849375 L 79.91785905029298,133.77691836279297 C 80.29810642382813,133.77691836279297 80.67852982617188,133.77691836279297 81.06530283911133,133.77623101220706 M 81.06530283911133,135.0678801157227 C 80.99508830029298,135.06855908398438 80.92041017382813,135.06907878808596 80.84299102734376,135.06719276513672 C 80.77346383911134,135.06650541455082 80.72951531323243,135.0278796645508 80.72951531323243,134.939982612793 C 80.73020266381837,134.84041736572266 80.72951531323243,134.8076257133789 80.72899980029297,134.65158036572268 L 79.91785905029298,134.65158036572268 L 79.91785905029298,134.55973523925783 C 80.32111171264648,134.5590478886719 80.72298967382812,134.55767318750003 81.06530283911133,134.55647451513673 L 81.06530283911133,134.65158036572268 L 80.83972611206056,134.65158036572268 C 80.84110081323243,134.76418850927735 80.84230367675781,134.82325036572269 80.84230367675781,134.9302004404297 C 80.84230367675781,134.97087985986332 80.85277319970703,134.97139956396487 80.89672172558593,134.97139956396487 C 80.96761942382813,134.97139956396487 81.01603153735351,134.97139956396487 81.06530283911133,134.97087985986332 L 81.06530283911133,135.0678801157227 Z M 79.91785905029298,133.14225068457034 C 80.36248267382814,133.14225068457034 80.77620905029298,133.14225068457034 80.87715318969728,133.1427703886719 C 80.94667618676758,133.1448324404297 81.01551602441407,133.16044871044923 81.06530283911133,133.22345025927734 L 81.06530283911133,133.42740058984378 C 81.04693297558595,133.38997351220706 81.03251118676758,133.36181728515626 81.02530238793946,133.35134776220704 C 80.98135386206056,133.2648254116211 80.92041017382813,133.23324081396487 80.84762226147461,133.23392816455078 C 80.54342352441407,133.2327211098633 80.22600586206056,133.23324081396487 79.91785905029298,133.23392816455078 L 79.91785905029298,133.14225068457034 Z M 81.06530283911133,134.3334711616211 L 81.08762077734376,134.3334711616211 C 81.18135192675783,134.33227248925783 81.23044719970704,134.28694926220703 81.23044719970704,134.15716573632812 C 81.22924852734376,134.0234341357422 81.18203927734376,133.9781109086914 81.08624607617188,133.9794856098633 L 81.06530283911133,133.9794856098633 L 81.06530283911133,134.08180025927737 L 81.08693342675781,134.08180025927737 C 81.12109558911133,134.0850693657227 81.12229426147462,134.1362266904297 81.12229426147462,134.1650618857422 C 81.12161110205079,134.20437498632813 81.1118247385254,134.22789578808596 81.08040778735352,134.23184386279297 L 81.06530283911133,134.23184386279297 L 81.06530283911133,134.3334711616211 Z M 81.06530283911133,133.22345025927734 C 81.07714706323243,133.23787623925784 81.08762077734376,133.25486721044922 81.0967155991211,133.27529493457033 C 81.20693058911132,133.47254778808593 81.22392575146485,133.5020703339844 81.37087627734375,133.77416896044923 C 81.51439005029297,133.77691836279297 81.53859401147461,133.82738833691408 81.54065606323243,133.90670188867188 C 81.54065606323243,133.99391158984378 81.5420307644043,134.3990460839844 81.5420307644043,134.53672575927737 C 81.54065606323243,134.60951786279298 81.49087343969728,134.65158036572268 81.39576758911133,134.65158036572268 C 81.39645493969726,134.806938362793 81.39576758911133,134.81414716162112 81.39696626147462,134.93929526220705 C 81.39576758911133,135.04693268750003 81.34203269970703,135.06135028515627 81.243666125,135.06461100927737 C 81.18924807617188,135.06581806396485 81.12881990087891,135.06719276513672 81.06530283911133,135.0678801157227 L 81.06530283911133,134.97087985986332 C 81.11834618676758,134.97019250927735 81.17276842675781,134.96950515869142 81.25929077734375,134.96813883984376 C 81.28623994970704,134.96745148925783 81.29087537500001,134.96487811572268 81.29087537500001,134.9386079116211 C 81.29087537500001,134.83320018457033 81.28761465087891,134.81672053515626 81.28813016381837,134.65158036572268 L 81.06530283911133,134.65158036572268 L 81.06530283911133,134.55647451513673 C 81.179289875,134.5557871645508 81.28692730029297,134.55509981396486 81.38598122558594,134.55509981396486 C 81.43250312500001,134.55046438867188 81.44486286206055,134.54136118457035 81.44366418969727,134.4836824116211 C 81.44039927441406,134.343429362793 81.44623756323243,134.0850693657227 81.44366418969727,133.9506504145508 C 81.4422894885254,133.88369240869142 81.43044526440431,133.87184818457033 81.35765735205078,133.87322288574222 C 81.2725055114746,133.87322288574222 81.17413893676758,133.87322288574222 81.06530283911133,133.87391023632813 L 81.06530283911133,133.77623101220706 C 81.12555917675782,133.77623101220706 81.18598735205079,133.7755436616211 81.24624368969728,133.7755436616211 C 81.19371166381836,133.6909073339844 81.11439811206054,133.52645451513672 81.06530283911133,133.42740058984378 L 81.06530283911133,133.22345025927734 Z"
        id="path2922" style="fill: rgb(238, 195, 39); fill-opacity: 1; fill-rule: nonzero; stroke: none;" />
    </g>
    <g id="selectionLayer10">
      <g id="DATID_010048" class="DATID_010048_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M14.781 36.628C15.22 36.899 16.013 37.451 16.426 37.686C19.009 39.054 21.981 40.731 24.382 41.456C25.068 41.731 25.184 41.719 25.411 41.784C25.426 42.088 25.576 42.554 25.987 43.618C26.452 44.953 27.129 46.764 27.782 47.767C28.094 48.243 27.928 51.735 27.928 53.204C27.928 54.774 27.78 55.67 27.28 55.969C27.271 55.972 26.905 56.185 26.884 56.197C26.376 56.466 25.524 56.164 24.132 55.359C24.132 55.359 22.998 54.706 22.998 54.706C22.416 54.368 22.37 54.341 22.37 53.448C22.37 52.555 22.039 51.63 21.589 50.881C21.589 50.881 21.167 51.125 21.167 51.125C18.022 46.057 13.289 43.421 10.226 44.918C8.51 46.063 7.822 48.473 8.134 51.35C8.169 51.917 8.146 52.13 8.073 52.149C8.073 52.149 7.711 52.357 7.711 52.357C7.644 52.396 7.405 52.219 7.251 52.131C7.251 52.131 2.94 49.642 2.94 49.642C2.94 48.1 3.282 47.734 3.783 47.445C4.285 47.156 4.477 47.015 4.477 46.585C4.477 46.155 4.517 45.969 4.71 45.859C4.902 45.747 4.974 45.429 4.974 45.164C4.974 45.164 5.367 44.936 5.367 44.936C5.937 45.267 6.394 45.234 6.839 45.024C7.487 44.588 9.221 43.226 9.935 41.923C10.695 40.533 11.952 39.542 14.003 37.424C14.285 37.134 14.542 36.874 14.781 36.628"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_009047" class="DATID_009047_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M14.781 36.628C16.28 35.094 16.934 34.292 16.649 32.067C16.318 29.486 16.152 27.618 15.955 26.329C15.865 25.745 15.643 25.051 15.294 24.371C15.294 23.417 15.634 23.242 16.966 23.865C19.027 25.246 20.908 26.525 21.905 27.265C22.125 27.428 22.326 27.586 22.515 27.746C25.356 29.923 26.216 30.981 26.533 31.437C26.615 31.644 26.786 31.902 26.955 32.308C27.336 33.223 29.623 37.378 30.501 38.75C31.333 40.478 31.986 41.24 32.588 41.954C32.807 42.213 32.59 42.484 32.421 42.485C31.496 42.442 30.661 42.372 30.158 42.304C29.318 42.189 27.146 41.771 26.46 41.503C26.079 41.356 25.757 41.255 25.521 41.265C25.51 41.286 25.501 41.307 25.492 41.331C25.437 41.484 25.402 41.606 25.411 41.784C25.184 41.719 25.068 41.731 24.382 41.456C21.981 40.731 19.009 39.054 16.426 37.686C16.013 37.451 15.22 36.899 14.781 36.628"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_008046" class="DATID_008046_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M16.653 32.104C17.128 32.249 17.538 32.475 17.981 32.811C18.525 33.223 19.653 34.155 20.428 34.194C21.204 34.232 22.181 34.298 22.875 34.811C23.57 35.325 24.826 36.499 25.62 36.597C26.414 36.697 27.803 36.845 28.25 37.143C28.602 37.378 29.676 38.243 30.454 38.676C30.472 38.707 30.486 38.728 30.501 38.75C31.333 40.478 31.986 41.24 32.588 41.954C32.807 42.213 32.59 42.484 32.421 42.485C31.496 42.442 30.661 42.372 30.158 42.304C29.318 42.189 27.146 41.771 26.46 41.503C26.079 41.356 25.757 41.255 25.521 41.265C25.51 41.286 25.501 41.307 25.492 41.331C25.339 41.75 25.339 41.941 25.987 43.618C26.452 44.953 27.129 46.764 27.782 47.767C28.094 48.243 27.928 51.735 27.928 53.204C27.928 54.774 27.78 55.67 27.28 55.969C27.271 55.972 26.905 56.185 26.884 56.197C26.376 56.466 25.524 56.164 24.132 55.359C24.132 55.359 22.998 54.706 22.998 54.706C22.416 54.368 22.37 54.341 22.37 53.448C22.37 52.555 22.039 51.63 21.589 50.881C21.589 50.881 21.167 51.125 21.167 51.125C18.022 46.057 13.289 43.421 10.226 44.918C8.51 46.063 7.822 48.473 8.134 51.35C8.169 51.917 8.146 52.13 8.073 52.149C8.073 52.149 7.711 52.357 7.711 52.357C7.644 52.396 7.405 52.219 7.251 52.131C7.251 52.131 2.94 49.642 2.94 49.642C2.94 48.1 3.282 47.734 3.783 47.445C4.285 47.156 4.477 47.015 4.477 46.585C4.477 46.155 4.517 45.969 4.71 45.859C4.902 45.747 4.974 45.429 4.974 45.164C4.974 45.164 5.367 44.936 5.367 44.936C5.937 45.267 6.394 45.234 6.839 45.024C7.487 44.588 9.221 43.226 9.935 41.923C10.695 40.533 11.952 39.542 14.003 37.424C16.053 35.307 16.984 34.685 16.653 32.104"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_007045" class="DATID_007045_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M16.649 32.067C16.98 34.646 16.053 35.307 14.003 37.424C11.952 39.542 10.695 40.533 9.935 41.923C9.711 42.329 9.39 42.743 9.032 43.131C9.38 43.496 9.673 43.865 9.894 44.113C10.103 44.347 10.323 44.591 10.537 44.783C13.602 43.6 18.128 46.225 21.167 51.125C21.167 51.125 21.589 50.881 21.589 50.881C22.039 51.63 22.37 52.555 22.37 53.448C22.37 54.341 22.416 54.368 22.998 54.706C22.998 54.706 24.132 55.359 24.132 55.359C25.524 56.164 26.376 56.466 26.884 56.197C26.905 56.185 27.28 55.969 27.28 55.969C27.78 55.67 27.928 54.774 27.928 53.204C27.928 51.735 28.094 48.243 27.782 47.767C27.129 46.764 26.452 44.953 25.987 43.618C25.339 41.941 25.339 41.75 25.492 41.331C25.501 41.307 25.51 41.286 25.521 41.265C25.757 41.255 26.079 41.356 26.46 41.503C27.146 41.771 29.318 42.189 30.158 42.304C30.661 42.372 31.496 42.442 32.421 42.485C32.59 42.484 32.807 42.213 32.588 41.954C31.986 41.24 31.333 40.478 30.501 38.75C30.486 38.728 30.472 38.707 30.458 38.685C29.676 38.243 28.602 37.378 28.25 37.143C27.803 36.845 26.414 36.697 25.62 36.597C24.826 36.499 23.57 35.325 22.875 34.811C22.181 34.298 21.204 34.232 20.428 34.194C19.653 34.155 18.525 33.223 17.981 32.811C17.538 32.475 17.123 32.21 16.649 32.067"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_006044" class="DATID_006044_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M26.533 31.437C26.533 31.437 26.533 31.437 26.533 31.437C26.615 31.644 26.786 31.902 26.955 32.308C27.336 33.223 29.623 37.378 30.501 38.75C31.333 40.478 31.986 41.24 32.588 41.954C32.807 42.213 32.59 42.484 32.421 42.485C31.496 42.442 30.661 42.372 30.158 42.304C29.318 42.189 27.146 41.771 26.46 41.503C26.079 41.356 25.757 41.255 25.521 41.265C25.51 41.286 25.501 41.307 25.492 41.331C25.339 41.75 25.339 41.941 25.987 43.618C26.452 44.953 27.129 46.764 27.782 47.767C28.094 48.243 27.928 51.735 27.928 53.204C27.928 54.774 27.78 55.67 27.28 55.969C27.28 55.969 26.905 56.185 26.884 56.197C26.376 56.466 25.524 56.164 24.132 55.359C24.132 55.359 22.998 54.704 22.998 54.704C22.416 54.368 22.37 54.341 22.37 53.448C22.37 52.555 22.039 51.63 21.589 50.881C21.589 50.881 21.167 51.125 21.167 51.125C18.022 46.057 13.289 43.421 10.226 44.918C8.51 46.063 7.822 48.473 8.134 51.35C8.169 51.917 8.146 52.13 8.073 52.149C8.073 52.149 7.711 52.357 7.711 52.357C7.644 52.396 7.405 52.219 7.251 52.131C7.251 52.131 2.94 49.642 2.94 49.642C2.94 48.1 3.282 47.734 3.783 47.445C4.285 47.156 4.477 47.015 4.477 46.585C4.477 46.155 4.517 45.969 4.71 45.859C4.902 45.747 4.974 45.429 4.974 45.164C4.974 45.164 5.367 44.936 5.367 44.936C5.937 45.267 6.394 45.234 6.839 45.024C7.487 44.588 9.221 43.226 9.935 41.923C10.695 40.533 11.952 39.542 14.003 37.424C16.053 35.307 16.98 34.646 16.649 32.067C16.318 29.486 16.152 27.618 15.955 26.329C15.865 25.745 15.643 25.051 15.294 24.371C15.294 23.417 15.634 23.242 16.966 23.865C19.027 25.246 20.908 26.525 21.905 27.265C22.125 27.428 22.326 27.586 22.515 27.746C25.356 29.923 26.216 30.981 26.533 31.437"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_055043" class="DATID_055043_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M65.664 19.289C65.664 19.289 63.725 20.621 63.725 20.621C62.371 21.824 57.643 24.844 55.661 25.957C53.49 27.176 49.982 28.893 48.383 29.655C47.082 30.274 45.881 30.692 44.741 31.01C44.173 31.138 44.152 31.481 43.847 31.748C43.689 31.887 43.368 32.218 43.012 32.585C43.012 32.585 43.012 32.588 43.012 32.588C43.012 32.588 40.344 34.057 40.344 34.057C40.079 33.6 40.265 33.92 39.99 33.442C39.658 32.857 38.807 31.286 38.939 31.022C38.817 30.899 38.686 30.771 38.543 30.637C38.658 28.748 38.917 27.908 39.725 27.088C40.597 26.204 45.756 22.768 50.116 20.221C50.116 20.221 50.497 20.001 50.497 20.001C50.497 20.001 50.514 19.992 50.53 19.983C50.646 19.914 50.762 19.847 50.878 19.78C55.328 17.211 58.292 15.827 59.396 15.541C60.503 15.255 61.112 15.179 62.045 15.368C62.488 15.442 64.677 17.669 65.037 18.03C65.394 18.385 65.177 18.522 65.662 19.291"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_052042" class="DATID_052042_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M75.472 34.954C75.385 34.479 75.31 33.743 75.31 32.741C75.31 30.907 75.334 30.501 75.549 30.216C75.748 29.951 75.765 29.861 75.638 29.606C75.194 28.987 74.144 27.914 73.179 26.85C73.179 26.85 73.036 27.004 73.036 27.004C73.12 27.481 72.941 28.036 72.352 28.809C71.248 30.257 67.43 33.911 65.111 35.25C59.868 38.277 56.707 40.148 48.951 42.11C46.996 42.604 46.651 42.939 45.991 42.064C45.991 42.064 44.96 42.28 44.96 42.28C45.695 43.627 45.735 43.758 45.759 44.159C45.777 44.47 45.887 44.707 46.103 45.414C46.103 45.414 48.256 45.414 48.256 45.414C48.677 45.427 49.106 45.435 49.523 45.435C51.085 45.435 51.505 45.435 51.886 46.159C52.267 46.884 52.42 47.14 52.533 47.408C52.606 47.578 52.655 47.823 52.662 48.06C54.21 47.332 61.299 44.701 64.921 42.529C68.542 40.356 73.457 36.889 74.983 35.363C74.983 35.363 75.471 34.954 75.472 34.954"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_053041" class="DATID_053041_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M52.662 48.06C52.655 47.823 52.606 47.578 52.533 47.408C52.42 47.14 52.267 46.884 51.886 46.159C51.505 45.435 51.085 45.435 49.523 45.435C49.106 45.435 48.677 45.427 48.256 45.414C48.256 45.414 46.103 45.414 46.103 45.414C45.887 44.707 45.777 44.47 45.759 44.159C45.735 43.758 45.695 43.627 44.96 42.28C44.96 42.28 45.991 42.064 45.991 42.064C46.651 42.939 46.996 42.604 48.951 42.11C56.707 40.148 59.868 38.277 65.111 35.25C67.43 33.911 71.248 30.257 72.352 28.809C72.941 28.036 73.12 27.481 73.036 27.004C73.036 27.004 73.179 26.85 73.179 26.85C74.144 27.914 75.194 28.987 75.638 29.606C75.765 29.861 75.748 29.951 75.549 30.216C75.334 30.501 75.31 30.907 75.31 32.741C75.31 33.743 75.385 34.479 75.472 34.954C75.474 34.97 75.481 35.002 75.478 34.985C75.471 34.954 75.471 34.954 75.471 34.954C75.546 35.35 75.629 35.564 75.686 35.597C75.686 35.597 76.046 35.805 76.046 35.805C76.099 36.484 76.087 36.945 76.087 38.088C76.087 39.232 75.706 40.356 74.563 41.423C73.381 42.527 69.456 45.198 65.263 47.618C65.049 47.741 64.837 47.862 64.626 47.982C64.57 48.017 64.189 48.237 64.189 48.237C64.189 48.237 64.167 48.247 64.144 48.259C60.281 50.451 57.088 51.957 55.582 52.601C53.981 53.287 53.258 53.744 52.42 53.898C51.581 54.049 51.2 53.936 50.305 53.673C50.109 53.372 49.896 52.99 49.624 52.478C48.713 52.084 48.139 51.859 47.655 50.92C47.655 50.92 49.402 51.116 49.404 51.115C49.656 51.006 49.872 50.895 50.055 50.789C50.637 50.454 50.99 50.113 51.676 49.6C52.362 49.085 52.552 48.741 52.628 48.436C52.63 48.432 52.631 48.429 52.631 48.424C52.631 48.424 52.651 48.198 52.662 48.06"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_054040" class="DATID_054040_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M47.655 50.92C47.655 50.92 49.402 51.116 49.404 51.115C49.656 51.006 49.872 50.895 50.055 50.789C50.637 50.454 50.99 50.113 51.676 49.6C52.362 49.085 52.552 48.741 52.628 48.436C52.63 48.432 52.631 48.429 52.631 48.424C52.631 48.424 52.662 48.06 52.662 48.06C54.21 47.332 61.299 44.701 64.921 42.529C68.542 40.356 73.457 36.889 74.983 35.363C74.983 35.363 75.471 34.954 75.471 34.954C75.546 35.35 75.629 35.564 75.686 35.597C75.686 35.597 76.046 35.805 76.046 35.805C76.099 36.484 76.087 36.945 76.087 38.088C76.087 39.232 75.706 40.356 74.563 41.423C73.381 42.527 69.456 45.198 65.263 47.618C65.049 47.741 64.837 47.862 64.626 47.982C64.57 48.017 64.189 48.237 64.189 48.237C64.189 48.237 64.167 48.247 64.144 48.259C60.281 50.451 57.088 51.957 55.582 52.601C53.981 53.287 53.258 53.744 52.42 53.898C51.581 54.049 51.2 53.936 50.305 53.673C50.109 53.372 49.896 52.99 49.624 52.478C48.713 52.084 48.139 51.859 47.655 50.92"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_028039" class="DATID_028039_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M32.792 88.804C34.481 90.612 33.206 91.483 34.659 93.498C33.5 93.7 32.122 93.487 31.26 93.563C31.26 93.563 23.492 97.341 23.492 97.341C23.492 97.341 23.425 95.242 23.425 95.242C23.425 95.242 24.219 95.308 24.219 95.308C24.219 95.308 24.219 93.951 24.219 93.951C24.219 93.951 24.897 93.951 24.897 93.951C24.897 93.951 24.873 93.179 24.873 93.179C24.873 93.179 25.481 92.105 25.481 92.105C25.605 92.069 28.461 90.99 32.792 88.804"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_026038" class="DATID_026038_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M24.115 83.802C24.121 83.831 24.138 83.859 24.167 83.89C26.182 85.971 25.565 86.978 25.843 88.501C25.621 88.599 23.339 89.618 23.339 89.618C23.339 89.618 19.722 91.706 19.722 91.706C19.722 91.706 19.364 91.454 19.364 91.454C19.364 91.454 16.536 93.087 16.536 93.087C16.536 93.087 15.419 91.177 15.419 91.177C15.419 91.177 15.419 86.141 15.419 86.141C15.419 86.141 15.923 85.852 15.923 85.852C15.923 85.852 15.923 84.069 15.923 84.069C15.923 84.069 16.754 83.289 16.754 83.289C16.754 83.289 16.754 82.161 16.754 82.161C16.754 82.161 18.175 82.522 18.175 82.522C18.515 82.465 18.928 82.32 19.413 82.04C20.37 81.487 21.96 80.701 23.28 80.066C23.448 81.307 22.836 82.004 24.115 83.802"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_011037" class="DATID_011037_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M30.777 36.552C30.777 36.552 30.373 36.32 30.373 36.32C29.748 35.17 29.207 34.133 29.039 33.728C28.871 33.328 28.704 33.073 28.62 32.868C28.568 32.728 28.55 32.625 28.58 32.512C28.614 32.387 28.708 32.256 28.885 32.09C29.219 31.777 29.643 31.339 30.01 30.963C30.01 30.963 30 30.257 30 30.257C30.004 30.193 29.96 30.114 29.939 30.067C29.939 30.067 29.798 29.943 29.798 29.943C29.798 29.943 28.332 28.649 28.332 28.649C28.123 28.815 27.916 28.96 27.838 28.905C27.722 28.823 27.343 28.541 27.343 28.393C27.343 28.243 27.408 28.128 27.475 28.161C27.541 28.193 27.69 28.227 27.838 27.995C27.986 27.763 28.251 27.483 28.41 27.638C29.103 28.196 29.803 28.806 30.548 29.512C30.727 29.734 30.835 30.004 30.818 30.303C30.818 30.303 30.821 30.658 30.821 30.658C30.823 30.742 30.814 30.829 30.795 30.917C30.332 33.2 30.182 34.314 30.414 34.877C30.646 35.439 30.629 35.482 30.777 35.728C30.925 35.973 31.027 36.304 30.777 36.552"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_024036" class="DATID_024036_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M28.731 75.269C28.731 75.269 28.445 73.799 28.445 73.799C28.431 73.727 28.519 73.65 28.643 73.626C28.701 73.614 28.76 73.617 28.809 73.634C28.809 73.634 29.66 73.76 29.66 73.76C29.705 73.777 29.736 73.804 29.743 73.84C29.743 73.84 29.891 74.599 29.891 74.599C29.891 74.599 29.972 74.552 29.972 74.552C29.972 74.552 30.274 74.531 30.274 74.531C30.274 74.531 31.239 73.974 31.239 73.974C31.239 73.974 32.335 74.087 32.335 74.087C32.335 74.087 32.38 74.403 32.38 74.403C32.38 74.403 31.414 74.959 31.414 74.959C31.414 74.959 31.112 74.981 31.112 74.981C31.112 74.981 29.077 76.156 29.077 76.156C29.077 76.156 28.847 76.445 28.847 76.445C28.847 76.445 27.881 77.003 27.881 77.003C27.881 77.003 26.786 76.891 26.786 76.891C26.786 76.891 26.74 76.573 26.74 76.573C26.74 76.573 27.707 76.016 27.707 76.016C27.707 76.016 27.937 75.727 27.937 75.727C27.937 75.727 28.731 75.269 28.731 75.269"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_027035" class="DATID_027035_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M50.825 81.31C51.03 81.109 51.215 80.909 51.372 80.706C51.372 80.706 51.868 78.936 51.868 78.936C51.868 78.936 52.265 78.671 52.265 78.671C52.265 78.671 52.265 77.861 52.265 77.861C52.265 77.861 51.527 77.429 51.527 77.429C51.527 77.429 51.13 77.155 51.179 77.135C38.334 87.248 25.743 92.03 25.481 92.105C25.481 92.105 24.873 93.179 24.873 93.179C24.873 93.179 24.897 93.951 24.897 93.951C24.897 93.951 24.219 93.951 24.219 93.951C24.219 93.951 24.219 95.308 24.219 95.308C24.219 95.308 23.425 95.242 23.425 95.242C23.425 95.242 23.492 97.341 23.492 97.341C23.492 97.341 31.26 93.563 31.26 93.563C32.341 93.468 34.229 93.826 35.487 93.253C36.765 92.668 41.774 89.968 44.543 88.046C45.359 87.48 46.023 86.234 46.726 85.156C46.726 85.156 50.826 82.31 50.826 82.31C50.826 82.31 50.825 81.31 50.825 81.31"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_025034" class="DATID_025034_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M25.538 79.003C25.538 79.003 28.832 77.1 28.832 77.1C28.832 77.1 29.091 77.049 29.242 77.103C29.393 77.159 29.696 77.313 29.986 77.146C29.986 77.146 29.701 76.625 29.701 76.625C29.701 76.625 29.753 76.575 29.826 76.533C29.9 76.489 29.969 76.469 29.969 76.469C29.969 76.469 30.251 76.984 30.251 76.984C30.251 76.984 32.761 75.536 32.761 75.536C32.761 75.536 32.478 75.021 32.478 75.021C32.478 75.021 32.53 74.971 32.604 74.929C32.679 74.885 32.747 74.865 32.747 74.865C32.747 74.865 33.011 75.347 33.011 75.347C33.024 75.34 33.204 75.29 33.335 74.856C33.466 74.423 33.75 74.233 33.75 74.233C33.75 74.233 37.314 72.016 39.398 70.323C41.481 68.631 43.064 66.938 43.064 66.938C43.064 66.938 44.484 67.301 44.484 67.301C44.484 67.301 44.171 67.635 43.64 68.156C43.628 68.168 43.64 68.12 43.64 68.12C43.64 68.12 43.64 69.792 43.64 69.792C43.64 69.792 44.246 69.792 44.246 69.792C44.246 69.792 44.246 74.739 44.246 74.739C44.246 74.739 43.28 77.647 43.28 77.647C43.28 77.647 40.331 79.35 40.331 79.35C40.331 79.35 40.331 79.805 40.331 79.805C40.331 79.805 40.338 79.805 40.338 79.805C40.338 79.805 36.719 81.893 36.719 81.893C36.719 81.893 33.915 83.871 33.915 83.871C33.915 83.871 26.085 88.393 26.085 88.393C26.085 88.393 23.339 89.618 23.339 89.618C23.339 89.618 19.722 91.706 19.722 91.706C19.722 91.706 19.364 91.454 19.364 91.454C19.364 91.454 16.536 93.087 16.536 93.087C16.536 93.087 15.419 91.177 15.419 91.177C15.419 91.177 15.419 86.141 15.419 86.141C15.419 86.141 15.923 85.852 15.923 85.852C15.923 85.852 15.923 84.069 15.923 84.069C15.923 84.069 16.754 83.289 16.754 83.289C16.754 83.289 16.754 82.161 16.754 82.161C16.754 82.161 18.175 82.522 18.175 82.522C18.515 82.465 18.928 82.32 19.413 82.04C21.292 80.956 25.608 78.971 25.538 79.003"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_068033" class="DATID_068033_V2L3St2Sz1004-S##">
        <polygon pointer-events="all" points="61.375 78.781 78.661 78.781 78.661 83.629 61.375 83.629 61.375 78.781"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_079032" class="DATID_079032_V2L2St2Sz680-S##">
        <path pointer-events="all"
          d="M60.24 113.457C60.277 113.503 60.317 113.537 60.359 113.561C60.359 113.561 62.378 114.725 62.378 114.725C62.412 114.837 62.472 114.914 62.546 114.953C62.853 115.131 63.396 114.763 63.759 114.135C64.123 113.504 64.17 112.85 63.862 112.674C63.862 112.674 63.46 112.441 63.46 112.441C63.453 112.37 63.426 112.298 63.389 112.233C63.289 112.061 63.128 111.968 63.029 112.025C63.029 112.025 62.771 112.174 62.771 112.174C62.771 112.174 61.563 111.477 61.563 111.477C61.231 111.284 60.692 111.596 60.359 112.171C60.253 112.355 60.18 112.545 60.143 112.718C59.625 112.718 59.447 113.319 59.447 113.447C59.447 113.575 59.493 113.769 59.493 113.769C59.493 113.769 60.265 115.106 60.265 115.106C60.344 115.242 60.561 115.263 60.75 115.155C60.939 115.045 61.027 114.846 60.95 114.712C60.95 114.712 60.24 113.457 60.24 113.457"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_023031" class="DATID_023031_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M10.596 57.567C10.596 57.567 10.804 57.682 10.804 57.682C10.804 57.682 10.803 57.679 10.803 57.679C10.82 57.668 10.837 57.658 10.855 57.648C12.35 56.784 13.173 56.6 13.173 56.6C14.801 56.17 15.74 56.232 18.062 57.573C21.617 59.625 23.693 63.511 24.794 66.331C25.548 68.262 24.939 69.647 24.939 69.647C24.757 70.349 24.027 70.734 24.027 70.734C24.027 70.734 21.916 71.953 21.916 71.953C21.916 71.953 21.126 69.759 21.126 69.759C21.126 69.759 20.344 70.036 20.344 70.036C19.361 67.209 17.236 62.633 13.268 60.341C12.971 60.17 12.689 60.021 12.416 59.892C12.278 60.146 12.182 60.378 12.182 60.378C12.182 60.378 10.537 61.651 10.537 61.651C10.537 61.651 10.125 62.473 10.125 62.473C10.125 62.473 8.928 63.185 8.928 63.185C8.928 63.185 8.599 64.099 8.599 64.099C8.599 64.099 7.301 64.849 7.301 64.849C7.301 64.849 7.301 62.521 7.301 62.521C7.301 62.521 7.572 60.71 8.66 59.563C8.66 59.563 9.06 59.134 9.567 58.665C9.567 58.665 9.558 58.167 9.558 58.167C9.557 58.094 9.753 57.923 10.021 57.768C10.31 57.601 10.573 57.517 10.596 57.567"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_002030" class="DATID_002030_V2L1St2Sz356-S##">
        <polygon pointer-events="all" points="7.884 2.61 16.292 2.61 16.292 7.464 7.884 7.464 7.884 2.61"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_044029" class="DATID_044029_V2L2St2Sz680-S##">
        <polygon pointer-events="all" points="70.254 1.201 78.661 1.201 78.661 6.054 70.254 6.054 70.254 1.201"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_004028" class="DATID_004028_V2L1St2Sz356-S##">
        <polygon pointer-events="all" points="7.884 8.557 16.291 8.557 16.291 13.412 7.884 13.412 7.884 8.557"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_021027" class="DATID_021027_V2L2St2Sz680-S##">
        <path pointer-events="all"
          d="M50.137 68.457C50.503 68.652 50.84 68.623 51.298 68.671C52.021 68.748 52.441 68.519 53.774 68.328C55.109 68.138 56.031 67.739 56.613 67.403C57.195 67.067 57.548 66.727 58.234 66.213C58.92 65.698 59.11 65.355 59.186 65.051C59.262 64.745 59.206 64.289 59.091 64.022C58.978 63.755 58.825 63.497 58.444 62.774C58.304 62.511 58.161 62.343 57.96 62.235C57.96 62.235 57.652 62.057 57.652 62.057C57.456 61.939 57.204 61.884 56.84 61.862C55.781 61.25 55.7 61.039 55.716 60.709C55.732 60.378 55.634 60.311 55.435 60.362C55.237 60.411 55.154 60.493 54.972 60.643C54.789 60.792 54.456 60.631 54.045 60.395C53.648 60.164 53.401 59.932 53.17 60.015C52.822 59.932 52.093 59.796 51.085 60.378C50.377 60.786 50.006 61.207 50.006 61.341C49.161 61.173 48.268 60.968 47.871 60.814C47.185 60.546 46.689 60.432 46.536 60.851C46.384 61.271 46.384 61.462 47.031 63.139C47.554 64.643 48.347 66.75 49.07 67.626C49.305 67.909 49.508 68.09 49.7 68.206C49.71 68.212 49.719 68.218 49.719 68.218C49.719 68.218 50.078 68.426 50.137 68.457"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_062026" class="DATID_062026_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M53.021 59.292C53.021 59.384 53.015 59.442 53.079 59.512C53.204 59.712 53.436 59.844 53.698 59.994C53.698 60.289 53.731 60.381 53.902 60.478C54.073 60.578 54.1 60.514 54.347 60.369C54.347 60.369 54.625 60.53 54.625 60.53C54.962 60.725 55.146 60.749 55.435 60.768C56.161 60.808 56.944 60.853 57.704 60.853C58.423 60.853 58.883 60.845 59.207 60.926C59.298 60.948 59.375 60.978 59.441 61.024C59.542 61.095 59.615 61.204 59.692 61.35C59.692 61.35 59.822 61.591 59.896 61.732C59.801 61.787 59.713 61.777 59.598 61.71C59.484 61.645 59.357 61.715 59.234 61.786C59.112 61.856 59.088 61.95 59.375 62.115C59.664 62.283 59.737 62.456 60.154 62.215C60.228 62.354 60.28 62.46 60.326 62.567C60.351 62.627 60.374 62.698 60.39 62.777C59.807 63.112 59.895 63.252 60.161 63.405C60.427 63.56 60.645 63.643 60.768 63.572C61.271 63.28 61.216 63.018 61.133 61.859C61.049 60.701 59.609 59.741 59.198 59.503C58.786 59.265 56.966 58.478 56.594 58.478C56.222 58.478 56.04 58.509 55.643 58.28C55.243 58.049 55.171 58.313 55.171 58.445C55.171 58.576 55.063 58.648 54.899 58.743C54.734 58.838 54.542 58.859 54.262 58.585C53.981 58.313 53.85 58.337 53.476 58.552C53.103 58.768 53.021 58.776 53.021 58.875C53.021 58.902 53.021 58.926 53.021 58.948C53.021 58.948 53.021 59.281 53.021 59.292"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_063025" class="DATID_063025_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M72.853 60.731C72.853 60.731 72.277 61.064 72.277 61.064C72.277 61.064 72.277 60.237 72.277 60.237C72.277 60.237 71.971 60.06 71.971 60.06C71.971 60.06 71.364 60.411 71.364 60.411C71.364 60.411 71.364 61.591 71.364 61.591C71.364 61.591 70.477 62.101 70.477 62.101C70.477 62.101 70.477 62.496 70.477 62.496C70.477 62.496 71.37 63.012 71.37 63.012C71.37 63.012 73.745 61.64 73.745 61.64C73.745 61.64 73.745 61.244 73.745 61.244C73.745 61.244 72.853 60.731 72.853 60.731"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_066024" class="DATID_066024_V2L2St2Sz680-S##">
        <path pointer-events="all"
          d="M31.594 71.233C31.466 71.159 31.26 71.278 31.132 71.5C31.004 71.719 31.004 71.959 31.132 72.031C31.132 72.031 31.393 72.194 31.393 72.194C31.393 72.194 31.385 72.536 31.385 72.536C31.385 72.536 31.498 72.603 31.498 72.603C31.498 72.603 31.434 72.701 31.434 72.701C31.423 72.737 31.42 72.781 31.426 72.831C31.426 72.831 30.6 73.31 30.6 73.31C30.554 73.335 30.536 73.405 30.559 73.49C30.568 73.522 30.58 73.555 30.6 73.587C30.666 73.703 30.774 73.765 30.841 73.727C30.841 73.727 30.87 73.709 30.87 73.709C30.87 73.709 31.667 73.248 31.667 73.248C31.754 73.313 31.879 73.353 31.945 73.316C31.945 73.316 32.222 73.155 32.222 73.155C32.222 73.155 32.374 73.242 32.374 73.242C32.374 73.242 34.503 71.998 34.503 71.998C34.503 71.998 34.503 71.135 34.503 71.135C34.503 71.135 33.972 70.828 33.972 70.828C33.972 70.828 33.972 70.215 33.972 70.215C33.972 70.215 33.631 70.018 33.631 70.018C33.631 70.018 32.981 70.393 32.981 70.393C32.981 70.393 32.622 70.974 32.622 70.974C32.622 70.974 31.897 71.4 31.897 71.4C31.897 71.4 31.594 71.233 31.594 71.233"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_058023" class="DATID_058023_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M68.555 47.896C68.501 47.872 68.453 47.856 68.406 47.844C68.248 47.804 68.125 47.826 68.048 47.851C68.007 47.865 67.977 47.883 67.977 47.883C67.977 47.883 66.355 48.859 66.355 48.859C66.123 48.993 65.95 49.057 65.822 49.079C65.7 49.1 65.606 49.082 65.537 49.045C65.478 49.017 65.433 48.972 65.399 48.918C65.341 48.828 65.319 48.698 65.319 48.557C65.319 48.472 65.349 48.372 65.406 48.268C65.51 48.079 65.703 47.868 65.847 47.725C65.948 47.627 66.024 47.561 66.024 47.561C66.024 47.561 66.037 47.551 66.18 47.442C66.162 47.383 66.168 47.329 66.203 47.296C66.203 47.296 67.686 46.176 67.686 46.176C67.727 46.143 67.799 46.149 67.876 46.194C67.968 46.152 68.093 46.101 68.191 46.088C68.57 46.033 69.052 46.131 69.37 46.375C69.667 46.6 69.837 46.874 69.918 47.03C69.93 47.054 69.939 47.075 69.947 47.093C69.947 47.093 70.213 46.939 70.213 46.939C70.213 46.939 70.927 46.939 70.927 46.939C70.927 46.939 71.51 47.978 71.51 47.978C71.51 47.978 71.51 48.268 71.51 48.268C71.51 48.268 68.713 49.883 68.713 49.883C68.713 49.883 67.578 49.228 67.578 49.228C67.578 49.228 67.578 48.46 67.578 48.46C67.578 48.46 67.965 48.237 67.965 48.237C67.965 48.237 68.334 48.237 68.334 48.237C68.334 48.237 68.334 48.024 68.334 48.024C68.334 48.024 68.555 47.896 68.555 47.896"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_059022" class="DATID_059022_V2L2St2Sz680-S##">
        <path pointer-events="all"
          d="M77.46 51.954C77.061 52.158 76.918 51.453 77.512 51.012C77.512 51.012 80.352 49.17 80.715 48.976C80.715 48.976 80.293 48.363 80.293 48.363C80.293 48.363 80.293 46.213 80.293 46.213C80.293 46.213 75.867 48.768 75.867 48.768C75.867 48.768 75.867 50.918 75.867 50.918C75.867 50.918 80.293 48.363 80.293 48.363C80.293 48.363 80.715 48.976 80.715 48.976C81.287 48.67 81.299 49.509 80.807 49.816C80.807 49.816 77.46 51.954 77.46 51.954"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_064021" class="DATID_064021_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M78.688 62.804C78.688 63.009 78.542 63.212 78.39 63.299C78.39 63.299 70.137 68.061 70.137 68.061C69.915 68.188 69.567 68.045 69.567 67.694C69.567 67.694 69.567 65.26 69.567 65.26C69.567 65.082 69.694 64.858 69.873 64.762C69.873 64.762 78.04 60.045 78.04 60.045C78.397 59.84 78.688 60.003 78.688 60.372C78.688 60.372 78.688 62.804 78.688 62.804"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_015020" class="DATID_015020_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M43.331 42.033C43.331 42.033 42.713 41.676 42.713 41.676C42.612 41.613 42.533 41.588 42.396 41.555C42.075 41.476 41.908 41.505 41.246 41.506C41.246 41.506 40.966 41.472 40.966 41.472C40.966 41.472 40.618 41.457 40.618 41.457C40.618 41.457 39.668 41.448 39.593 41.447C39.531 41.384 39.469 41.319 39.405 41.256C38.503 40.378 37.64 38.762 37.194 37.887C37.194 37.887 32.488 29.533 32.488 29.533C32.226 29.074 31.574 27.899 31.507 27.469C32.006 26.31 32.743 25.471 34.459 24.431C34.459 24.431 40.777 20.71 40.777 20.71C45.368 18.028 50.881 14.843 51.965 14.321C52.406 14.109 52.844 13.996 53.295 13.999C53.756 14.002 54.222 14.121 54.71 14.359C55.258 14.721 55.84 15.226 56.475 15.874C56.7 16.079 57.165 16.557 58.783 18.251C58.783 18.251 61.09 20.457 61.09 20.457C61.945 21.265 62.648 21.928 63.09 22.393C63.651 22.954 63.811 23.989 63.926 24.818C64.021 25.506 64.317 25.986 64.635 26.469C64.787 26.699 64.944 26.932 65.058 27.217C65.37 28 65.594 29.225 65.594 31.463C65.594 32.529 65.307 33.575 64.706 34.536C64.087 35.527 63.14 36.423 61.844 37.171C61.844 37.171 45.633 46.765 45.633 46.765C45.633 46.765 45.554 46.805 45.554 46.805C45.289 46.959 45.143 47.006 44.936 47.036C44.909 47.04 44.883 47.043 44.856 47.045C44.845 47.027 44.835 47.009 44.823 46.99C44.491 46.435 44.409 45.624 44.409 45.116C44.409 43.847 44.103 43.171 43.893 42.707C43.773 42.478 43.572 42.198 43.412 42.085C43.381 42.063 43.347 42.042 43.331 42.033"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
        <path pointer-events="all"
          d="M42.648 42.518C42.832 42.936 43.151 43.539 43.151 44.759C43.151 45.594 43.371 47.113 44.256 47.561C44.256 47.561 44.771 47.859 44.765 47.856C45.185 48.125 45.771 48.142 46.578 47.676C48.915 46.326 59.987 39.698 62.783 38.084C65.581 36.469 66.853 34.185 66.853 31.821C66.853 29.455 66.602 28.163 66.271 27.335C65.941 26.509 65.311 26.045 65.18 25.086C65.048 24.128 64.837 22.943 64.186 22.292C64.186 22.292 64.186 22.292 64.186 22.292C63.386 21.454 61.746 19.968 59.878 18.149C58.24 16.434 57.783 15.987 57.554 15.778C56.764 14.971 56.039 14.378 55.356 13.98C55.35 13.977 55.344 13.974 55.344 13.974C55.344 13.974 54.726 13.617 54.726 13.617C54.726 13.617 54.719 13.612 54.712 13.608C53.396 12.85 52.237 12.818 51.067 13.382C49.28 14.243 35.53 22.294 33.512 23.518C31.495 24.74 30.728 25.722 30.181 27.135C30.091 27.448 30.916 28.964 31.242 29.536C32.25 31.306 33.011 32.521 34.963 36.093C35.362 36.826 35.682 37.408 35.941 37.878C36.521 39.015 37.793 41.283 38.951 41.908C38.951 41.908 39.49 42.219 39.49 42.219C39.704 42.347 39.914 42.423 40.118 42.426C40.118 42.426 41.393 42.448 41.393 42.448C41.936 42.448 42.35 42.485 42.648 42.518"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_051019" class="DATID_051019_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M62.819 34.408C62.819 34.408 62.436 34.628 62.436 34.628C62.436 34.628 62.423 34.636 62.408 34.643C58.28 37.006 55.11 38.618 48.49 40.292C48.49 40.292 47.619 40.529 47.572 40.54C47.359 40.598 47.222 40.551 47.158 40.435C46.243 38.954 44.243 35.323 43.896 34.49C43.896 34.49 43.896 34.49 43.896 34.49C43.825 34.317 43.753 34.165 43.685 34.03C43.295 33.164 43.085 32.92 43.009 32.579C43.365 32.212 43.686 31.881 43.844 31.743C44.149 31.477 44.17 31.134 44.738 31.006C45.878 30.687 47.079 30.268 48.378 29.649C49.979 28.887 53.485 27.172 55.658 25.952C57.64 24.839 62.368 21.818 63.722 20.617C63.726 20.611 63.817 20.53 63.822 20.533C64.239 20.945 64.653 21.542 66.331 23.268C66.331 23.268 68.621 25.457 68.621 25.457C69.245 26.047 69.787 26.559 70.2 26.969C70.518 27.277 70.596 27.699 70.362 28.042C69.209 29.469 65.858 32.654 63.792 33.847C63.47 34.033 63.156 34.213 62.819 34.408"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
        <path pointer-events="all"
          d="M64.626 47.982C64.837 47.862 65.049 47.741 65.263 47.618C69.456 45.198 73.381 42.527 74.563 41.423C75.706 40.356 76.087 39.232 76.087 38.088C76.087 36.945 76.099 36.484 76.046 35.805C76.046 35.805 75.686 35.597 75.686 35.597C75.56 35.524 75.31 34.578 75.31 32.741C75.31 30.907 75.334 30.501 75.549 30.216C75.748 29.951 75.765 29.861 75.638 29.606C75.135 28.903 73.847 27.614 72.793 26.42C72.668 26.225 72.504 26.035 72.314 25.835C71.513 24.996 69.875 23.51 68.007 21.693C66.368 19.978 65.912 19.53 65.683 19.321C65.168 18.521 65.396 18.385 65.034 18.024C64.673 17.664 62.485 15.436 62.042 15.364C61.109 15.173 60.5 15.25 59.393 15.535C58.289 15.82 55.325 17.207 50.875 19.775C50.759 19.843 50.643 19.91 50.527 19.978C50.511 19.986 50.494 19.995 50.494 19.995C50.494 19.995 50.112 20.216 50.112 20.216C45.753 22.763 40.594 26.199 39.722 27.082C38.914 27.902 38.654 28.743 38.54 30.632C38.682 30.765 38.814 30.893 38.936 31.018C38.802 31.282 39.655 32.851 39.987 33.435C40.996 35.206 41.758 36.42 43.709 39.993C45.659 43.564 45.725 43.597 45.759 44.159C45.792 44.722 46.122 45.036 46.85 48.344C47.578 51.651 48.173 51.85 49.624 52.478C49.896 52.99 50.109 53.372 50.305 53.673C51.2 53.936 51.581 54.049 52.42 53.898C53.258 53.744 53.981 53.287 55.582 52.601C57.088 51.957 60.281 50.451 64.144 48.259C64.167 48.247 64.189 48.237 64.189 48.237C64.189 48.237 64.57 48.017 64.626 47.982"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_017018" class="DATID_017018_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M42.57 52.213C42.57 52.213 43.037 52.484 43.037 52.484C43.429 52.688 43.584 53.249 43.584 53.996C43.584 54.899 43.131 55.487 42.408 55.758C41.686 56.029 41.64 56.073 41.822 57.473C42.003 58.874 41.798 59.213 41.369 59.46C40.942 59.707 40.423 59.896 40.219 60.014C40.07 60.1 39.909 60.253 39.728 60.131C39.728 60.131 39.238 59.848 39.238 59.848C39.159 59.811 39.082 59.726 38.993 59.564C38.103 58.194 37.179 55.72 36.545 53.896C35.659 51.606 35.659 51.345 35.868 50.774C35.88 50.741 35.893 50.712 35.906 50.685C36.231 50.67 36.67 50.807 37.189 51.009C38.127 51.375 41.094 51.947 42.238 52.103C42.365 52.115 42.475 52.152 42.57 52.213"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
        <path pointer-events="all"
          d="M39.539 58.676C39.713 58.603 39.942 58.478 40.246 58.302C40.781 57.993 40.686 57.421 40.686 56.756C40.686 56.713 40.685 56.673 40.68 56.637C40.561 56.682 40.426 56.752 40.292 56.829C39.643 57.204 39.524 57.371 39.524 58.116C39.524 58.348 39.527 58.534 39.539 58.676"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_056017" class="DATID_056017_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M70.418 37.561C70.418 37.561 70.322 37.499 70.048 37.664C69.774 37.829 69.277 38.133 69.277 38.133C69.277 38.133 69.299 39.125 69.299 39.125C69.299 39.125 68.792 40.774 68.792 40.774C68.792 40.774 69.623 41.222 69.623 41.222C69.623 41.222 69.894 41.082 70.105 40.796C70.316 40.512 70.524 40.189 70.524 40.189C70.524 40.189 70.734 39.67 70.734 39.67C70.734 39.67 70.798 39.561 70.945 39.515C71.094 39.47 71.316 39.39 71.316 39.39C71.316 39.39 71.65 39.719 71.941 39.77C71.941 39.77 71.876 39.984 71.984 40.171C72.093 40.36 72.427 41.003 72.427 41.003C72.427 41.003 73.582 40.335 73.582 40.335C73.582 40.335 73.058 39.426 73.058 39.426C73.405 39.31 73.629 39.003 73.703 38.612C73.703 38.612 74.838 38.427 74.838 38.427C74.838 38.427 74.691 37.692 74.691 37.692C74.691 37.692 76.79 37.35 76.79 37.35C77.042 37.31 77.195 36.948 77.155 36.515C77.155 36.515 77.29 36.493 77.29 36.493C77.322 36.274 77.215 36.076 77.215 36.076C77.215 36.076 77.054 36.103 77.054 36.103C76.923 35.787 76.707 35.618 76.512 35.646C76.512 35.646 73.74 36.098 73.74 36.098C73.734 36.063 73.722 36.029 73.706 35.999C73.641 35.877 73.513 35.817 73.424 35.865C73.424 35.865 72.644 36.29 72.644 36.29C72.597 36.316 72.569 36.365 72.563 36.423C72.563 36.423 72.501 36.457 72.501 36.457C72.397 36.356 72.271 36.332 72.177 36.381C72.177 36.381 72.084 36.429 72.084 36.429C72.084 36.429 71.427 36.476 71.427 36.476C71.427 36.476 70.587 36.962 70.587 36.962C70.406 37.06 70.34 37.305 70.418 37.561"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_057016" class="DATID_057016_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M70.462 43.756C70.873 43.899 71.382 43.506 71.596 42.881C71.811 42.256 71.653 41.634 71.242 41.493C70.915 41.381 70.527 41.604 70.269 42.023C69.938 41.909 69.546 42.139 69.289 42.564C68.95 42.473 68.561 42.726 68.316 43.164C67.908 43.063 67.43 43.456 67.224 44.052C67.009 44.677 67.168 45.299 67.579 45.441C67.923 45.558 68.331 45.307 68.587 44.851C68.912 44.932 69.28 44.701 69.524 44.298C69.846 44.384 70.215 44.158 70.462 43.756"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_018015" class="DATID_018015_V2L2St2Sz680-S##">
        <path pointer-events="all"
          d="M9.887 51.362C9.275 51.735 8.747 52.24 8.319 52.868C7.831 53.582 7.471 54.466 7.274 55.5C7.128 56.488 7.161 57.051 7.161 57.448C7.161 57.54 7.16 57.615 7.152 57.671C7.131 57.671 7.11 57.671 7.088 57.671C6.945 57.671 6.844 57.689 6.769 57.731C6.769 57.731 6.424 57.93 6.424 57.93C6.291 58 6.259 58.151 6.259 58.426C6.259 58.424 7.698 59.256 7.698 59.256C7.934 59.392 8.054 59.539 8.135 59.496C8.135 59.496 8.521 59.274 8.521 59.274C8.593 59.256 8.617 59.042 8.582 58.475C8.269 55.6 8.958 53.188 10.673 52.043C13.868 50.472 18.841 53.429 21.937 58.789C22.58 59.889 23.155 61.152 23.579 62.463C23.579 62.462 24.602 62.462 24.602 62.462C24.602 62.462 24.957 62.256 24.957 62.256C24.542 60.889 23.937 59.478 23.138 58.095C20.757 53.969 17.297 51.22 14.292 50.587C12.839 50.28 11.475 50.444 10.323 51.109C10.323 51.109 10.317 51.112 10.317 51.112C10.307 51.118 10.297 51.125 10.287 51.131C10.275 51.137 10.265 51.143 10.265 51.143C10.265 51.143 9.909 51.35 9.887 51.362"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_005014" class="DATID_005014_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M34.359 18.62C34.192 18.463 34.076 18.3 34.039 18.21C33.93 17.949 34.42 18.129 33.621 17.644C33.481 17.561 33.18 17.439 33.18 17.015C33.18 16.797 33.213 16.678 33.442 16.602C33.67 16.525 34.137 16.268 34.256 16.199C34.448 16.088 34.572 16.091 34.68 16.265C34.738 16.359 35.103 16.754 35.179 16.829C35.256 16.905 35.234 17.286 35.32 17.437C35.304 17.458 35.277 17.489 35.223 17.542C35.198 18.106 35.515 18.318 35.865 18.343C35.905 18.099 35.908 17.824 35.865 17.634C35.777 17.254 35.408 16.655 35.246 16.46C34.959 16.117 34.223 15.308 34.223 15.102C34.223 14.896 34.731 14.582 34.865 14.503C35.112 14.362 35.451 14.178 35.561 14.124C35.668 14.07 36.252 14.022 36.322 14.134C36.494 14.417 36.441 14.56 36.429 14.734C36.418 14.912 36.356 15.07 35.343 15.656C35.164 15.759 36.17 16.922 36.223 17.21C36.353 17.917 36.978 17.388 37.017 17.579C37.093 17.96 36.939 18.271 35.853 18.899C35.654 19.257 35.176 19.228 34.953 19.175C34.787 19.135 34.621 19.07 34.487 18.963C34.353 18.856 34.42 18.763 34.359 18.62"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_014013" class="DATID_014013_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M3.016 40.825C2.835 40.47 2.508 40.265 2.198 40.338C1.811 40.429 1.593 40.917 1.713 41.429C1.833 41.942 2.243 42.284 2.632 42.195C2.702 42.179 2.774 42.146 2.842 42.095C3.016 42.469 3.352 42.689 3.673 42.615C3.742 42.6 3.815 42.567 3.882 42.518C4.059 42.89 4.394 43.11 4.714 43.036C4.785 43.021 4.857 42.987 4.924 42.938C5.099 43.311 5.437 43.531 5.756 43.457C6.144 43.366 6.362 42.878 6.242 42.365C6.123 41.853 5.711 41.511 5.323 41.6C5.254 41.615 5.184 41.648 5.118 41.697C4.936 41.323 4.601 41.106 4.282 41.179C4.213 41.195 4.141 41.228 4.073 41.277C3.895 40.905 3.56 40.685 3.24 40.759C3.181 40.771 3.121 40.798 3.016 40.825"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_020012" class="DATID_020012_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M43.167 58.926C43.167 58.926 43.482 59.109 43.482 59.109C43.482 59.109 43.591 59.046 43.591 59.046C43.682 59.006 43.725 58.999 43.866 59.081C43.866 59.081 44.012 59.164 44.012 59.164C44.192 59.268 44.326 59.697 44.603 60.478C45 61.601 45.054 61.697 44.359 62.097C44.359 62.097 42.957 62.908 42.957 62.908C42.601 63.115 42.487 63.164 42.353 63.085C42.353 63.085 42.146 62.966 42.146 62.966C41.862 62.802 41.844 62.561 41.874 61.304C41.887 60.698 41.884 60.363 41.938 60.173C41.938 60.173 41.938 59.64 41.938 59.64C41.938 59.64 43.17 58.929 43.167 58.926"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_046011" class="DATID_046011_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M47.348 21.015C47.176 21.106 47.067 21.237 46.957 21.361C46.871 21.457 46.787 21.55 46.692 21.632C46.551 21.757 46.273 22.044 45.956 22.368C45.597 22.736 45.192 23.152 44.871 23.454C44.771 23.548 44.706 23.591 44.674 23.664C44.674 23.664 44.328 23.864 44.328 23.864C43.375 22.268 43.009 21.42 41.536 20.033C41.649 18.146 41.909 17.303 42.719 16.484C43.618 15.571 49.04 11.965 53.49 9.397C57.939 6.828 60.904 5.442 62.009 5.157C63.113 4.871 63.723 4.794 64.658 4.985C65.1 5.059 67.287 7.285 67.65 7.647C68.01 8.007 67.781 8.144 68.298 8.942C68.418 9.053 68.602 9.228 68.987 9.621C68.987 9.621 68.641 9.823 68.641 9.823C68.641 9.823 67.703 9.278 67.703 9.278C67.629 9.333 67.552 9.416 67.489 9.522C67.448 9.571 67.406 9.621 67.364 9.673C67.364 9.673 67.266 9.791 67.266 9.791C67.266 9.791 67.022 10.092 67.022 10.092C67.022 10.092 66.903 10.217 66.903 10.217C66.459 10.784 64.048 12.43 61.786 13.871C61.722 14.25 61.503 14.378 60.789 14.791C59.84 15.339 57.597 16.647 56.688 17.172C55.941 17.605 55.884 17.763 55.393 17.519C53.758 18.349 52.113 19.149 51.152 19.606C49.795 20.253 48.536 20.689 47.348 21.015"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_048010" class="DATID_048010_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M59.576 16.868C59.557 16.879 59.539 16.888 59.523 16.899C59.347 16.998 59.162 17.1 58.97 17.204C58.792 17.1 58.488 17.181 58.488 17.463C58.304 17.562 58.113 17.664 57.92 17.765C57.417 17.475 57.317 17.472 57.1 17.597C56.884 17.724 56.837 17.681 56.837 17.913C56.837 18.143 56.811 18.21 56.993 18.315C56.877 18.466 56.744 18.64 56.679 18.739C56.597 18.864 56.619 18.841 57.106 19.123C58 19.638 57.886 19.518 58.795 18.993C59.704 18.469 61.948 17.16 62.896 16.612C63.844 16.065 63.92 16.016 63.92 15.224C63.92 15.224 63.063 14.727 63.063 14.727C63.006 14.763 62.951 14.798 62.898 14.833C62.396 14.542 62.295 14.541 62.079 14.666C61.862 14.791 61.814 14.75 61.814 14.98C61.814 15.212 61.834 15.291 62.014 15.396C61.478 15.733 60.968 16.047 60.514 16.32C60.012 16.028 59.911 16.027 59.695 16.152C59.478 16.277 59.43 16.234 59.43 16.466C59.43 16.698 59.396 16.765 59.576 16.868"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_013009" class="DATID_013009_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M18.243 41.892C18.243 41.892 18.548 41.715 18.548 41.715C18.832 41.551 18.687 41.274 18.553 41.031C18.338 40.636 15.022 34.527 15.022 34.527C13.922 32.573 13.227 31.321 11.371 29.948C10.374 29.207 8.493 27.928 6.432 26.548C6.264 26.434 6.192 26.445 6.038 26.533C6.038 26.533 5.785 26.679 5.785 26.679C5.649 26.748 5.615 26.99 5.69 27.257C5.774 27.553 5.831 27.873 5.897 28.24C5.897 28.24 5.966 28.509 5.966 28.509C5.966 28.509 5.987 28.737 5.987 28.737C5.987 28.737 6.065 29.367 6.065 29.367C6.371 30.978 6.561 31.798 6.905 34.479C6.905 34.479 6.958 35.057 6.958 35.057C6.958 35.057 7.113 36.014 7.113 36.014C7.113 36.014 7.155 36.38 7.155 36.38C7.184 36.575 7.206 36.732 7.222 36.862C7.29 37.503 7.403 37.512 7.897 37.686C8.606 37.993 10.323 38.734 10.323 38.734C12.138 39.53 13.978 40.337 15.231 40.814C15.711 40.997 16.117 41.173 16.486 41.335C16.987 41.551 17.422 41.74 17.902 41.88C18.064 41.933 18.176 41.93 18.243 41.892"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_075008" class="DATID_075008_V2L2St2Sz680-S##">
        <path pointer-events="all"
          d="M66.246 103.457C66.246 103.457 66.246 103.349 66.246 103.349C66.246 103.349 65.855 103.114 65.855 103.114C65.855 103.114 65.865 102.664 65.865 102.664C65.865 102.664 65.9 102.584 65.769 102.507C65.635 102.43 65.435 102.319 65.435 102.319C65.435 102.319 65.299 102.257 65.087 102.381C65.087 102.381 63.738 103.159 63.738 103.159C63.738 103.159 63.509 103.233 63.509 103.68C63.509 103.68 61.493 104.843 61.493 104.843C61.493 104.843 61.335 104.968 61.335 105.388C61.335 105.388 60.77 105.713 60.77 105.713C60.77 105.713 60.646 105.757 60.646 106.034C60.646 106.308 60.841 106.257 60.841 106.257C60.841 106.257 60.515 106.445 60.515 106.445C60.515 106.445 56.484 107.897 56.484 107.897C56.484 107.897 55.475 107.581 55.475 107.581C55.475 107.581 54.841 107.509 54.765 107.552C54.765 107.552 54.52 107.411 54.52 107.411C54.52 107.411 53.438 107.79 53.438 107.79C53.438 107.79 48.167 104.724 48.167 104.724C48.167 104.724 47.365 105.186 47.365 105.186C47.365 105.186 47.365 105.421 47.365 105.421C47.365 105.421 47.822 105.683 47.822 105.683C47.822 105.683 47.822 106.272 47.822 106.272C47.822 106.272 47.365 106.536 47.365 106.536C47.365 106.536 47.365 106.769 47.365 106.769C47.365 106.769 53.085 110.072 53.085 110.072C53.085 110.072 53.085 110.156 53.085 110.156C53.085 110.156 53.09 110.29 53.201 110.355C53.314 110.42 53.317 110.412 53.317 110.412C53.317 110.412 53.43 110.463 53.542 110.399C53.542 110.399 53.862 110.215 53.862 110.215C53.862 110.215 56.363 110.215 56.363 110.215C56.363 110.215 56.363 110.376 56.363 110.376C56.363 110.376 56.914 110.376 56.914 110.376C56.914 110.376 57.64 110.093 57.64 109.659C57.64 109.224 57.655 109.224 57.655 109.224C57.655 109.224 59.198 108.662 59.198 108.662C59.198 108.662 59.198 108.98 59.198 108.98C59.198 108.98 58.46 109.406 58.46 109.406C58.46 109.406 58.46 109.902 58.46 109.902C58.46 109.902 58.734 110.06 58.734 110.06C58.734 110.06 60.604 108.98 60.604 108.98C60.604 108.98 60.604 108.734 60.604 108.734C60.604 108.734 62.1 107.602 62.1 107.602C62.1 107.602 62.527 107.397 62.527 107.397C62.527 107.397 62.517 108.852 62.517 108.852C62.517 108.852 62.499 109.016 62.615 109.082C62.729 109.149 62.78 109.197 63.027 109.054C63.027 109.054 63.609 108.718 63.609 108.718C63.609 108.718 63.716 108.665 63.716 108.506C63.716 108.506 63.716 107.853 63.716 107.853C63.716 107.853 63.898 107.957 63.898 107.957C63.898 107.957 63.898 108.26 63.898 108.26C63.898 108.26 64.102 108.962 64.912 109.43C65.406 109.715 65.751 109.935 65.947 110.063C65.968 110.078 65.959 110.085 65.971 110.108C66.15 110.418 66.441 110.585 66.62 110.483C66.62 110.483 66.789 110.552 66.856 110.561C66.856 110.561 67.329 110.618 67.329 110.618C67.329 110.618 68.64 110.626 68.64 109.507C68.64 109.507 68.64 109.1 68.64 109.1C68.795 109.016 68.871 108.903 68.871 108.789C68.871 108.789 68.871 108.057 68.871 108.057C68.871 107.819 68.537 107.625 68.126 107.625C67.712 107.625 67.379 107.819 67.379 108.057C67.379 108.057 67.379 108.64 67.379 108.64C67.177 108.496 66.986 108.471 66.852 108.549C66.852 108.549 66.615 108.685 66.615 108.685C66.615 108.685 66.15 108.65 65.852 108.412C65.555 108.173 64.974 107.621 64.974 107.621C64.974 107.621 65.465 107.337 65.465 107.337C65.465 107.337 65.465 105.733 65.465 105.733C65.465 105.733 68.703 103.862 68.703 103.862C68.703 103.862 68.923 103.817 69.387 103.382C69.85 102.947 71.837 101.135 71.837 101.135C71.837 101.135 72.069 101.403 72.069 101.403C72.069 101.403 72.26 101.649 72.498 101.522C72.56 101.489 72.625 101.396 72.625 101.179C72.625 100.96 72.625 100.022 72.625 100.022C72.625 100.022 72.73 99.564 72.73 99.564C72.73 99.564 72.751 97.477 72.751 97.477C72.751 97.477 72.275 97.201 72.275 97.201C72.275 97.201 72.293 97.15 72.207 97.099C72.121 97.049 72.081 97.037 72.081 97.037C72.081 97.037 71.816 97.266 71.816 97.266C71.816 97.266 66.566 94.234 66.566 94.234C66.566 94.234 66.436 94.18 66.364 94.221C66.364 94.221 65.79 94.552 65.79 94.552C65.561 94.685 65.51 94.933 65.51 94.933C65.51 94.933 65.51 96.091 65.51 96.091C65.51 96.091 71.316 99.444 71.316 99.444C71.316 99.444 68.394 102.108 68.394 102.108C68.394 102.108 67.944 102.489 67.524 102.731C67.102 102.974 66.246 103.457 66.246 103.457"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_012007" class="DATID_012007_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M12.311 33.344C12.759 33.002 13.131 32.716 13.378 32.531C13.042 32.046 12.679 31.597 12.236 31.157C11.75 31.535 10.724 32.286 9.602 33.076C9.602 33.076 9.602 32.656 9.602 32.656C10.618 31.945 11.522 31.283 11.989 30.92C11.708 30.661 11.396 30.402 11.041 30.138C10.923 30.051 10.794 29.957 10.653 29.856C9.776 30.536 8.445 31.46 6.645 32.6C6.724 33.118 6.809 33.722 6.905 34.472C6.905 34.472 6.905 34.472 6.905 34.472C7.727 33.951 8.676 33.305 9.548 32.695C9.548 32.695 9.548 33.113 9.548 33.113C8.664 33.734 7.724 34.375 6.94 34.872C6.952 34.996 6.956 35.049 6.956 35.049C6.956 35.049 7.112 36.006 7.112 36.006C7.112 36.006 7.155 36.372 7.155 36.372C7.182 36.567 7.205 36.725 7.222 36.856C7.235 36.985 7.251 37.09 7.272 37.173C7.706 36.86 8.329 36.393 9.016 35.871C9.167 35.978 9.335 36.018 9.484 35.968C9.492 35.965 9.5 35.963 9.506 35.96C9.698 36.308 10.005 36.481 10.257 36.405C10.455 36.75 10.762 36.915 11.013 36.834C11.021 36.832 11.028 36.829 11.032 36.826C11.228 37.183 11.542 37.359 11.8 37.274C11.827 37.265 11.852 37.253 11.874 37.24C12.141 37.087 12.227 36.652 12.086 36.228C11.926 35.756 11.541 35.46 11.226 35.567C11.218 35.569 11.211 35.57 11.205 35.573C11.012 35.22 10.705 35.046 10.449 35.122C10.4 35.036 10.346 34.963 10.287 34.901C10.949 34.392 11.753 33.774 12.311 33.344"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_065006" class="DATID_065006_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M80.072 64.042C80.072 64.231 79.936 64.42 79.796 64.501C79.796 64.501 72.12 68.93 72.12 68.93C71.915 69.049 71.59 68.915 71.59 68.588C71.59 68.588 71.59 66.325 71.59 66.325C71.59 66.159 71.707 65.95 71.875 65.861C71.875 65.861 79.471 61.475 79.471 61.475C79.802 61.284 80.072 61.436 80.072 61.78C80.072 61.78 80.072 64.042 80.072 64.042"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_061005" class="DATID_061005_V2L1St2Sz356-S##">
        <polygon pointer-events="all" points="61.063 58.164 65.061 55.856 65.061 53.942 61.063 56.25 61.063 58.164"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_016004" class="DATID_016004_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M39.237 49.512C39.237 49.512 38.877 49.304 38.877 49.304C38.823 49.29 38.807 49.219 38.802 49.085C38.802 49.085 38.802 48.674 38.802 48.674C38.802 48.674 38.795 48.68 38.795 48.689C38.77 48.84 38.701 48.962 38.588 49.027C38.296 49.195 37.825 48.923 37.533 48.418C37.429 48.24 37.354 48.039 37.326 47.841C37.298 47.494 37.381 47.326 37.57 47.179C37.57 47.179 40.712 45.365 40.712 45.365C40.917 45.25 41.222 45.341 41.505 45.628C41.505 45.628 41.945 45.372 41.945 45.372C41.945 45.372 42.307 45.581 42.307 45.581C42.307 45.581 42.322 45.606 42.322 45.606C42.322 45.606 42.322 49.847 42.322 49.847C42.322 50.249 42.168 50.42 42.026 50.503C41.942 50.503 41.878 50.487 41.829 50.448C41.829 50.448 41.521 50.271 41.521 50.271C41.404 50.222 41.35 50.094 41.35 49.814C41.35 49.814 41.35 49.557 41.35 49.557C41.345 49.549 40.953 49.777 40.953 49.777C40.841 49.841 40.777 49.856 40.74 49.817C40.728 49.804 40.771 49.835 40.771 49.835C40.771 49.835 40.411 49.628 40.411 49.628C40.357 49.613 40.341 49.542 40.341 49.408C40.341 49.408 40.341 48.92 40.341 48.92C40.341 48.92 39.417 49.453 39.417 49.453C39.307 49.517 39.243 49.533 39.237 49.512"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_049003" class="DATID_049003_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M66.608 35.539C67.423 35.079 68.263 34.593 69.165 34.072C71.486 32.732 75.302 29.079 76.408 27.631C77.513 26.181 77.17 25.495 76.37 24.658C75.569 23.818 73.93 22.332 72.063 20.515C70.424 18.8 69.966 18.352 69.737 18.143C68.486 16.879 67.581 16.762 66.509 17.362C66.498 17.368 66.487 17.375 66.487 17.375C66.487 17.375 66.128 17.582 66.128 17.582C66.128 17.582 66.114 17.589 66.102 17.597C65.87 17.731 65.632 17.896 65.381 18.091C63.768 19.338 60.735 21.217 57.469 23.103C57.454 23.111 57.438 23.12 57.438 23.12C57.438 23.12 57.076 23.329 57.076 23.329C57.076 23.329 57.06 23.335 57.046 23.344C53.505 25.364 49.799 27.335 47.399 28.474C47.159 28.586 46.929 28.71 46.712 28.841C46.712 28.841 46.694 28.855 46.694 28.855C46.694 28.855 46.334 29.062 46.334 29.062C46.334 29.062 46.317 29.068 46.317 29.068C44.936 29.899 44.033 30.968 44.155 31.201C44.573 31.809 44.911 32.439 45.408 33.273C45.468 33.5 45.664 33.77 45.86 34.241C46.243 35.156 48.529 39.31 49.405 40.682C50.283 42.054 50.435 41.7 52.646 41.14C58.993 39.533 62.264 37.988 66.117 35.82C66.15 35.802 66.183 35.787 66.183 35.787C66.183 35.787 66.545 35.579 66.608 35.539"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_047002" class="DATID_047002_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M71.972 12.489C71.915 12.647 71.933 12.778 72.01 12.849C72.01 12.849 77.486 17.951 77.486 17.951C77.582 18.042 77.756 18.024 77.93 17.922C77.93 17.922 80.977 20.762 80.977 20.762C80.887 20.963 80.908 21.149 81.016 21.254C81.016 21.254 81.766 21.951 81.766 21.951C81.974 22.146 82.326 22.106 82.552 21.864C82.778 21.623 82.792 21.268 82.584 21.074C82.584 21.074 81.834 20.376 81.834 20.376C81.718 20.268 81.527 20.272 81.341 20.371C81.341 20.371 78.293 17.532 78.293 17.532C78.39 17.341 78.384 17.173 78.292 17.086C78.292 17.086 72.816 11.984 72.816 11.984C72.734 11.907 72.599 11.909 72.454 11.971C72.454 11.971 72.373 11.897 72.373 11.897C72.43 11.737 72.412 11.608 72.334 11.535C72.334 11.535 71.371 10.637 71.371 10.637C71.155 10.435 70.799 10.465 70.575 10.705C70.353 10.943 70.349 11.301 70.566 11.503C70.566 11.503 71.528 12.4 71.528 12.4C71.611 12.477 71.746 12.477 71.891 12.413C71.891 12.413 71.972 12.489 71.972 12.489"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
      </g>
      <g id="DATID_050001" class="DATID_050001_V2L1St2Sz356-S##">
        <path pointer-events="all"
          d="M73.629 19.282C73.629 19.282 74.021 18.818 74.021 18.818C74.021 18.818 73.816 18.615 73.816 18.615C73.816 18.615 64.914 28.254 64.914 28.254C64.914 28.254 65.203 28.411 65.203 28.411C65.203 28.411 65.593 28.022 65.593 28.022C65.921 28.146 66.212 27.899 66.212 27.899C66.212 27.899 69.469 24.626 69.469 24.626C69.469 24.626 69.796 24.539 69.796 24.539C69.796 24.539 70.248 23.954 70.248 23.954C70.248 23.954 70.259 23.777 70.259 23.777C70.259 23.777 73.331 20.28 73.331 20.28C73.331 20.28 73.772 19.754 73.629 19.282"
          stroke-width="0.25" fill-opacity="0" stroke-opacity="0" />
        <path pointer-events="all"
          d="M73.629 19.282C73.629 19.282 74.021 18.818 74.021 18.818C74.021 18.818 73.816 18.615 73.816 18.615C73.816 18.615 64.914 28.254 64.914 28.254C64.914 28.254 65.203 28.411 65.203 28.411C65.203 28.411 65.593 28.022 65.593 28.022C65.921 28.146 66.212 27.899 66.212 27.899C66.212 27.899 69.469 24.626 69.469 24.626C69.469 24.626 69.796 24.539 69.796 24.539C69.796 24.539 70.975 26.088 70.975 26.088C72.894 23.89 76.795 19.414 76.795 19.414C76.795 19.414 77.001 19.169 77.317 19.169C77.635 19.169 77.87 19.39 77.691 19.617C77.513 19.844 69.864 29.413 69.864 29.413C69.864 29.413 70.594 30.346 70.004 30.687C69.414 31.027 69.406 31.039 69.406 31.039C69.406 31.039 68.873 31.356 68.45 30.579C68.025 29.801 68.356 29.425 68.356 29.425C68.356 29.425 69.135 28.35 70.975 26.088C70.975 26.088 69.796 24.539 69.796 24.539C69.796 24.539 70.248 23.954 70.248 23.954C70.248 23.954 70.259 23.777 70.259 23.777C70.259 23.777 73.331 20.28 73.331 20.28C73.331 20.28 73.772 19.754 73.629 19.282"
          stroke-width="0.12" fill-opacity="0" stroke-opacity="0" />
      </g>
    </g>
  </g>
</svg>