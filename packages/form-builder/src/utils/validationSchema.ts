import * as Yup from 'yup';

export const getValidationSchema = (fieldsObj: Record<string, any>) => {
    let shape = {};
    fieldsObj.fields.forEach((field: DAT2.Plugins.FormBuilder.DataField) => {
        if (field.pattern?.max) {
            shape = {
                ...shape,
                ...{
                    [field.id]: Yup.string().max(field.pattern.max, `Must be ${field.pattern.max} characters or less`)
                }
            };
        }
        if (field.pattern?.mask) {
            switch (field.pattern.mask) {
                case '99/9999':
                    shape = {
                        ...shape,
                        ...{
                            [field.id]: Yup.string().matches(
                                /^(0[1-9]|1[0-2])\/\d{4}$/,
                                'Invalid date format (MM/YYYY)'
                            )
                        }
                    };
                    break;
                default:
                    break;
            }
        }
        if (field.pattern?.min) {
            shape = {
                ...shape,
                ...{
                    [field.id]: Yup.string().min(field.pattern.min, `Must be ${field.pattern.min} characters or more`)
                }
            };
        }
        if (field.required) {
            shape = {
                ...shape,
                ...{
                    [field.id]: Yup.string().required('Required')
                }
            };
        }
        if (field.type === 'email') {
            shape = {
                ...shape,
                ...{
                    [field.id]: Yup.string().email('Invalid email')
                }
            };
        }
        if (field.pattern?.max_value) {
            shape = {
                ...shape,
                ...{
                    [field.id]: Yup.number().max(field.pattern.max_value, `Must be ${field.pattern.max_value} or less`)
                }
            };
        }
        if (field.pattern?.min_value) {
            shape = {
                ...shape,
                ...{
                    [field.id]: Yup.number().min(field.pattern.min_value, `Must be ${field.pattern.max_value} or more`)
                }
            };
        }
    });

    return shape;
};
