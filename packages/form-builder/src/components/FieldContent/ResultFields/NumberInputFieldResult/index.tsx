import { memo, useEffect, useRef } from 'react';
import { useField } from 'formik';
import { NumberInputField, NumberInputFieldProps } from '@wedat/ui-kit/Formik';

export interface InputFieldResultProps extends NumberInputFieldProps {
    computedValue?: string;
    onComputedChange?: (params: { value: number; name: string }) => void;
}

export const NumberInputFieldResult = memo<InputFieldResultProps>(({ computedValue, onComputedChange, ...props }) => {
    const { name } = props;
    const [field, , { setValue }] = useField<number | undefined>(name);

    const setValueRef = useRef(setValue);
    setValueRef.current = setValue;

    useEffect(() => {
        if (typeof computedValue !== 'undefined' && !Number.isNaN(+computedValue) && field.value !== +computedValue) {
            setValueRef.current(+computedValue);
            onComputedChange?.({ name, value: +computedValue });
        }
    }, [name, onComputedChange, field.value, computedValue]);

    return <NumberInputField {...props} />;
});
