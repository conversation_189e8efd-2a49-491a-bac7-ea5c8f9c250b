import { useCallback } from 'react';
import { useUnit } from 'effector-react';
import { conditionRulesStore } from '../stores/conditions';

export const useGetConditionRules = () => {
    const conditionRules = useUnit(conditionRulesStore);

    return useCallback(
        (formName: string, currentConditionName: string) => {
            return Object.entries(conditionRules).reduce<DAT2.Plugins.FormBuilder.DataCondition | null>(
                (acc, [key, value]) => {
                    if (key === formName) {
                        return (
                            value.find(
                                (el: DAT2.Plugins.FormBuilder.DataCondition) =>
                                    el.namedCondition && el.namedCondition === currentConditionName
                            ) || acc
                        );
                    }
                    return acc;
                },
                null
            );
        },
        [conditionRules]
    );
};
