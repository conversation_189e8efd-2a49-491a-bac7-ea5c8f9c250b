<!DOCTYPE html>
<html lang="en" translate="no">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Form Builder</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        window.FORM_BUILDER &&
          window.FORM_BUILDER.init({
            selector: '#root',
            onComplete: result => {
              alert(result);
            },
            data: [
              {
                formName: 'formName_01'
              },
              {
                groups: [
                  {
                    groupName: 'Sample fields',
                    content: {
                      rows: [
                        {
                          name: '',
                          fields: [
                            {
                              Type: 'string',
                              id: 'string',
                              label: 'string',
                              visible: true
                            }
                          ]
                        },
                        {
                          name: '',
                          fields: [
                            {
                              Type: 'integer',
                              id: 'integer',
                              label: 'integer',
                              visible: true
                            }
                          ]
                        },
                        {
                          name: '',
                          fields: [
                            {
                              Type: 'float',
                              id: 'float',
                              label: 'float',
                              initValue: '1',
                              visible: true
                            }
                          ]
                        },
                        {
                          name: '',
                          fields: [
                            {
                              Type: 'memo',
                              id: 'memo',
                              label: 'memo',
                              visible: true
                            },
                            {
                              Type: 'float',
                              id: 'float',
                              label: 'float',
                              initValue: '1',
                              visible: true
                            }
                          ]
                        },
                        {
                          name: '',
                          fields: [
                            {
                              Type: 'select',
                              id: 'select',
                              label: 'select',
                              options: [
                                { key: '1', value: 'one' },
                                { key: '2', value: 'two' }
                              ],
                              visible: true
                            },
                            {
                              Type: 'radio',
                              id: 'radio',
                              label: 'radio',
                              visible: true,
                              items: [
                                { value: '1', label: 'one' },
                                { value: '2', label: 'two' }
                              ]
                            }
                          ]
                        },
                        {
                          name: '',
                          fields: [
                            {
                              Type: 'date',
                              id: 'date',
                              label: 'date',
                              visible: true
                            },
                            {
                              Type: 'boolean',
                              id: 'boolean',
                              label: 'boolean',
                              visible: true
                            }
                          ]
                        }
                      ]
                    }
                  },
                  {
                    groupName: 'GroupNname/ mask validation',
                    condition: {
                      namedCondition: 'CONDITION-Z',
                      applyConditionFor: ['visible', 'required']
                    },
                    content: {
                      rows: [
                        {
                          name: 'RowName01',
                          fields: [
                            {
                              Type: 'string',
                              id: 'field_01',
                              className: 'col-md-2',
                              label: 'mask',
                              visible: true,
                              required: true,
                              pattern: {
                                max: 9,
                                mask: '###-##-##'
                              }
                            },
                            {
                              Type: 'string',
                              id: 'field_02',
                              label: 'max',
                              visible: true,
                              required: true,
                              pattern: {
                                max: 4
                              }
                            }
                          ]
                        }
                      ]
                    }
                  },
                  {
                    groupName: 'Condition/ (field1 = 10 or field2 notNull)',
                    condition: {
                      namedCondition: 'CONDITION-X',
                      applyConditionFor: ['visible', 'required']
                    },
                    content: {
                      rows: [
                        {
                          name: 'RowName02',
                          fields: [
                            {
                              Type: 'string',
                              id: 'field_1',
                              className: 'col-md-2',
                              label: 'field1/ set 10',
                              visible: true,
                              condition: {
                                applyConditionFor: ['visible'],
                                operator: 'filter',
                                operands: [
                                  {
                                    operator: 'equal',
                                    constants: [
                                      {
                                        constant: 'status',
                                        type: 'Status'
                                      },
                                      {
                                        constant: 'Entered',
                                        type: 'string'
                                      }
                                    ]
                                  }
                                ]
                              }
                            },
                            {
                              Type: 'string',
                              id: 'field_3',
                              label: 'Field3',
                              visible: true
                            },
                            {
                              Type: 'string',
                              id: 'field_2',
                              label: 'Field2/ not null',
                              visible: true
                            },
                            {
                              Type: 'string',
                              id: 'field_5',
                              visible: true,
                              label: 'labelForField5'
                            }
                          ]
                        }
                      ]
                    }
                  },
                  {
                    groupName: 'Condition/ (field 8 > 5 and status1 notNull)',
                    condition: {
                      namedCondition: 'CONDITION-Y',
                      applyConditionFor: ['visible', 'readOnly']
                    },
                    content: {
                      rows: [
                        {
                          name: 'RowName04',
                          fields: [
                            {
                              Type: 'string',
                              id: 'field_8',
                              className: 'col-md-2',
                              label: 'Field8',
                              visible: true,
                              condition: {
                                applyConditionFor: ['visible'],
                                operator: 'filter',
                                operands: [
                                  {
                                    operator: 'nonEqual',
                                    constants: [
                                      {
                                        constant: 'status',
                                        type: 'Status'
                                      },
                                      {
                                        constant: 'Entered',
                                        type: 'string'
                                      }
                                    ]
                                  }
                                ]
                              }
                            },
                            {
                              Type: 'string',
                              id: 'field_9',
                              label: 'readOnly',
                              visible: true,
                              initValue: 'some '
                            },
                            {
                              Type: 'string',
                              id: 'field_10',
                              label: 'readOnly',
                              visible: true,
                              initValue: 'readOnly'
                            },
                            {
                              Type: 'string',
                              id: 'field_11',
                              label: 'readOnly',
                              visible: true,
                              initValue: 'fields'
                            }
                          ]
                        }
                      ]
                    }
                  },
                  {
                    groupName: 'GroupNname/ status',
                    condition: {
                      namedCondition: 'CONDITION-Z',
                      applyConditionFor: ['visible', 'required']
                    },
                    content: {
                      rows: [
                        {
                          name: 'RowName05',
                          fields: [
                            {
                              Type: 'string',
                              id: 'field_001',
                              label: 'status1',
                              visible: true
                            },
                            {
                              Type: 'string',
                              id: 'field_002',
                              label: 'status2',
                              visible: true
                            }
                          ]
                        }
                      ]
                    }
                  },
                  {
                    groupName: 'Calculated/ (f1 * f2) + (f3 - f4)',
                    content: {
                      rows: [
                        {
                          name: 'RowName06',
                          fields: [
                            {
                              Type: 'string',
                              label: 'f_1',
                              id: 'field_c1',
                              visible: true
                            },
                            {
                              Type: 'string',
                              id: 'field_c2',
                              label: 'f_2',
                              visible: true
                            },
                            {
                              Type: 'string',
                              id: 'field_c3',
                              label: 'f_3',
                              visible: true
                            },
                            {
                              Type: 'string',
                              id: 'field_c4',
                              label: 'f_4',
                              visible: true
                            }
                          ]
                        },
                        {
                          name: 'RowName07',
                          fields: [
                            {
                              Type: 'submit',
                              id: 'button_submit',
                              label: 'label_btn',
                              text: 'submit',
                              action: 'submit',
                              visible: true
                            }
                          ]
                        }
                      ]
                    }
                  }
                ]
              },
              {
                conditions: [
                  {
                    namedCondition: 'CONDITION-X',
                    conditionType: 'comparison',
                    condition: [
                      {
                        operator: 'or',
                        operands: [
                          {
                            operator: 'equal',
                            constants: [
                              {
                                constant: 'field_1',
                                type: 'field'
                              },
                              {
                                constant: '10',
                                type: 'string'
                              }
                            ]
                          },
                          {
                            operator: 'isNotNull',
                            constants: [
                              {
                                constant: 'field_2',
                                type: 'field'
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  },
                  {
                    namedCondition: 'CONDITION-Z',
                    conditionType: 'comparison',
                    condition: [
                      {
                        operator: 'equal',
                        constants: [
                          {
                            constant: 'status',
                            type: 'option'
                          },
                          {
                            constant: 'Entered',
                            type: 'string'
                          }
                        ]
                      }
                    ]
                  },
                  {
                    namedCondition: 'CONDITION-Y',
                    conditionType: 'comparison',
                    condition: [
                      {
                        operator: 'and',
                        operands: [
                          {
                            operator: 'greater',
                            constants: [
                              {
                                constant: 'field_8',
                                type: 'field'
                              },
                              {
                                constant: '6',
                                type: 'string'
                              }
                            ]
                          },
                          {
                            operator: 'isNotNull',
                            constants: [
                              {
                                constant: 'field_001',
                                type: 'field'
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  },
                  {
                    namedCondition: 'CALCULATE',
                    conditionType: 'calculate',
                    condition: [
                      {
                        operator: 'plus',
                        operands: [
                          {
                            operator: 'multiply',
                            constants: [
                              {
                                constant: 'field_c1',
                                type: 'field'
                              },
                              {
                                constant: 'field_c2',
                                type: 'field'
                              }
                            ]
                          },
                          {
                            operator: 'minus',
                            constants: [
                              {
                                constant: 'field_c3',
                                type: 'field'
                              },
                              {
                                constant: 'field_c4',
                                type: 'field'
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ],
            options: {
              status: 'Entered'
            }
          });
      });
    </script>
  </body>
</html>
