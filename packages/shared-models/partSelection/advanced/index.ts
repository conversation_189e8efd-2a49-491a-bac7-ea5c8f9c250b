import { attach, combine, createApi, createDomain } from 'effector';

import { getParsedArraySafe } from '@dat/api2/utils';
import { bffAxiosInstance } from '@dat/api2/bffAxios';
// eslint-disable-next-line no-restricted-imports
import { contractModel } from '@dat/dat-data-models/src/models/contractModel';

import { sharedLoginEvents } from '../../auth/login';
import { sharedTemplateStores } from '../../template';
import { sharedUserStores } from '../../user';
import { contractEffects, contractEvents, contractStores } from '../../contract';

import { parseTemplateFromConfig } from './utils/parseTemplateFromConfig';
import { parsePartsFromContract, sortContractParts } from './utils/parsePartsFromContract';
import { parsePartsFromMemoField } from './utils/parsePartsFromMemoField';
import {
    AdvancedPart,
    AdvancedPartType,
    CalculationRepairPosition,
    MemoFieldWithAdvancedParts,
    UserAdvancedParts
} from './types';
import { addPart, deletePart, updatePart } from './reducers/parts';
import { mergeAdvancedParts } from './utils/mergeAdvancedParts';
import { PACKAGES_MEMO_FIELD_NAME } from './constants/memoField';

export const sharedAdvancedPartDomain = createDomain('packages');
sharedAdvancedPartDomain.onCreateStore(store => store.reset([sharedLoginEvents.loggedOut]));

const { createStore } = sharedAdvancedPartDomain;

// Part Templates
const advancedPartTemplates = sharedTemplateStores.productsConfiguration.map(parseTemplateFromConfig);
const isAdvancedPartEnabled = advancedPartTemplates.map(templates => templates.length > 0);

// AdvancedPart stored in backend memo field (not ephemeral)
const getUserAdvancedPartsFx = attach({
    source: sharedUserStores.customerNumber,
    effect: customerNumber =>
        bffAxiosInstance.get(
            `/user-config/memo-field/${customerNumber}/${PACKAGES_MEMO_FIELD_NAME}`
        ) as Promise<MemoFieldWithAdvancedParts>
});

const updateUserAdvancedPartsFx = attach({
    source: sharedUserStores.customerNumber,
    effect: (customerNumber, userAdvancedParts: UserAdvancedParts) => {
        return bffAxiosInstance.put<void>(`/user-config/memo-field/${customerNumber}`, {
            memoField: PACKAGES_MEMO_FIELD_NAME,
            config: {
                [PACKAGES_MEMO_FIELD_NAME]: userAdvancedParts
            }
        });
    }
});

const userAdvancedParts = createStore<UserAdvancedParts>({})
    .on(getUserAdvancedPartsFx.doneData, (_, payload) => parsePartsFromMemoField(payload))
    .on(updateUserAdvancedPartsFx.done, (_, payload) => payload.params || {});

const addAdvancedPartFx = attach({
    source: userAdvancedParts,
    mapParams: ({ type, part }: { type: AdvancedPartType; part: AdvancedPart }, userAdvancedParts) => {
        return addPart(userAdvancedParts, type, part);
    },
    effect: updateUserAdvancedPartsFx
});
const updateAdvancedPartFx = attach({
    source: userAdvancedParts,
    mapParams: (
        { type, partIndex, part }: { type: AdvancedPartType; partIndex: number; part: AdvancedPart },
        userAdvancedParts
    ) => {
        return updatePart(userAdvancedParts, type, partIndex, part);
    },
    effect: updateUserAdvancedPartsFx
});
const removeAdvancedPartFx = attach({
    source: userAdvancedParts,
    mapParams: ({ type, partIndex }: { type: AdvancedPartType; partIndex: number }, userAdvancedParts) => {
        return deletePart(userAdvancedParts, type, partIndex);
    },
    effect: updateUserAdvancedPartsFx
});

const isUserAdvancedPartLoading = combine([getUserAdvancedPartsFx.pending, updateAdvancedPartFx.pending], source =>
    source.reduce((a, b) => a || b, false)
);

// AdvancedPart stored in contract
const contractAdvancedParts = contractStores.customTemplateData.map(parsePartsFromContract);
const sortedContractParts = contractAdvancedParts.map(sortContractParts);

// Ephemeral parts, they are bound to a specific claim, so it has to be reset on contract change
const ephemeralParts = createStore<UserAdvancedParts>({}).reset(contractEvents.resetContract);
const ephemeralPartsApi = createApi(ephemeralParts, {
    addPart: (parts, { type, part }: { type: AdvancedPartType; part: AdvancedPart }) => addPart(parts, type, part),
    updatePart: (parts, { type, partIndex, part }: { type: AdvancedPartType; partIndex: number; part: AdvancedPart }) =>
        updatePart(parts, type, partIndex, part),
    deletePart: (parts, { type, partIndex }: { type: AdvancedPartType; partIndex: number }) =>
        deletePart(parts, type, partIndex)
});

const userAdvancedPartsWithEphemeral = combine(
    [ephemeralParts, userAdvancedParts],
    ([ephemeralParts, userAdvancedParts]): UserAdvancedParts => ({
        ...userAdvancedParts,
        ...ephemeralParts
    })
);

// includes contract, user and ephemeral (merged like Object.assign from left to right)
const allAdvancedParts = combine([sortedContractParts, userAdvancedPartsWithEphemeral], mergeAdvancedParts);

// Some calculation events mapped with a specific format
const calculateDAT5DoneData = contractModel.effect.calculateContractFx.doneData.map<
    CalculationRepairPosition[] | undefined
>(data => {
    const [Dossier] = data.responseObj.calculationResult?.Dossier || [];
    return Dossier?.RepairCalculation?.RepairPositions?.RepairPosition;
});
const calculateDoneData = contractEffects.calculateContractFx.doneData.map<CalculationRepairPosition[] | null>(data => {
    return (
        getParsedArraySafe(data.calculationResult?.Dossier?.RepairCalculation?.RepairPositions?.RepairPosition) ?? null
    );
});

export const sharedAdvancedPartStores = {
    advancedPartTemplates,
    isAdvancedPartEnabled,
    userAdvancedParts,
    contractAdvancedParts,
    isUserAdvancedPartLoading,
    sortedContractParts,
    ephemeralParts,
    userAdvancedPartsWithEphemeral,
    allAdvancedParts
};
export const sharedAdvancedPartEvents = {
    ephemeralPartsApi,
    calculateDAT5DoneData,
    calculateDoneData
};
export const sharedAdvancedPartEffects = {
    addAdvancedPartFx,
    updateAdvancedPartFx,
    removeAdvancedPartFx,
    getUserAdvancedPartsFx
};
