import { CONTRACT_ENTRIES_KEYS } from '@dat/core/constants/contract';
import { contractStores } from '../stores';
import { getFieldText } from '@dat/api2/utils';

const { customTemplateData } = contractStores;

const nationalCodeAustria = customTemplateData.map(
    customTemplateData =>
        getFieldText(
            customTemplateData?.find(data => data.key === CONTRACT_ENTRIES_KEYS.MEMO.nationalCodeAustria)?.value
        ) ?? null
);

export const sharedCustomTemplateDataStores = {
    nationalCodeAustria
};
