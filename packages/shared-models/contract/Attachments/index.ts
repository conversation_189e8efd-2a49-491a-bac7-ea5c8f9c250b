import { createEffect, createEvent, createStore, restore, sample } from 'effector';
import { v4 as uuidv4 } from 'uuid';

import * as DAT5 from '@wedat/api';
import { API2 } from '@dat/api2';
import { parseAttachmentItem } from '@dat/core/utils/attachments/parseAttachmentItem';
import { GetYouCitPicturesById } from '@dat/api2/services/youCit';
import { convertBlobToBinaryData } from '@dat/core/utils/convertBlobToBinaryData';
import { offlineDb } from '@dat/api2/utils/indexed-db';
import { anonymizeImage } from '@dat/api2/services/imageAnonymizer';
import { base64ToBlob } from '@dat/core/utils/base64ToBlob';
import { urlToBlob } from '@dat/core/utils/urlToBlob';
import { ParsedAttachmentItem } from '@dat/gallery/src/types/attachments';
import { generateBase64String } from '@dat/core/utils/generateBase64String';
import { overlayTextToYouCitImages } from '@dat/core/utils/attachments/overlayTextToYouCitImages';
import { extractDataFromBase64 } from '@dat/core/utils/attachments/extractDataFromBase64';
import { GroupedAttachments } from '@dat/core/types/attachments';
import { compressFiles } from '@dat/core/utils/images/compressFiles';
import { convertBase64ToFile } from '@dat/core/utils/data/convertBase64ToFile';
import { convertFileToBase64 } from '@dat/core/utils/data/convertFileToBase64';

import { toastEffects } from '../../smart-components/Toast';
import { sharedPluginCarsEvents } from '../../plugin-cars';
import { contractStores } from '../stores';
import { getFilenameWithoutExt } from './utils/getFilenameWithoutExt';
import {
    AttachmentGroup,
    FailedUploadAttachmentType,
    ForldersIndexWithAttachments,
    PayloadForUploadMultipleAttachmentsByFolderId
} from './types';
import { getErrorMessageKey } from './utils/getErrorMessageKey';

const { resetCurrentNumber, incrementCurrentNumber } = sharedPluginCarsEvents;
const folders = createStore<DAT2.Internal.FolderOfClaim[]>([]);
const updateFoldersData = createEvent<DAT2.Internal.FolderOfClaim[]>();
folders.on(updateFoldersData, (_, newFolders) => newFolders);

const listAttachmentsOfContractFx = createEffect(async (payload: DAT2.Request.ListAttachmentsOfContract) => {
    try {
        if (navigator.onLine) {
            const [res, folders] = await Promise.all([
                DAT5.MyClaimExternalService.listAttachmentsOfContract(payload),
                API2.myClaimInternal.getFoldersOfClaim({ claimId: payload.contractId })
            ]);

            const response = res.responseObj;
            await offlineDb.images.put(response, payload.contractId);

            if (!response) return [];

            const receivedFolders = Array.isArray(folders) ? folders : [];

            const validFolders = receivedFolders.filter(item => !!item.attachments.length);

            const foldersIndexedByFileName = validFolders.reduce((index, folder) => {
                folder.attachments.forEach(attachment => {
                    index[attachment.fileName] = { attachment };
                });
                return index;
            }, {} as ForldersIndexWithAttachments);

            const data = Array.isArray(response.return) ? response.return : [response.return];

            const mergedArray = data.map(responseItem => {
                if (responseItem) {
                    const match = foldersIndexedByFileName[responseItem.fileName];

                    if (match) {
                        return { ...responseItem, id: match.attachment.id };
                    } else {
                        return responseItem;
                    }
                }
            });

            /* TODO: remove any after replaced DAT2.AttachmentItem with DAT5 in all places */
            const attachments = (mergedArray || []) as any[];
            const attachmentsWithBase64 = attachments.map(parseAttachmentItem);
            return attachmentsWithBase64.reverse();
        } else {
            const response = await offlineDb.images.get(payload.contractId);

            /* TODO: remove any after replaced DAT2.AttachmentItem with DAT5 in all places */
            const attachments = ((Array.isArray(response) ? response : response?.return) || []) as any[];
            const attachmentsWithBase64 = attachments.map(parseAttachmentItem);
            return attachmentsWithBase64.reverse();
        }
    } catch {
        return [];
    }
});

const uploadSingleAttachmentFx = createEffect(
    async (payload: DAT5.MyClaimExternalService_schema1.uploadAttachmentByFolderID) => {
        // Since MyClaim service crop the fileName with a '/' inside it, we replace it by a space

        if (!payload.attachmentItem) throw new Error('No attachment data');

        const fileName = payload.attachmentItem.fileName?.replace(/\//g, ' ');

        if (!navigator.onLine) {
            try {
                await offlineDb.syncStore.put(
                    {
                        type: 'attachment',
                        payload: {
                            ...payload,
                            attachmentItem: {
                                published: new Date().toISOString(),
                                uploaded: new Date().toISOString(),
                                ...payload.attachmentItem,
                                fileName
                            }
                        },
                        contractId: payload.contractId,
                        updatedAt: new Date()
                    },
                    `${payload.contractId}_${new Date()}_attachment`
                );
            } catch (err) {
                console.log(err);
            }
            return {
                return: true
            };
        }

        const result = await DAT5.MyClaimExternalService.uploadAttachmentByFolderID({
            contractId: payload.contractId,
            attachmentItem: {
                published: new Date().toISOString(),
                uploaded: new Date().toISOString(),
                ...payload.attachmentItem,
                uploaderCustomerNumber: String(payload.attachmentItem.uploaderCustomerNumber),
                fileName
            }
        });

        return result.responseObj;
    }
);

const resetFailedUploadAttachments = createEvent();
const setFailedUploadAttachment = createEvent<FailedUploadAttachmentType>();
const failedUploadAttachments = createStore<FailedUploadAttachmentType[] | null>(null)
    .on(setFailedUploadAttachment, (oldArr, attachment) => (oldArr ? [...oldArr, attachment] : [attachment]))
    .reset(resetFailedUploadAttachments);
const uploadMultipleAttachmentsFx = createEffect(
    async ({ contractId, attachments, storingFailedUploads }: PayloadForUploadMultipleAttachmentsByFolderId) => {
        const result: DAT2.API2DAT5.MyClaimExternalService_schema1.uploadAttachmentByFolderIDResponse[] = [];
        resetFailedUploadAttachments();
        resetCurrentNumber();

        // Works for only one file...
        for (let i = 0; i < attachments.length; i++) {
            try {
                const attachment = await uploadSingleAttachmentFx({ contractId, attachmentItem: attachments[i] });
                if (attachment) {
                    result.push(attachment);
                }
                if (!attachment?.return && storingFailedUploads) {
                    throw new Error('Default error: get negative response');
                }
            } catch (e) {
                const error = e as unknown as Error;
                storingFailedUploads &&
                    setFailedUploadAttachment({
                        ...attachments[i],
                        base64: `data:${attachments[i].mimeType};base64,${attachments[i].binaryData}`,
                        error: error.message,
                        step: i
                    });
                console.error(`Error uploading attachment ${i}:`, error);

                toastEffects.showErrorToastFx({
                    message: {
                        namespace: 'claim-management',
                        key: getErrorMessageKey(e)
                    }
                });
            } finally {
                incrementCurrentNumber();
            }
        }

        return result;
    }
);

const deleteAttachmentFx = createEffect(
    async ({ contractId, folderId, filename }: { contractId: number; folderId: number; filename: string }) => {
        // collect attachments for ids
        const attachments = await API2.myClaimInternal.getAttachmentsOfFolder({
            claimId: contractId,
            folderId
        });

        const filenameToDelete = getFilenameWithoutExt(filename);
        const attachmentToDelete = attachments.find(
            attachment => getFilenameWithoutExt(attachment.fileName) === filenameToDelete
        );

        if (attachmentToDelete) {
            const response = await DAT5.MyClaimExternalService.deleteAttachments({
                contractId,
                deleteAttachmentFilter: { folderId, fileNames: [attachmentToDelete.fileName] }
            });

            return response.responseObj.return;
        }
    }
);

const deleteAttachmentsFx = createEffect(
    async ({
        contractId,
        folderId,
        attachments
    }: {
        contractId: number;
        folderId: number;
        attachments: ParsedAttachmentItem[];
    }) => {
        if (attachments?.length) {
            const response = await DAT5.MyClaimExternalService.deleteAttachments({
                contractId,
                deleteAttachmentFilter: {
                    folderId,
                    fileNames: [...attachments.map(attachment => attachment.fileName || '')]
                }
            });

            return response.responseObj.return;
        }
    }
);

const GetPicturesByIdFx = createEffect(async (id: number) => {
    if (!navigator.onLine) {
        return [] as YouCit.ArrayWithBinaryData[];
    }
    const pictures = await GetYouCitPicturesById(id);

    const ArrayWithBinaryData = await Promise.all(
        pictures.map(async (item: YouCit.Response.GetPictures) => {
            const { createdAt, latitude, longtitude, id, categoryExternalID, url } = item;

            const getBinaryDataFromUrl = await fetch(url)
                .then(res => res.blob())
                .then(blob => convertBlobToBinaryData(blob));

            const generateBinaryToBase64 = generateBase64String({
                binaryData: getBinaryDataFromUrl,
                mimeType: 'image/jpeg'
            });

            const canvasUrl = await overlayTextToYouCitImages(generateBinaryToBase64, {
                createdAt,
                latitude,
                longtitude
            });

            const fileFromCanvas = convertBase64ToFile(canvasUrl as string);
            const [compressedFile] = await compressFiles([fileFromCanvas], {
                useWebWorker: true
            });
            const compressedBase64 = await convertFileToBase64(compressedFile);
            const binaryData = extractDataFromBase64(compressedBase64);

            return { binaryData, id, categoryExternalID };
        })
    );

    return ArrayWithBinaryData as YouCit.ArrayWithBinaryData[];
});

const anonymizeImageFx = createEffect({
    handler: async ({
        contractId,
        originalAttachment,
        anonymizedImagesFolderId
    }: {
        contractId: number;
        originalAttachment: ParsedAttachmentItem;
        anonymizedImagesFolderId?: number;
    }) => {
        const originalMimeType = originalAttachment.mimeType || 'image/jpeg';

        const formData = new FormData();
        const blob = base64ToBlob(originalAttachment.base64, originalMimeType);
        formData.append('fileobject', blob, originalAttachment.fileName);

        const anonymizedData = await anonymizeImage(formData);

        const anonymizedBlob = await urlToBlob(anonymizedData.anonymised_url);
        const anonymizedBinaryDataFromBlob = await convertBlobToBinaryData(anonymizedBlob);

        await uploadSingleAttachmentFx({
            contractId,
            attachmentItem: {
                binaryData: anonymizedBinaryDataFromBlob,
                documentID: anonymizedImagesFolderId,
                fileName: `anonymized-image-${uuidv4()}`,
                mimeType: originalMimeType
            }
        });
    }
});

const setOCRAttachment = createEvent<DAT2.API2DAT5.MyClaimExternalService_schema1.attachmentItem>();

const setSharedAttachmentsWithoutGroup = createEvent<ParsedAttachmentItem[]>();
const setSharedGroupAttachments = createEvent<GroupedAttachments>();
const resetSharedAttachments = createEvent();
const shouldSetActiveGroupInNewTab = createEvent();
const setAttachmentsForSwiper = createEvent<ParsedAttachmentItem[]>();

const groups = createStore<AttachmentGroup[]>([]);
const setGroups = createEvent<AttachmentGroup[]>();

groups.on(setGroups, (_, newGroups) => newGroups);

const filteredAttachments = createStore<ParsedAttachmentItem[]>([]);
const updatefilteredAttachments = createEvent<ParsedAttachmentItem[]>();

filteredAttachments
    .on(updatefilteredAttachments, (_, newFilteredAttachments) => newFilteredAttachments)
    .reset(resetSharedAttachments);

const attachmentsForSwiper = createStore<ParsedAttachmentItem[]>([]);

attachmentsForSwiper
    .on(setAttachmentsForSwiper, (_, payload) =>
        payload?.length ? payload.filter(item => item.mimeType === 'image/jpeg' || item.mimeType === 'image/png') : []
    )
    .reset(resetSharedAttachments);

const sharedGroupAttachments = restore(setSharedGroupAttachments, []).reset(resetSharedAttachments);
const sharedAttachmentsWithoutGroup = restore(setSharedAttachmentsWithoutGroup, []).reset(resetSharedAttachments);

const { contractId } = contractStores;

sample({
    clock: setOCRAttachment,
    source: contractId,
    fn: (contractId, attachmentItem) => ({
        contractId,
        attachmentItem
    }),
    target: uploadSingleAttachmentFx
});

sample({
    clock: uploadSingleAttachmentFx.doneData,
    source: contractId,
    filter: contractId => !!contractId,
    fn: contractId => {
        return {
            contractId
        };
    },
    target: listAttachmentsOfContractFx
});

sample({
    clock: updateFoldersData,
    source: { folders, groups, attachmentsForSwiper },
    fn: ({ folders, groups, attachmentsForSwiper }) => {
        const filterByMimeType = (attachments: DAT2.Internal.FolderOfClaimAttachment[]) =>
            attachments.filter(
                item => item?.binaryData?.mimeType === 'image/jpeg' || item?.binaryData?.mimeType === 'image/png'
            );
        const visibleGroups = groups.filter(group => group.visible !== false);

        const filteredAttachments = visibleGroups.flatMap(group => {
            const folder = folders.find(folder => folder.id === group.id);
            return folder?.attachments ? filterByMimeType(folder.attachments) : [];
        });

        const filteredSwiperAttachments = attachmentsForSwiper.filter(attachment =>
            filteredAttachments.some(filtered => filtered.id === attachment.id)
        );

        return filteredSwiperAttachments;
    },
    target: updatefilteredAttachments
});

export const sharedAttachmentsEffects = {
    listAttachmentsOfContractFx,
    uploadSingleAttachmentFx,
    uploadMultipleAttachmentsFx,
    deleteAttachmentFx,
    GetPicturesByIdFx,
    anonymizeImageFx,
    deleteAttachmentsFx
};

export const sharedAttachmentsEvents = {
    setOCRAttachment,
    setAttachmentsForSwiper,
    setSharedAttachmentsWithoutGroup,
    setSharedGroupAttachments,
    resetSharedAttachments,
    shouldSetActiveGroupInNewTab,
    resetFailedUploadAttachments,
    setFailedUploadAttachment,
    updateFoldersData,
    setGroups
};

export const sharedAttachmentsStores = {
    attachmentsForSwiper,
    failedUploadAttachments,
    sharedGroupAttachments,
    sharedAttachmentsWithoutGroup,
    folders,
    groups,
    filteredAttachments
};
