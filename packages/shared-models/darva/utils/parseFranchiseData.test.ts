import { parseFranchiseData } from './parseFranchiseData';

describe('parseFranchiseData', () => {
    it('should parse valid JSON with complete data', () => {
        const json = JSON.stringify({
            A: {
                type: 'A',
                description: 'Glass breakage',
                value: 75,
                percent: 0,
                minPercent: 0,
                maxPercent: 0,
                support: 100,
                maxSupport: 0,
                wear: true,
                tvaRecoverable: false,
                included: true
            }
        });
        const result = parseFranchiseData(json);
        expect(result).toEqual({
            A: {
                type: 'A',
                description: 'Glass breakage',
                value: 75,
                percent: 0,
                minPercent: 0,
                maxPercent: 0,
                support: 100,
                maxSupport: 0,
                wear: true,
                tvaRecoverable: false,
                included: true
            }
        });
    });

    it('should handle empty JSON string', () => {
        const json = '';
        const result = parseFranchiseData(json);
        expect(result).toEqual({});
    });

    it('should handle invalid JSON string', () => {
        const json = 'invalid json';
        const result = parseFranchiseData(json);
        expect(result).toEqual({});
    });

    it('should handle JSON with missing fields', () => {
        const json = JSON.stringify({
            A: {
                type: 'A',
                description: 'Glass breakage'
            }
        });
        const result = parseFranchiseData(json);
        expect(result).toEqual({
            A: {
                type: 'A',
                description: 'Glass breakage',
                value: 0,
                percent: 0,
                minPercent: 0,
                maxPercent: 0,
                support: 0,
                maxSupport: 0,
                wear: false,
                tvaRecoverable: false,
                included: false
            }
        });
    });

    it('should handle JSON with extra fields', () => {
        const json = JSON.stringify({
            A: {
                type: 'A',
                description: 'Glass breakage',
                value: 75,
                extraField: 'extra'
            }
        });
        const result = parseFranchiseData(json);
        expect(result).toEqual({
            A: {
                type: 'A',
                description: 'Glass breakage',
                value: 75,
                percent: 0,
                minPercent: 0,
                maxPercent: 0,
                support: 0,
                maxSupport: 0,
                wear: false,
                tvaRecoverable: false,
                included: false,
                extraField: 'extra'
            }
        });
    });

    it('should handle JSON with null values', () => {
        const json = JSON.stringify({
            A: null
        });
        const result = parseFranchiseData(json);
        expect(result).toEqual({});
    });

    it('should handle JSON with non-object values', () => {
        const json = JSON.stringify({
            A: 'string value'
        });
        const result = parseFranchiseData(json);
        expect(result).toEqual({});
    });
});
