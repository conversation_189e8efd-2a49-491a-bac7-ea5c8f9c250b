export const PANELS_FOR_AI_CHECK = {
    DATID_0008: 'bumperRear',
    DATID_0001: 'bumperFront',
    DATID_0033: 'doorFrontLeft',
    DATID_0034: 'doorFrontRight',
    DATID_0041: 'doorRearLeft',
    DATID_0042: 'doorRearRight',
    DATID_0045: 'fenderRearLeft',
    DATID_0046: 'fenderRearRight',
    DATID_0025: 'fenderFrontLeft',
    DATID_0026: 'fenderFrontRight',
    DATID_0023: 'headlightLeft',
    DATID_0024: 'headlightRight',
    DATID_0051: 'taillightexternalLeft',
    DATID_0052: 'taillightexternalRight',
    DATID_0053: 'taillightinternalLeft',
    DATID_0054: 'taillightinternalRight',
    DATID_0007: 'trunk',
    DATID_0005: 'roof',
    DATID_0003: 'hood',
    DATID_0010: 'windowRear',
    DATID_0004: 'windscreen',
    DATID_0037: 'mirrorLeft',
    DATID_0038: 'mirrorRight'
};

export const DEFAULT_ORCHESTRATOR_CONFIG: DAT2.Plugins.FastTrack.Orchestrator = {
    highSeverity: 70,
    green: {
        inScopeImagesPrc: { min: { value: 65, excludeValue: true } },
        inScopeImagesCount: { min: { value: 4 } },
        highSeverityCount: { max: { value: 3 } }
    },
    yellow: {
        inScopeImagesPrc: { min: { value: 50 }, max: { value: 65 } },
        inScopeImagesCount: { min: { value: 2 }, max: { value: 3 } },
        highSeverityCount: { min: { value: 4 }, max: { value: 4 } }
    },
    red: {
        inScopeImagesPrc: { max: { value: 50, excludeValue: true } },
        inScopeImagesCount: { max: { value: 1 } },
        highSeverityCount: { min: { value: 5 } }
    }
};
