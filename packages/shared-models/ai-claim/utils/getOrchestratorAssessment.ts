import { AiGallery } from '@dat/api2dat3/src/additionalTypes/aiGallery';
import { DEFAULT_ORCHESTRATOR_CONFIG, PANELS_FOR_AI_CHECK } from '../constants';

const compareThreshold = (comparison: number, threshold: DAT2.Plugins.FastTrack.Threshold) => {
    const { min, max } = threshold;
    if (!min && !max) return false;
    return (
        (min?.excludeValue ? comparison > min.value : comparison >= (min?.value || 0)) &&
        (max?.excludeValue ? comparison < max.value : comparison <= (max?.value || Infinity))
    );
};

const checkOrchestratorThreshold = (
    assessment: DAT2.Plugins.FastTrack.Assessment,
    inScopePrcComp: number,
    inScopeCountComp: number,
    highSeverityCountComp: number
) => {
    const { inScopeImagesPrc, inScopeImagesCount, highSeverityCount } = assessment;

    const check1 = compareThreshold(inScopePrcComp, inScopeImagesPrc);
    const check2 = compareThreshold(inScopeCountComp, inScopeImagesCount);
    const check3 = compareThreshold(highSeverityCountComp, highSeverityCount);

    return [check1, check2, check3];
};

export const getOrchestratorAssessment = ({
    AIResult,
    orchestrator
}: {
    AIResult: AiGallery | undefined;
    orchestrator: DAT2.Plugins.FastTrack.Orchestrator | undefined;
}) => {
    const { green, yellow, red, highSeverity } = orchestrator || DEFAULT_ORCHESTRATOR_CONFIG;
    if (!AIResult || !highSeverity || (!green && !yellow && !red)) return;

    const { aiPipelineImages, damageSummaries } = AIResult?.aiResults || {};
    const inScopeImagesCount = aiPipelineImages?.filter(img => img.classification === 'inScope')?.length || 0;
    const inScopeImagesPercents =
        aiPipelineImages?.length && inScopeImagesCount
            ? (inScopeImagesCount / aiPipelineImages.filter(img => img.classification !== 'document').length) * 100
            : 0;
    const damagesWithHighSeverityCount =
        damageSummaries?.filter(
            dmg => Object.keys(PANELS_FOR_AI_CHECK).includes(dmg.datId) && parseInt(dmg.severity) > highSeverity
        ).length || 0;

    const isRed = !!red
        ? checkOrchestratorThreshold(red, inScopeImagesPercents, inScopeImagesCount, damagesWithHighSeverityCount).find(
              result => result
          )
        : false;
    const isYellow =
        !isRed && !!yellow
            ? checkOrchestratorThreshold(
                  yellow,
                  inScopeImagesPercents,
                  inScopeImagesCount,
                  damagesWithHighSeverityCount
              ).find(result => result)
            : false;
    const isGreen =
        !isRed && !isYellow && !!green
            ? checkOrchestratorThreshold(
                  green,
                  inScopeImagesPercents,
                  inScopeImagesCount,
                  damagesWithHighSeverityCount
              ).every(result => result)
            : false;

    return isGreen ? 'green' : isYellow ? 'yellow' : 'red';
};
