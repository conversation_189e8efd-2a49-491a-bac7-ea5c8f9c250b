import { ChangeEvent } from 'react';
import { combine, createEvent, createStore, createEffect, restore } from 'effector';
import { createToggle } from '@dat/core/utils/effector/createToggle';
import { CONTRACT_ID, IS_GALLERY_DRAWER_OPEN } from './constants';
import { sharedContractStatusStores } from '../contract/Status';
import { updateContractBaseFx } from '../baseEffects';
import { FilesInGroupSelected, GroupedAttachments, HandleFileInputChangeParams } from './types';

const [isGalleryDrawerOpen, toggleIsGalleryDrawerOpen] = createToggle(false);

const selectedContractId = createStore<number>(0);
const setSelectedContractId = createEvent<number>();
const setAttachmentNumber = createEvent<number>();
const resetAttachmentNumber = createEvent();
const attachmentNumber = createStore<string>('').reset(resetAttachmentNumber);
const galleryPhotoComments = createStore({ gallery: [] });

const setContractId = createEvent<number>();
const resetContractId = createEvent<number>();
const contractId = createStore<number>(0)
    .on(setContractId, (_, contractId) => contractId)
    .reset(resetContractId);

const filesInGroupSelected = createEvent<FilesInGroupSelected>();
const handleFileInputChange = createEvent<HandleFileInputChangeParams>();

const setGroupedAttachment = createEvent<GroupedAttachments>();
const groupedAttachments = createStore<GroupedAttachments>({}).on(setGroupedAttachment, (_, payload) => payload);

const updateContractWithFavoritesAttachmentFx = createEffect(updateContractBaseFx);

const setFavoritesAttachment = createEvent<string>();
const resetFavoritesAttachment = createEvent();
const nameFavoritesAttachment = createStore<string | null>(null)
    .on(setFavoritesAttachment, (_, favoritesAttachment) => favoritesAttachment)
    .reset(resetFavoritesAttachment);

const attachmentNumberAfterFullUpload = createStore<number | null>(null);
const selectedToUploadFiles = createStore<null | ChangeEvent<HTMLInputElement>>(null);

const setReadOnly = createEvent<boolean>();
const isReadOnlyFromPreview = restore(setReadOnly, false);

const isReadOnly = combine(
    [sharedContractStatusStores.isClaimHasDisableStatus, isReadOnlyFromPreview],
    ([isClaimHasDisableStatus, isReadOnlyFromPreview]) => isReadOnlyFromPreview || isClaimHasDisableStatus
);

const setShowGalleryFormBuilder = createEvent<boolean>();
const isShownGalleryInFormBuilder = restore(setShowGalleryFormBuilder, true);

selectedContractId
    .on(setSelectedContractId, (_, state) => state)
    .on(selectedContractId.updates, state => {
        if (state > 0) {
            localStorage.setItem(IS_GALLERY_DRAWER_OPEN, JSON.stringify(true));
            localStorage.setItem(CONTRACT_ID, JSON.stringify(state));
        }
    });

export const sharedGalleryEffect = {
    updateContractWithFavoritesAttachmentFx
};

export const sharedGalleryEvents = {
    setSelectedContractId,
    toggleIsGalleryDrawerOpen,
    setAttachmentNumber,
    setReadOnly,
    setFavoritesAttachment,
    resetFavoritesAttachment,
    setShowGalleryFormBuilder,
    setGroupedAttachment,
    setContractId,
    filesInGroupSelected,
    handleFileInputChange,
    resetContractId,
    resetAttachmentNumber
};

export const sharedGalleryStores = {
    selectedContractId,
    isGalleryDrawerOpen,
    attachmentNumber,
    isReadOnly,
    attachmentNumberAfterFullUpload,
    selectedToUploadFiles,
    nameFavoritesAttachment,
    galleryPhotoComments,
    isShownGalleryInFormBuilder,
    groupedAttachments,
    contractId
};
