import { combine, createEffect } from 'effector';

import { sharedProfilesStores } from '../profiles';
import { sharedPartnersStores } from '../contract/Partners';
import { sendNotifications } from '@dat/api2/services/bff/notifications';

const { sameCustomerProfiles } = sharedProfilesStores;
const { contractPartners } = sharedPartnersStores;

const isDialogsEmpty = combine(contractPartners, sameCustomerProfiles, (contractPartners, sameCustomerProfiles) => {
    if (Object.keys(sameCustomerProfiles).length > 1 || Object.keys(contractPartners).length !== 0) {
        return false;
    }

    return true;
});

const sendNotificationFx = createEffect(sendNotifications);

export const sharedChatStores = {
    isDialogsEmpty
};

export const sharedChatEffects = {
    sendNotificationFx
};
