import { filterArrayOfObjectsByExistingKeys } from '@dat/core/utils';
import { KeyValueOption } from '@wedat/ui-kit/components/Select';

interface ExtendedKeyValueOption extends KeyValueOption {
    defaultWagesSettings?: boolean;
}

export const createNumberedSelectOptions = ({ from, to }: { from: number; to: number }): KeyValueOption[] => {
    const result = [];

    for (let i = from; i <= to; i++) {
        result.push({ value: String(i), key: i, label: String(i) });
    }

    return result;
};

export const createRatesSelectOptions = (rates: DAT2.Internal.RateItem[]): ExtendedKeyValueOption[] =>
    filterArrayOfObjectsByExistingKeys(rates, ['name', 'id', 'defaultWagesSettings']).map(
        ({ name, id, defaultWagesSettings }) => ({
            value: String(name),
            label: String(name),
            key: id,
            defaultWagesSettings
        })
    );

export const createPriceDatesSelectOptions = (priceDates: DAT2.PriceDate[]) =>
    priceDates.map(({ date }) => {
        const currDate = new Date(date).toLocaleDateString('fr');
        return {
            value: currDate,
            label: currDate,
            key: new Date(date).getTime()
        };
    });

export const getSelectedLacquerMethod = (paintMethodFromRates?: number | null) => {
    switch (paintMethodFromRates) {
        case 1:
            return 'EURO_LACQUER';
        case 2:
            return 'MANUFACTURER_SPECIFIC';
        case 3:
            return 'CZ';
        case 6:
            return 'FULLY_MANUAL';
        default:
            return null;
    }
};
