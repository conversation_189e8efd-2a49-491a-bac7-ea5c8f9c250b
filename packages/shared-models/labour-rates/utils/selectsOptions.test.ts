import {
    createRatesSelectOptions,
    createNumberedSelectOptions,
    createPriceDatesSelectOptions,
    getSelectedLacquerMethod
} from './selectsOptions';

describe('selectsOptions', () => {
    it('createNumberedSelectOptions', () => {
        expect(createNumberedSelectOptions({ from: 1, to: 5 })).toStrictEqual([
            { key: 1, value: '1', label: '1' },
            { key: 2, value: '2', label: '2' },
            { key: 3, value: '3', label: '3' },
            { key: 4, value: '4', label: '4' },
            { key: 5, value: '5', label: '5' }
        ]);
    });
    it('createInvoiceRatesSelectOptions', () => {
        const invoiceRates = [{ name: 'name2', id: 55666, defaultWagesSettings: false }] as DAT2.Internal.RateItem[];

        expect(createRatesSelectOptions(invoiceRates)).toStrictEqual([
            { key: 55666, value: 'name2', label: 'name2', defaultWagesSettings: false }
        ]);
    });
    it('should return the correct select options for the given price dates', () => {
        const priceDates = [
            { date: '2022-01-01', generation: 1 },
            { date: '2022-02-01', generation: 2 },
            { date: '2022-03-01', generation: 3 }
        ];

        const expectedResult = [
            { value: '01/01/2022', label: '01/01/2022', key: new Date('2022-01-01').getTime() },
            { value: '01/02/2022', label: '01/02/2022', key: new Date('2022-02-01').getTime() },
            { value: '01/03/2022', label: '01/03/2022', key: new Date('2022-03-01').getTime() }
        ];

        const result = createPriceDatesSelectOptions(priceDates);

        expect(result).toEqual(expectedResult);
    });

    it('should return an empty array if no price dates are provided', () => {
        const priceDates: DAT2.PriceDate[] = [];

        const expectedResult: {
            value: string;
            label: string;
            key: number;
        }[] = [];

        const result = createPriceDatesSelectOptions(priceDates);

        expect(result).toEqual(expectedResult);
    });
});

describe('getSelectedLacquerMethod', () => {
    test('returns EURO_LACQUER for paintMethodFromRates 1', () => {
        expect(getSelectedLacquerMethod(1)).toBe('EURO_LACQUER');
    });

    test('returns MANUFACTURER_SPECIFIC for paintMethodFromRates 2', () => {
        expect(getSelectedLacquerMethod(2)).toBe('MANUFACTURER_SPECIFIC');
    });

    test('returns CZ for paintMethodFromRates 3', () => {
        expect(getSelectedLacquerMethod(3)).toBe('CZ');
    });

    test('returns FULLY_MANUAL for paintMethodFromRates 6', () => {
        expect(getSelectedLacquerMethod(6)).toBe('FULLY_MANUAL');
    });

    test('returns null for paintMethodFromRates not in 1, 2, 3, 6', () => {
        expect(getSelectedLacquerMethod(4)).toBeNull();
        expect(getSelectedLacquerMethod(5)).toBeNull();
        expect(getSelectedLacquerMethod(7)).toBeNull();
        expect(getSelectedLacquerMethod(null)).toBeNull();
        expect(getSelectedLacquerMethod(undefined)).toBeNull();
    });
});
