export const extractClaimRateFullData = (res: string | undefined): DAT2.Internal.Response.Rate | null => {
    if (typeof res !== 'string') {
        return null;
    }

    const equalSignPosition = res.indexOf('=');
    if (equalSignPosition === -1) {
        return null;
    }

    // Extract the substring starting from the position after `=`.
    let jsonString = res.substring(equalSignPosition + 1).trim();

    // Check if the string ends with a `;` and remove it.
    if (jsonString.endsWith(';')) {
        jsonString = jsonString.slice(0, -1);
    }

    try {
        const parsedData = JSON.parse(jsonString);
        return parsedData;
    } catch (error) {
        console.error('Error parsing JSON string:', error);
        return null;
    }
};
