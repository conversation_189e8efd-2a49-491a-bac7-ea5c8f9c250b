import { getParsedArraySafe } from '@dat/api2/utils';
import { AppraisalObject } from '../../../api2/services/appraisal-axa';
import { AppraisalResult_MDOMAT } from '../types';

export const extractAppraisalResult_MDOMATFromContract = (
    contract: DAT2.ContractFromGetContract | null,
    key: string
): AppraisalResult_MDOMAT | null => {
    if (contract === null) {
        return null;
    }

    const entries = getParsedArraySafe(contract?.customTemplateData?.entry);
    const foundeEntry = entries.find(entry => entry.key === key);
    const foundeEntryValue = foundeEntry?.value._text as string;

    if (!!foundeEntryValue) {
        try {
            return JSON.parse(foundeEntryValue);
        } catch (e) {
            console.error('a-error', e);
        }
    }

    return null;
};

export const extractCalculationUserChoicesObjFromContract = (
    contract: DAT2.ContractFromGetContract | null,
    key: string
): AppraisalObject['data'] | null => {
    if (contract === null) {
        return null;
    }

    const entries = getParsedArraySafe(contract?.customTemplateData?.entry);
    const foundeEntry = entries.find(entry => entry.key === key);
    const foundeEntryValue = foundeEntry?.value._text as string;

    if (!!foundeEntryValue) {
        try {
            return JSON.parse(foundeEntryValue);
        } catch (e) {
            console.error('a-error', e);
        }
    }

    return null;
};

export const extractMemoFieldFromContract = (
    contract: DAT2.ContractFromGetContract | null,
    key: string
): string | number | null => {
    if (contract === null) {
        return null;
    }

    const entries = getParsedArraySafe(contract?.customTemplateData?.entry);
    const foundeEntry = entries.find(entry => entry.key === key);
    const foundeEntryValue = foundeEntry?.value._text as string | number;
    return foundeEntryValue ?? null;
};

export const extractExtraLabourRates = (contract: DAT2.ContractFromGetContract | null) => {
    const found = extractMemoFieldFromContract(contract, 'extraLabourRates') as string;

    return found ? found?.replace(/'/g, '"') : '';
};
