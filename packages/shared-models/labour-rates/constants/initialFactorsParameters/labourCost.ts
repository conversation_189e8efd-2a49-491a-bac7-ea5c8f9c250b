export const labourCostInitialParameters: DAT2.Internal.LabourCostParameters = {
    mechanicWage1: {
        mode: 0,
        value: null
    },
    mechanicWage2: {
        mode: 0,
        value: null
    },
    mechanicWage3: {
        mode: 0,
        value: null
    },
    bodyWage1: {
        mode: 0,
        value: null
    },
    bodyWage2: {
        mode: 0,
        value: null
    },
    bodyWage3: {
        mode: 0,
        value: null
    },
    electricWage1: {
        mode: 0,
        value: null
    },
    electricWage2: {
        mode: 0,
        value: null
    },
    electricWage3: {
        mode: 0,
        value: null
    },
    dentWage: {
        mode: 0,
        value: null
    },
    dentsCountInProtocol: false,
    isSuppressCavityProtection: false,
    discount: {
        mode: 0,
        value: null
    },
    discountBiw: {
        mode: 0,
        value: null
    },
    labourLumpSum: undefined,

    _attributes: {
        mode: {
            discount: 'PERCENT',
            discountBiw: 'PERCENT'
        }
    }
};
