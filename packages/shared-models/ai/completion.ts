import axios from 'axios';

interface AiResponse {
    conversationId: string;
    functionName: string;
    functionArguments: Record<string, any>;
    functionContent: string;
}

export const doCompletion = async (
    userInput: string,
    userId: string,
    conversationId: string | null = null
): Promise<AiResponse> => {
    const userLocale = localStorage.getItem('DAT-locale') || 'en-EN';
    const url = 'https://wedat-ai-jlted.ondigitalocean.app/wedat-ai';
    // const url = 'http://localhost:8080/wedat-ai';
    const request = await axios.post(url, {
        userInput,
        userId,
        conversationId,
        additionalInfo: {
            userLanguage: userLocale
        }
        // conversationId: '67619bf2488ab96feb07b6b3'
    });

    return request.data;
};
