import { parseRepairerZipMemo } from './parseRepairerZipMemo';

describe('parseRepairerZipMemo', () => {
    it('should return correct ZIP codes', () => {
        expect(parseRepairerZipMemo('1200A')).toBe('1200A');
        expect(parseRepairerZipMemo('12345')).toBe('12345');
        expect(parseRepairerZipMemo('ABC123')).toBe('ABC123');
        expect(parseRepairerZipMemo('SW1A 1AA')).toBe('SW1A 1AA');
        expect(parseRepairerZipMemo('K1A-0A6')).toBe('K1A-0A6');
        expect(parseRepairerZipMemo('10001')).toBe('10001');
    });

    it('should handle ZIP codes with leading/trailing spaces', () => {
        expect(parseRepairerZipMemo('  12345  ')).toBe('12345');
        expect(parseRepairerZipMemo('\t ABC123 \n')).toBe('ABC123');
    });

    it('should extract year from various date formats', () => {
        expect(parseRepairerZipMemo('01.01.1200 08:34:20')).toBe('1200');
        expect(parseRepairerZipMemo('1200-12-31')).toBe('1200-12-31');
        expect(parseRepairerZipMemo('31.12.1200')).toBe('1200');
        expect(parseRepairerZipMemo('12/31/1200')).toBe('1200');
        expect(parseRepairerZipMemo('1200')).toBe('1200');
    });

    it('should extract year from strings with time', () => {
        expect(parseRepairerZipMemo('1200-01-01 12:30:45')).toBe('1200');
        expect(parseRepairerZipMemo('01.01.1200 08:34:20')).toBe('1200');
        expect(parseRepairerZipMemo('Jan 1, 1200 10:00 AM')).toBe('1200');
    });

    it('should handle numeric input data', () => {
        expect(parseRepairerZipMemo(12345)).toBe('12345');
        expect(parseRepairerZipMemo(2023)).toBe('2023');
    });

    it('should handle null and undefined', () => {
        expect(parseRepairerZipMemo(null)).toBe(null);
        expect(parseRepairerZipMemo(undefined)).toBe(undefined);
    });
});
