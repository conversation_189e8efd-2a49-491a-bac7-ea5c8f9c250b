import { getPaintTypes } from '.';
import { FormState } from '../types';

export const modifyInputByCountry = (inputFromSubmit: FormState, country: DAT2.CountryCode) => {
    const sparePartSingleDiscount = {
        value: inputFromSubmit['SparePartFactor-value-singleDiscount'] || 0,
        mode: inputFromSubmit['SparePartFactor-mode-singleDiscount'] || 0
    };
    const preparationLacquerCost = inputFromSubmit['EuroLacquerFactor-preparationLacquerCost'] || null;
    const inputs: FormState = {
        ...inputFromSubmit,
        'MetadataFactor-address': JSON.stringify({
            sparePartSingleDiscount,
            ...(preparationLacquerCost && { preparationLacquerCost })
        }),
        //TODO: delete this line when smallSparePart model is configured in administration rate
        'SparePartFactor-smallSparePartCalculationModel': country === 'AT' ? 2 : 1,
        ...(country === 'ES' && {
            'ManufacturerLacquerFactor-materialMode': 1
        }),
        ...(country === 'AT' && {
            'AztLacquerFactor-colourMixCounter': inputFromSubmit['AztLacquerFactor-colourMixCounter'] || 1,
            'AztLacquerFactor-addition': inputFromSubmit['AztLacquerFactor-addition']?.length
                ? inputFromSubmit['AztLacquerFactor-addition']
                : ['A']
        })
    };

    switch (country) {
        case 'FR':
            return {
                ...inputs,
                'MetadataFactor-address': JSON.stringify({
                    ...getPaintTypes(inputs),
                    sparePartSingleDiscount,
                    ...(preparationLacquerCost && { preparationLacquerCost })
                })
            };
        default:
            return inputs;
    }
};
