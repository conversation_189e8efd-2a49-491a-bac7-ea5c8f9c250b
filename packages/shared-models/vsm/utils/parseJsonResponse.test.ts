import { parseJsonResponse } from './parseJsonResponse';

describe('parseJsonResponse', () => {
    test('should parse JSON response and return result value', () => {
        const testCases = [
            {
                input: '```json\n{"result": "91300"}\n```',
                expected: '91300'
            },
            {
                input: '```json\n{"result": 12345}\n```',
                expected: 12345
            }
        ];

        testCases.forEach(({ input, expected }) => {
            const result = parseJsonResponse(input);
            expect(result).toEqual(expected);
        });
    });

    test('should return null if data is invalid JSON', () => {
        const result = parseJsonResponse('Invalid JSON');
        expect(result).toBeNull();
    });

    test('should return null if result is missing from JSON', () => {
        const result = parseJsonResponse('```json\n{"someKey": "someValue"}\n```');
        expect(result).toBeNull();
    });

    test('should return result if data is already in a clean format', () => {
        const result = parseJsonResponse('{"result": "67890"}');
        expect(result).toEqual('67890');
    });
});
