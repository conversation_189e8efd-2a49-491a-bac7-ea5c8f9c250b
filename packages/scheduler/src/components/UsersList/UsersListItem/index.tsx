import { FC } from 'react';
import { useUnit } from 'effector-react';
import { schedulerEvents, schedulerStores } from '@dat/shared-models/scheduler';
import { TextWrapper, UserStyled } from '../styles';
import { Text } from '@wedat/ui-kit/components/Text';
import { PartnerAssignSelection } from '@dat/shared-models/scheduler/types';

interface Props {
    partnerList: PartnerAssignSelection[];
    title: string;
}

export const UsersListItem: FC<Props> = ({ partnerList, title }) => {
    const selectedUserID = useUnit(schedulerStores.selectedUser);

    return partnerList?.length ? (
        <>
            <TextWrapper>
                <Text fontSize="14px" fontWeight="bolder">
                    {title}
                </Text>
            </TextWrapper>

            {partnerList.map(partner => (
                <UserStyled
                    isActive={selectedUserID === partner.value}
                    key={partner.value}
                    onClick={() => schedulerEvents.setSelectedUser(partner.value)}
                >
                    {partner.label}
                </UserStyled>
            ))}
        </>
    ) : null;
};
