import { format, parseISO } from 'date-fns';
import { TFunction } from 'react-i18next';
import { DailyEventValuesType, SchedulerEvent } from '../types';

const convertTimeFormat = (time: string | Date) => {
    const parsedStartDateTime = new Date(time);
    const hours = parsedStartDateTime.getHours().toString().padStart(2, '0');
    const minutes = parsedStartDateTime.getMinutes().toString().padStart(2, '0');
    const formattedTimeStart = `${hours}:${minutes}`;
    return formattedTimeStart;
};

export const createNotificationData = (
    t: TFunction,
    values: DailyEventValuesType,
    contractId: number,
    userName: string,
    customerNumber: number,
    isRemove: boolean,
    schedule?: SchedulerEvent
): BFF.SendNotifications<'assign-partner'> => {
    let formattedDate;
    try {
        // Ensure schedule.start is a valid ISO string before parsing
        const parsedDate = schedule?.start ? parseISO(schedule.start as string) : null;

        // Check if parsedDate is a valid Date object
        if (!parsedDate || isNaN(parsedDate.getTime())) {
            throw new Error('Invalid date format');
        }

        formattedDate = format(parsedDate, 'dd/MM');
    } catch {
        console.error('Error formatting date');
        formattedDate = 'N/A'; // Provide a default value or handle the error as needed
    }
    const startDate = convertTimeFormat(values.start || (schedule?.start as string));
    const endDate = convertTimeFormat(values.end || (schedule?.end as string));

    const startDateForRemove = convertTimeFormat(schedule?.start as string);
    const endDateForRemove = convertTimeFormat(schedule?.end as string);
    const message = `${values.assignTo} - ${t('notification.add')} ${formattedDate} - ${startDate} -> ${endDate} ${t(
        'notification.plate'
    )}: ${schedule?.registrationNumber}`;
    const messageForRemove = `${schedule?.assignee} - ${t(
        'notification.remove'
    )} ${formattedDate} - ${startDateForRemove} -> ${endDateForRemove} ${t('notification.plate')}: ${
        schedule?.registrationNumber
    }`;

    return {
        customerNumber: customerNumber,
        notification: {
            type: 'assign-partner',
            data: {
                contractId: contractId,
                sender: `${customerNumber}-${userName}`,
                messagePreview: isRemove ? messageForRemove : message
            }
        }
    };
};
