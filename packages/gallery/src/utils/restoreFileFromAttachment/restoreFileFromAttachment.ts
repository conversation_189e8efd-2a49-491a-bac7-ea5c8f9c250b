import { ParsedAttachmentItem } from '../../types/attachments';

export const restoreFileFromAttachment = (attachment: ParsedAttachmentItem): File => {
    // Если binaryData содержит data-url, извлекаем только base64-часть
    const base64String = attachment.binaryData.includes(',')
        ? attachment.binaryData.split(',')[1]
        : attachment.binaryData;
    const byteString = atob(base64String);
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
    }
    return new File([ab], attachment.fileName, { type: attachment.mimeType });
};
