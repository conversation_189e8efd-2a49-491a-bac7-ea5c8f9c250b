import { ParsedAttachmentItem } from '@dat/core/types/attachments';
import { SavedImageData } from '../components/FileEditor/types';

export interface FileEditorState {
    isOpen: boolean;
    groupId: number;
    fileUrl: string;
    fileName: string;
    binaryData: string;
    selectedFileIndex: number;
}

export interface SaveFile {
    savedImageData: SavedImageData;
    backupFolderId: number;
    shouldOpenEditedImage: boolean;
}

export interface ResetFile {
    fileData: ParsedAttachmentItem;
    originalFolderId: number;
    backupFolderId: number;
}
