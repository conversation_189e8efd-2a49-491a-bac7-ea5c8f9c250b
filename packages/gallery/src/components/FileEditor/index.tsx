import { useCallback, useMemo, useRef, useState } from 'react';
import { useUnit } from 'effector-react';
import { isMobile } from 'react-device-detect';
import { useTranslation } from 'react-i18next';

import { Modal } from '@wedat/kit';
import { Preloader } from '@wedat/ui-kit';
import { Editor } from './Editor';
import { Gallery } from './Gallery';
import { ConfirmationModal } from './ConfirmationModal';

import { fileEditorEffects, fileEditorEvents, fileEditorStores } from '../../stores/fileEditor';
import { attachmentsEffects, attachmentsStores } from '../../stores/attachments';

import { EditedImageFunction, SavedImageData } from './types';

import styles from './FileEditor.module.css';

const FileEditor = () => {
    const { t } = useTranslation();

    const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
    const [isImageProcessing, setIsImageProcessing] = useState(false);

    const fileEditorState = useUnit(fileEditorStores.fileEditorState);
    const editedImageRef = useRef<EditedImageFunction>();

    const groups = useUnit(attachmentsStores.groups);
    const backupFolderId = useMemo(() => groups.find(group => group.name === 'Backup')?.id || 0, [groups]);

    const isRestoring = useUnit(fileEditorEffects.restoreFileFx.pending);
    const isSaving = useUnit(fileEditorEffects.saveEditedFileFx.pending);
    const isOrderChanging = useUnit(attachmentsEffects.changeAttachmentsOrderFx.pending);

    const handleSave = useCallback(
        async ({
            shouldSave,
            shouldOpenEditedImage = true,
            imageData
        }: {
            shouldSave: boolean;
            shouldOpenEditedImage?: boolean;
            imageData?: SavedImageData;
        }) => {
            if (shouldSave) {
                await fileEditorEffects.saveEditedFileFx({
                    savedImageData: imageData || (editedImageRef?.current?.().imageData as SavedImageData),
                    backupFolderId,
                    shouldOpenEditedImage
                });
            }
        },
        [backupFolderId]
    );

    const handleModalButtonClick = (shouldSave: boolean) => {
        if (shouldSave) {
            handleSave({
                shouldSave: true,
                shouldOpenEditedImage: false
            });
        }
        fileEditorEvents.hideFileEditor();
        setIsConfirmationModalOpen(false);
    };

    return (
        <Modal
            visible={fileEditorState.isOpen}
            onHide={() =>
                !document.querySelector('.FIE_topbar-reset-button')?.hasAttribute('disabled')
                    ? setIsConfirmationModalOpen(true)
                    : fileEditorEvents.hideFileEditor()
            }
            header={t('imageEditor.title')}
            fullWidth
            fullScreen={isMobile}
            closeOnEscape={false}
            isAutoZIndex
        >
            <div className={styles.EditorWrapper}>
                <Gallery handleSave={handleSave} setIsImageProcessing={setIsImageProcessing} />
                <Editor handleSave={handleSave} setIsImageProcessing={setIsImageProcessing} ref={editedImageRef} />
            </div>

            <ConfirmationModal
                isConfirmationModalOpen={isConfirmationModalOpen}
                setIsConfirmationModalOpen={setIsConfirmationModalOpen}
                handleModalButtonClick={handleModalButtonClick}
                title={t('imageEditor.saveModal.title')}
            />

            <Preloader isLoading={isRestoring || isSaving || isImageProcessing || isOrderChanging} />
        </Modal>
    );
};

export default FileEditor;
