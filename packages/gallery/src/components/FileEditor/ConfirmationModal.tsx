import { FC, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Modal } from '@wedat/kit';

import styles from './FileEditor.module.css';

interface Props {
    isConfirmationModalOpen: boolean;
    setIsConfirmationModalOpen: (value: boolean) => void;
    handleModalButtonClick: (shouldSave: boolean) => void;
    title: string;
}

export const ConfirmationModal: FC<Props> = ({
    isConfirmationModalOpen,
    setIsConfirmationModalOpen,
    handleModalButtonClick,
    title
}) => {
    const { t } = useTranslation();

    const modalFooter = useMemo(
        () => (
            <div className={styles.ButtonsWrapper}>
                <Button
                    fitContent
                    aria-label={t('imageEditor.confirmationModal.yes')}
                    label={t('imageEditor.confirmationModal.yes')}
                    onClick={() => handleModalButtonClick(true)}
                />

                <Button
                    fitContent
                    aria-label={t('imageEditor.confirmationModal.no')}
                    label={t('imageEditor.confirmationModal.no')}
                    onClick={() => handleModalButtonClick(false)}
                />
            </div>
        ),
        [handleModalButtonClick, t]
    );

    return (
        <Modal
            isAutoZIndex
            visible={isConfirmationModalOpen}
            bodyHeight="auto"
            bodyNoPadding
            header={title}
            footer={modalFooter}
            onHide={() => setIsConfirmationModalOpen(false)}
        />
    );
};
