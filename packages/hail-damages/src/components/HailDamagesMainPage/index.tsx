import { useUnit } from 'effector-react';
import { noop } from 'lodash-es';

import { hailDamagesStores } from '../../stores/hailDamage';

import { Formik } from 'formik';
import { CreateNewRate } from '../CreateHailRate';
import { HailDamages } from '../HailDamages';

export const HailDamagesMainPage = () => {
    const activeTab = useUnit(hailDamagesStores.hailDamagesTab);
    const initialValues = useUnit(hailDamagesStores.hailDamagesInitialValues);

    const renderComponent = () => {
        switch (activeTab) {
            case 'newRate':
                return <CreateNewRate />;
            default:
                return <HailDamages />;
        }
    };

    return (
        <Formik initialValues={initialValues} onSubmit={noop} enableReinitialize>
            {renderComponent()}
        </Formik>
    );
};
