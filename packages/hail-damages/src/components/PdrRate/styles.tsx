import { Text } from '@wedat/ui-kit';
import { media } from '@wedat/ui-kit/mediaQueries';
import styled from 'styled-components/macro';

export const Content = styled.div`
    width: 100%;
`;

export const TitleWrapper = styled.div``;

export const PercentageWrapper = styled.div`
    display: flex;
    align-items: center;
    margin: 20px 0;
    ${media.phone`
        flex-direction: column;
    `};
`;

export const FieldWrapper = styled.div`
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-right: 25px;
    gap: 10px;
    ${media.phone`
        width: 100%;
        margin-right: 0;
        margin-bottom: 5px;
        > div {
            width: 100%;
        }
    `};
`;

export const Field = styled.div``;

export const StyledText = styled(Text)`
    margin-right: 15px;
`;

export const CardsWrapper = styled.div`
    width: 100%;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    ${media.laptop`
        flex-direction: column;
        height: auto;
    `};
`;

export const StyledHeader = styled(Text)`
    ${({ theme }) => theme.typography.heading};
`;
