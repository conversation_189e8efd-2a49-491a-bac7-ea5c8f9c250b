import { createDomain, restore } from 'effector';

import { PluginOptions } from '../../types/plugin';

export const hailDamagesDomain = createDomain();
const { createEvent, createStore } = hailDamagesDomain;

const initPlugin = createEvent<PluginOptions>();
const unmountPlugin = createEvent<PluginOptions>();
const pluginOptions = restore(initPlugin, null);
const pluginInited = createStore(false).on(initPlugin, () => true);

export const pluginEvents = {
    initPlugin,
    unmountPlugin
};

export const pluginStores = {
    pluginOptions,
    pluginInited
};
