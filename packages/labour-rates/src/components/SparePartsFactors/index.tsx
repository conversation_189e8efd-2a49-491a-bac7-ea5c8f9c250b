import { FC, useMemo } from 'react';
import { useFormikContext } from 'formik';
import { useUnit } from 'effector-react';

import { useTranslation } from 'react-i18next';

import { radioItems } from '@dat/shared-models/labour-rates/constants/radioItems';
import { Text } from '@wedat/ui-kit/components/Text';
import { CheckboxField, InputField, NumberInputField, TabsField } from '@wedat/ui-kit/Formik';
import { useGetCurrencySymbol } from '@dat/shared-models/hooks/useGetCurrencySymbol';
import { apiStores } from '@dat/api2/stores';
import { TabButton } from '@wedat/ui-kit';
import { useMedia } from '@dat/core/hooks/useMedia';
import { sizes } from '@wedat/ui-kit/mediaQueries';

import { CheckboxFieldContainer, Field, FieldsContainer, ItemContent, TabsWrapper } from '../Layout/styles';

import { InputWithSelect } from '../InputWithSelect';
import { factoresStores } from '../../stores/factors';
import { invoiceRatesStores } from '../../stores/selects/invoiceRates';
import {
    POSSIBLE_SMALL_SPARE_PARTS,
    SMALL_SPARE_PARTS_KEYS,
    getTabValueForSmallSparePart
} from '../../constants/factors';

import { StyledErrorMessage } from './styles';
import { getErrorMessage } from '../../utils/getErrorMessage';

export const SparePartsFactors: FC = () => {
    const { t } = useTranslation();
    const isPhoneBig = useMedia(sizes.phoneBig);
    const isDisabledByConfig = useUnit(factoresStores.isLabourRatesReadOnly);

    const additionalData = useUnit(invoiceRatesStores.additionalData);

    const symbol = useGetCurrencySymbol();

    const {
        values: { SparePartFactor },
        initialValues: { SparePartFactor: SparePartFactorInitials },
        errors
    } = useFormikContext<DAT2.Internal.FactorsParametersObject>();

    const percentOrAbsolute = radioItems.percentOrAbsolute(symbol);
    const datCountryIndicator = useUnit(apiStores.country);
    const isAustrian = datCountryIndicator === 'AT';
    const isSpain = datCountryIndicator == 'ES';

    const existingFieldsUnique = useMemo(() => {
        const dataFromAdditional = additionalData?.countrySpecificCdData
            ? Object.keys(additionalData.countrySpecificCdData)
            : [];

        const existingFields = [...SMALL_SPARE_PARTS_KEYS, ...dataFromAdditional].filter(field => {
            const fieldKey = field as keyof typeof SparePartFactor;
            return SparePartFactorInitials?.[fieldKey] !== undefined;
        });
        return [...new Set(existingFields)];
    }, [SparePartFactorInitials, additionalData?.countrySpecificCdData]);

    const filteredTabValues = useMemo(() => {
        const result = getTabValueForSmallSparePart(t);

        return result.filter(tab => {
            const matchingField = existingFieldsUnique.find(field => {
                const fieldValue = POSSIBLE_SMALL_SPARE_PARTS[`SparePartFactor.${field}`];
                return fieldValue === tab.value || String(fieldValue) === tab.id;
            });
            return matchingField !== undefined;
        });
    }, [existingFieldsUnique, t]);

    const generateSmallPartsFields = () =>
        existingFieldsUnique.map(field => {
            const fieldName = `SparePartFactor.${field}`;
            const fieldGroup = POSSIBLE_SMALL_SPARE_PARTS[fieldName];
            const fieldKey = field as keyof DAT2.Internal.Response.CountrySpecificCdData;

            const defaultSymbol = additionalData?.countrySpecificCdData?.[fieldKey]
                ? field === 'smallSparePartPercentOfPart' || field === 'smallSparePartPercentOfWage'
                    ? `${Number(additionalData?.countrySpecificCdData?.[fieldKey]).toFixed(2)}%`
                    : `${Number(additionalData?.countrySpecificCdData?.[fieldKey]).toFixed(2)}€`
                : '';

            const errorMessage = errors?.SparePartFactor?.[field as keyof typeof SparePartFactor];

            if (isSpain && field === 'smallSparePartPercentOfPartBiw') {
                return null;
            }
            if (fieldGroup !== undefined && fieldGroup === Number(SparePartFactor?.smallSparePartCalculationModel)) {
                return (
                    <Field key={field}>
                        <NumberInputField
                            withoutZeroAfterClearing
                            type="tel"
                            disabled={isDisabledByConfig}
                            name={
                                field === 'smallSparePartFlatRatePrice'
                                    ? `SparePartFactor.${field}.value`
                                    : `SparePartFactor.${field}`
                            }
                            label={t(`spareParts.${field}`)}
                            currency={defaultSymbol}
                        />
                        {errorMessage && (
                            <StyledErrorMessage>
                                {t('SparePartFactor.errorMessage', `${getErrorMessage(errorMessage)}`)}
                            </StyledErrorMessage>
                        )}
                    </Field>
                );
            }
        });

    return (
        <ItemContent>
            <FieldsContainer marginBottom>
                <CheckboxFieldContainer>
                    <CheckboxField
                        disabled={SparePartFactor?.surchargeSeparated || isDisabledByConfig}
                        name="SparePartFactor.surchargeInProtocolOly"
                        label={t('spareParts.surchargeInProtocolOly') || 'spareParts.surchargeInProtocolOly'}
                    />
                </CheckboxFieldContainer>
                <CheckboxFieldContainer>
                    <CheckboxField
                        disabled={SparePartFactor?.surchargeInProtocolOly || isDisabledByConfig}
                        name="SparePartFactor.surchargeSeparated"
                        label={t('spareParts.surchargeSeparated') || 'spareParts.surchargeSeparated'}
                    />
                </CheckboxFieldContainer>
            </FieldsContainer>
            <FieldsContainer marginBottom>
                <Field>
                    <InputField
                        disabled={isDisabledByConfig}
                        name="SparePartFactor.increaseDecrease"
                        label={t('spareParts.increaseDecrease')}
                    />
                </Field>
                <Field>
                    <NumberInputField
                        withoutZeroAfterClearing
                        type="tel"
                        disabled={isDisabledByConfig}
                        name="SparePartFactor.sparePartsDisposalCosts"
                        label={t('spareParts.sparePartsDisposalCosts')}
                        currency="2.00%"
                    />
                </Field>
                {isAustrian && (
                    <Field>
                        <NumberInputField
                            withoutZeroAfterClearing
                            type="tel"
                            disabled={isDisabledByConfig}
                            name="SparePartFactor.sparePartsDisposalCostsMax"
                            label={t('spareParts.sparePartsDisposalCostsMax')}
                            currency={`109.00${symbol}`}
                            fixedDecimalScale
                        />
                    </Field>
                )}
                <Field>
                    <NumberInputField
                        withoutZeroAfterClearing
                        type="tel"
                        disabled={isDisabledByConfig}
                        currency={symbol}
                        name="SparePartFactor.sparePartLumpSum.value"
                        label={t('spareParts.sparePartLumpSum')}
                        fixedDecimalScale
                    />
                </Field>
                <Field>
                    <NumberInputField
                        withoutZeroAfterClearing
                        type="tel"
                        disabled={isDisabledByConfig}
                        currency={symbol}
                        name="SparePartFactor.bracketSetRentCost.value"
                        label={t('spareParts.bracketSetRentCost')}
                        fixedDecimalScale
                    />
                </Field>
                <Field>
                    <InputWithSelect
                        isFloat
                        inputProps={{
                            disabled: isDisabledByConfig,
                            type: 'number',
                            name: 'SparePartFactor.discount.value',
                            label: t('common.discount')
                        }}
                        selectProps={{
                            disabled: isDisabledByConfig,
                            name: 'SparePartFactor.discount.mode',
                            options: percentOrAbsolute
                        }}
                    />
                </Field>
                {!isSpain && (
                    <Field>
                        <InputWithSelect
                            isFloat
                            inputProps={{
                                disabled: isDisabledByConfig,
                                type: 'number',
                                name: 'SparePartFactor.discountBiw.value',
                                label: t('common.discountBiw')
                            }}
                            selectProps={{
                                disabled: isDisabledByConfig,
                                name: 'SparePartFactor.discountBiw.mode',
                                options: percentOrAbsolute
                            }}
                        />
                    </Field>
                )}
            </FieldsContainer>
            <Text font="defaultBold">{t('spareParts.procurementCostsSubtitle')}</Text>
            <FieldsContainer marginTop marginBottom>
                <Field>
                    <NumberInputField
                        withoutZeroAfterClearing
                        type="tel"
                        disabled={isDisabledByConfig}
                        currency={symbol}
                        name="SparePartFactor.bracketSetProcurementCost.value"
                        label={t('spareParts.bracketSetProcurementCost')}
                        fixedDecimalScale
                    />
                </Field>
                <Field>
                    <NumberInputField
                        withoutZeroAfterClearing
                        type="tel"
                        disabled={isDisabledByConfig}
                        currency={symbol}
                        name="SparePartFactor.procurementCost.value"
                        label={t('spareParts.procurementCost')}
                        fixedDecimalScale
                    />
                </Field>
                {isAustrian && (
                    <>
                        <Field>
                            <NumberInputField
                                withoutZeroAfterClearing
                                type="tel"
                                disabled={isDisabledByConfig}
                                currency="5.00%"
                                name="SparePartFactor.procurementCostProc"
                                label={t('spareParts.procurementCostProc')}
                            />
                        </Field>
                        <Field>
                            <NumberInputField
                                withoutZeroAfterClearing
                                type="tel"
                                disabled={isDisabledByConfig}
                                currency={`350.00${symbol}`}
                                name="SparePartFactor.procurementCostMax"
                                label={t('spareParts.procurementCostMax')}
                                fixedDecimalScale
                            />
                        </Field>
                    </>
                )}
                {!isSpain && (
                    <Field>
                        <NumberInputField
                            withoutZeroAfterClearing
                            type="tel"
                            disabled={isDisabledByConfig}
                            currency={symbol}
                            name="SparePartFactor.bodyInWhiteProcurementCost.value"
                            label={t('spareParts.bodyInWhiteProcurementCost')}
                            fixedDecimalScale
                        />
                    </Field>
                )}
            </FieldsContainer>
            <Text font="defaultBold">{t('spareParts.smallPartsAndConsumablesSubtitle')}</Text>
            <TabsWrapper>
                <TabsField
                    name="SparePartFactor.smallSparePartCalculationModel"
                    values={filteredTabValues as TabButton[]}
                    typeStyle={filteredTabValues.length > 1 ? 'mobile' : 'single'}
                    fillWidth={isPhoneBig}
                />
            </TabsWrapper>
            <FieldsContainer marginTop>{generateSmallPartsFields()}</FieldsContainer>
        </ItemContent>
    );
};
