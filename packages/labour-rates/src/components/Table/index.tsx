import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldInputProps, useFormikContext } from 'formik';
import { useUnit } from 'effector-react';

import { KeyValueOption } from '@wedat/ui-kit';
import { InputField, SelectField } from '@wedat/ui-kit/Formik';
import { AdditionalTablePropsData, InputType } from '../../types/factors';
import { selectsOptions } from '../../constants/selectsOptions';
import { OPTIONS_FOR_PANELS } from '../../constants/factors';
import { factoresStores } from '../../stores/factors';
import { updateAddition } from '../../utils/selectsOptions';
import { PaintFactorTypes } from '../../types/paintTypes';

import {
    Container,
    Content,
    TitleContainer,
    Component,
    BoxSwitcherFieldStyled,
    StyledText,
    RowWrapper
} from './styles';

interface TableForAdditionsProps {
    data: AdditionalTablePropsData;
    paintName: string;
}

export const TableForAdditions: FC<TableForAdditionsProps> = ({ data, paintName }) => {
    const { t } = useTranslation();
    const isDisabledByConfig = useUnit(factoresStores.isLabourRatesReadOnly);
    const { values, setFieldValue } = useFormikContext<DAT2.Internal.FactorsParametersObject>();

    const optionForSelect = (name: string) => {
        switch (name) {
            case `${paintName}.colourMixCounter`:
                return [selectsOptions.EMPTY_ENTRY, ...selectsOptions.colourMixCounterCount];
            case `${paintName}.exemplarySheets`:
                return [selectsOptions.EMPTY_ENTRY, ...selectsOptions.colourMixCounterCount];
            case `${paintName}.matBlackWindowFrameCount`:
                return [selectsOptions.EMPTY_ENTRY, ...selectsOptions.matBlackWindowFrameCount];
            case `${paintName}.maskingWorkPlasticCount`:
                return [selectsOptions.EMPTY_ENTRY, ...selectsOptions.maskingWorkPlasticCount];
            case `${paintName}.maskingWorkGlassCount`:
                return [selectsOptions.EMPTY_ENTRY, ...selectsOptions.maskingWorkPlasticCount];
            default:
                return OPTIONS_FOR_PANELS;
        }
    };

    const onSelectUserChange = (e: FieldInputProps<number>) => {
        const value = e?.value;
        value === 0 && setFieldValue(e?.name, null);

        const paintNameKey = paintName as keyof typeof values;
        const paintTypes = values?.[paintNameKey] as PaintFactorTypes;
        const addition = paintTypes?.addition as string[] | null;
        const valueToSetForColourMixCounter = value && Number(value) >= 1 ? 'A' : '';
        const valueToSetForExemplarySheets = value && Number(value) >= 1 ? 'B' : '';

        if (e?.name === `${paintName}.colourMixCounter`) {
            updateAddition({
                key: `${paintName}.addition`,
                value: valueToSetForColourMixCounter,
                existingAddition: addition,
                setFieldValue: setFieldValue,
                name: e?.name,
                paintName: paintName
            });
        } else if (e?.name === `${paintName}.exemplarySheets`) {
            updateAddition({
                key: `${paintName}.addition`,
                value: valueToSetForExemplarySheets,
                existingAddition: addition,
                setFieldValue: setFieldValue,
                name: e?.name,
                paintName: paintName
            });
        }
    };

    const renderInputElement = (item: string, inputType: InputType) => {
        switch (inputType) {
            case 'select':
                return (
                    <SelectField
                        menuShouldBlockScroll
                        name={item}
                        options={optionForSelect(item) as KeyValueOption[]}
                        valueType="number"
                        valueKey="key"
                        inputSize="verySmall"
                        borderRadius={1}
                        disabled={isDisabledByConfig}
                        noBorder
                        onUserChange={e => onSelectUserChange(e)}
                    />
                );
            case 'input':
                return <InputField type="number" inputSize="verySmall" borderRadius={1} name={item} noBorder />;
            case 'BoxSwitcher':
            default:
                return <BoxSwitcherFieldStyled noBorder disabled={isDisabledByConfig} name={item} borderRadius={1} />;
        }
    };

    return (
        <>
            <Container>
                <Content withBackground>
                    <TitleContainer isHeader>
                        <StyledText font="footnote">{t('addition.title')}</StyledText>
                    </TitleContainer>
                    <Component isHeader>
                        <StyledText font="footnote">{t('addition.amount')}</StyledText>
                    </Component>
                </Content>
                {Object.keys(data).map((item, index, array) => {
                    const isLast = index === array.length - 1;

                    return (
                        <Content key={`${item}_${index}`}>
                            <RowWrapper>
                                <TitleContainer withoutBottomBorder={isLast}>{data[item].label}</TitleContainer>
                                <Component withoutBottomBorder={isLast}>
                                    {renderInputElement(item, data[item].type)}
                                </Component>
                            </RowWrapper>
                        </Content>
                    );
                })}
            </Container>
        </>
    );
};
