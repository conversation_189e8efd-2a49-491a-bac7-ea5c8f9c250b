import { useMemo, FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useUnit } from 'effector-react';
import { useFormikContext } from 'formik';

import { radioItems } from '@dat/shared-models/labour-rates/constants/radioItems';
import { sharedTemplateStores } from '@dat/shared-models/template';
import { LacquerMethodID } from '@dat/shared-models/labour-rates-generic/constants/paints';
import { useMedia } from '@dat/core/hooks/useMedia';
import { sizes } from '@wedat/ui-kit/mediaQueries';
import { DISABLE_COUNTRIES } from '@dat/shared-models/labour-rates/constants/initialFactorsParameters/aztLacquer';
import { AZT_ID, EUROLACK_ID, MANUFACTURER_ID } from '@dat/shared-models/labour-rates/constants/factors';
import { useHiddenBlock } from '@dat/smart-components/HideBlock/hooks';
import { contractStores } from '@dat/shared-models/contract';
import { extractVinResultFromContract } from '@dat/core/utils/extractVinResultFromContract';
import { useGetCurrencySymbol } from '@dat/shared-models/hooks/useGetCurrencySymbol';
import { TabButton } from '@wedat/ui-kit';
import { InputField, NumberInputField, SelectField, TabsField } from '@wedat/ui-kit/Formik';

import { configurationStores } from '../../stores/configuration';
import { invoiceRatesStores } from '../../stores/selects/invoiceRates';
import { paintTypesStores } from '../../stores/selects/paintTypes';
import { factoresStores } from '../../stores/factors';

import { Field, FieldContainer, FieldsContainer, ItemContent, TabsWrapper, VinContainer } from '../Layout/styles';
import { TableForAdditions } from '../Table';
import { generateAztAdditionTableData, generateEuroLacquerAdditionTableData } from '../../constants/factors';
import { AztLacquerCondition } from '../PaintFactors/ComponentConditions/AztLacquerCondition';
import { EuroLacquerCondition } from '../PaintFactors/ComponentConditions/EuroLacquerCondition';

export const AustrianMixedFields: FC = () => {
    const { t } = useTranslation();
    const isPhoneBig = useMedia(sizes.phoneBig);
    const country = useUnit(sharedTemplateStores.templateSettings.country);
    const configuration = useUnit(configurationStores.configuration);
    const additionalData = useUnit(invoiceRatesStores.additionalData);
    const lacquerFromVin = useUnit(invoiceRatesStores.lacquerFromVin);

    const isDisabledByConfig = useUnit(factoresStores.isLabourRatesReadOnly);

    const { values } = useFormikContext<DAT2.Internal.FactorsParametersObject>();

    const isWithHintDefaultLacquerMethod =
        !!additionalData?.hintDefaultLacquerMethod && values?.CalculationFactor?.selectedLacquerMethod === EUROLACK_ID;

    const selectedLacquerMethod = useMemo(() => radioItems.selectedLacquerMethod(t), [t]);

    const lacquerMethods = additionalData?.lacquerMethods ? Object.values(additionalData?.lacquerMethods) : [];

    const lacquerMethodsIntKeys = lacquerMethods.map(item => item.intKey);

    const countriesForHideAzt = configuration.countriesForHideAzt || [];

    const hideAztLacquer = [...countriesForHideAzt, ...DISABLE_COUNTRIES].includes(country);

    const contract = useUnit(contractStores.contract);
    const vinResultJson = extractVinResultFromContract(contract || {});
    const vinColorsLength = vinResultJson?.vinColors?.length ?? 0;

    const eurolackPaintTypesSelectOptions = useUnit(paintTypesStores.selectOptionsEuro);
    const manufPaintTypeSelectOptions = useUnit(paintTypesStores.selectOptionsManufacturer);
    const aztPaintTypesSelectOptions = useUnit(paintTypesStores.selectOptionsAzt);

    const symbol = useGetCurrencySymbol();

    const nameForPaintFields = useMemo(() => {
        if (values.CalculationFactor?.selectedLacquerMethod === LacquerMethodID.EURO_LACQUER)
            return 'EuroLacquerFactor';
        if (values.CalculationFactor?.selectedLacquerMethod === LacquerMethodID.MANUFACTURER_SPECIFIC)
            return 'ManufacturerLacquerFactor';
        if (values.CalculationFactor?.selectedLacquerMethod === LacquerMethodID.AZT) return 'AztLacquerFactor';
        return 'ManufacturerLacquerFactor';
    }, [values.CalculationFactor?.selectedLacquerMethod]);

    const paintTypeOptions = useMemo(() => {
        switch (nameForPaintFields) {
            case 'EuroLacquerFactor':
                return eurolackPaintTypesSelectOptions;
            case 'ManufacturerLacquerFactor':
                return manufPaintTypeSelectOptions;
            case 'AztLacquerFactor':
                return aztPaintTypesSelectOptions;
            default:
                return manufPaintTypeSelectOptions;
        }
    }, [aztPaintTypesSelectOptions, eurolackPaintTypesSelectOptions, manufPaintTypeSelectOptions, nameForPaintFields]);
    const selectedLacquerMethodValues = selectedLacquerMethod.filter(item =>
        lacquerMethodsIntKeys.includes(Number(item.value))
    );
    const tabValues = selectedLacquerMethodValues.map(method => ({ ...method, id: method.value }));

    const filteredTabValues = hideAztLacquer
        ? tabValues.filter(tabValue => tabValue.id !== LacquerMethodID.AZT)
        : tabValues;

    const resultTabValues = filteredTabValues.filter(filteredTabValue => filteredTabValue.id !== LacquerMethodID.CZ);

    const resultTabValuesCorrectedForType = resultTabValues.map(({ valueType: _, ...itemCopy }) => itemCopy);

    const isAztHidden = useHiddenBlock({
        configPath: 'labour-rates.isAztShown',
        showByDefault: true
    });
    const isEurolackHidden = useHiddenBlock({
        configPath: 'labour-rates.isEurolackShown',
        showByDefault: true
    });

    const isManufacturerHidden = useHiddenBlock({
        configPath: 'labour-rates.isManufacturerShown',
        showByDefault: true
    });

    const isPreparationLacquerCostHidden = useHiddenBlock({
        configPath: 'labour-rates.customPreparationLacquerCost',
        showByDefault: false
    });

    const preparationLacquerCost = values?.preparationLacquerCost;

    const resultTabValuesCorrectedForTypeFiltered = resultTabValuesCorrectedForType.filter(result => {
        if (
            (isAztHidden && result.id === AZT_ID) ||
            (isEurolackHidden && result.id === EUROLACK_ID && !isWithHintDefaultLacquerMethod) ||
            (isManufacturerHidden && result.id === MANUFACTURER_ID)
        ) {
            return false;
        }
        return true;
    });

    const dataForAdditionTable =
        nameForPaintFields === 'AztLacquerFactor'
            ? generateAztAdditionTableData(t, isPreparationLacquerCostHidden, preparationLacquerCost)
            : generateEuroLacquerAdditionTableData(t, isPreparationLacquerCostHidden, preparationLacquerCost);

    const shouldShowAdditionTable =
        nameForPaintFields === 'EuroLacquerFactor' || nameForPaintFields === 'AztLacquerFactor';

    return (
        <>
            <ItemContent>
                <TabsWrapper>
                    <TabsField
                        name="CalculationFactor.selectedLacquerMethod"
                        values={resultTabValuesCorrectedForTypeFiltered as TabButton[]}
                        typeStyle={resultTabValues.length > 1 ? 'mobile' : 'single'}
                        fillWidth={isPhoneBig}
                    />
                </TabsWrapper>
                <FieldsContainer marginBottom>
                    <Field>
                        <SelectField
                            disabled={isDisabledByConfig}
                            menuShouldBlockScroll
                            name={`${nameForPaintFields}.type`}
                            options={paintTypeOptions}
                            label={`${t('paint.type')}`}
                            valueType="string"
                            valueKey="key"
                            required
                            formatOptionLabel={({ label, key }) => (
                                <>
                                    {!!key && lacquerFromVin.includes(String(key)) && <VinContainer>VIN</VinContainer>}
                                    {label}
                                </>
                            )}
                        />
                    </Field>

                    <InputField
                        disabled={isDisabledByConfig}
                        type="text"
                        name={`${nameForPaintFields}.colorName`}
                        label={t('paint.colorName')}
                        prefix={vinColorsLength ? 'VIN' : ''}
                    />
                    <Field>
                        <NumberInputField
                            withoutZeroAfterClearing
                            type="tel"
                            currency={symbol}
                            name={`${nameForPaintFields}.wage.value`}
                            // 0 - absolute
                            disabled={values[`${nameForPaintFields}`]?.wageMode !== 0 || isDisabledByConfig}
                            label={t('paint.wage')}
                            required
                            fixedDecimalScale
                        />
                    </Field>
                </FieldsContainer>
                <FieldsContainer isAustria>
                    <FieldContainer isAustria>
                        <NumberInputField
                            withoutZeroAfterClearing
                            type="tel"
                            disabled={isDisabledByConfig}
                            name="SparePartFactor.smallSparePartPercentOfWage"
                            label={t('spareParts.smallSparePartPercentOfWage')}
                            currency="6.00%"
                        />
                    </FieldContainer>
                    <FieldContainer isAustria>
                        <NumberInputField
                            withoutZeroAfterClearing
                            type="tel"
                            disabled={isDisabledByConfig}
                            currency="5.00%"
                            name="SparePartFactor.procurementCostProc"
                            label={t('spareParts.procurementCostProc')}
                        />
                    </FieldContainer>
                    <FieldContainer isAustria>
                        <NumberInputField
                            withoutZeroAfterClearing
                            type="tel"
                            disabled={isDisabledByConfig}
                            name="CalculationFactor.additionalCostsPercent"
                            label={t('calculations.additionalCostsPercent')}
                            currency="100%"
                        />
                    </FieldContainer>
                    <FieldContainer isAustria>
                        <NumberInputField
                            withoutZeroAfterClearing
                            type="tel"
                            disabled={isDisabledByConfig}
                            name="SparePartFactor.sparePartsDisposalCosts"
                            label={t('spareParts.sparePartsDisposalCosts')}
                            currency="2.00%"
                        />
                    </FieldContainer>
                </FieldsContainer>
            </ItemContent>

            <FieldsContainer inTable isDomus>
                {shouldShowAdditionTable && (
                    <TableForAdditions data={dataForAdditionTable} paintName={nameForPaintFields} />
                )}
                {nameForPaintFields === 'AztLacquerFactor' && <AztLacquerCondition />}
                {nameForPaintFields === 'EuroLacquerFactor' && <EuroLacquerCondition />}
            </FieldsContainer>
        </>
    );
};
