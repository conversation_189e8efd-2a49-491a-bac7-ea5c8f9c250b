import { attach, restore } from 'effector';
import { LacquerManufValuesForRatesProps } from '@dat/shared-models/labour-rates/types';
import {
    getClaimRate,
    getClaimRateCurrentData,
    getClaimRateOnLacquerChange
} from '@dat/api2/services/myClaim-internal';
import { generateLacquerDataBasedOnCountry } from '@dat/shared-models/labour-rates/utils/generateLacquerManufMaterial';
import { contractEffects, contractEvents, contractStores } from '@dat/shared-models/contract';

import { ExtendedKeyValueOption } from '../../../utils/selectsOptions';
import { labourRatesDomain } from '../../plugin';
import { sharedLabourRatesEffects, sharedLabourRatesEvents } from '@dat/shared-models/labour-rates';
import { CONTRACT_ENTRIES_KEYS } from '@dat/core/constants/contract';
import { extractClaimRateFullData } from '@dat/shared-models/labour-rates/utils/extractClaimRateFullData';
import { sharedVehicleStores } from '@dat/shared-models/contract/Dossier/Vehicle';

const { createEffect, createEvent, createStore } = labourRatesDomain;

const { newContractReceived } = contractEvents;

// Effects

const getClaimRateFx = createEffect(getClaimRate);

const getClaimRateCurrentDataFx = createEffect(async (payload: { claimId: number }) => {
    const result = await getClaimRateCurrentData(payload);
    const modifiedData = extractClaimRateFullData(result);
    return modifiedData;
});
const getManufMaterialDataFx = createEffect(getClaimRateOnLacquerChange);
const updateRateFx = createEffect<DAT2.Internal.FactorsParametersObject, void>();

const { contractId, contract } = contractStores;
const { labourRatesUpdated } = sharedLabourRatesEvents;

const { isVehicleIdentified } = sharedVehicleStores;
const { createOrUpdateContractFx, getContractFx } = contractEffects;

const updateRateFromAddressBookFx = attach({
    source: { contractId, contract, isVehicleIdentified },
    effect: async ({ contractId, contract, isVehicleIdentified }, id: number) => {
        const isContractCalculated = !!contract?.Dossier?.RepairCalculation?.CalculationSummary?.TotalNetCosts;
        const shouldCalculate = isVehicleIdentified && isContractCalculated;

        if (!contractId) throw new Error('ContractId is empty. Contract update is not possible!');

        //Empty jsonLabourRates to let setting new rate correctly
        await createOrUpdateContractFx({
            contractId,
            templateData: {
                entry: [
                    {
                        key: CONTRACT_ENTRIES_KEYS.MEMO.labourRates,
                        value: ''
                    }
                ]
            }
        });

        await sharedLabourRatesEffects.handleDataWithoutJsonLabourRatesFx({ rateId: id });
        await getContractFx(contractId);

        labourRatesUpdated();

        shouldCalculate && (await contractEffects.calculateCurrentContractFx());
    }
});

const updateLacquerManufDataBaseFx = createEffect(
    async ({
        claimId,
        lacquerType,
        selectedLacquerMethod,
        country,
        locale,
        vehicle,
        addition
    }: LacquerManufValuesForRatesProps) => {
        const data = (await getManufMaterialDataFx({
            claimId,
            lacquerType,
            selectedLacquerMethod,
            addition
        })) as DAT2.Internal.Response.LacquerManufMaterial;
        return generateLacquerDataBasedOnCountry({
            claimId,
            lacquerType,
            selectedLacquerMethod,
            country,
            locale,
            vehicle,
            data
        }) as DAT2.Internal.Response.LacquerManufMaterial;
    }
);

const updateOnPaintTypeChangeFx = attach({
    effect: updateLacquerManufDataBaseFx
});

// Events

const rateSelected = createEvent<number>();
const setIsManualRateChange = createEvent<boolean>();
const triggerAutoSelectFirstRate = createEvent();
const firstRateAutoSelected = createEvent<number | 'SHARED'>();
const setIsNewContractReceived = createEvent<boolean>();
const setAdditionalData = createEvent<DAT2.Internal.Response.AdditionalData>();
const resetAdditionalData = createEvent();
const paintTypeChanged = createEvent<string>();
const setRateSelectOptions = createEvent<ExtendedKeyValueOption[]>();
const manufacturerAdditionChange = createEvent<string[]>();
const setCurrentManufacturerValues = createEvent<DAT2.Internal.ManufacturerLacquerParameters | void>();
const setShouldSelectRate = createEvent<boolean>();
const timeUnitChanged = createEvent<string>();

const resetLacquerFromVin = createEvent();

// Stores

const currentManufacturerValues = restore(setCurrentManufacturerValues, null);
const firstRateAutoSelectedStore = restore(firstRateAutoSelected, null);
const isNewContractReceived = createStore<boolean>(false)
    .on(setIsNewContractReceived, (_state, payload) => payload)
    .on(newContractReceived, (_state, _payload) => true);

const selectOptions = createStore<ExtendedKeyValueOption[]>([]).on(setRateSelectOptions, (_, payload) => payload);

const additionalData = createStore<DAT2.Internal.Response.AdditionalData | null>(null)
    .on(setAdditionalData, (_state, payload) => payload)
    .reset(resetAdditionalData);

const lacquerManufMaterial = restore(
    getManufMaterialDataFx.doneData.map(response => response ?? null),
    null
);
const manufacturerPaintTypeAfterChange = restore(paintTypeChanged, '');

const isRateManuallyChanged = restore(setIsManualRateChange, false);

const shouldSelectRate = restore(setShouldSelectRate, false).reset(sharedLabourRatesEvents.resetShouldSelectRate);

const lacquerFromVin = createStore<string>('').reset(resetLacquerFromVin);
//
/*** Export ***/
//
export const invoiceRatesEvents = {
    triggerAutoSelectFirstRate,
    rateSelected,
    firstRateAutoSelected,
    setIsNewContractReceived,
    setAdditionalData,
    paintTypeChanged,
    setIsManualRateChange,
    setRateSelectOptions,
    manufacturerAdditionChange,
    setCurrentManufacturerValues,
    setShouldSelectRate,
    resetAdditionalData,
    timeUnitChanged,
    resetLacquerFromVin
};
export const invoiceRatesStores = {
    selectOptions,
    additionalData,
    isNewContractReceived,
    lacquerManufMaterial,
    manufacturerPaintTypeAfterChange,
    isRateManuallyChanged,
    currentManufacturerValues,
    firstRateAutoSelectedStore,
    shouldSelectRate,
    lacquerFromVin
};
export const invoiceRatesEffects = {
    getClaimRateFx,
    getManufMaterialDataFx,
    updateRateFx,
    updateRateFromAddressBookFx,
    updateOnPaintTypeChangeFx,
    getClaimRateCurrentDataFx
};
