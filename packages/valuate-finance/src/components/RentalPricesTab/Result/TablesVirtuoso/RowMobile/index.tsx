import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useUnit } from 'effector-react';

import { currencyValues } from '@dat/core/constants/currency';
import { sharedTemplateStores } from '@dat/shared-models/template';

import { ColumnWrapper, RowStyled, RowHeader, RowContent, ColName, ContentWrapper } from './styles';
import { getColumnNames } from '../utils';
import { RowData } from '../types';

interface Props {
    rowData: RowData;
}

export const RowMobile = memo<Props>(({ rowData }) => {
    const { t } = useTranslation();
    // temporarily hidden at request of customer
    // const { name, offersNumber, min, max, mean, mode, median, standardDeviation } = rowData;
    const { name, offersNumber, min, max, mean } = rowData;

    const templates = useUnit(sharedTemplateStores.availableTemplates);
    const currency = templates.default?.settings?.currency;
    const currentCurrency = currencyValues[currency || 'EUR'];

    const columns = getColumnNames(t, false, currentCurrency);

    return (
        <RowStyled>
            <ContentWrapper>
                <ColName>{name}</ColName>
                <ColumnWrapper>
                    <RowHeader>{columns.offersNumber}</RowHeader>
                    <RowContent>{offersNumber}</RowContent>
                </ColumnWrapper>
                <ColumnWrapper>
                    <RowHeader>{columns.min}</RowHeader>
                    <RowContent>{min}</RowContent>
                </ColumnWrapper>
                <ColumnWrapper>
                    <RowHeader>{columns.max}</RowHeader>
                    <RowContent>{max}</RowContent>
                </ColumnWrapper>
                <ColumnWrapper>
                    <RowHeader>{columns.mean}</RowHeader>
                    <RowContent>{mean}</RowContent>
                </ColumnWrapper>
                {/*// temporarily hidden at request of customer*/}
                {/*<ColumnWrapper>*/}
                {/*    <RowHeader>{columns.mode}</RowHeader>*/}
                {/*    <RowContent>{mode}</RowContent>*/}
                {/*</ColumnWrapper>*/}
                {/*<ColumnWrapper>*/}
                {/*    <RowHeader>{columns.median}</RowHeader>*/}
                {/*    <RowContent>{median}</RowContent>*/}
                {/*</ColumnWrapper>*/}
                {/*<ColumnWrapper>*/}
                {/*    <RowHeader>{columns.standardDeviation}</RowHeader>*/}
                {/*    <RowContent>{standardDeviation}</RowContent>*/}
                {/*</ColumnWrapper>*/}
            </ContentWrapper>
        </RowStyled>
    );
});
