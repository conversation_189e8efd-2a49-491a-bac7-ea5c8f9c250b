import { FC } from 'react';
import { useFormikContext } from 'formik';

import { RadioGroupField, RadioGroupFieldProps } from '@dat/smart-components/FormikFields';

interface Props extends RadioGroupFieldProps {
    withoutSubmit?: boolean;
}

export const EvaluationRadioGroupField: FC<Props> = ({ withoutSubmit, ...props }) => {
    const { submitForm } = useFormikContext();

    return <RadioGroupField {...props} changeCallback={!withoutSubmit ? submitForm : undefined} />;
};
