import { Form, useFormikContext } from 'formik';
import { useUnit } from 'effector-react';

import { useMedia } from '@dat/core/hooks/useMedia';

import { Portal } from '@wedat/ui-kit';
import { sizes } from '@wedat/ui-kit/mediaQueries';

import { ValuateTabs } from '../../types/plugin';

import { pluginStores } from '../../stores/plugin';
import { valuationEvents } from '../../stores/valuation';
import { tabsStores } from '../../stores/tabs';

import { ValuationTab } from '../ValuationTab';
import { RentalPricesTab } from '../RentalPricesTab';
import { ResidualPredictionTab } from '../ResidualPredictionTab';
import { Content } from '../../pages/styles';
import { TopSection } from '../sections/TopSection';
import { Footer } from '../sections/Footer';

export const MainForm = () => {
    const options = useUnit(pluginStores.pluginOptions);
    const activeTab = useUnit(tabsStores.activeTab);

    const { submitForm } = useFormikContext();
    const isMobile = useMedia(sizes.laptop);

    const portalTargetId = options?.stickyFooterTargetId;

    const handleOnSubmit = () => {
        submitForm().then(() => {
            valuationEvents.setIsAllowedSubmitForm(false);
        });
    };

    if (activeTab === ValuateTabs.Valuation) {
        return (
            <Form>
                <TopSection isMobile={isMobile} />
                <Content>
                    <ValuationTab />
                </Content>

                {portalTargetId ? (
                    <Portal id={portalTargetId}>
                        <Footer isInPortal onSubmit={handleOnSubmit} />
                    </Portal>
                ) : (
                    <Footer onSubmit={handleOnSubmit} />
                )}
            </Form>
        );
    }
    if (activeTab === ValuateTabs.RentalPrices) {
        return (
            <Form>
                <TopSection isMobile={isMobile} />

                <Content>
                    <RentalPricesTab />
                </Content>
            </Form>
        );
    }
    if (activeTab === ValuateTabs.ResidualPrediction) {
        return (
            <>
                <Form>
                    <TopSection isMobile={isMobile} />
                </Form>
                <Content>
                    <ResidualPredictionTab />
                </Content>
            </>
        );
    }
    return (
        <Form>
            <TopSection isMobile={isMobile} />

            <Content />
        </Form>
    );
};
