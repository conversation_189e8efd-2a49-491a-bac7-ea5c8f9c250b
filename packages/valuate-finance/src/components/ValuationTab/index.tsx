import { MouseEvent } from 'react';
import { useTranslation } from 'react-i18next';
import { useUnit } from 'effector-react';
import { useFormikContext } from 'formik';

import { useMedia } from '@dat/core/hooks/useMedia';
import { sharedValuateStores } from '@dat/shared-models/valuate';

import { Tooltip } from '@wedat/ui-kit';
import { Accordion, AccordionTab, Switcher, Text } from '@wedat/kit';
import { sizes } from '@wedat/ui-kit/mediaQueries';
import { ManualFields } from '../../types/manualFields';
import { equipmentStores } from '../../stores/equipment';
import { freeValuationStores } from '../../stores/freeValuation';
import { getPromptAnswerFx } from '@dat/smart-components/PromptModal';

import { BasicData } from '../sections/BasicData';
import { BasicValuation } from '../sections/BasicValuation';
import { EquipmentValuation } from '../sections/EquipmentValuation';
import { EquipmentValue } from '../sections/EquipmentValue';
import { VehicleCondition } from '../sections/VehicleCondition';
import { SalesProcurement } from '../sections/SalesProcurement';
import { ManualEquipmentButton } from '../sections/EquipmentValuation/ManualEquipment/ManualEquipmentButton';
import { ManualEquipmentPopup } from '../sections/EquipmentValuation/ManualEquipment/ManualEquipmentPopup';
import { ButtonOpenEquipmentModal } from '../sections/EquipmentValuation/EquipmentPlugin/ButtonOpenEquipmentModal';
import { PredefinedManualEquipmentButton } from '../sections/EquipmentValuation/PredefinedManualEquipment/PredefinedManualEquipmentButton';
import { PredefinedManualEquipmentDrawer } from '../sections/EquipmentValuation/PredefinedManualEquipment/PredefinedManualEquipmentDrawer';
import { EquipmentModal } from '../sections/EquipmentValuation/EquipmentPlugin/EquipmentModal';
import {
    AccordionContainer,
    AccrodionItemWrapper,
    AccordionHeader,
    RightContentAccordionHeader,
    ToggleFreeValuation,
    RelativeWrapperPopup,
    AbsoluteWrapperPopup
} from '../../pages/styles';
import { Questions } from '../sections/Questions';

export const ValuationTab = () => {
    const { t } = useTranslation();
    const isMobile = useMedia(sizes.laptop);
    const isShowEquipment = useUnit(equipmentStores.isShowEquipment);
    const isFreeValuationEnabled = useUnit(freeValuationStores.isFreeValuationEnabled);
    const isFreeValuationActivated = useUnit(freeValuationStores.isFreeValuationActivated);
    const isEquipmentModalOpen = useUnit(equipmentStores.isEquipmentModalOpen);
    const questions = useUnit(sharedValuateStores.valuationQuestions);

    const { values, setFieldValue, submitForm } = useFormikContext<ManualFields>();
    const additionalEquipment = (values.Vehicle?.Equipment?.AdditionalEquipment?.EquipmentPosition?.map(
        ({ Description }) => Description
    ) || []) as string[];

    const handleClickToggleFreeValuation = (e: MouseEvent) => {
        e.stopPropagation(); // so that the accordion does not expand
        e.preventDefault();

        if (!isFreeValuationActivated) {
            getPromptAnswerFx({
                title: t('freeValuation.confirmModal.title'),
                message: 'valuate-finance:freeValuation.confirmModal.message',
                noAnswer: t('freeValuation.confirmModal.no'),
                yesAnswer: t('freeValuation.confirmModal.yes'),
                customBodyType: 'translateWithTransComponent'
            }).then(isConfirm => {
                if (isConfirm) {
                    setFieldValue('Vehicle.IsDisengagedN', true).then(() => {
                        submitForm();
                    });
                }
            });
        }
    };

    return (
        <AccordionContainer>
            <Accordion multiple>
                {questions.length > 0 && (
                    <AccordionTab key="Questions" header={<Text font="font-default-bold">{t('questions.title')}</Text>}>
                        <AccrodionItemWrapper>
                            <Questions />
                        </AccrodionItemWrapper>
                    </AccordionTab>
                )}

                <AccordionTab
                    key="BasicData"
                    header={<Text font="font-default-bold">{t('basicData.Output of result')}</Text>}
                >
                    <AccrodionItemWrapper>
                        <BasicData />
                    </AccrodionItemWrapper>
                </AccordionTab>

                <AccordionTab
                    key="BasicValuation"
                    header={
                        <AccordionHeader>
                            <Text font="font-default-bold">{t('basicValuation.title')}</Text>

                            <RightContentAccordionHeader>
                                {isFreeValuationEnabled && (
                                    <ToggleFreeValuation onClick={handleClickToggleFreeValuation}>
                                        {t('freeValuation.switcherLabel', { defaultValue: 'Free valuation' })}
                                        <Switcher
                                            inputId="Free valuation"
                                            aria-label={t('freeValuation.switcherLabel', {
                                                defaultValue: 'Free valuation'
                                            })}
                                            checked={isFreeValuationActivated}
                                        />
                                    </ToggleFreeValuation>
                                )}
                            </RightContentAccordionHeader>
                        </AccordionHeader>
                    }
                >
                    <AccrodionItemWrapper>
                        <BasicValuation isMobile={isMobile} />
                    </AccrodionItemWrapper>
                </AccordionTab>

                <AccordionTab
                    key="EquipmentValuation"
                    header={
                        <RelativeWrapperPopup>
                            <AccordionHeader>
                                <Text textOverflow="ellipsis" font="font-default-bold">
                                    {t('equipmentValuation.title')}
                                </Text>

                                <RightContentAccordionHeader>
                                    <Tooltip
                                        text={t('predefinedManualEquipmentDrawer.title')}
                                        width="auto"
                                        isFollowCursor={false}
                                        placement="top-end"
                                    >
                                        <PredefinedManualEquipmentButton />
                                    </Tooltip>
                                    {isShowEquipment && (
                                        <Tooltip
                                            text={t('vehicle-selection:equipment')}
                                            width="auto"
                                            isFollowCursor={false}
                                            placement="top-end"
                                        >
                                            <ButtonOpenEquipmentModal />
                                        </Tooltip>
                                    )}
                                    <Tooltip
                                        text={t('manualEquipmentModal.title')}
                                        width="auto"
                                        isFollowCursor={false}
                                        placement="top-end"
                                    >
                                        <ManualEquipmentButton />
                                    </Tooltip>
                                </RightContentAccordionHeader>
                            </AccordionHeader>
                            <AbsoluteWrapperPopup>
                                <ManualEquipmentPopup />
                            </AbsoluteWrapperPopup>
                        </RelativeWrapperPopup>
                    }
                >
                    <AccrodionItemWrapper>
                        <EquipmentValuation isMobile={isMobile} isEquipmentModalOutside />
                    </AccrodionItemWrapper>
                </AccordionTab>

                <AccordionTab
                    key="EquipmentValue"
                    header={<Text font="font-default-bold">{t('equipmentValue.title')}</Text>}
                >
                    <AccrodionItemWrapper>
                        <EquipmentValue />
                    </AccrodionItemWrapper>
                </AccordionTab>

                <AccordionTab
                    key="VehicleCondition"
                    header={<Text font="font-default-bold">{t('vehicleCondition.title')}</Text>}
                >
                    <AccrodionItemWrapper>
                        <VehicleCondition />
                    </AccrodionItemWrapper>
                </AccordionTab>

                <AccordionTab
                    key="SalesProcurement"
                    header={<Text font="font-default-bold">{t('salesProcurement.title')}</Text>}
                >
                    <AccrodionItemWrapper>
                        <SalesProcurement inLowerSection isMobile={isMobile} />
                    </AccrodionItemWrapper>
                </AccordionTab>
            </Accordion>
            {isEquipmentModalOpen && <EquipmentModal />}
            <PredefinedManualEquipmentDrawer additionalEquipment={additionalEquipment} />
        </AccordionContainer>
    );
};
