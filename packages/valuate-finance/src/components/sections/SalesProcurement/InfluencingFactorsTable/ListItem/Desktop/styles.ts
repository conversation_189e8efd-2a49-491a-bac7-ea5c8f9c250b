import styled from 'styled-components/macro';

export const Row = styled.div<{ disabled?: boolean; isGray: boolean }>`
    width: 100%;
    display: flex;
    position: relative;
    ${({ theme }) => theme.typography.note};
    ${({ isGray, theme }) => isGray && `background-color: ${theme.colors.dustBlue[50]}`};

    ${({ disabled, theme }) =>
        disabled &&
        `
        color: ${theme.colors.dustBlue[400]};
        background-color: ${theme.colors.gray['50']};
    `}
`;

export const Container = styled.div<{ width?: string; maxWidth?: string; alignRight?: boolean }>`
    padding: 8px 12px;
    display: flex;
    justify-content: ${({ alignRight }) => (alignRight ? 'flex-end' : 'flex-start')};
    align-items: center;
    ${({ width }) => width && `min-width: ${width};`};
    width: ${({ width = '100%' }) => width};
    max-width: ${({ maxWidth = '100%' }) => maxWidth};
`;
