import styled, { css } from 'styled-components/macro';
import { media, makeCustomScrollBar } from '@wedat/ui-kit/mediaQueries';

export const EquipmentValuationStyled = styled.div`
    width: 100%;
    margin-top: 24px;
    ${makeCustomScrollBar()}

    ${media.phoneBig`
        background-color: unset;
    `}
`;

export const Content = styled.div<{ withoutMarginTop?: boolean }>`
    ${({ withoutMarginTop }) => !withoutMarginTop && 'margin-top: 30px'};
`;

export const GroupContent = styled.div`
    display: flex;
    align-items: center;
`;

export const Table = styled.table`
    border-spacing: 0;
    width: 100%;
`;

export const Th = styled.th`
    padding: 8px 10px;
    color: ${({ theme }) => theme.colors.gray_40};
    background-color: ${({ theme }) => theme.colors.white};
    font-weight: 500;
    font-size: 18px;
    line-height: 27px;
`;

export const Td = styled.td<{ disabled?: boolean }>`
    min-width: 120px;
    padding: 8px 10px;
    color: ${({ theme }) => theme.colors.gray_300};
    background-color: ${({ theme }) => theme.colors.white};
    font-weight: 500;
    font-size: 18px;
    line-height: 27px;

    ${({ disabled }) =>
        disabled &&
        css`
            opacity: 0.7;
            pointer-events: none;
        `}
`;

export const Tr = styled.tr`
    background-color: var(--light);
    &:not(:first-child) {
        border-top: 2px solid ${({ theme }) => theme.colors.gray_10};
    }
`;

export const NoWrap = styled.div`
    display: flex;
    align-items: center;

    ${media.phoneBig`
        flex-direction: column;
        align-items: start;
        margin-bottom: 10px;
    `}
`;

export const TabsWrapper = styled.div`
    max-width: 600px;

    ${media.laptop`
        max-width: 100%;
    `}
`;

export const ButtonWrapper = styled.div`
    button {
        height: 40px;
        width: 40px;
    }
`;
