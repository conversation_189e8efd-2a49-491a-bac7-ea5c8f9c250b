import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { NumericFormat } from 'react-number-format';
import { Text } from '@wedat/kit';
import { ListItemProps } from '../types';
import { DescriptionContainer, Row, Column, HalfWidthContainer, TextWrapper, PlusCircleIconStyled } from './styles';

export const ListItemMobile: FC<ListItemProps> = ({ position, decreaseTypeLabel, onSelect, isSelected }) => {
    const { t } = useTranslation();
    const { Description, OriginalPrice, PercentageOfBasePrice, ResidualValue } = position;

    return (
        <Column onClick={onSelect} isSelected={isSelected}>
            <Row>
                <PlusCircleIconStyled $isSelected={isSelected} width={24} height={24} />
                <DescriptionContainer>{Description}</DescriptionContainer>
            </Row>
            <Row>
                <HalfWidthContainer>
                    <TextWrapper>
                        <Text textOverflow="ellipsis" font="font-footnote" color="inherit">
                            {' '}
                            {t('equipmentValuation.Original price')}
                        </Text>
                    </TextWrapper>
                    <NumericFormat
                        displayType="text"
                        decimalScale={2}
                        fixedDecimalScale={true}
                        thousandSeparator="."
                        decimalSeparator=","
                        value={OriginalPrice}
                    />
                </HalfWidthContainer>
                <HalfWidthContainer>
                    <TextWrapper>
                        <Text textOverflow="ellipsis" font="font-footnote" color="inherit">
                            {t('equipmentValuation.Devaluation')}
                        </Text>
                    </TextWrapper>
                    {decreaseTypeLabel}
                </HalfWidthContainer>
            </Row>
            <Row>
                <HalfWidthContainer>
                    <TextWrapper>
                        <Text textOverflow="ellipsis" font="font-footnote" color="inherit">
                            {t('equipmentValuation.% Manually')}
                        </Text>
                    </TextWrapper>
                    <NumericFormat
                        displayType="text"
                        decimalScale={2}
                        fixedDecimalScale={true}
                        thousandSeparator="."
                        decimalSeparator=","
                        value={PercentageOfBasePrice}
                    />
                </HalfWidthContainer>
                <HalfWidthContainer>
                    <TextWrapper>
                        <Text textOverflow="ellipsis" font="font-footnote" color="inherit">
                            {t('Manually')}
                        </Text>
                    </TextWrapper>
                    <NumericFormat
                        displayType="text"
                        decimalScale={2}
                        fixedDecimalScale={true}
                        thousandSeparator="."
                        decimalSeparator=","
                        value={ResidualValue}
                    />
                </HalfWidthContainer>
            </Row>
        </Column>
    );
};
