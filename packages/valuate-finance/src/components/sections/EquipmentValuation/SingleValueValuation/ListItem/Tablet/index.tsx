import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { DECREASE_TYPE_SELECT_OPTIONS } from '@dat/core/constants/valuationDecreaseTypeSelectOptions';
import { ListItemProps } from '../types';
import { DatNumberDisplayField } from '../../../../../DatNumberDisplayField';
import { EvaluationSelectField } from '../../../../../evaluationFields/Select';
import {
    EvaluationNumberInputField,
    EvaluationWithoutGrossNumberInputField
} from '../../../../../evaluationFields/Input';
import { EditEquipmentButton } from '../../EditEquipmentButton';
import { DeleteEquipmentButton } from '../../DeleteEquipmentButton';
import {
    ActionWrapper,
    DatPercentageContainer,
    DatResidualValueContainer,
    DecreaseTypeContainer,
    DescriptionContainer,
    InstallDate,
    OriginalPriceContainer,
    PercentageContainer,
    ResidualValueContainer,
    Row,
    Column
} from './styles';
import { ColumnsHeader } from '../../ColumnsHeader';
import { useUnit } from 'effector-react';
import { equipmentStores } from '../../../../../../stores/equipment';

export const ListItemTablet: FC<ListItemProps> = ({ position, isAdditional, isAustria, isFreeValuationActivated }) => {
    const { t } = useTranslation();
    const { isOpen: isManualEquipmentPopupOpen } = useUnit(equipmentStores.manualEquipmentPopupState);
    const isPercEquip = position?.DecreaseType === 'EquipPercentage';

    return (
        <Column disabled={position?.isMarkDeleted || isManualEquipmentPopupOpen}>
            <Row>
                <DescriptionContainer>
                    {position.Description}
                    {isAdditional && (
                        <InstallDate>
                            {t('equipmentValuation.Fitting date')}: {position.InstallDate || ''}
                        </InstallDate>
                    )}
                </DescriptionContainer>
                <ActionWrapper>
                    {isAdditional && <EditEquipmentButton position={position} />}
                    <DeleteEquipmentButton isMarkDeleted={position.isMarkDeleted} path={position.path} />
                </ActionWrapper>
            </Row>
            <Row>
                <ColumnsHeader isMobile />
            </Row>
            <Row>
                <OriginalPriceContainer>
                    <DatNumberDisplayField
                        disabled={position?.isMarkDeleted}
                        alwaysIsInactive
                        propertyPath={`${position.path}.OriginalPrice`}
                    />
                </OriginalPriceContainer>
                <DecreaseTypeContainer>
                    <EvaluationSelectField
                        disabled={position?.isMarkDeleted}
                        name={`${position.path}.DecreaseType`}
                        inputId={`${position.path}.DecreaseType`}
                        aria-label={`${position.path}.DecreaseType`}
                        inputSize="small"
                        valueKey="key"
                        valueType="string"
                        options={DECREASE_TYPE_SELECT_OPTIONS({ isAustria, isFreeValuationActivated })}
                    />
                </DecreaseTypeContainer>
                <DatPercentageContainer>
                    <DatNumberDisplayField
                        disabled={position?.isMarkDeleted}
                        alwaysIsInactive
                        onlyWithDatPrefix
                        propertyPath={`${position.path}.PercentageOfBasePrice`}
                    />
                </DatPercentageContainer>
                <PercentageContainer>
                    <EvaluationWithoutGrossNumberInputField
                        disabled={position?.isMarkDeleted || !position?.DecreaseType?.includes('Percentage')}
                        withoutZeroAfterClearing
                        fixedDecimalScale
                        decimalScale={0}
                        min={isPercEquip ? -100 : -25}
                        max={isPercEquip ? 100 : 25}
                        width={50}
                        inputSize="small"
                        fontSize="13"
                        name={`${position.path}.PercentageOfBasePrice`}
                    />
                </PercentageContainer>
                <DatResidualValueContainer>
                    <DatNumberDisplayField
                        disabled={position?.isMarkDeleted}
                        onlyWithDatPrefix
                        propertyPath={`${position.path}.ResidualValue`}
                    />
                </DatResidualValueContainer>
                <ResidualValueContainer>
                    <EvaluationNumberInputField
                        disabled={position?.isMarkDeleted}
                        withoutFixingDecimalInCalc
                        withoutZeroAfterClearing
                        fixedDecimalScale
                        decimalScale={2}
                        inputSize="small"
                        fontSize="13"
                        name={`${position.path}.ResidualValue`}
                    />
                </ResidualValueContainer>
            </Row>
        </Column>
    );
};
