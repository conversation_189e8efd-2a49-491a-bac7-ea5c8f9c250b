import styled from 'styled-components';

export const Row = styled.div<{ disabled?: boolean }>`
    width: 100%;
    display: flex;
    position: relative;
    ${({ theme }) => theme.typography.note};

    ${({ disabled, theme }) =>
        disabled &&
        `
        color: ${theme.colors.dustBlue[400]};
        background-color: ${theme.colors.gray['50']};
    `}
`;

export const Container = styled.div`
    padding: 8px 12px;
    display: flex;
    justify-content: start;
    align-items: center;
`;

export const DescriptionContainer = styled(Container)`
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    align-items: start;
    row-gap: 4px;
    ${({ theme }) => `color: ${theme.colors.dustBlue[900]}`}
`;
export const InstallDate = styled.div`
    ${({ theme }) => `color: ${theme.colors.dustBlue[400]}`};
    ${({ theme }) => theme.typography.footnote};
`;
export const OriginalPriceContainer = styled(Container)`
    flex: 0 0 84px;
`;
export const DecreaseTypeContainer = styled(Container)`
    flex: 0 0 96px;
`;
export const DatPercentageContainer = styled(Container)`
    flex: 0 0 60px;
`;
export const PercentageContainer = styled(Container)`
    flex: 0 0 90px;
`;
export const DatResidualValueContainer = styled(Container)`
    flex: 0 0 84px;
`;
export const ResidualValueContainer = styled(Container)`
    flex: 0 0 96px;
`;
export const ActionWrapper = styled(Container)`
    flex: 0 0 84px;
    display: flex;
    align-items: center;
    justify-content: end;
    column-gap: 12px;
`;
