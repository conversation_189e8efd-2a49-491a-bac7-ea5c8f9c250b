import { GetTextValuesFromParsedObjectRecursive } from '@dat/api2/utils/getTextValuesFromParsedObjectRecursive';

export type DossierWithExtractedTextValues = GetTextValuesFromParsedObjectRecursive<DAT2.Dossier> & {
    Vehicle?: {
        Equipment?: {
            SpecialEquipment?: {
                EquipmentPosition?: DAT2.API2DAT5.VehicleIdentificationService_schema1.EquipmentPosition[];
            };
        };
    };
};
