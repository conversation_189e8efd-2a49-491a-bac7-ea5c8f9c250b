import { sample } from 'effector';
import { pluginEvents } from '../plugin';
import { equipmentEffects } from '../equipment';
import { valuationServiceLogicEffects, valuationServiceLogicEvents } from './index';

sample({
    clock: pluginEvents.initPluginWithValuationService,
    target: valuationServiceLogicEffects.openingValuationFx
});

sample({
    clock: valuationServiceLogicEvents.handleSubmit,
    target: valuationServiceLogicEffects.handleSubmitValuationFx
});
sample({
    clock: valuationServiceLogicEvents.handleSubmitInfluencingFactors,
    target: valuationServiceLogicEffects.handleSubmitInfluencingFactorsFx
});

sample({
    clock: equipmentEffects.onCompleteEquipmentPluginFx.doneData,
    target: valuationServiceLogicEffects.updateDossierWithUpdatedEquipmentValuationFx
});
