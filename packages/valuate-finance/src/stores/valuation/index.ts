import { restore } from 'effector';

import { INITIAL_MANUAL_FIELDS } from '../../constants/initialManualFields';
import { ManualFields } from '../../types/manualFields';

import { valuateDomain } from '../plugin';

const { createEvent, createStore } = valuateDomain;

const setValuationResultMemo = createEvent<DAT2.ValuationResultMemoField>();
const valuationResultMemo = createStore<DAT2.ValuationResultMemoField | null>(null).on(
    setValuationResultMemo,
    (_, data) => data
);

const setFormikValuesData = createEvent<ManualFields>();
const formikValuesData = createStore<ManualFields>(INITIAL_MANUAL_FIELDS).on(
    setFormikValuesData,
    (_, newValues) => newValues
);
const datECodeEquipment = createStore<DAT2.API2DAT5.VehicleIdentificationService_schema1.EquipmentPosition[]>([]);

const setIsAllowedSubmitForm = createEvent<boolean>();
const isAllowedSubmitForm = restore(setIsAllowedSubmitForm, false);

export const valuationStores = {
    valuationResultMemo,
    formikValuesData,
    datECodeEquipment,
    isAllowedSubmitForm
};

export const valuationEvents = {
    setValuationResultMemo,
    setIsAllowedSubmitForm,
    setFormikValuesData
};
