import { FC, lazy, Suspense, useEffect, useReducer } from 'react';
import { useUnit } from 'effector-react';

import { pluginEvents } from './stores/plugin';
import { commonStores } from './stores/common';
import './stores/init';

import { PluginProvider } from '@dat/smart-components/PluginProvider';

import { PluginOptions } from './types/plugin';
import { useLazyLoading } from '@dat/core/hooks/useLazyLoading';
import { Preloader } from '@wedat/ui-kit';

const ValuateFinancePage = lazy(() => import('./pages/ValuateFinancePage'));

interface Props {
    options: PluginOptions;
}

const App: FC<Props> = ({ options }) => {
    //TODO refactor loading later. This is a temporary solution
    //https://incodewetrust.kaiten.ru/space/88422/card/28152219
    const isLoadingBaseEffects = useUnit(commonStores.isLoadingBaseEffects);
    const isLoading = useUnit(commonStores.isLoading);
    const isLazyLoading = useLazyLoading(isLoadingBaseEffects, 2500) || isLoading;
    // TODO: delete hack
    const [, forceUpdate] = useReducer(x => x + 1, 0);

    useEffect(() => {
        if (options.isComponent) {
            forceUpdate();
        }
    }, [options.isComponent]);

    return (
        <PluginProvider
            name="valuate-finance"
            options={options}
            onInit={pluginEvents.initPlugin}
            isLoading={isLazyLoading}
            onUnmount={pluginEvents.unmountPlugin}
        >
            <Suspense fallback={<Preloader isLoading />}>
                <ValuateFinancePage />
            </Suspense>
        </PluginProvider>
    );
};

export default App;
