<!DOCTYPE html>
<html lang="en" translate="no">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="https://test-wedat.dat.eu/residual-prediction/plugin.css" />
    <title>Prediction (no UI)</title>
  </head>

  <body>
    <script src="https://test-wedat.dat.eu/residual-prediction/plugin.js"></script>

    <div style="display: flex">
      <div
        style="
          display: flex;
          flex-direction: column;
          width: 360px;
          height: 600px;
          padding: 80px;
          border: 2px solid gray;
        "
      >
        <label>datECode</label>
        <input id="datEcode" value="010601840970001" />
        <label>mileage</label>
        <input id="mileage" value="40000" />
        <label>registrationDate</label>
        <input id="registrationDate" value="2020-11-24" />
        <label>forecastItem -> ageInMonth</label>
        <input id="ageInMonth" value="12" />
        <label>forecastItem -> mileagePerYear</label>
        <input id="mileagePerYear" value="40000" />

        <button style="margin-top: 40px" id="send">SEND</button>
      </div>
      <pre id="result" style="width: 800px; height: 600px; margin-left: 40px">RESULT will be here</pre>
    </div>

    <script>
      send.addEventListener('click', () => {
        window.RESIDUAL_PREDICTION_API.init({
          onComplete: values => {
            console.log('onComplete');
            // alert(JSON.stringify(values, null, 4));
            const result = document.getElementById('result');
            result.innerHTML = JSON.stringify(values, null, 4);
          },
          settings: { country: 'DE' },
          credentials: {
            customerNumber: '3131411',
            user: 'ferrsimo',
            password: 'somePassword'
          },
          requestData: {
            datECode: document.getElementById('datEcode').value,
            container: 'DE002',
            mileage: document.getElementById('mileage').value,
            constructionTime: 5980,
            registrationDate: document.getElementById('registrationDate').value,
            restriction: 'APPRAISAL',
            coverage: 'SIMPLE',
            save: 'TRUE',
            forecastItems: {
              forecastItem: [
                {
                  ageInMonth: document.getElementById('ageInMonth').value,
                  mileagePerYear: document.getElementById('mileagePerYear').value
                }
              ]
            },
            includeVat: 'true',
            curveType: 'Maximum',
            mileageType: 'Year',
            priceType: 'SalesPrice',
            valueType: 'Monetary',
            decreaseType: 'Table1'
          }
        });
      });
    </script>
  </body>
</html>
