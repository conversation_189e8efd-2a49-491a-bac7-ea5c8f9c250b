import { AvailableLocale } from '../types/weDatPublicDataTypes';

export const availableLocale: AvailableLocale[] = [
    {
        country: 'BG',
        language: 'BG'
    },
    {
        country: 'RUS',
        language: 'RUS'
    },
    {
        country: 'TR',
        language: 'TR'
    },
    {
        country: 'A',
        language: 'D'
    },
    {
        country: 'E',
        language: 'P'
    },
    {
        country: 'I',
        language: 'I'
    },
    {
        country: 'PL',
        language: 'PL'
    },
    {
        country: 'CZ',
        language: 'CZ'
    },
    {
        country: 'RO',
        language: 'RO'
    },
    {
        country: 'D',
        language: 'LT'
    },
    {
        country: 'SK',
        language: 'SK'
    },
    {
        country: 'H',
        language: 'H'
    },
    {
        country: 'D',
        language: 'ENG'
    },
    {
        country: 'E',
        language: 'E'
    },
    {
        country: 'CN',
        language: 'CN'
    },
    {
        country: 'CH',
        language: 'I'
    },
    {
        country: 'D',
        language: 'D'
    },
    {
        country: 'GR',
        language: 'GR'
    },
    {
        country: 'NL',
        language: 'NL'
    },
    {
        country: 'CH',
        language: 'F'
    },
    {
        country: 'F',
        language: 'F'
    },
    {
        country: 'D',
        language: 'ROK'
    },
    {
        country: 'CH',
        language: 'D'
    }
];

// private String datCountryIndicator;
// private String country;
// private String language;

// to see used values possible to use next SQL request:

// SELECT DISTINCT XLLA_SPR, XLLA_LKZ_SPR FROM Datrxlla;

// language country

// BG 	BG
// RUS	RUS
// TR 	TR
// D  	A
// P  	E
// I  	I
// PL 	PL
// CZ 	CZ
// RO 	RO
// LT 	D
// SK 	SK
// H  	H
// ENG	D
// E  	E
// CN 	CN
// I  	CH
// D  	D
// GR 	GR
// NL 	NL
// F  	CH
// F  	F
// ROK	D
// D  	CH

// SELECT DISTINCT DTBA_LKZ from Datrdtba;

// datCountryIndicator

// CZ
// SK
// GR
// H
// I
// NL
// PL
// RUS
// A
// RO
// D
// E
// BG
// CN
// F
// TR
// CH
