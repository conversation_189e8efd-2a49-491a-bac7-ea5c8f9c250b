import { AssemblyGroupGraphic_XGRHPublicSVG } from '../types/datDBTablesPublicTypes';
import { GetCountryVehicleTypePublicData } from '../types/weDatPublicDataServicesTypes';
import {
    AssemblyGroupGraphic,
    CountryVehicleTypePublicData,
    CountryVehicleTypePublicDataDB
} from '../types/weDatPublicDataTypes';
import { weDatPublicServiceSendRequest } from './commonRequest';

// Example request:
// https://grapa-data.fra1.digitaloceanspaces.com/grapa-service2/1/150/21/1_150_21_ENG_D.json

export async function getCountryVehicleTypePublicData(params: GetCountryVehicleTypePublicData | undefined) {
    if (!params) return;

    const { mainVehicleType, locale } = params;

    const pathVariableUrl = `/${mainVehicleType.vehicleType}/${mainVehicleType.manufacturer}/${mainVehicleType.mainType}\
/${mainVehicleType.vehicleType}_${mainVehicleType.manufacturer}_${mainVehicleType.mainType}\
_${locale.language}_${locale.country}.json`;

    // send request
    const result = await weDatPublicServiceSendRequest<CountryVehicleTypePublicDataDB>(pathVariableUrl);
    return result;
}

export function parseCountryVehicleTypePublicDataDB(data: CountryVehicleTypePublicDataDB | null) {
    if (!data) return data;

    const result: CountryVehicleTypePublicData = {
        mainVehicleType: data.mainVehicleType,
        locale: data.locale,
        zoneGraphicId: data.zoneGraphicId,
        assemblyGroupGraphics: parseXGRHPublicSVG(data.assemblyGroupGraphic_XGRHPublicSVG)
    };

    return result;
}

function parseXGRHPublicSVG(XGRHPublicSVG?: AssemblyGroupGraphic_XGRHPublicSVG[]) {
    if (!XGRHPublicSVG) return;

    const result: AssemblyGroupGraphic[] = XGRHPublicSVG.map(dbData => {
        const zones: { zoneId: number; order: number }[] = [];
        dbData.DTBA_ZONE_VEKT?.split('#')
            .filter(str => str.length)
            .forEach(str => {
                const subst = str.split('-');
                if (subst.length === 2) {
                    zones.push({
                        zoneId: Number(subst[0]),
                        order: Number(subst[1])
                    });
                }
            });

        return {
            groupGraphicId: dbData.DTBA_BGR,
            localeStringId: dbData.DTBA_STNR,
            localeDescription: dbData.XLLA_TE,
            isHailGraphic: dbData.DTBA_KZ_ART === 'H',
            beginPeriod: dbData.DTBA_BZ_VON,
            zones,
            svg: dbData.SVG
        };
    });
    return result;
}
