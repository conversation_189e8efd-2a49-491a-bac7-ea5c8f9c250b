import { DAT3 } from '@dat/api2dat3/dat3';
import { parseFTDvnMap } from '../utils/parseFTDvnMap';
import { weDatPublicServiceSendRequest } from './commonRequest';

export async function getFtCommonDefaultConfig(): Promise<DAT3.FastTrackTypes.FtCommonDefaultConfigType> {
    // download config from server on start application in model just once
    const ftCommonDefaultConfig = weDatPublicServiceSendRequest<DAT3.FastTrackTypes.FtCommonDefaultConfigType>(
        '/common/ftCommonDefaultConfig.json'
    );
    return ftCommonDefaultConfig;
}

// if file ftCommonDefaultConfig.json is absent or need to be update (not actual)
// as data source can be use 4 original files
export async function getFtCommonDefaultConfig_4files(): Promise<DAT3.FastTrackTypes.FtCommonDefaultConfigType> {
    // download config from server on start application in model just once
    const ftRepairPromise = weDatPublicServiceSendRequest<DAT3.FastTrackTypes.FtRepairType>('/common/ftRepairs.json');
    const ftDvnMapPromise = weDatPublicServiceSendRequest<DAT3.FastTrackTypes.FtDvnMapType>('/common/ftDvnMap.json');
    const ftGroupsPromise = weDatPublicServiceSendRequest<DAT3.FastTrackTypes.FtGroupsType>('/common/ftGroups.json');
    const ftDamagesPromise = weDatPublicServiceSendRequest<DAT3.FastTrackTypes.FtDamagesType>('/common/ftDamages.json');

    const [ftRepair, ftDvnMap, ftGroups, ftDamages] = await Promise.all([
        ftRepairPromise,
        ftDvnMapPromise,
        ftGroupsPromise,
        ftDamagesPromise
    ]);

    const ftDvnMapItems = parseFTDvnMap(ftDvnMap);

    return { ftRepair, ftDvnMapItems, ftGroups, ftDamages };
}
