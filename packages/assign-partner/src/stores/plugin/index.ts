import { createDomain, restore } from 'effector';
import { PluginOptions } from '../../types/plugin';

export const assignPartnerDomain = createDomain();
const { createEvent, createStore } = assignPartnerDomain;

const initPlugin = createEvent<PluginOptions>();
const unmountPlugin = createEvent<PluginOptions>();

const pluginOptions = restore(initPlugin, null).reset(unmountPlugin);

const pluginInited = createStore(false).on(initPlugin, () => true);

export const pluginEvents = {
    initPlugin,
    unmountPlugin
};
export const pluginStores = {
    pluginOptions,
    pluginInited
};
