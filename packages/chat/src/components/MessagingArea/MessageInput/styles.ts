import styled from 'styled-components/macro';
import { makeCustomScrollBar } from '@wedat/ui-kit/mediaQueries';

export const Container = styled.div`
    display: flex;
    width: 100%;
    height: fit-content;
    align-items: flex-end;
    padding: 16px;
    background-color: ${({ theme }) => theme.colors.white};
`;

export const TextareaWrapper = styled.div`
    width: 100%;
    margin-right: 10px;
    margin-bottom: 0;
    line-height: 27px;
    border: none;

    ${makeCustomScrollBar()}

    &::placeholder {
        line-height: 27px;
        font-weight: 500;
    }
`;

export const SendButtonWrapper = styled.div`
    button {
        width: 48px;
        height: 48px;
        margin: 0 0 10px;
        border-radius: 3px;
        border: none;
        flex-shrink: 0;

        svg {
            transform: rotate(90deg);
        }

        &:hover,
        &:focus {
            border: none;
            background-color: ${({ theme }) => theme.colors.deepBlue['800']};
        }
    }
`;
