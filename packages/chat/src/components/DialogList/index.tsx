import { useUnit } from 'effector-react';

import { dialogStores } from '../../stores/dialogs';
import { sharedUserStores } from '@dat/shared-models/user';
import { sharedProfilesStores } from '@dat/shared-models/profiles';
import { getDialogItemCompanionName } from '../../utils/getDialogItemCompanionName';
import { getCompanionUserAvatar } from '../../utils/getCompanionUserAvatar';
import { Text } from '@wedat/kit';

// import { SearchAndFilter } from './SearchAndFilter';
import { DialogItem } from './DialogItem';
import { Container, RoleBlocksWrapper, RoleBlock, TextWrapper, Header, HeaderTextWrapper } from './styles';
import { pluginEvents } from '../../stores/plugin';
import { CloseIconStyled } from '../MessagingArea/Header/styles';
import { useTranslation } from 'react-i18next';
import { contractStores } from '@dat/shared-models/contract';

export const DialogList = () => {
    const { t } = useTranslation();
    const partners = useUnit(dialogStores.partners);
    const roleSortedDialogs = useUnit(dialogStores.roleSortedDialogs);
    const username = useUnit(sharedUserStores.username);
    const customerNumber = useUnit(sharedUserStores.customerNumber);
    const sameCustomerProfiles = useUnit(sharedProfilesStores.sameCustomerProfiles);
    const contract = useUnit(contractStores.contract);

    return (
        <Container>
            {/* <SearchAndFilter /> */}
            {/* TODO: delete after add SearchAndFilter */}
            <Header>
                <HeaderTextWrapper>
                    <Text font="font-semi-heading">{t('title')}</Text>
                </HeaderTextWrapper>
                <CloseIconStyled
                    aria-label={t('iconClose')}
                    width="18"
                    height="18"
                    onClick={() => {
                        pluginEvents.setChatModalOpened(false);
                    }}
                />
            </Header>
            <RoleBlocksWrapper>
                {Object.entries(roleSortedDialogs).map(([role, dialogs]) => (
                    <RoleBlock key={role}>
                        <TextWrapper>
                            <Text textTransform="capitalize">{t(`all-components:roles.${role.toLowerCase()}`)}</Text>
                        </TextWrapper>
                        {dialogs.map(dialog => {
                            const companionName = getDialogItemCompanionName({
                                dialog,
                                username,
                                sameCustomerProfiles,
                                partners,
                                customerNumber,
                                contract
                            });

                            const companioUserAvatar = getCompanionUserAvatar({
                                dialog,
                                username,
                                sameCustomerProfiles
                            });

                            return (
                                <DialogItem
                                    key={dialog.id}
                                    dialogId={dialog.id}
                                    companionName={companionName}
                                    companionAvatar={companioUserAvatar}
                                    dialogType={dialog.type}
                                    companionPartnerId={dialog.companionPartnerId}
                                    contract={contract}
                                />
                            );
                        })}
                    </RoleBlock>
                ))}
            </RoleBlocksWrapper>
        </Container>
    );
};
