import { sample } from 'effector';
import { deductibleEffects, deductibleEvent } from '.';

const { getDeductibleGroup } = deductibleEvent;

const { getDeductibleGroupingFx, getDeductibleGroupingDownloadFx } = deductibleEffects;

sample({
    clock: getDeductibleGroup,
    target: getDeductibleGroupingFx
});
sample({
    clock: getDeductibleGroupingDownloadFx.doneData,
    target: getDeductibleGroupingFx
});
