import { extractFactorsParametersFromContractMemoField } from './extractFactorsParametersFromContractMemoField';
import { vi } from 'vitest';

describe('extractFactorsParametersFromContractMemoField', () => {
    test('should return null if contract is null', () => {
        expect(extractFactorsParametersFromContractMemoField(null)).toBeNull();
    });

    test('should return null if labourRatesJSON is empty', () => {
        const contract = {
            customTemplateData: {
                entry: [
                    {
                        key: 'jsonLabourRates',
                        value: {
                            _text: ''
                        }
                    }
                ]
            }
        } as any;
        expect(extractFactorsParametersFromContractMemoField(contract)).toBeNull();
    });

    test('should parse labourRatesJSON and return factors parameters object', () => {
        const labourRatesJSON = JSON.stringify({
            costRateFactors: {
                LabourCostFactor: {
                    rate: 1.0,
                    type: 'fix'
                }
            }
        });
        const contract = {
            customTemplateData: {
                entry: [
                    {
                        key: 'jsonLabourRates',
                        value: {
                            _text: labourRatesJSON
                        }
                    }
                ]
            }
        } as any;
        expect(extractFactorsParametersFromContractMemoField(contract)).toEqual({
            costRateFactors: {
                LabourCostFactor: {
                    rate: 1.0,
                    type: 'fix'
                }
            }
        });
    });

    test('should log an error and return null if labourRatesJSON is invalid', () => {
        const labourRatesJSON = 'invalidJSON';
        const contract = {
            customTemplateData: {
                entry: [
                    {
                        key: 'jsonLabourRates',
                        value: {
                            _text: labourRatesJSON
                        }
                    }
                ]
            }
        } as any;
        console.error = vi.fn();
        expect(extractFactorsParametersFromContractMemoField(contract)).toBeNull();
        expect(console.error).toHaveBeenCalled();
    });
});
