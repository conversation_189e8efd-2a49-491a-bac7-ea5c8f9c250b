import { getMergeAppraisalEquipmentsValuatePluginResult } from './getMergeAppraisalEquipmentsValuatePluginResult';

describe('getMergeAppraisalEquipmentsValuatePluginResult', () => {
    test('should merge appraisalEquipmentsFromContract and appraisalEquipmentsFromValuatePluginResult', () => {
        const valuateEquipmentsFromContract: DAT2.Equipment = {
            EquipmentValue: 111,
            EquipmentValueType: 'some type',
            SpecialEquipment: {
                EquipmentPosition: [
                    {
                        DatEquipmentId: 1,
                        Description: 'Equipment 1',
                        ResidualValue: 123,
                        OriginalPrice: 123,
                        ValuationControlType: 'no valuation'
                    },
                    {
                        DatEquipmentId: 2,
                        Description: 'Equipment 2',
                        ResidualValue: 123,
                        OriginalPrice: 123,
                        ValuationControlType: 'no valuation'
                    }
                ]
            },
            SeriesEquipment: {
                EquipmentPosition: [
                    { DatEquipmentId: 5, Description: 'Equipment 5' },
                    { DatEquipmentId: 6, Description: 'Equipment 6' }
                ]
            }
        };
        const appraisalEquipmentsFromContract: DAT2.Equipment = {
            EquipmentValue: 111,
            EquipmentValueType: 'some type',
            SpecialEquipment: {
                EquipmentPosition: [
                    {
                        DatEquipmentId: 1,
                        Description: 'Equipment 1',
                        ResidualValue: 123,
                        OriginalPrice: 123,
                        ValuationControlType: 'no valuation'
                    },
                    {
                        DatEquipmentId: 2,
                        Description: 'Equipment 2',
                        ResidualValue: 123,
                        OriginalPrice: 123,
                        ValuationControlType: 'no valuation'
                    },
                    { DatEquipmentId: 3, Description: 'Equipment 3' },
                    { DatEquipmentId: 4, Description: 'Equipment 4' }
                ]
            },
            SeriesEquipment: {
                EquipmentPosition: [
                    { DatEquipmentId: 5, Description: 'Equipment 5' },
                    { DatEquipmentId: 6, Description: 'Equipment 6' }
                ]
            }
        };
        const valuatePluginResult: DAT2.Equipment = {
            EquipmentValue: 222,
            EquipmentValueType: 'some type 1',
            AdditionalEquipment: {
                EquipmentPosition: [
                    {
                        DatEquipmentId: 7,
                        Description: 'Equipment 7',
                        ResidualValue: 111,
                        OriginalPrice: 111,
                        ValuationControlType: 'no price'
                    },
                    {
                        DatEquipmentId: 8,
                        Description: 'Equipment 8',
                        ResidualValue: 111,
                        OriginalPrice: 111,
                        ValuationControlType: 'no price'
                    }
                ]
            },
            SpecialEquipment: {
                EquipmentPosition: [
                    {
                        DatEquipmentId: 1,
                        Description: 'Equipment 1',
                        ResidualValue: 234,
                        OriginalPrice: 234,
                        ValuationControlType: 'no price'
                    },
                    {
                        DatEquipmentId: 2,
                        Description: 'Equipment 2',
                        ResidualValue: 234,
                        OriginalPrice: 234,
                        ValuationControlType: 'no price'
                    }
                ]
            },
            SeriesEquipment: {
                EquipmentPosition: [
                    { DatEquipmentId: 5, Description: 'Equipment 5' },
                    { DatEquipmentId: 6, Description: 'Equipment 6' }
                ]
            }
        };

        const expectedResult = {
            EquipmentValue: 222,
            EquipmentValueType: 'some type 1',
            AdditionalEquipment: {
                EquipmentPosition: [
                    {
                        DatEquipmentId: 7,
                        Description: 'Equipment 7',
                        ResidualValue: 111,
                        OriginalPrice: 111,
                        ValuationControlType: 'no price'
                    },
                    {
                        DatEquipmentId: 8,
                        Description: 'Equipment 8',
                        ResidualValue: 111,
                        OriginalPrice: 111,
                        ValuationControlType: 'no price'
                    }
                ]
            },
            SpecialEquipment: {
                EquipmentPosition: [
                    { DatEquipmentId: 3, Description: 'Equipment 3' },
                    { DatEquipmentId: 4, Description: 'Equipment 4' },
                    {
                        DatEquipmentId: 1,
                        Description: 'Equipment 1',
                        ResidualValue: 234,
                        OriginalPrice: 234,
                        ValuationControlType: 'no price'
                    },
                    {
                        DatEquipmentId: 2,
                        Description: 'Equipment 2',
                        ResidualValue: 234,
                        OriginalPrice: 234,
                        ValuationControlType: 'no price'
                    }
                ]
            },
            SeriesEquipment: {
                EquipmentPosition: [
                    { DatEquipmentId: 5, Description: 'Equipment 5' },
                    { DatEquipmentId: 6, Description: 'Equipment 6' }
                ]
            }
        };

        const receivedResult = getMergeAppraisalEquipmentsValuatePluginResult(
            valuatePluginResult,
            appraisalEquipmentsFromContract,
            valuateEquipmentsFromContract
        );

        expect(receivedResult).toEqual(expectedResult);
    });

    test('should handle undefined appraisalEquipmentsFromContract', () => {
        const appraisalEquipmentsFromContract = undefined;
        const appraisalEquipmentsFromValuatePluginResult: DAT2.Equipment = {
            EquipmentValue: 222,
            EquipmentValueType: 'some type 1',
            SpecialEquipment: {
                EquipmentPosition: [
                    {
                        DatEquipmentId: 1,
                        Description: 'Equipment 1',
                        ResidualValue: 234,
                        OriginalPrice: 234,
                        ValuationControlType: 'no price'
                    },
                    {
                        DatEquipmentId: 2,
                        Description: 'Equipment 2',
                        ResidualValue: 234,
                        OriginalPrice: 234,
                        ValuationControlType: 'no price'
                    }
                ]
            },
            SeriesEquipment: {
                EquipmentPosition: [{ DatEquipmentId: 5, Description: 'Equipment 5' }]
            }
        };

        const expectedResult = {
            EquipmentValue: 222,
            EquipmentValueType: 'some type 1',
            SpecialEquipment: {
                EquipmentPosition: [
                    {
                        DatEquipmentId: 1,
                        Description: 'Equipment 1',
                        ResidualValue: 234,
                        OriginalPrice: 234,
                        ValuationControlType: 'no price'
                    },
                    {
                        DatEquipmentId: 2,
                        Description: 'Equipment 2',
                        ResidualValue: 234,
                        OriginalPrice: 234,
                        ValuationControlType: 'no price'
                    }
                ]
            },
            SeriesEquipment: {
                EquipmentPosition: [{ DatEquipmentId: 5, Description: 'Equipment 5' }]
            }
        };

        const receivedResult = getMergeAppraisalEquipmentsValuatePluginResult(
            appraisalEquipmentsFromValuatePluginResult,
            appraisalEquipmentsFromContract
        );

        expect(receivedResult).toEqual(expectedResult);
    });
});
