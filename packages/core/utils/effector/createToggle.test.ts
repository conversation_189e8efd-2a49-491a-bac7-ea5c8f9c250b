import { createToggle } from './createToggle';

describe('createToggle', () => {
    it('Common toggles', async () => {
        const [store, toggle] = createToggle(true);

        toggle();
        expect(store.getState()).toBe(false);
        toggle();
        expect(store.getState()).toBe(true);
        toggle();
        toggle();
        expect(store.getState()).toBe(true);
        toggle();
        toggle();
        toggle();
        expect(store.getState()).toBe(false);
        toggle(true);
        expect(store.getState()).toBe(true);
        toggle(false);
        expect(store.getState()).toBe(false);
        toggle(false);
        expect(store.getState()).toBe(false);
        toggle();
        expect(store.getState()).toBe(true);
        toggle('');
        expect(store.getState()).toBe(false);
        toggle(1);
        expect(store.getState()).toBe(true);
        toggle({});
        expect(store.getState()).toBe(false);
    });
});
