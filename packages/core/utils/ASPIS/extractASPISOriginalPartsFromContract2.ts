import { CONTRACT_ENTRIES_KEYS } from '@dat/core/constants/contract';

import * as DAT5 from '@wedat/api';

// the same like extractASPISOriginalPartsFromContract but on api2dat5
export const extractASPISOriginalPartsFromContract2 = (
    contract: DAT5.MyClaimExternalService_schema1.contractDetails | null
): DAT5.MyClaimExternalService_schema2.PositionItaly[] => {
    const entries = contract?.customTemplateData?.entry || [];

    try {
        return JSON.parse(
            String(entries.find(entry => entry.key === CONTRACT_ENTRIES_KEYS.MEMO.originalPartListASPIS)?.value?._value)
        ) as DAT5.MyClaimExternalService_schema2.PositionItaly[];
    } catch {
        return [];
    }
};
