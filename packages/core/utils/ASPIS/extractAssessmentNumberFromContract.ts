import { CONTRACT_ENTRIES_KEYS } from '@dat/core/constants/contract';

// eslint-disable-next-line no-restricted-imports
import { getParsedArraySafe } from '@dat/api2/utils';

export const extractAssessmentNumberFromContract = (contract: DAT2.ContractFromGetContract | null): string | null => {
    const entries = getParsedArraySafe(contract?.customTemplateData?.entry);

    try {
        return entries.find(entry => entry.key === CONTRACT_ENTRIES_KEYS.MEMO.AspisAssessmentNumber)?.value
            ._text as string;
    } catch {
        return null;
    }
};
