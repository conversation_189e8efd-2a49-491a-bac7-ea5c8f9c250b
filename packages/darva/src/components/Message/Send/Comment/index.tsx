import { FC, useCallback } from 'react';
import { Formik, FormikHelpers } from 'formik';
import { object, string } from 'yup';

import { ModalPrimary } from '@wedat/ui-kit';
import { TextareaField } from '@wedat/ui-kit/Formik';
import { useToggle } from '@dat/core/hooks/useToggle';

import { Button } from '../../../Common/styles';
import { useTranslation } from '../../../../utils/i18nProvider';
import { commentEffects } from '../../../../stores/message/Comment';
import { CommentForm, FooterButtonSubmit, FooterContainer } from './styles';

interface Props {
    onSendComment?: () => void;
}

export const SendComment: FC<Props> = ({ onSendComment }) => {
    const { t } = useTranslation();
    const [isOpen, toggle] = useToggle();

    const title = t('messages.comment.title');

    const initValues = {
        comment: ''
    };

    const onSubmit = useCallback(
        (values: typeof initValues, helpers: FormikHelpers<any>) =>
            commentEffects
                .sendCommentFx(values.comment)
                .then(toggle)
                .then(onSendComment)
                .then(() => helpers.resetForm()),
        [onSendComment, toggle]
    );

    const validation = object({
        comment: string().required()
    });

    return (
        <Formik initialValues={initValues} onSubmit={onSubmit} validationSchema={validation}>
            <>
                <Button typeStyle={{ type: 'primary' }} onClick={toggle}>
                    {title}
                </Button>
                <ModalPrimary
                    onDismiss={toggle}
                    isOpen={isOpen}
                    title={title}
                    bodyWidth="500px"
                    footerChildren={
                        <FooterContainer>
                            <FooterButtonSubmit type="submit" form="comment-form">
                                {t('messages.comment.fields.submit')}
                            </FooterButtonSubmit>
                        </FooterContainer>
                    }
                >
                    <CommentForm id="comment-form">
                        <TextareaField
                            name="comment"
                            label={t('messages.comment.fields.comment')}
                            defaultHeight="200px"
                        />
                    </CommentForm>
                </ModalPrimary>
            </>
        </Formik>
    );
};
