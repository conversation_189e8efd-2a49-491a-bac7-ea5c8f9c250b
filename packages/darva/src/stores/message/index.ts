import { attach, createEffect } from 'effector';

import { contractEvents, contractStores } from '@dat/shared-models/contract';
import { sharedUserStores } from '@dat/shared-models/user';
import { darvaContractDomain } from '@dat/shared-models/darva';
import { DarvaAPI } from '@dat/api2/services/fr/DARVA';

const { createStore } = darvaContractDomain;
const { customerNumber } = sharedUserStores;
const { contractId } = contractStores;
const { resetContract } = contractEvents;

const listMessagesFx = createEffect('get messages list', {
    handler: (params: { customerNumber: string; filter: FR.DARVA.MessageFilter }) =>
        DarvaAPI.message.list(params.customerNumber, params.filter).then(response => response.data)
});
const listMessagesAttachFx = attach({
    source: { customerNumber, contractId },
    effect: listMessagesFx,
    mapParams: (filters: Omit<FR.DARVA.MessageFilter, 'claimId'>, { customerNumber, contractId: claimId }) => {
        return { customerNumber: customerNumber.toString(), filter: { ...(filters || {}), claimId } };
    }
});

// -null: not checked
const listMessages = createStore<FR.DARVA.Message[]>([])
    .on(listMessagesFx.doneData, (_, response) =>
        response.data.map((item: FR.DARVA.Message) => ({
            createdAt: item.createdAt,
            messageLabel: item.messageLabel,
            direction: item.direction,
            isValid: item.isValid,
            link: item.link,
            errors: item.errors
        }))
    )
    .reset(resetContract);

export const messageStore = {
    listMessages
};

export const messageEffects = {
    listMessagesAttachFx,
    listMessagesFx
};
