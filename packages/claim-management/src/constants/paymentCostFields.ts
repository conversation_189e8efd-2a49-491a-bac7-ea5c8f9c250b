export const COST_PAYMENT_FIELDS_NAME = [
    'videoExpertise',
    'interimExpertise',
    'visuraAlPra',
    'ardcase',
    'agreedExpertise',
    'canalization',
    'ctu',
    'uneconomicalWithEstimation',
    'uneconomicalWithoutEstimation',
    'expertiseBodyshopNetwork',
    'photoWidgetExpertise',
    'technicalConsultancy',
    'deposit',
    'dealer',
    'testimonyAmount',
    'complaintRecovery',
    'registeredEmail',
    'kmAmount',
    'amountPerKm',
    'amountKmForfait',
    'photoNumber',
    'photoCollection',
    'photoCollectionForfait',
    'PlTransfer',
    'PlCheck',
    'PlCheckLawyer',
    'detailAnalysis',
    'telematicDataMgmt',
    'varie',
    'endOfJobExpertise',
    'recommended',
    'simpleMail'
];

export const PARCELLA_FIELDS = [
    'parcella_collaborator_expertise_basicPayment',
    'parcella_collaborator_totalParcella',
    'parcella_expert_expertise_basicPayment',
    'parcella_expert_totalParcella'
];

export const PARCELLA_VAT_CLAIM = 1.22; //for parcella_totalVatClaim need to multiple total_taxableClaim with this number
