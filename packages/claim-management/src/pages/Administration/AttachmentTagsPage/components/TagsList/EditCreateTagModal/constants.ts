import * as Yup from 'yup';

export const TAG_INITIAL = {
    data: {
        networkType: 'ANIA' as DAT2.NetworkType,
        description: '',
        identification: ''
    },
    commit: ''
};

export const TAG_INITIAL_VALIDATION = Yup.object({
    data: Yup.object({
        networkType: Yup.string().required(),
        description: Yup.string().required(),
        identification: Yup.string().required()
    }).required(),
    commit: Yup.string().required()
});
