import { FC, SVGProps } from 'react';

export interface RightMenuItems {
    icons: Array<{
        tooltipText: string;
        Icon: FC<SVGProps<SVGSVGElement>>;
        onClick: (id: string | number) => void;
        isVisible?: boolean;
        onDoubleClick?: (id: string) => void;
        metaInfo?: string;
    }>;
    dropdown?: Array<{
        text: string;
        Icon: FC<SVGProps<SVGSVGElement>>;
        isVisible?: boolean;
        onClick: (id: string | number) => void;
        onDoubleClick?: (id: string) => void;
    }>;
}
