import { useUnit } from 'effector-react';

import { <PERSON>ton, Modal } from '@wedat/kit';
import { valuationFormbuilderStores, valuationFormbuilderEvents } from '../../../../stores/valuationFormbuilder';
import { ValuationQuestionType } from '../../../../stores/valuationFormbuilder/types';

import { Container, Content, Footer, Tab } from './styles';
import { useTranslation } from 'react-i18next';
import { useMedia } from '@dat/core/hooks/useMedia';
import { sizes } from '@wedat/ui-kit/mediaQueries';

export const QuestionTypeModal = () => {
    const { t } = useTranslation();
    const isPhone = useMedia(sizes.phone);
    const isOpen = useUnit(valuationFormbuilderStores.isValuationQuestionTypeOpen);
    const valuationQuestionType = useUnit(valuationFormbuilderStores.valuationQuestionType);

    const handleClose = () => {
        valuationFormbuilderEvents.toggleIsValuationQuestionTypeOpen(false);
    };

    const handleTabClick = (type: ValuationQuestionType) => {
        valuationFormbuilderEvents.setValuationQuestionType(type);
    };

    const handleChoose = () => {
        valuationFormbuilderEvents.toggleIsValuationQuestionTypeOpen(false);
        valuationFormbuilderEvents.toggleIsValuationQuestionFormOpen(true);
    };

    return (
        <Modal
            visible={isOpen}
            onHide={handleClose}
            header="Choose question type"
            aria-label="Choose question type"
            bodyNoPadding
            fullScreen={isPhone}
        >
            <Container>
                <Content>
                    <Tab onClick={() => handleTabClick('text')} isActive={valuationQuestionType === 'text'}>
                        {t('administration.predefinedValuationQuestions.question')}
                    </Tab>
                    <Tab onClick={() => handleTabClick('checkbox')} isActive={valuationQuestionType === 'checkbox'}>
                        {t('administration.predefinedValuationQuestions.checkbox')}
                    </Tab>
                    <Tab onClick={() => handleTabClick('list')} isActive={valuationQuestionType === 'list'}>
                        {t('administration.predefinedValuationQuestions.list')}
                    </Tab>
                </Content>

                <Footer>
                    <Button
                        label={t('administration.predefinedValuationQuestions.choose')}
                        aria-label={t('administration.predefinedValuationQuestions.choose')}
                        onClick={handleChoose}
                    />
                </Footer>
            </Container>
        </Modal>
    );
};
