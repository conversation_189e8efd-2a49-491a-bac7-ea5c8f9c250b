import { FC, useCallback, useMemo } from 'react';
import { useUnit } from 'effector-react';
import { generatePath } from 'react-router';

import { contractEvents } from '@dat/shared-models/contract';
import { sharedClaimManagementStores } from '@dat/shared-models/claim-management';
import { useMedia } from '@dat/core/hooks/useMedia';
import { sizes } from '@wedat/ui-kit/mediaQueries';
import { Inbox, InboxPluginOptions } from '@dat/inbox';

import { vehicleImagesEvents } from '../../stores/images';
import { routerEffects } from '../../stores/router';

import { ROUTES, ROUTES_PARAMS_VALUES } from '../../constants/router';

import { containedPluginsEvents } from '../../stores/containedPlugins';
import { fleetViewEvents } from '../../stores/fleetView';

const InboxPage: FC = () => {
    const isMobile = useMedia(sizes.phoneBig);
    const isFleetView = useUnit(sharedClaimManagementStores.configuration);
    const handleContractClick = useCallback<Required<InboxPluginOptions>['onContractClick']>(
        ({ contractId }) => {
            fleetViewEvents.setIsCheckInClaim(false);
            isFleetView && contractEvents.resetContract();
            contractId && routerEffects.pushClaimRouteByIdFx(contractId);
        },
        [isFleetView]
    );

    const handleContractClickWithPressedCtrl = useCallback<
        Required<InboxPluginOptions>['onContractClickWithPressedCtrl']
    >(({ contractId }) => {
        contractId && window.open(generatePath(ROUTES.claim.opening, { contractId }), '_blank');
    }, []);

    const handleCreateContractClick = useCallback<Required<InboxPluginOptions>['onCreateContractClick']>(() => {
        // We reset these stores coz of inbox status change modal, that sets contract id for status update.
        // If we don't clear these stores, then we'd have a claim with that id opened or pictures pulled
        contractEvents.resetContract();
        vehicleImagesEvents.resetVehicleImages();
        routerEffects.pushClaimRouteByIdFx(ROUTES_PARAMS_VALUES.claim.contractId.new);
    }, []);

    const handleRedirectClick = useCallback<Required<InboxPluginOptions>['onRedirectClick']>(redirectPath => {
        contractEvents.resetContract();
        vehicleImagesEvents.resetVehicleImages();
        void routerEffects.pushRoutePathFx(redirectPath);
    }, []);

    const options: InboxPluginOptions = useMemo(
        () => ({
            isComponent: true,
            onRedirectClick: handleRedirectClick,
            onContractClick: handleContractClick,
            onCreateContractClick: handleCreateContractClick,
            onContractClickWithPressedCtrl: handleContractClickWithPressedCtrl,
            newOrderLink: isMobile ? ROUTES.inbox.new : undefined,
            getGalleryLink: (contractId: number) => generatePath(ROUTES.claim.gallery, { contractId }),
            resetVSMStores: containedPluginsEvents.vehicleSelectionReset
        }),
        [
            isMobile,
            handleContractClick,
            handleCreateContractClick,
            handleRedirectClick,
            handleContractClickWithPressedCtrl
        ]
    );

    return <Inbox options={options} />;
};

export default InboxPage;
