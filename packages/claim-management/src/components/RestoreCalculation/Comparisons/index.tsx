import { useEffect, useRef } from 'react';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';
import { disableBodyScroll, clearAllBodyScrollLocks } from 'body-scroll-lock-upgrade';

import { calculationStores } from '../../../stores/calculation';
import { getComparisons } from './utils/getComparisons';

import { Portal } from '@wedat/ui-kit';
import { Accordion, AccordionTab } from '@wedat/kit';
import { Comparison } from './Comparison';
import { ComparisonHeader } from './ComparisonHeader';
import { Container, HeadingText } from './styles';

export const Comparisons = () => {
    const containerRef = useRef(null);
    const { t } = useTranslation();
    const calculationComparisons = useUnit(calculationStores.calculationComparisons);
    const showNonModifiedCategories = useUnit(calculationStores.showNonModifiedCategories);
    const comparisons = getComparisons(t, { calculationComparisons, showNonModifiedCategories });

    useEffect(() => {
        if (containerRef.current) {
            disableBodyScroll(containerRef.current);
        }

        return () => {
            clearAllBodyScrollLocks();
        };
    }, [comparisons]);

    return (
        <Portal>
            <Container ref={containerRef}>
                <ComparisonHeader />

                <Accordion multiple tabClassName="accordion-tab-styles">
                    {comparisons
                        .filter(({ isVisible }) => !!isVisible)
                        .map((item, index) => (
                            <AccordionTab
                                key={item.name || index}
                                header={<HeadingText font="font-default-bold">{item.name}</HeadingText>}
                            >
                                <Comparison {...item} />
                            </AccordionTab>
                        ))}
                </Accordion>
            </Container>
        </Portal>
    );
};
