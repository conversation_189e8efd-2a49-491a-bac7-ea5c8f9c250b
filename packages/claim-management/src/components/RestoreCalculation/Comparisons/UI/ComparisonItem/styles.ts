import styled, { css } from 'styled-components/macro';
import { Text } from '@wedat/kit';
import { ComparisonItemCellProps } from '../../types';

export const ComparisonItemContent = styled.div`
    width: 100%;
`;

export const ContentHeader = styled(Text)`
    display: flex;
    align-items: center;
    border-top: 1px solid ${({ theme }) => theme.colors.dustBlue['100']};
    background: ${({ theme }) => theme.colors.gray['100']};
    height: 40px;
    padding-left: 24px;
`;

export const ContentWrapper = styled.div`
    border-top: 1px solid ${({ theme }) => theme.colors.dustBlue['100']};
`;

export const ContentCell = styled.div<ComparisonItemCellProps>`
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: end;
    border-left: 1px solid ${({ theme }) => theme.colors.dustBlue['100']};
    padding: 4px 12px;
    background: ${({ isAdded, isRemoved, theme }) => {
        if (isAdded) return theme.colors.green['50'];
        if (isRemoved) return theme.colors.red['50'];
        return 'transparent';
    }};

    ${({ isGreater, theme }) =>
        isGreater &&
        css`
            color: ${theme.colors.green['600']};
        `};

    ${({ isLesser, theme }) =>
        isLesser &&
        css`
            color: ${theme.colors.red['600']};
        `};

    ${({ isGreater, isLesser, theme }) => css`
        svg {
            margin-left: 8px;
            width: 20px;
            transform: ${isLesser ? 'rotate(180deg)' : 'none'};
            ${isGreater && `color: ${theme.colors.green['600']}`};
            ${isLesser && `color: ${theme.colors.red['600']}`};
        }
    `};

    svg {
        margin-left: 8px;
        width: 20px;
    }

    &:first-child {
        justify-content: start;
        padding-left: 24px;
        border: none;
    }
`;

export const ContentRow = styled.div<{ length: number }>`
    position: relative;
    width: 100%;
    display: flex;

    & > * {
        max-width: ${({ length }) => 100 / length}%;
        min-width: ${({ length }) => 100 / length}%;
    }

    &:not(:last-of-type) {
        &:before {
            content: '';
            position: absolute;
            left: 12px;
            right: 12px;
            bottom: 0;
            height: 1px;
            background-color: ${({ theme }) => theme.colors.dustBlue['100']};
        }
    }
`;
