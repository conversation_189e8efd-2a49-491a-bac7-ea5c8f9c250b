import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';

import { calculationStores } from '../../../../../stores/calculation';

import {
    getVehicleHeadings,
    getVehicleMileageHeadings,
    getVehicleRegistrationHeadings
} from '../utils/getFieldHeadings';
import { ComparisonItemType } from '../../types';

import { ComparisonItem } from '../../UI';
import { checkVisibilityValues } from '../../utils/checkVisibilityValues';

export const Vehicle = () => {
    const { t } = useTranslation();
    const calculationComparisons = useUnit(calculationStores.calculationComparisons);
    const showNonModifiedValues = useUnit(calculationStores.showNonModifiedValues);

    const formatter = (input: number | undefined) => (typeof input === 'number' ? format(input, 'dd/MM/yyyy') : input);

    const vehicleHeadings = getVehicleHeadings(t);
    const vehicleMileageHeadings = getVehicleMileageHeadings(t);
    const vehicleRegistrationHeadings = getVehicleRegistrationHeadings(t);

    const items: ComparisonItemType[] = [
        {
            headings: vehicleHeadings,
            differenceField: 'vehicle',
            differenceItems: [
                {
                    field: 'brandText',
                    type: 'text',
                    isVisible: checkVisibilityValues(
                        'vehicle',
                        'brandText',
                        showNonModifiedValues,
                        calculationComparisons
                    )
                },
                {
                    field: 'typeText',
                    type: 'text',
                    isVisible: checkVisibilityValues(
                        'vehicle',
                        'typeText',
                        showNonModifiedValues,
                        calculationComparisons
                    )
                },
                {
                    field: 'modelText',
                    type: 'text',
                    isVisible: checkVisibilityValues(
                        'vehicle',
                        'modelText',
                        showNonModifiedValues,
                        calculationComparisons
                    )
                },
                {
                    field: 'vin',
                    type: 'text',
                    isVisible: checkVisibilityValues('vehicle', 'vin', showNonModifiedValues, calculationComparisons)
                },
                {
                    field: 'eCode',
                    type: 'text',
                    isVisible: checkVisibilityValues('vehicle', 'eCode', showNonModifiedValues, calculationComparisons)
                },
                {
                    field: 'productionDate',
                    type: 'text',
                    formatter,
                    isVisible: checkVisibilityValues(
                        'vehicle',
                        'productionDate',
                        showNonModifiedValues,
                        calculationComparisons
                    )
                },
                {
                    field: 'engine',
                    type: 'text',
                    isVisible: checkVisibilityValues('vehicle', 'engine', showNonModifiedValues, calculationComparisons)
                },
                {
                    field: 'power',
                    type: 'text',
                    isVisible: checkVisibilityValues('vehicle', 'power', showNonModifiedValues, calculationComparisons)
                }
            ]
        },
        {
            name: t('compareCalculations.vehicleMileage'),
            headings: vehicleMileageHeadings,
            differenceField: 'vehicle',
            differenceItems: [
                {
                    field: 'mileageEstimated',
                    type: 'text',
                    isVisible: checkVisibilityValues(
                        'vehicle',
                        'mileageEstimated',
                        showNonModifiedValues,
                        calculationComparisons
                    )
                },
                {
                    field: 'mileageNote',
                    type: 'text',
                    isVisible: checkVisibilityValues(
                        'vehicle',
                        'mileageNote',
                        showNonModifiedValues,
                        calculationComparisons
                    )
                },
                {
                    field: 'mileageRead',
                    type: 'text',
                    isVisible: checkVisibilityValues(
                        'vehicle',
                        'mileageRead',
                        showNonModifiedValues,
                        calculationComparisons
                    )
                }
            ]
        },
        {
            name: t('compareCalculations.vehicleRegistration'),
            headings: vehicleRegistrationHeadings,
            differenceField: 'vehicle',
            differenceItems: [
                {
                    field: 'registrationNumber',
                    type: 'price',
                    isVisible: checkVisibilityValues(
                        'vehicle',
                        'registrationNumber',
                        showNonModifiedValues,
                        calculationComparisons
                    )
                },
                {
                    field: 'registrationFirst',
                    type: 'price',
                    formatter,
                    isVisible: checkVisibilityValues(
                        'vehicle',
                        'registrationFirst',
                        showNonModifiedValues,
                        calculationComparisons
                    )
                },
                {
                    field: 'registrationCurrent',
                    type: 'price',
                    formatter,
                    isVisible: checkVisibilityValues(
                        'vehicle',
                        'registrationCurrent',
                        showNonModifiedValues,
                        calculationComparisons
                    )
                },
                {
                    field: 'nextInspection',
                    type: 'price',
                    formatter,
                    isVisible: checkVisibilityValues(
                        'vehicle',
                        'nextInspection',
                        showNonModifiedValues,
                        calculationComparisons
                    )
                },
                {
                    field: 'age',
                    type: 'price',
                    formatter,
                    isVisible: checkVisibilityValues('vehicle', 'age', showNonModifiedValues, calculationComparisons)
                }
            ]
        }
    ];

    return <ComparisonItem translationKey="vehicle" comparisonItems={items} comparisons={calculationComparisons} />;
};
