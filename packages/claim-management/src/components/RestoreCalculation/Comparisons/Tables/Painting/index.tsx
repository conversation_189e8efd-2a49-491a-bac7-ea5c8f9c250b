import { Column } from 'react-table';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';

import { useFormattedPrice } from '@dat/shared-models/hooks/useFormattedPrice';
import { calculationStores } from '../../../../../stores/calculation';
import { ComparisonItemTable } from '../../UI';

import { getPrice } from '../../../utils/getPrice';
import { getFieldName } from '../../utils/getFieldName';
import { getCellProps } from '../../utils/getCellProps';
import { getFormattedCell } from '../../utils/getFormattedCell';
import { ComparisonItemTableType, ComparisonItemTableDataType } from '../../types';

export const Painting = () => {
    const { t } = useTranslation();
    const calculationComparisons = useUnit(calculationStores.calculationComparisons);
    const showNonModifiedValues = useUnit(calculationStores.showNonModifiedValues);

    const formatPrice = useFormattedPrice();
    const formatter = (price: number | undefined) => getPrice(price, formatPrice);

    const items: ComparisonItemTableType[] = calculationComparisons.map((comparison, idx) => {
        const fieldName = getFieldName(idx);
        const columns: Column<ComparisonItemTableDataType>[] = [
            {
                id: 'dvn' + idx,
                Header: t('compareCalculations.dvn'),
                accessor: 'difference',
                Cell: ({ row: { original } }) => original.difference?.dvn?.[fieldName]
            },
            {
                id: 'position' + idx,
                Header: t('compareCalculations.item'),
                accessor: 'difference',
                Cell: ({ row: { original } }) => getFormattedCell(original?.difference?.position?.[fieldName])
            },
            {
                id: 'description' + idx,
                Header: t('compareCalculations.description'),
                accessor: 'difference',
                Cell: ({ row: { original } }) => getFormattedCell(original.difference?.description?.[fieldName])
            },
            {
                id: 'time' + idx,
                Header: t('compareCalculations.time'),
                accessor: 'difference',
                Cell: ({ row: { original } }) => original.difference?.material?.[fieldName]
            },
            {
                id: 'price' + idx,
                Header: t('compareCalculations.price'),
                accessor: 'difference',
                Cell: ({ row: { original } }) =>
                    typeof original.difference?.price?.[fieldName] === 'number'
                        ? formatter(original.difference.price[fieldName])
                        : (original.difference?.price?.[fieldName] ?? '-')
            },
            {
                id: 'finishType' + idx,
                Header: t('compareCalculations.finish'),
                accessor: 'difference',
                Cell: ({ row: { original } }) => original?.difference?.finishType?.[fieldName]
            },
            {
                id: 'scratches' + idx,
                Header: t('compareCalculations.scratches'),
                accessor: 'difference',
                Cell: ({ row: { original } }) => original?.difference?.scratches?.[fieldName]
            },
            {
                id: 'surface' + idx,
                Header: t('compareCalculations.level'),
                accessor: 'difference',
                Cell: ({ row: { original } }) => original?.difference?.surface?.[fieldName]
            },
            {
                id: 'manual' + idx + 'last',
                Header: t('compareCalculations.manual'),
                accessor: 'difference',
                Cell: ({ row: { original } }) => original.difference?.userDefined?.[fieldName]
            }
        ];

        const data: ComparisonItemTableDataType[] = (comparison.difference?.workPositions?.difference || []).map(
            (item, i) => ({
                ...item,
                ...getCellProps(item.result, i)
            })
        );

        return {
            name: calculationComparisons[idx].difference.calculation.difference.referenceNumber[fieldName],
            data,
            field: 'paintingPositions',
            columns
        };
    });

    const columns = items.reduce((acc, { columns }) => {
        acc.push(...columns);
        return acc;
    }, [] as Column<ComparisonItemTableDataType>[]);

    return <ComparisonItemTable columns={columns} data={items[0].data} showNonModifiedValues={showNonModifiedValues} />;
};
