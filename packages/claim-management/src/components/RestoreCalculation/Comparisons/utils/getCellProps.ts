import { ComparisonItemCellProps } from '../types';

export const getCellProps = (
    result: DAT2.Internal.CalculationComparisonResult,
    index: number,
    includeFirst?: boolean
): ComparisonItemCellProps => {
    if (index === 0 && !includeFirst) return {};

    return {
        isAdded: result === 'added',
        isRemoved: result === 'removed',
        isGreater: result === 'greater',
        isLesser: result === 'lesser'
    };
};
