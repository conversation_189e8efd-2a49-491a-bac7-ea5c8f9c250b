import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { Text, Button } from '@wedat/kit';

import styles from './DeleteEquipmentPopup.module.css';

interface Props {
    onClose: () => void;
    onConfirm: () => void;
}
export const DeleteEquipmentPopup: FC<Props> = ({ onClose, onConfirm }) => {
    const { t } = useTranslation();

    return (
        <div className={styles.popup}>
            <Text>{t('administration.predefinedValuationEquipment.deleteEquipmentPopup.title')}</Text>
            <div className={styles.buttonsWrapper}>
                <Button
                    aria-label={t('administration.predefinedValuationEquipment.deleteEquipmentPopup.no')}
                    className={styles.buttonStyled}
                    variant="outline"
                    onClick={onClose}
                >
                    <Text textOverflow="ellipsis" color="inherit">
                        {t('administration.predefinedValuationEquipment.deleteEquipmentPopup.no')}
                    </Text>
                </Button>
                <Button
                    aria-label={t('administration.predefinedValuationEquipment.deleteEquipmentPopup.yes')}
                    className={styles.buttonStyled}
                    onClick={onConfirm}
                >
                    <Text textOverflow="ellipsis" color="inherit">
                        {t('administration.predefinedValuationEquipment.deleteEquipmentPopup.yes')}
                    </Text>
                </Button>
            </div>
        </div>
    );
};
