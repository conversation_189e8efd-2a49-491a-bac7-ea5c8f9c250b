import { TabButton } from '@wedat/ui-kit';
import { HISTORY_TABS } from '../constants';
import { TabHeader } from '../../../types/fleetView';

export const generateTabValues = (
    folderFilters: DAT2.Internal.FolderFilter[] | undefined,
    foldersCounts: {
        [folderId: number]: number;
    } | null,
    historyTabsHeaders?: TabHeader[],
    isDisabled?: boolean
): TabButton[] => {
    if (!folderFilters) {
        return [];
    }

    const mainTabs = folderFilters.reduce((array: TabButton[], item) => {
        if (item.name === 'Vehicles') {
            return array;
        } else if (item.id !== 0) {
            array = [
                ...array,
                {
                    id: String(item.id),
                    label: `${item.name}${foldersCounts?.[item.id] !== undefined ? ` (${foldersCounts?.[item.id]})` : ''}`,
                    disabled: isDisabled
                }
            ];
        }
        return array;
    }, []);

    const updatedHistoryTabs = HISTORY_TABS.map(defaultTab => {
        const headerTab = historyTabsHeaders?.find((header: TabHeader) => header.id === defaultTab.id);
        return {
            ...defaultTab,
            label: headerTab?.label || defaultTab.label
        };
    });

    const historyTabs = updatedHistoryTabs.filter(tab => !mainTabs.some(existingTab => existingTab.id === tab.id));

    return [...mainTabs, ...historyTabs];
};
