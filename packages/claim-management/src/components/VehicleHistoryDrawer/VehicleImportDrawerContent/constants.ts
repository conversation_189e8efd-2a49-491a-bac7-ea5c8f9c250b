import * as Yup from 'yup';
export const VALIDATION_SCHEMA = Yup.object()
    .shape({
        checkboxImportAllData: Yup.boolean(),
        ADDRESS_DATA: Yup.boolean(),
        ATTACHMENTS: Yup.boolean(),
        CALCULATION_DATA: Yup.boolean(),
        CLAIM_DATA: Yup.boolean(),
        INSURANCE_DATA: Yup.boolean(),
        PARTNERS: Yup.boolean(),
        PARTS_SELECTION: Yup.boolean(),
        PROCESS_RELATED_DATA: Yup.boolean(),
        VEHICLE_DATA: Yup.boolean()
    })
    .test('at-least-one-checked', 'Select at least one option', values => {
        const formValues = values || {};
        return Object.values(formValues).some(v => v === true);
    });
export const COPY_PARTS_CHECKBOX = [
    {
        name: 'ADDRESS_DATA',
        label: 'copyClaim.AddressData'
    },
    {
        name: 'ATTACHMENTS',
        label: 'copyClaim.Attachments'
    },
    {
        name: 'CALCULATION_DATA',
        label: 'copyClaim.CalculationData'
    },
    {
        name: 'CLAIM_DATA',
        label: 'copyClaim.GeneralOrderData'
    },
    {
        name: 'INSURANCE_DATA',
        label: 'copyClaim.InsuranceData'
    },
    {
        name: 'PARTNERS',
        label: 'copyClaim.AllocatedPartner'
    },
    {
        name: 'PARTS_SELECTION',
        label: 'copyClaim.DataOfPartsSelection'
    },
    {
        name: 'PROCESS_RELATED_DATA',
        label: 'copyClaim.ProcedureRelatedData'
    },
    {
        name: 'VEHICLE_DATA',
        label: 'copyClaim.VehicleData'
    }
];
