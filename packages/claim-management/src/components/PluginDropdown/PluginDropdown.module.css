.buttonStyled {
    background-color: transparent;
    padding: 14px;
    border: none;
    color: var(--color-white);
}

.buttonStyled:hover,
.buttonStyled:focus,
.buttonStyled:active {
    background-color: var(--color-blue-50);
    color: var(--color-blue-700);
}

.item {
    min-height: 44px;
    height: 48px;
    display: flex;
    align-items: center;
    padding-top: 8px;
    padding-bottom: 8px;
    cursor: pointer;
    padding-left: 20px;
}

.item.text {
    cursor: pointer;
}

.item:hover {
    background-color: var(--color-blue-50);
}

.item:first-child {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.item:last-child {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.dropdownContainer {
    position: absolute;
    top: 20px;
    bottom: 0px;
    left: 140px;
}
