import { createRoot } from 'react-dom/client';

import { ErrorBoundary } from '@wedat/ui-kit/components/ErrorBoundary';
import App from './App';

// import './registerServiceWorker'; // logic changed with virtual:pwa-register. Pls check <PwaModal />
import PwaModal from './components/PwaModal';

import { PluginOptions } from './types/plugin';

if (import.meta.env.MODE === 'development') {
    createRoot(document.getElementById('root') as HTMLElement).render(<App options={{}} />);
} else {
    window.CLAIM_MANAGEMENT_API = {
        init: (options: PluginOptions) => {
            const renderElement = document.querySelector(String(options.selector));

            if (renderElement) {
                createRoot(renderElement).render(
                    <ErrorBoundary>
                        <App options={options} PwaModal={<PwaModal />} />
                    </ErrorBoundary>
                );
            }
        }
    };
}
