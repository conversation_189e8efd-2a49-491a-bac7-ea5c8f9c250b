import { sample } from 'effector';
import { apiStores } from '@dat/api2/stores';
import { sharedDamageSelectorEvents } from '@dat/shared-models/damage-selector';
import { sharedClaimManagementEvents } from '@dat/shared-models/claim-management';
import { pluginEvents, pluginStores } from './index';

const { pluginOptions, isStandAloneClaim } = pluginStores;
const { locale } = apiStores;
const { initPlugin } = pluginEvents;

sample({
    source: pluginOptions,
    filter: options => !!options?.settings?.language,
    fn: options => (options?.settings?.language as DAT2.Locale) || null,
    target: locale
});

sample({
    clock: initPlugin,
    fn: () => false,
    target: sharedDamageSelectorEvents.setIsInAIClaim
});

sample({
    clock: isStandAloneClaim.updates,
    target: sharedClaimManagementEvents.setIsClaimManagementStandalone
});
