import { createEvent, restore } from 'effector';
import { PluginOptions } from '../../types/plugin';

const initPlugin = createEvent<PluginOptions>();
const pluginOptions = restore(initPlugin, null);

const urlPath = pluginOptions.map(pluginOptions => pluginOptions?.urlPath || '');
const isStandAloneClaim = pluginOptions.map(
    pluginOptions => Boolean(pluginOptions?.claimId) || pluginOptions?.claimId === ''
);

const setIsOpenPWAUpdatesModal = createEvent<boolean>();
const isOpenPWAUpdatesModal = restore(setIsOpenPWAUpdatesModal, false);

export const pluginEvents = {
    initPlugin,
    setIsOpenPWAUpdatesModal
};

export const pluginStores = {
    pluginOptions,
    urlPath,
    isStandAloneClaim,
    isOpenPWAUpdatesModal
};
