import { createEvent, restore } from 'effector';
import { createToggle } from '@dat/core/utils/effector/createToggle';
import { CUSTOMER_RIGHTS } from '../../pages/Administration/AttachmentTagsPage/constants';

const [isOpenedSettingsAttributeModal, setIsOpenedSettingsAttributeModal] = createToggle(false);
const [isOpenedEditDocumentDrawer, setIsOpenedEditDocumentDrawer] = createToggle(false);

const setActiveTab = createEvent<{ index: number; value: string }>();

const activeTab = restore(setActiveTab, { index: 0, value: CUSTOMER_RIGHTS.ADMIN_DOCUMENTS });

export const attachmentTagsCommonEvents = {
    setActiveTab,
    setIsOpenedSettingsAttributeModal,
    setIsOpenedEditDocumentDrawer
};
export const attachmentTagsCommonStores = {
    activeTab,
    isOpenedSettingsAttributeModal,
    isOpenedEditDocumentDrawer
};
