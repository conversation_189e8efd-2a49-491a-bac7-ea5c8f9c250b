import { dynamicComparisonForFields } from '@dat/form-builder/src/utils/dynamicComparison';
import { updatedFieldsValueBasedCondition } from './updatedFieldsValueBasedCondition';

export const fieldsDynamicUpdate = (
    currentChange: any,
    customisedFieldsValue: DAT2.Plugins.FormBuilder.CustomisedFieldsValue
): DAT2.Field.Entry<string, DAT2.Field.Primitive>[] => {
    const { condition, then: efficientConditions, else: failedConditions } = customisedFieldsValue;

    if (dynamicComparisonForFields(condition.operator)(condition.value, currentChange || '')) {
        return !!efficientConditions && updatedFieldsValueBasedCondition(efficientConditions);
    } else {
        return !!failedConditions && updatedFieldsValueBasedCondition(failedConditions);
    }
};
