import { restore } from 'effector';
import { createToggle } from '@dat/core/utils/effector/createToggle';
import { VehicleSelectionModalPluginResult } from '@dat/vehicle-selection-modal';
import { domain } from '../plugin';

const { createEvent, createStore } = domain;

const [isOpenVSM, setIsOpenVSM] = createToggle(false, { domain });
// Vehicle should be fully selected, otherwise "createOrUpdateContract" will work incorrect.
// This comment is just a tip, because VSM has own validation which checks the selection of vehicle.
const vehicleSelectionCompleted = domain.createEvent<VehicleSelectionModalPluginResult>();
const vehicleSelectionPluginResult = restore(vehicleSelectionCompleted, null);

const isOpenModalStore = createStore(false); // for ProcessedModal
const poiSelected = createStore<string[]>([]);

const setPoiSelected = createEvent<string[]>();
const toggleModalEvent = createEvent<boolean>();
const closeModalEvent = createEvent<void>();

isOpenModalStore.on(toggleModalEvent, (_, payload) => payload).on(closeModalEvent, () => false);
poiSelected.on(setPoiSelected, (_, payload) => payload);

const setLabourRatesValues = createEvent<DAT2.Internal.FactorsParametersObject>();
const labourRatesValues = createStore<DAT2.Internal.FactorsParametersObject>({}).on(
    setLabourRatesValues,
    (_, payload) => payload
);

const toggleLabourRatesModal = createEvent<boolean>();
const isLabourRatesModalOpen = createStore(false).on(toggleLabourRatesModal, (_, payload) => payload);

const delayedCloseModalEvent = createEvent();

export const containedPluginsEvents = {
    vehicleSelectionCompleted,
    setPoiSelected,
    setIsOpenVSM,
    toggleModalEvent,
    closeModalEvent,
    toggleLabourRatesModal,
    setLabourRatesValues,
    delayedCloseModalEvent
};
export const containedPluginsStores = {
    vehicleSelectionPluginResult,
    poiSelected,
    isOpenVSM,
    isOpenModalStore,
    isLabourRatesModalOpen,
    labourRatesValues
};
