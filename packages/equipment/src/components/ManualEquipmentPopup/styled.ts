import { media } from '@wedat/ui-kit/mediaQueries';
import styled from 'styled-components/macro';

export const ManualPopup = styled.div`
    position: absolute;
    top: 100px;
    left: 50%;
    width: 90%;
    z-index: 11;
    transform: translateX(-50%);

    box-shadow: 0px 8px 56px 0px #aeaec0;

    background: ${({ theme }) => theme.colors.white};
    padding: 16px;
    border-radius: 16px;
    text-align: start;

    ${media.phone`
      position: fixed;
      bottom: 0;
      top: unset;
      left: 0;
      width: 100%;
      transform: none;
    `}
`;

export const ManualPopupButtons = styled.div`
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 10px;

    button {
        width: unset;
        height: 40px;
        padding: 0 30px;
    }
`;

export const FieldContainer = styled.div`
    margin: 16px 0;
`;
