import { EquipmentItemData, TransferEquipmentItem } from '../../types/equipment';
import { createRowsWithConflicted, createRowsWithDisabled, findExpandingRows } from './utils';

describe('findExpandingRows', () => {
    it('should return true if row has children', () => {
        const row = {
            key: '1',
            description: 'item 1',
            children: [{ key: '2', description: 'item2 ' }]
        } as TransferEquipmentItem;
        const result = findExpandingRows(row);
        expect(result).toBe(true);
    });

    it('should return false if row does not have children', () => {
        const row = {
            key: '1',
            description: 'item 1'
        } as TransferEquipmentItem;
        const result = findExpandingRows(row);
        expect(result).toBe(false);
    });
});

describe('createRowsWithDisabled', () => {
    it('should create rows with disabled state', () => {
        const rows: TransferEquipmentItem[] = [
            {
                key: 'row1',
                DatEquipmentId: 1,
                description: 'row 1',
                EquipmentClass: 1,
                EquipmentGroup: 'B',
                isSpecial: true,
                vin: true
            },
            {
                key: 'row2',
                DatEquipmentId: 2,
                description: 'row 2',
                EquipmentClass: 2,
                EquipmentGroup: 'D',
                children: [
                    {
                        key: 'child1',
                        DatEquipmentId: 22,
                        description: 'child 1'
                    },
                    {
                        key: 'child2',
                        DatEquipmentId: 23,
                        description: 'child 2'
                    }
                ]
            }
        ];
        const firstTableSelectedRowsIds = [
            {
                key: 'row1',
                class: 'A',
                group: 'B'
            },
            {
                key: 'row2',
                class: 'C',
                group: 'D'
            }
        ];
        const result = createRowsWithDisabled(rows, firstTableSelectedRowsIds);
        expect(result).toEqual([
            {
                col0: '',
                col1: 'row1',
                col2: true,
                col3: true,
                col4: 'row 1',
                children: undefined,
                item: {
                    key: 'row1',
                    DatEquipmentId: 1,
                    description: 'row 1',
                    EquipmentClass: 1,
                    EquipmentGroup: 'B',
                    isSpecial: true,
                    vin: true
                },
                isDisabled: false
            },
            {
                col0: '',
                col1: 'row2',
                col2: undefined,
                col3: undefined,
                col4: 'row 2',
                item: {
                    key: 'row2',
                    DatEquipmentId: 2,
                    description: 'row 2',
                    EquipmentClass: 2,
                    EquipmentGroup: 'D',
                    children: [
                        {
                            key: 'child1',
                            DatEquipmentId: 22,
                            description: 'child 1'
                        },
                        {
                            key: 'child2',
                            DatEquipmentId: 23,
                            description: 'child 2'
                        }
                    ]
                },
                children: [
                    {
                        col0: '',
                        col1: '',
                        col2: '',
                        col3: '',
                        col4: 'child 1',
                        col5: ''
                    },
                    {
                        col0: '',
                        col1: '',
                        col2: '',
                        col3: '',
                        col4: 'child 2',
                        col5: ''
                    }
                ]
            }
        ]);
    });
});

describe('createRowsWithConflicted', () => {
    const transferEquipment = [
        {
            key: '1',
            description: 'Equipment 1',
            isSpecial: true,
            vin: '123456789'
        },
        {
            key: '2',
            description: 'Equipment 2',
            isSpecial: false,
            vin: '987654321',
            children: [
                {
                    key: '2.1',
                    description: 'Equipment 2.1'
                },
                {
                    key: '2.2',
                    description: 'Equipment 2.2'
                }
            ]
        },
        {
            key: '3',
            description: 'Equipment 3',
            isSpecial: true,
            vin: '111123456'
        }
    ] as unknown as TransferEquipmentItem[];

    const conflictedEquipment = [
        {
            key: '1',
            description: 'Conflicted Equipment 1'
        },
        {
            key: '2.1',
            description: 'Conflicted Equipment 2.1'
        }
    ] as EquipmentItemData[];

    it('should return all the rows when onlyConflicted is set to false', () => {
        const result = createRowsWithConflicted(transferEquipment, conflictedEquipment, false);
        expect(result).toHaveLength(3);
    });

    it('should return only the conflicted rows when onlyConflicted is set to true', () => {
        const result = createRowsWithConflicted(transferEquipment, conflictedEquipment, true);
        expect(result).toHaveLength(1);
    });

    it('should return correct conflict information for the rows', () => {
        const result = createRowsWithConflicted(transferEquipment, conflictedEquipment, false);
        expect(result[0].conflictedItem).toEqual({
            key: '1',
            description: 'Conflicted Equipment 1'
        });
        expect(result[1].conflictedItem).toBeUndefined();
        expect(result[2].conflictedItem).toBeUndefined();
    });
});
