import { FC, RefObject, useCallback, useContext, useState } from 'react';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';

// State and actions
import { sharedTemplateStores } from '@dat/shared-models/template';
import { FormContext, actions } from '../../reducers/form';
import { photoStepsTrackerStores } from '../../stores/photoWidgetLog';
import { sharedPluginCarsStores } from '@dat/shared-models/plugin-cars';

// Styles
import { useTheme } from 'styled-components';
import { BtnSection, ExitButton, PortraitWrapper, Wrapper } from './styles';
import { PhotoSteps } from '../PhotoStepsPage/styles';

// Icons
import { RightArrow } from '@wedat/ui-kit/assets/Icon';

// Components
import { Layout } from '../Layout';
import { AdditionalPhotoStepsList } from './AdditionalPhotoStepsList/AdditionalPhotoStepsList';
import { PhotoButton } from '../PhotoButton';
import { RetakePhotoButton } from '../RetakePhotoButton';
import { Video } from '../Video';
import { TakenPhotoPreview } from '../PhotoStepsPage/TakenPhotoPreview';
import { SettingsButton } from '../SettingsButton';

// Hooks
import { useOrientation } from '@dat/core/hooks/orientation';
import { useCameraControl } from '../../hooks/useCameraControl';
import { usePhotosUpload } from '../../hooks/usePhotosUpload';
import { useCompleteFormCallback } from '../../hooks/plugin';

// Types
import { PluginProps, Steps as MainSteps, AiPhotosJson } from '../../types';

// Utils
import { offlineDb } from '@dat/api2/utils/indexed-db';
import { captureVideo } from '../../utils/mediaDevices';
import { handleFormSubmission } from '../../utils/formSubmission';

enum AdditionalSteps {
    MakePhoto,
    RetakePhoto
}

interface Props {
    additionalPhotoOrientation?: PluginProps['additionalPhotoForceLandscape'];
    additionalPhotoLayout?: 'squared' | 'wide' | 'standard';
    embedGeo?: boolean;
    additionalPhotoFolderId?: number;
    videoRef: RefObject<HTMLVideoElement>;
    showSettingsButton?: boolean;
    showConfirmationStep?: boolean;
    withSummary?: boolean;
    completeFormCallback: PluginProps['completeFormCallback'];
    steps?: DAT2.Plugins.PluginCars.PluginStep[];
    searchAutomatic?: boolean;
    onCompleteAiStepsResult?: (result: AiPhotosJson) => void;
    isCompressImage?: boolean;
    compressToSize?: number;
}

const { changeStep } = actions;
const { initialDateTime } = photoStepsTrackerStores;
const { stepLogsStore } = sharedPluginCarsStores;

export const AdditionalFreePhotoStep: FC<Props> = ({
    additionalPhotoOrientation,
    additionalPhotoLayout,
    embedGeo,
    additionalPhotoFolderId,
    videoRef,
    showSettingsButton,
    showConfirmationStep,
    withSummary,
    completeFormCallback,
    steps,
    searchAutomatic,
    onCompleteAiStepsResult,
    isCompressImage,
    compressToSize
}) => {
    const [additionalPhotos, setAdditionalPhotos] = useState<string[]>([]);
    const [photoStep, setPhotoStep] = useState<AdditionalSteps>(AdditionalSteps.MakePhoto);
    const [step, setStep] = useState(0);
    const [isRetake, setIsRetake] = useState(false);
    const { dispatch } = useContext(FormContext);
    const dateTime = useUnit(initialDateTime);
    const stepLogs = useUnit(stepLogsStore);
    const country = useUnit(sharedTemplateStores.templateSettings.country);
    const completeCallback = useCompleteFormCallback(completeFormCallback);
    const { handleStorePhotos, updateResult, clearIndexedDBData, contractId } = usePhotosUpload();
    const { isLandscape } = useOrientation();
    const {
        colors: { black }
    } = useTheme();
    const { t } = useTranslation();

    const isSquared = additionalPhotoLayout === 'squared';
    const isStandard = additionalPhotoLayout === 'standard';

    const fetchPhotos = useCallback(async () => {
        const photos = await offlineDb.photoWidget.get(contractId);
        setAdditionalPhotos(photos?.additionalSteps || []);
    }, [contractId]);

    const handlePhoto = useCallback(async () => {
        const image = await captureVideo(videoRef.current, embedGeo, additionalPhotoOrientation);
        await handleStorePhotos(image, true);
        updateResult();
        setStep(additionalPhotos.length);

        if (isRetake) {
            const updatedPhotos = [...additionalPhotos];
            updatedPhotos[step] = image;
            await offlineDb.photoWidget.update(contractId, { additionalSteps: updatedPhotos });
        }

        setIsRetake(false);
        fetchPhotos();
    }, [
        videoRef,
        embedGeo,
        additionalPhotoOrientation,
        handleStorePhotos,
        isRetake,
        step,
        additionalPhotos,
        fetchPhotos,
        contractId,
        updateResult
    ]);

    const handleRetake = () => {
        setPhotoStep(AdditionalSteps.MakePhoto);
        setIsRetake(true);
    };

    const handleSwitchStep = (nextStep: number) => {
        setPhotoStep(AdditionalSteps.RetakePhoto);
        setStep(nextStep);
    };

    const handleAddNewPhoto = () => {
        setPhotoStep(AdditionalSteps.MakePhoto);
        setStep(additionalPhotos.length);
    };

    const handleExitPhotoCapture = async () => {
        if (withSummary) {
            dispatch(changeStep(MainSteps.SendForm));
        } else {
            await handleFormSubmission(
                contractId,
                clearIndexedDBData,
                dateTime,
                stepLogs,
                completeCallback,
                showConfirmationStep,
                dispatch,
                steps,
                searchAutomatic,
                onCompleteAiStepsResult,
                country,
                isCompressImage,
                compressToSize
            );
        }
    };

    useCameraControl(videoRef);

    const ButtonsSection = (
        <BtnSection isLandscape={additionalPhotoOrientation} retakeStep={photoStep === AdditionalSteps.RetakePhoto}>
            {photoStep === AdditionalSteps.MakePhoto ? (
                <PhotoButton onClick={handlePhoto} />
            ) : (
                <RetakePhotoButton onClick={handleRetake} isLandscape={additionalPhotoOrientation} />
            )}
            <ExitButton onClick={handleExitPhotoCapture} aria-label={t('exitBtn')}>
                <RightArrow color={black} />
            </ExitButton>
        </BtnSection>
    );

    return (
        <PhotoSteps>
            <Layout forceLandscape={additionalPhotoOrientation} isStandard={isStandard}>
                <Video
                    layout={additionalPhotoLayout}
                    ref={videoRef}
                    isVisible={photoStep === AdditionalSteps.MakePhoto}
                />

                {photoStep === AdditionalSteps.RetakePhoto && (
                    <TakenPhotoPreview
                        takenPhoto={additionalPhotos[step]}
                        isLandscape={additionalPhotoOrientation}
                        isSquared={isSquared}
                    />
                )}
                {additionalPhotoOrientation || isLandscape ? (
                    <Wrapper>
                        <AdditionalPhotoStepsList
                            steps={additionalPhotos}
                            step={step}
                            handleSwitchStep={handleSwitchStep}
                            additionalPhotoFolderId={additionalPhotoFolderId}
                            onAddStep={handleAddNewPhoto}
                        />
                        {ButtonsSection}
                        {showSettingsButton && <SettingsButton videoRef={videoRef} />}
                    </Wrapper>
                ) : (
                    <PortraitWrapper isLandscape={additionalPhotoOrientation} isSquared={isSquared}>
                        {ButtonsSection}
                        {showSettingsButton && <SettingsButton videoRef={videoRef} />}
                    </PortraitWrapper>
                )}
            </Layout>
        </PhotoSteps>
    );
};
