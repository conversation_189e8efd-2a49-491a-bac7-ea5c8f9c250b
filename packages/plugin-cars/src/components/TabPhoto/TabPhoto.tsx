import { FC, useContext, useRef, ChangeEvent, PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import { useToggle } from '@dat/core/hooks/useToggle';
import { FormStep, PluginProps, ResultForm, Steps } from '../../types';
import { FormContext, actions } from '../../reducers/form';
import {
    Photos,
    PhotoStyled,
    PhotoTitle,
    PhotoWrapper,
    TabPhotoPanelStyled,
    Title,
    Image,
    AdditionalIconStyled,
    SlideUpMenuItem,
    InputFile
} from './styles';
import { SlideUpMenu } from '@wedat/ui-kit';
import { getBase64FromFile } from '../../utils/getBase64FromFile';
import { usePhotosUpload } from '../../hooks/usePhotosUpload';
import { Text } from '@wedat/kit';
import { useTheme } from 'styled-components';

const { changeStep, retakePhoto } = actions;

interface Props extends PropsWithChildren {
    title: string;
    steps: PluginProps['steps'];
    formSteps: ResultForm['steps'];
    filterKey: FormStep['photoType'];
}

interface AdditionalProps extends PropsWithChildren {
    title: string;
    showGalleryButton?: boolean;
}

interface PhotoProps extends PropsWithChildren {
    photo: string;
    title?: string;
    onClick?: () => void;
}

interface PhotoItemProps extends PropsWithChildren {
    title?: string;
    onClick?: () => void;
}

interface TabPhotoPanelProps extends PropsWithChildren {
    title: string;
}

const PhotoItem: FC<PhotoItemProps> = ({ children, title = '', onClick }) => {
    const theme = useTheme();

    return (
        <PhotoStyled onClick={onClick}>
            <PhotoWrapper>{children}</PhotoWrapper>
            <PhotoTitle fontSize="10px" color={theme.colors.black}>
                {title}
            </PhotoTitle>
        </PhotoStyled>
    );
};

const Photo: FC<PhotoProps> = ({ photo, title, onClick }) => (
    <PhotoItem title={title} onClick={onClick}>
        <Image alt="photo" src={photo} />
    </PhotoItem>
);

const TabPhotoPanel: FC<TabPhotoPanelProps> = ({ children, title }) => {
    const { t } = useTranslation();
    const theme = useTheme();

    return (
        <TabPhotoPanelStyled>
            <Title asTag="p" font="font-default-medium" fontWeight={600} color={theme.colors.black}>
                {t(title)}
            </Title>
            <Photos>{children}</Photos>
        </TabPhotoPanelStyled>
    );
};

export const TabPhotoAdditional: FC<AdditionalProps> = ({ title, showGalleryButton }) => {
    const { dispatch } = useContext(FormContext);
    const { handleStorePhotos, resultForm, updateResult } = usePhotosUpload();

    const [isOpenSlideUp, toggleSlideUp] = useToggle(false);
    const inputRef = useRef<HTMLInputElement | null>(null);
    const { t } = useTranslation();

    const handleChange = async (event: ChangeEvent<HTMLInputElement>) => {
        const filesUploaded = event.target.files ? Array.from(event.target.files) : [];
        const filesBase64 = await Promise.all(filesUploaded.map(async file => String(await getBase64FromFile(file))));

        await handleStorePhotos(filesBase64, true);
        updateResult();
        toggleSlideUp();
    };
    const additionalSteps = resultForm?.additionalSteps;

    const handleClick = () => {
        inputRef.current?.click();
    };

    return (
        <TabPhotoPanel title={title}>
            {additionalSteps?.map((photo, idx) => (
                <Photo key={idx.toString()} photo={photo} title={`${t('Photo')} ${idx + 1}`} />
            ))}
            <PhotoItem onClick={toggleSlideUp} title={`${t('Photo')} ${(additionalSteps?.length ?? 0) + 1}`}>
                <AdditionalIconStyled />
            </PhotoItem>
            <SlideUpMenu isOpen={isOpenSlideUp} closeSlideUpMenu={toggleSlideUp}>
                {showGalleryButton && (
                    <SlideUpMenuItem onClick={handleClick}>
                        <Text font="font-footnote">{t('gallery')}</Text>
                        <InputFile ref={inputRef} onChange={handleChange} type="file" accept="image/*" multiple />
                    </SlideUpMenuItem>
                )}
                <SlideUpMenuItem onClick={() => dispatch(changeStep(Steps.Additional))}>
                    <Text font="font-footnote">{t('capture')}</Text>
                </SlideUpMenuItem>
            </SlideUpMenu>
        </TabPhotoPanel>
    );
};

export const TabPhoto: FC<Props> = ({ title, steps, formSteps, filterKey }) => {
    const { dispatch } = useContext(FormContext);
    const items = formSteps
        .map((step, idx) => ({
            ...step,
            formIndex: idx
        }))
        .filter(step => step.photoType?.toLowerCase() === filterKey.toLowerCase());

    const filteredSteps = steps?.filter(step => step.photoType?.toLowerCase() === filterKey.toLowerCase()) || [];

    if (items.length === 0) return null;

    return (
        <TabPhotoPanel title={title}>
            {items.map((formStep, idx) => (
                <Photo
                    key={idx.toString()}
                    photo={formStep.photo}
                    title={filteredSteps[idx]?.mask?.title}
                    onClick={() => dispatch(retakePhoto(formStep.formIndex))}
                />
            ))}
        </TabPhotoPanel>
    );
};
