import styled from 'styled-components/macro';

interface PrivacyPolicyButtonProps {
    isTransparent?: boolean;
}

export const PrivacyPolicyContainer = styled.div`
    width: 100%;
    text-align: center;
    flex-direction: column;
    margin-bottom: 28px;
    padding: 0 16px;
`;

export const PrivacyPolicyMessage = styled.div`
    background-color: ${({ theme }) => theme.colors.blue[50]};
    padding: 20px 12px;
    align-items: center;
    border-radius: 12px;
    color: ${({ theme }) => theme.colors.blue[800]};
    margin: 20px 5px;
    display: flex;
    align-items: center;
    text-align: start;
    gap: 11px;
`;

export const PrivacyPolicyContent = styled.iframe`
    width: 100%;
    height: 80vh;
    border: none;
`;

export const ButtonsWrapper = styled.div`
    position: fixed;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 10px;
    box-shadow:
        0px -8px 56px 0px ${({ theme }) => theme.colors.white},
        0px 8px 56px 0px rgba(174, 174, 192, 0.6);
    border-radius: 12px;
    background-color: ${({ theme }) => theme.colors.white};
`;

export const PrivacyPolicyButton = styled.button<PrivacyPolicyButtonProps>`
    padding: 12px 50px;
    color: ${({ theme, isTransparent }) => (isTransparent ? theme.colors.dustBlue['900'] : theme.colors.white)};
    background-color: ${({ theme, isTransparent }) =>
        isTransparent ? theme.colors.white : theme.colors.deepBlue['800']};
    border: ${({ theme }) => `1px solid ${theme.colors.dustBlue['400']}`};
    border-radius: 8px;
    cursor: pointer;
`;
