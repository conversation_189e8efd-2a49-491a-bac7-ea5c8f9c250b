import styled from 'styled-components';

import { media } from '@wedat/ui-kit/mediaQueries';
import { Text } from '@wedat/kit';
import { SuccessIcon } from '@wedat/ui-kit/assets/Icon';
import { CloseIcon } from '@wedat/ui-kit/components/Icons';

export const StyledSuccessIcon = styled(SuccessIcon)`
    color: green;
`;

export const StyledCloseIcon = styled(CloseIcon)`
    color: red;
`;

export const DamageSelectorContainer = styled.div`
    width: 35%;

    ${media.laptopSmall`
        width: 75%
    `}
    ${media.phoneBig`
        width:100%;
    `}
`;

export const StyledContainer = styled.div`
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    background-color: ${({ theme }) => theme.colors.white};
`;

export const StyledButtonWrapper = styled.div`
    width: 30%;

    ${media.phoneBig`
        width: 100%;
    `};
`;

export const StyledText = styled(Text)`
    margin-top: 20px;
`;
