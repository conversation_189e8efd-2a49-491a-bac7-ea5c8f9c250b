import { AxiosResponse } from 'axios';
import {
    vehicleSelectionModalEffects as sharedVehicleSelectionModalEffects,
    vehicleSelectionModalEvents
} from '@dat/shared-models/vsm/store';
import { sharedPluginCarsEvents } from '@dat/shared-models/plugin-cars';
import { sharedAiEvents } from '@dat/shared-models/ai/init';
import { contractEffects as sharedContractEffects } from '@dat/shared-models/contract';
import { handleOcrImage } from '@dat/shared-models/vsm/utils/getOcrImage';
import { extractAIResponse } from '@dat/shared-models/vsm/utils/extractAIResponse';
import { AiPhotosJson, FormStep } from '../types';
import { extractAiPhotosJson } from './imageAiRecognition';

const { handleSearch, vinOcrScan } = vehicleSelectionModalEvents;
const { getVehicleByOcr } = sharedPluginCarsEvents;

export const processAiRecognitionSteps = async (
    aiRecognitionSteps: DAT2.Plugins.PluginCars.PluginStep[],
    steps: DAT2.Plugins.PluginCars.PluginStep[],
    formSteps: FormStep[],
    contractId: number,
    searchAutomatic?: boolean,
    country?: string,
    onCompleteAiStepsResult?: (result: AiPhotosJson) => void,
    OCRWithoutTrigger?: boolean
): Promise<void> => {
    const ocrResults: Record<string, AxiosResponse<any, any>> = {};

    const processOcrResult = async (photo: string, recognitionType: string): Promise<AxiosResponse<any, any>> => {
        if (!ocrResults[recognitionType]) {
            ocrResults[recognitionType] = await handleOcrImage(photo, recognitionType);
        }
        return ocrResults[recognitionType];
    };

    const handleRecognitionType = async (
        type: string,
        ocrResult: AxiosResponse<any, any>,
        photo: string
    ): Promise<void> => {
        switch (type) {
            case 'plate':
                await handlePlateRecognition(ocrResult, photo);
                break;
            case 'mileage':
                handleMileageRecognition(ocrResult);
                break;
            case 'vin':
                await handleVinRecognition(ocrResult);
                break;
            default:
                console.warn(`Unknown recognition type: ${type}`);
        }
    };

    // Helper: Handle plate recognition
    const handlePlateRecognition = async (ocrResult: AxiosResponse<any, any>, photo: string): Promise<void> => {
        const numberPlate = extractAIResponse(ocrResult);

        if (searchAutomatic) {
            getVehicleByOcr({
                OCRPayload: { mmc: true, upload: photo },
                country: country || '',
                isAiOcrPlateProvider: true,
                OCRWithoutTrigger
            });
            await sharedAiEvents.handleSubmitCarSearch();
            handleSearch({ value: numberPlate as string, type: 'licencePlate' });
            sharedPluginCarsEvents.hideVSMModalForPluginCars(true);
        } else {
            await sharedContractEffects.createOrUpdateContractFx({
                contractId,
                Dossier: { Vehicle: { RegistrationData: { LicenseNumber: numberPlate } } }
            });
        }
    };

    // Helper: Handle mileage recognition
    const handleMileageRecognition = (ocrResult: AxiosResponse<any, any>): void => {
        sharedVehicleSelectionModalEffects.getMileageDataFx({ response: ocrResult });
    };

    // Helper: Handle VIN recognition
    const handleVinRecognition = async (ocrResult: AxiosResponse<any, any>): Promise<void> => {
        const vin = extractAIResponse(ocrResult);

        if (searchAutomatic) {
            vinOcrScan(vin);
            await sharedAiEvents.handleSubmitCarSearch();
            handleSearch({ value: vin as string, type: 'vin' });
            sharedPluginCarsEvents.hideVSMModalForPluginCars(true);
        } else {
            await sharedContractEffects.createOrUpdateContractFx({
                contractId,
                Dossier: { Vehicle: { VehicleIdentNumber: vin } }
            });
        }
    };

    for (const step of aiRecognitionSteps) {
        const aiRecognitionType = step.aiRecognitionType;
        const stepIndex = steps.indexOf(step);
        if (stepIndex === -1) continue;

        const photo = formSteps[stepIndex]?.photo;
        if (!photo) continue;

        try {
            const ocrResult = await processOcrResult(photo, aiRecognitionType || '');
            const aiPhotosResult = await extractAiPhotosJson(aiRecognitionSteps, formSteps, steps, ocrResults);

            onCompleteAiStepsResult?.(aiPhotosResult);

            if (aiRecognitionType) {
                await handleRecognitionType(aiRecognitionType, ocrResult, photo);
            }
        } catch (error) {
            console.error(`Error processing step: ${aiRecognitionType}`, error);
        }
    }
};
