<svg xmlns="http://www.w3.org/2000/svg" width="294.063" height="240.265" viewBox="0 0 294.063 240.265">
  <g id="Group_757" data-name="Group 757" transform="translate(-14.128 -102.8)">
    <rect id="rectangle" width="98.223" height="20.316" stroke-width="1" fill="none" stroke="#fff" stroke-miterlimit="10" transform="translate(14.628 322.248)"/>
    <text id="Front_Bumper" data-name="Front Bumper" transform="translate(21.541 336.89)" fill="#fff" font-size="14" font-family="SFUIDisplay-Semibold, SF UI Display" font-weight="600"><tspan x="0" y="0">Front Bumper</tspan></text>
    <path id="path" d="M305.8,259.6" transform="translate(-46.79 -24.325)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.5"/>
    <path id="path-2" data-name="path" d="M318.1,363.3" transform="translate(-48.764 -40.968)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="0.5"/>
    <text id="Driver_Side" data-name="Driver Side" transform="translate(224.978 336.175)" fill="#fff" font-size="14" font-family="SFUIDisplay-Semibold, SF UI Display" font-weight="600"><tspan x="0" y="0">Driver Side</tspan></text>
    <path id="path-3" data-name="path" d="M135.481,316.607c-25.521,0-48.188-3.946-67.581-11.669-16.2-6.464-26.864-14.272-33.916-19.477-1.847-1.343-3.358-2.519-4.785-3.442-13.684-9.067-12.593-40.968-12.509-42.311v-.084a106.867,106.867,0,0,1,3.106-16.79c3.274-12.593,8.059-22.5,14.188-29.467,6.548-7.472,63.887-29.887,64.391-30.138a28.7,28.7,0,0,0,9.235-6.128l.084-.084.084-.084c19.645-16.454,38.785-28.627,42.647-30.138,3.778-1.511,9.151-3.358,9.319-3.442h.168c.084,0,12.005-2.519,35.931-5.289A348.472,348.472,0,0,1,232.78,116.3c14.943,0,29.8.672,36.855,1.763,11.585,1.679,24.43,18.721,27.032,22.247l5.373,1.091.336,1.007c2.267,5.877,5.541,22.331,4.03,31.65a71.953,71.953,0,0,1-3.526,13.348,17.644,17.644,0,0,0-.84,2.686c-2.351,11.921-4.7,18.889-6.884,20.736-2.6,2.183-8.311,2.519-11.5,2.519h-.672l-2.1-.168-43.487,47.1c-.5,7.388-4.533,18.133-4.7,18.637-.168.588-5.373,15.867-14.356,21.659a19.573,19.573,0,0,1-10.914,2.854,36.449,36.449,0,0,1-11.249-1.931l-3.442,2.6-.084.084c-18.805,11.417-39.541,12.005-43.571,12.005h-.5C144.044,316.523,139.678,316.607,135.481,316.607Z" transform="translate(-0.382 -1.327)" fill="none"/>
    <g id="group" transform="translate(14.256 113.294)">
      <path id="path-4" data-name="path" d="M232.4,118.078c15.531,0,29.887.756,36.6,1.763,12.005,1.763,26.193,22.079,26.193,22.079l5.121,1.007c2.351,6.044,5.289,21.911,3.946,30.726-1.427,8.815-3.946,13.6-4.365,15.951-.5,2.351-3.442,17.294-6.212,19.645-2.267,1.931-8.311,2.1-10.326,2.1h-.588l-2.938-.252-44.746,48.188c-.252,7.136-4.617,18.721-4.617,18.721s-5.121,15.279-13.6,20.82a18.271,18.271,0,0,1-9.906,2.6,36.332,36.332,0,0,1-11.585-2.1l-4.2,3.19c-18.469,11.165-38.953,11.669-42.563,11.669h-.588c-4.533.252-8.815.336-13.012.336-66.154,0-93.018-26.109-105.191-34.168-12.928-8.647-11.669-40.632-11.669-40.632s2.435-28.879,16.79-45.166c5.709-6.548,58.178-27.536,63.635-29.551a31.181,31.181,0,0,0,9.906-6.548c19.309-16.2,38.45-28.292,42.143-29.887,3.778-1.511,9.235-3.358,9.235-3.358s11.585-2.435,35.679-5.2a300.024,300.024,0,0,1,36.855-1.931m0-3.778a354.031,354.031,0,0,0-37.19,1.763c-24.01,2.77-35.6,5.2-36.1,5.289l-.252.084-.168.084c-.252.084-5.625,1.931-9.486,3.442-4.617,1.931-24.6,14.943-43.151,30.474l-.168.084-.168.168a27.4,27.4,0,0,1-8.563,5.625c-2.6.923-17.126,6.716-31.9,13.18C36.2,187.17,33.1,190.612,32.007,191.955c-6.3,7.22-11.249,17.378-14.608,30.222A110.736,110.736,0,0,0,14.293,239.3v.168a108.9,108.9,0,0,0,1.007,17.8c1.931,13.18,6.044,22,12.341,26.193,1.343.923,2.854,2.015,4.7,3.358C46.278,297.062,75.157,318.3,134.931,318.3c4.282,0,8.647-.084,13.1-.336h.5c4.03,0,25.269-.588,44.494-12.257l.168-.084.168-.084,2.6-1.931a40.087,40.087,0,0,0,10.83,1.679A21.872,21.872,0,0,0,218.8,302.1c9.319-6.044,14.691-21.324,15.111-22.667.42-1.007,4.114-11,4.785-18.385l42.4-45.921,1.091.084h.923c4.2,0,9.738-.5,12.677-2.938,1.175-.923,3.778-3.19,7.556-21.827a16.926,16.926,0,0,1,.84-2.435,73.85,73.85,0,0,0,3.61-13.684c1.511-9.319-1.511-25.857-4.114-32.657l-.756-1.931-2.015-.42-3.61-.672c-3.61-4.953-15.951-20.652-27.788-22.415-7.136-1.175-22.079-1.931-37.106-1.931Z" transform="translate(-14.256 -114.3)" fill="#fff"/>
    </g>
    <path id="path-5" data-name="path" d="M293.5,145.107s-13.684-19.645-25.269-21.324-47.516-2.686-70.855,0-34.5,5.037-34.5,5.037-5.289,1.763-8.9,3.274-22.079,13.264-40.8,28.879a29.774,29.774,0,0,1-9.57,6.3c-5.289,1.931-56.919,25.773-62.46,32.069-13.936,15.867-15.447,40.213-15.447,40.213s-1.175,30.978,11.333,39.289,41.052,36.351,114.341,32.741c0,0,22,.588,41.64-11.333l4.03-3.022s12.838,3.237,21.066-2.136,12.851-18.348,12.851-18.348,4.282-11.165,4.449-18.05l43.169-46.768,3.161-.109s7.511.3,10.2-1.968,5.512-16.3,6.016-18.568c.42-2.267,2.938-6.884,4.282-15.363s-1.6-23.926-3.778-29.719Z" transform="translate(-1.831 -2.262)" fill="#fff"/>
    <path id="path-6" data-name="path" d="M258.933,133.291,249.782,166.7s-1.6,6.716-3.358,7.807-27.452,1.847-123.324-7.556c0,0,32.573-26.948,43.319-30.726s32.741-4.03,32.741-4.03h59.773Z" transform="translate(-17.468 -3.879)"/>
    <path id="path-7" data-name="path" d="M280.4,178.83s4.785-39.793,13.684-47.264,24.262,0,24.262,0,9.738,7.64,14.272,17.378c0,0-2.6,2.938-18.721,12.089,0,0-11.585-3.526-17.294-1.343a16.481,16.481,0,0,0-9.822,7.891c-1.007,1.679,2.1,6.968,2.1,6.968C286.193,175.807,283.338,177.319,280.4,178.83Z" transform="translate(-42.713 -3.244)"/>
    <path id="path-8" data-name="path" d="M121.5,278.3s9.486-18.805,34.5-23.171A461.989,461.989,0,0,0,201.338,244.8l-7.975,20.064s-12.089,6.464-21.659,8.227C162.132,274.938,121.5,278.3,121.5,278.3Z" transform="translate(-17.211 -21.95)"/>
    <path id="path-9" data-name="path" d="M46.42,213.915l5.877,9.319L40.627,241.451,32.4,231.293s3.61-11.333,9.738-17.21A4.5,4.5,0,0,1,46.42,213.915Z" transform="translate(-2.912 -16.922)"/>
    <path id="path-10" data-name="path" d="M107.1,324.3v8.983s67.833,9.738,65.062,2.938c-2.686-6.716-4.617-8.143-4.617-8.143S147.648,328.246,107.1,324.3Z" transform="translate(-14.9 -34.709)"/>
    <path id="path-11" data-name="path" d="M86.946,317.019S43.375,302.159,35.4,287.3c0,0,3.778,8.4,9.151,12.928s23.087,17.462,42.4,22.919Z" transform="translate(-3.393 -28.771)"/>
    <path id="path-12" data-name="path" d="M227.8,317.019s2.6-12.173,2.6-19.729,11.837-35.008,20.82-46.761c8.059-10.578,11-11.753,11-11.753s6.632-5.037,10.158,2.267,1.175,25.1,1.175,25.1-.756,5.541-2.015,6.884l-2.519,4.282s-4.869,21.24-12.928,32.153a21.389,21.389,0,0,1-5.289,5.373c-4.617,3.274-10.83,4.365-21.24.84Z" transform="translate(-34.271 -20.711)"/>
    <ellipse id="ellipse" cx="27.284" cy="10.914" rx="27.284" ry="10.914" transform="translate(202.433 286.412) rotate(-74.396)" fill="#fff"/>
    <path id="path-13" data-name="path" d="M326.9,221.282l.168-.084,8.479-6.884s3.694-7.052,3.862-12.005c.252-5.037,7.22-20.148,8.563-21.324s1.427-.252,1.427-.252,1.343,1.931,0,9.235c-1.343,7.22-2.1,6.716-2.77,9.4s-1.343,16.287-5.961,19.729C338.485,220.695,336.219,221.954,326.9,221.282Z" transform="translate(-50.176 -11.613)"/>
    <ellipse id="ellipse-2" data-name="ellipse" cx="13.348" cy="2.938" rx="13.348" ry="2.938" transform="matrix(0.163, -0.987, 0.987, 0.163, 287.74, 204.233)" fill="#fff"/>
    <path id="path-14" data-name="path" d="M114.043,255.974s-17.8,13.18-22.079,13.6-34.588-7.052-40.884-21.492c0,0-1.007-8.227,3.19-14.859l.672-.923C55.025,232.3,66.191,253.624,114.043,255.974Z" transform="translate(-5.893 -19.944)"/>
    <path id="path-15" data-name="path" d="M42.738,135.129A21.361,21.361,0,0,1,39.8,123.963,22.409,22.409,0,0,1,42.738,112.8a23.151,23.151,0,0,1,8.059-8.059A21.361,21.361,0,0,1,61.963,101.8a21.978,21.978,0,0,1,19.225,11,21.361,21.361,0,0,1,2.938,11.166,21.978,21.978,0,0,1-11,19.225,21.361,21.361,0,0,1-11.166,2.938A22.409,22.409,0,0,1,50.8,143.188,21.831,21.831,0,0,1,42.738,135.129Zm2.1-21.072a19.608,19.608,0,0,0,0,19.812,19.847,19.847,0,0,0,7.22,7.22,19.608,19.608,0,0,0,19.812,0,19.847,19.847,0,0,0,7.22-7.22,19.608,19.608,0,0,0,0-19.812,19.847,19.847,0,0,0-7.22-7.22,19.608,19.608,0,0,0-19.812,0A19.846,19.846,0,0,0,44.837,114.057Zm5.457,8.647,2.938-2.938,6.884,6.632,10.578-10.326,2.938,2.938-13.516,13.6Z" transform="translate(-4.1 1)" fill="#fff"/>
    <line id="line" y2="125.675" transform="translate(259.011 195.986)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="1"/>
    <line id="line-2" data-name="line" y2="125.675" transform="translate(259.851 195.986)" fill="none" stroke="#262626" stroke-miterlimit="10" stroke-width="1"/>
    <circle id="ellipse-3" data-name="ellipse" cx="2.519" cy="2.519" r="2.519" transform="translate(257.332 193.467)" fill="#fff" stroke="#262626" stroke-width="1"/>
    <line id="line-3" data-name="line" y1="55.911" transform="translate(63.825 266.589)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="1"/>
    <line id="line-4" data-name="line" y1="55.911" transform="translate(64.664 266.589)" fill="none" stroke="#262626" stroke-miterlimit="10" stroke-width="1"/>
    <circle id="ellipse-4" data-name="ellipse" cx="2.519" cy="2.519" r="2.519" transform="translate(62.146 264.07)" fill="#fff" stroke="#262626" stroke-width="1"/>
    <rect id="rectangle-2" data-name="rectangle" width="79.166" height="20.316" transform="translate(219.385 321.493)" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="1"/>
  </g>
</svg>
