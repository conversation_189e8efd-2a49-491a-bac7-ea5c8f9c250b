import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useUnit } from 'effector-react';

import { sharedSmartRepairEffects, sharedSmartRepairStores } from '@dat/shared-models/smartRepair';
import { smartRepairEvents } from '../../stores/smartRepair';
import { SmartRepairItemProps } from '../../types/plugin';
import { smartRepairModalEvents, smartRepairModalStores } from '../../stores/modals';
import { sharedUserStores } from '@dat/shared-models/user';

import { Text } from '@wedat/ui-kit/components/Text';
import { SMART_REPAIR_EDIT_MODE } from '../../constants/constant';
import { ConfirmModal } from '../ConfirmModal';

import { IconsWrapper, SmartRepairContent, StyledArrowIcon, StyledCloseIcon, StyledEditIcon } from './styles';
import { Button } from '@wedat/kit';

export const SmartRepairItem: FC<SmartRepairItemProps> = ({ id, description }) => {
    const { t } = useTranslation();

    const smartRepairConf = useUnit(sharedSmartRepairStores.smartRepairConfig);
    const customerNumber = useUnit(sharedUserStores.customerNumber);
    const isOpenDeleteVehicleClassModal = useUnit(smartRepairModalStores.isOpenDeleteVehicleClassModal);

    const handleClickEditIcon = () => {
        smartRepairModalEvents.setIsOpenNewVehicleClassModal(true);
        const editableSmartRepair = smartRepairConf?.find(repair => repair.id === id);
        editableSmartRepair && smartRepairEvents.setEditableSmartRepair(editableSmartRepair);
        editableSmartRepair && smartRepairEvents.setSmartRepairInitialValues(editableSmartRepair);
        smartRepairEvents.setSmartRepairMode(SMART_REPAIR_EDIT_MODE);
    };

    const handleClickDeleteIcon = () => {
        const filtratedData = smartRepairConf?.filter(hailDamage => hailDamage.id !== id);
        sharedSmartRepairEffects.postSmartRepairDataFx({
            data: filtratedData || [],
            customerNumber: customerNumber
        });
        smartRepairModalEvents.setIsOpenDeleteVehicleClassModal(false);
    };

    const handleOpenSmartRepair = () => {
        smartRepairEvents.openRulesTab();
        const editableSmartRepair = smartRepairConf?.find(repair => repair.id === id);
        editableSmartRepair && smartRepairEvents.setEditableSmartRepair(editableSmartRepair);
        editableSmartRepair && smartRepairEvents.setSmartRepairInitialValues(editableSmartRepair);
    };

    const handleCloseModal = () => {
        smartRepairModalEvents.setIsOpenDeleteVehicleClassModal(false);
    };

    return (
        <SmartRepairContent>
            <Text>{description}</Text>
            <IconsWrapper>
                <Button
                    aria-label={t('complexRule.openRule')}
                    icon={<StyledArrowIcon />}
                    variant="transparent"
                    onClick={handleOpenSmartRepair}
                />
                <Button
                    aria-label={t('complexRule.editRule')}
                    icon={<StyledEditIcon />}
                    variant="transparent"
                    onClick={handleClickEditIcon}
                />
                <Button
                    aria-label={t('complexRule.editRule')}
                    icon={<StyledCloseIcon />}
                    variant="transparent"
                    onClick={() => {
                        smartRepairModalEvents.setIsOpenDeleteVehicleClassModal(true);
                    }}
                />
            </IconsWrapper>
            <ConfirmModal
                onDismiss={handleCloseModal}
                isOpenModal={isOpenDeleteVehicleClassModal}
                handleConfirm={handleClickDeleteIcon}
                titleName={t('smartRepair.vehicleClass')}
            />
        </SmartRepairContent>
    );
};
