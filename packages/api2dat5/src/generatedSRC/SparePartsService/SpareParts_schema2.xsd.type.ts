// Auto generated code. Don't change it manually (But some times it is allow :)
import { tns as ns1 } from './SpareParts_schema1.xsd.type';

export namespace tns {
    // =============== generated types ===============

    export interface anyType {
        _value?: string;
        _attr_type: string;
    }

    // getExtPartNoInfoByVinAndIntPartNo
    // getExtPartNoInfoByVinAndIntPartNo
    export interface getExtPartNoInfoByVinAndIntPartNo {
        request?: vinAndIntPartNoRequest; // tns:vinAndIntPartNoRequest
    }

    // vinAndIntPartNoRequest
    // vinAndIntPartNoRequest
    export interface vinAndIntPartNoRequest extends abstractCoverageRequest {
        intPartNo?: number[]; // xs:int
        vin?: string; // xs:string
    }

    // abstractCoverageRequest
    // abstractCoverageRequest
    export interface abstractCoverageRequest extends abstractSelectionRequest {
        coverage: string; // xs:string
    }

    // abstractSelectionRequest
    // abstractSelectionRequest
    export interface abstractSelectionRequest {
        locale: locale; // tns:locale
        sessionID: string; // xs:string
    }

    // locale
    // locale
    export interface locale {
        _attr_country: string;
        _attr_datCountryIndicator: string;
        _attr_language: string;
    }

    // getExtPartNoInfoByVinAndIntPartNoResponse
    // getExtPartNoInfoByVinAndIntPartNoResponse
    export interface getExtPartNoInfoByVinAndIntPartNoResponse {
        result?: sparePartResult; // tns:sparePartResult
    }

    // sparePartResult
    // sparePartResult
    export interface sparePartResult {
        Dossiers: ns1.Dossiers;
    }

    // getExtPartNoInfoByModelAndExtPartNo
    // getExtPartNoInfoByModelAndExtPartNo
    export interface getExtPartNoInfoByModelAndExtPartNo {
        request?: modelAndExtPartNoRequest; // tns:modelAndExtPartNoRequest
    }

    // modelAndExtPartNoRequest
    // modelAndExtPartNoRequest
    export interface modelAndExtPartNoRequest extends abstractCoverageRequest {
        baseModel: number; // xs:int
        extPartNo: string[]; // xs:string
        manufacturer: number; // xs:int
        subModel: number; // xs:int
        vehicleType: number; // xs:int
    }

    // getExtPartNoInfoByModelAndExtPartNoResponse
    // getExtPartNoInfoByModelAndExtPartNoResponse
    export interface getExtPartNoInfoByModelAndExtPartNoResponse {
        result?: sparePartResult; // tns:sparePartResult
    }

    // getModelInfoByMfrAndExtPartNo
    // getModelInfoByMfrAndExtPartNo
    export interface getModelInfoByMfrAndExtPartNo {
        request?: modelInfoMfrAndExtPartNoRequest; // tns:modelInfoMfrAndExtPartNoRequest
    }

    // modelInfoMfrAndExtPartNoRequest
    // modelInfoMfrAndExtPartNoRequest
    export interface modelInfoMfrAndExtPartNoRequest extends abstractCoverageRequest {
        extPartNo?: string[]; // xs:string
        manufacturer: number; // xs:int
    }

    // getModelInfoByMfrAndExtPartNoResponse
    // getModelInfoByMfrAndExtPartNoResponse
    export interface getModelInfoByMfrAndExtPartNoResponse {
        result?: sparePartResult; // tns:sparePartResult
    }

    // getExtPartNoInfoByFullVehicleAndIntPartNo
    // getExtPartNoInfoByFullVehicleAndIntPartNo
    export interface getExtPartNoInfoByFullVehicleAndIntPartNo {
        request?: fullVehicleAndIntPartNoRequest; // tns:fullVehicleAndIntPartNoRequest
    }

    // fullVehicleAndIntPartNoRequest
    // fullVehicleAndIntPartNoRequest
    export interface fullVehicleAndIntPartNoRequest extends ecodeAndIntPartNoRequest {
        contructionTime: number; // xs:int
        equipment?: number[]; // xs:long
    }

    // ecodeAndIntPartNoRequest
    // ecodeAndIntPartNoRequest
    export interface ecodeAndIntPartNoRequest extends abstractCoverageRequest {
        datECode?: string; // xs:string
        intPartNo?: number[]; // xs:int
    }

    // getExtPartNoInfoByFullVehicleAndIntPartNoResponse
    // getExtPartNoInfoByFullVehicleAndIntPartNoResponse
    export interface getExtPartNoInfoByFullVehicleAndIntPartNoResponse {
        result?: sparePartResult; // tns:sparePartResult
    }

    // getExtPartNoInfoByMfrAndExtPartNo
    // getExtPartNoInfoByMfrAndExtPartNo
    export interface getExtPartNoInfoByMfrAndExtPartNo {
        request?: mfrAndExtPartNoRequest; // tns:mfrAndExtPartNoRequest
    }

    // mfrAndExtPartNoRequest
    // mfrAndExtPartNoRequest
    export interface mfrAndExtPartNoRequest extends abstractCoverageRequest {
        extPartNo?: string[]; // xs:string
        mfr: number; // xs:int
    }

    // getExtPartNoInfoByMfrAndExtPartNoResponse
    // getExtPartNoInfoByMfrAndExtPartNoResponse
    export interface getExtPartNoInfoByMfrAndExtPartNoResponse {
        result?: sparePartResult; // tns:sparePartResult
    }
} // namespace tns close {
