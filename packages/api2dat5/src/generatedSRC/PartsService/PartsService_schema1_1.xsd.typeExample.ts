// Auto generated code. Don't change it manually (But some times it is allow :)
import { tns } from './PartsService_schema1_1.xsd.type';
import { tns_Example as ns1 } from './PartsService_schema2.xsd.typeExample';

export namespace tns_Example {
    // =============== generated example values for types ===============

    // sparePartsDetailsResponseForDPN level: 0
    // sparePartsDetailsResponseForDPN
    export const sparePartsDetailsResponseForDPN_Example: tns.sparePartsDetailsResponseForDPN = {
        sparePartsResultPerDPN: [] // SparePartsResultPerDPN tns:SparePartsResultPerDPN
    };

    // SparePartsInformations level: 0
    // SparePartsInformations
    export const SparePartsInformations_Example: tns.SparePartsInformations = {
        sparePartsInformation: [] // SparePartsInformation tns:SparePartsInformation
    };

    // PreviousPrices level: 0
    // PreviousPrices
    export const PreviousPrices_Example: tns.PreviousPrices = {
        previousPrice: [] // PreviousPrice tns:PreviousPrice
    };

    // SparePartsVehicles level: 0
    // SparePartsVehicles
    export const SparePartsVehicles_Example: tns.SparePartsVehicles = {
        sparePartsVehicle: [] // SparePartsVehicle tns:SparePartsVehicle
    };

    // Equipments level: 0
    // Equipments
    export const Equipments_Example: tns.Equipments = {
        equipment: [] // Equipment tns:Equipment
    };

    // SparePartsSubModels level: 0
    // SparePartsSubModels
    export const SparePartsSubModels_Example: tns.SparePartsSubModels = {
        sparePartsSubModel: [] // SparePartsSubModel tns:SparePartsSubModel
    };

    // sparePartsDetailsResponseForPartNumber level: 0
    // sparePartsDetailsResponseForPartNumber
    export const sparePartsDetailsResponseForPartNumber_Example: tns.sparePartsDetailsResponseForPartNumber = {
        sparePartsResultPerSparePartNumber: [] // anyType xs:anyType
    };

    // sparePartsDetailsForDPNByVINResponse level: 0
    // sparePartsDetailsForDPNByVINResponse
    export const sparePartsDetailsForDPNByVINResponse_Example: tns.sparePartsDetailsForDPNByVINResponse = {
        sparePartsResultPerDPN: [], // anyType xs:anyType
        Dossier: ns1.Dossier_Example // ns1.Dossier
    };

    // sparePartsDetailsByVINResponse level: 0
    // sparePartsDetailsByVINResponse
    export const sparePartsDetailsByVINResponse_Example: tns.sparePartsDetailsByVINResponse = {
        sparePartsResultPerSparePartNumber: [], // anyType xs:anyType
        Dossier: ns1.Dossier_Example // ns1.Dossier
    };

    //  level: 1
    export const anyType_Example: tns.anyType = {
        _value: '', // string
        _attr_type: '' // string
    };

    // locale level: 1
    // locale
    export const locale_Example: tns.locale = {
        _attr_country: '', // string
        _attr_datCountryIndicator: '', // string
        _attr_language: '' // string
    };

    // sparePartsRestrictionForDPN level: 1
    // sparePartsRestrictionForDPN
    export const sparePartsRestrictionForDPN_Example: tns.sparePartsRestrictionForDPN = {
        datProcessNo: [], // number xs:long
        vehicleType: 0, // number xs:int
        manufacturer: 0, // number xs:int
        baseModel: 0, // number xs:int
        subModel: 0, // number xs:int
        constructionTime: 0, // number xs:int
        manufacturerCode: [], // string xs:string
        equipment: [], // number xs:long
        vin: '' // string xs:string
    };

    // orderInfo level: 1
    // orderInfo
    export const orderInfo_Example: tns.orderInfo = {
        order: '', // string xs:string
        priority: 0 // number xs:int
    };

    // getSparePartsDetailsForDPNResponse level: 1
    // getSparePartsDetailsForDPNResponse
    export const getSparePartsDetailsForDPNResponse_Example: tns.getSparePartsDetailsForDPNResponse = {
        SparePartsDetailsForDPNResponse: sparePartsDetailsResponseForDPN_Example // sparePartsDetailsResponseForDPN tns:sparePartsDetailsResponseForDPN
    };

    // SparePartsResultPerDPN level: 1
    // SparePartsResultPerDPN
    export const SparePartsResultPerDPN_Example: tns.SparePartsResultPerDPN = {
        pageSize: 0, // number xs:int
        pageNumber: 0, // number xs:int
        datProcessNumber: 0, // number xs:int
        vehiclesFound: 0, // number xs:int
        vehiclesReturned: 0, // number xs:int
        sparePartsInformations: SparePartsInformations_Example, // SparePartsInformations tns:SparePartsInformations
        sparePartsVehicles: SparePartsVehicles_Example, // SparePartsVehicles tns:SparePartsVehicles
        equipments: Equipments_Example // Equipments tns:Equipments
    };

    // SparePartsInformation level: 1
    // SparePartsInformation
    export const SparePartsInformation_Example: tns.SparePartsInformation = {
        name: '', // string xs:string
        partNumber: '', // string xs:string
        price: 0, // number xs:decimal
        priceDate: '', // string xs:date
        orderable: '', // string xs:string
        possibleNames: [], // PossibleNames tns:PossibleNames
        previousPrices: PreviousPrices_Example // PreviousPrices tns:PreviousPrices
    };

    // PossibleNames level: 1
    // PossibleNames
    export const PossibleNames_Example: tns.PossibleNames = {
        identifier: '', // string xs:string
        name: '' // string xs:string
    };

    // PreviousPrice level: 1
    // PreviousPrice
    export const PreviousPrice_Example: tns.PreviousPrice = {
        price: 0, // number xs:double
        priceDate: '' // string xs:date
    };

    // SparePartsVehicle level: 1
    // SparePartsVehicle
    export const SparePartsVehicle_Example: tns.SparePartsVehicle = {
        datProcessNumber: 0, // number xs:int
        partNumber: '', // string xs:string
        vehicleType: 0, // number xs:int
        vehicleTypeName: '', // string xs:string
        manufacturer: 0, // number xs:int
        manufacturerName: '', // string xs:string
        baseModel: 0, // number xs:int
        baseModelName: '', // string xs:string
        constructionTimeFrom: 0, // number xs:int
        constructionTimeTo: 0, // number xs:int
        constructionCondition: '', // string xs:string
        excludingCondition: '', // string xs:string
        descriptionIdentifier: '', // string xs:string
        sparePartsSubModels: SparePartsSubModels_Example // SparePartsSubModels tns:SparePartsSubModels
    };

    // Equipment level: 1
    // Equipment
    export const Equipment_Example: tns.Equipment = {
        number: 0, // number xs:long
        name: '' // string xs:string
    };

    // SparePartsSubModel level: 1
    // SparePartsSubModel
    export const SparePartsSubModel_Example: tns.SparePartsSubModel = {
        subModel: 0, // number xs:int
        subModelName: '' // string xs:string
    };

    // sparePartsRestrictionForETN level: 1
    // sparePartsRestrictionForETN
    export const sparePartsRestrictionForETN_Example: tns.sparePartsRestrictionForETN = {
        sparePartNo: [], // string xs:string
        vehicleType: 0, // number xs:int
        manufacturer: 0, // number xs:int
        baseModel: 0, // number xs:int
        subModel: 0, // number xs:int
        constructionTime: 0, // number xs:int
        manufacturerCode: [], // string xs:string
        equipment: [], // number xs:long
        vin: '' // string xs:string
    };

    // getSparePartsDetailsResponse level: 1
    // getSparePartsDetailsResponse
    export const getSparePartsDetailsResponse_Example: tns.getSparePartsDetailsResponse = {
        SparePartsDetailsResponse: sparePartsDetailsResponseForPartNumber_Example // sparePartsDetailsResponseForPartNumber tns:sparePartsDetailsResponseForPartNumber
    };

    // getSparePartsDetailsForDPNByVINResponse level: 1
    // getSparePartsDetailsForDPNByVINResponse
    export const getSparePartsDetailsForDPNByVINResponse_Example: tns.getSparePartsDetailsForDPNByVINResponse = {
        SparePartsDetailsForDPNByVINResponse: sparePartsDetailsForDPNByVINResponse_Example // sparePartsDetailsForDPNByVINResponse tns:sparePartsDetailsForDPNByVINResponse
    };

    // getSparePartsDetailsByVINResponse level: 1
    // getSparePartsDetailsByVINResponse
    export const getSparePartsDetailsByVINResponse_Example: tns.getSparePartsDetailsByVINResponse = {
        SparePartsDetailsByVINResponse: sparePartsDetailsByVINResponse_Example // sparePartsDetailsByVINResponse tns:sparePartsDetailsByVINResponse
    };

    // sparePartsSortingCriterions level: 2
    // sparePartsSortingCriterions
    export const sparePartsSortingCriterions_Example: tns.sparePartsSortingCriterions = {
        baseModel: orderInfo_Example, // orderInfo tns:orderInfo
        constructionTime: orderInfo_Example, // orderInfo tns:orderInfo
        subModel: orderInfo_Example // orderInfo tns:orderInfo
    };

    // sparePartsDetailsForDPNByVINRequest level: 2
    // sparePartsDetailsForDPNByVINRequest
    export const sparePartsDetailsForDPNByVINRequest_Example: tns.sparePartsDetailsForDPNByVINRequest = {
        datProcessNo: [], // number xs:long
        locale: locale_Example, // locale tns:locale
        vin: '' // string xs:string
    };

    // sparePartsDetailsByVINRequest level: 2
    // sparePartsDetailsByVINRequest
    export const sparePartsDetailsByVINRequest_Example: tns.sparePartsDetailsByVINRequest = {
        locale: locale_Example, // locale tns:locale
        sparePartNo: [], // string xs:string
        vin: '' // string xs:string
    };

    // sparePartsSettings level: 3
    // sparePartsSettings
    export const sparePartsSettings_Example: tns.sparePartsSettings = {
        pageSize: 0, // number xs:int
        pageNumber: 0, // number xs:int
        withPriceHistory: true, // boolean xs:boolean
        withVehicleData: true, // boolean xs:boolean
        sortingCriterions: sparePartsSortingCriterions_Example // sparePartsSortingCriterions tns:sparePartsSortingCriterions
    };

    // getSparePartsDetailsForDPNByVIN level: 3
    // getSparePartsDetailsForDPNByVIN
    export const getSparePartsDetailsForDPNByVIN_Example: tns.getSparePartsDetailsForDPNByVIN = {
        request: sparePartsDetailsForDPNByVINRequest_Example // sparePartsDetailsForDPNByVINRequest tns:sparePartsDetailsForDPNByVINRequest
    };

    // getSparePartsDetailsByVIN level: 3
    // getSparePartsDetailsByVIN
    export const getSparePartsDetailsByVIN_Example: tns.getSparePartsDetailsByVIN = {
        request: sparePartsDetailsByVINRequest_Example // sparePartsDetailsByVINRequest tns:sparePartsDetailsByVINRequest
    };

    // sparePartsDetailsForDPNRequest level: 4
    // sparePartsDetailsForDPNRequest
    export const sparePartsDetailsForDPNRequest_Example: tns.sparePartsDetailsForDPNRequest = {
        locale: locale_Example, // locale tns:locale
        restriction: sparePartsRestrictionForDPN_Example, // sparePartsRestrictionForDPN tns:sparePartsRestrictionForDPN
        settings: sparePartsSettings_Example // sparePartsSettings tns:sparePartsSettings
    };

    // sparePartsDetailsRequest level: 4
    // sparePartsDetailsRequest
    export const sparePartsDetailsRequest_Example: tns.sparePartsDetailsRequest = {
        locale: locale_Example, // locale tns:locale
        restriction: sparePartsRestrictionForETN_Example, // sparePartsRestrictionForETN tns:sparePartsRestrictionForETN
        settings: sparePartsSettings_Example // sparePartsSettings tns:sparePartsSettings
    };

    // getSparePartsDetailsForDPN level: 5
    // getSparePartsDetailsForDPN
    export const getSparePartsDetailsForDPN_Example: tns.getSparePartsDetailsForDPN = {
        request: sparePartsDetailsForDPNRequest_Example // sparePartsDetailsForDPNRequest tns:sparePartsDetailsForDPNRequest
    };

    // getSparePartsDetails level: 5
    // getSparePartsDetails
    export const getSparePartsDetails_Example: tns.getSparePartsDetails = {
        request: sparePartsDetailsRequest_Example // sparePartsDetailsRequest tns:sparePartsDetailsRequest
    };

    sparePartsDetailsResponseForDPN_Example.sparePartsResultPerDPN = [SparePartsResultPerDPN_Example]; // SparePartsResultPerDPN tns:SparePartsResultPerDPN
    SparePartsInformations_Example.sparePartsInformation = [SparePartsInformation_Example]; // SparePartsInformation tns:SparePartsInformation
    PreviousPrices_Example.previousPrice = [PreviousPrice_Example]; // PreviousPrice tns:PreviousPrice
    SparePartsVehicles_Example.sparePartsVehicle = [SparePartsVehicle_Example]; // SparePartsVehicle tns:SparePartsVehicle
    Equipments_Example.equipment = [Equipment_Example]; // Equipment tns:Equipment
    SparePartsSubModels_Example.sparePartsSubModel = [SparePartsSubModel_Example]; // SparePartsSubModel tns:SparePartsSubModel
    sparePartsDetailsResponseForPartNumber_Example.sparePartsResultPerSparePartNumber = [anyType_Example]; // anyType xs:anyType
    sparePartsDetailsForDPNByVINResponse_Example.sparePartsResultPerDPN = [anyType_Example]; // anyType xs:anyType
    sparePartsDetailsByVINResponse_Example.sparePartsResultPerSparePartNumber = [anyType_Example]; // anyType xs:anyType
    sparePartsRestrictionForDPN_Example.datProcessNo = [0]; // number xs:long
    sparePartsRestrictionForDPN_Example.manufacturerCode = ['']; // string xs:string
    sparePartsRestrictionForDPN_Example.equipment = [0]; // number xs:long
    SparePartsInformation_Example.possibleNames = [PossibleNames_Example]; // PossibleNames tns:PossibleNames
    sparePartsRestrictionForETN_Example.sparePartNo = ['']; // string xs:string
    sparePartsRestrictionForETN_Example.manufacturerCode = ['']; // string xs:string
    sparePartsRestrictionForETN_Example.equipment = [0]; // number xs:long
    sparePartsDetailsForDPNByVINRequest_Example.datProcessNo = [0]; // number xs:long
    sparePartsDetailsByVINRequest_Example.sparePartNo = ['']; // string xs:string
}
