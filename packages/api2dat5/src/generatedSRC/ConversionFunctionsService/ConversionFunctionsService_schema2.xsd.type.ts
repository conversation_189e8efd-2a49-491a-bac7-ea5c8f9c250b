// Auto generated code. Don't change it manually (But some times it is allow :)
import { tns as ns1 } from './ConversionFunctionsService_schema1.xsd.type';

export namespace tns {
    // =============== generated types ===============

    export interface anyType {
        _value?: string;
        _attr_type: string;
    }

    // valueCode2Description
    // valueCode2Description
    export interface valueCode2Description {
        request?: constructionPeriodSelectionRequest; // tns:constructionPeriodSelectionRequest
    }

    // constructionPeriodSelectionRequest
    // constructionPeriodSelectionRequest
    export interface constructionPeriodSelectionRequest extends priceFocusSelectionRequest {
        container?: string; // xs:string
    }

    // priceFocusSelectionRequest
    // priceFocusSelectionRequest
    export interface priceFocusSelectionRequest extends basicSelectionRequest {
        datECode: string; // xs:string
    }

    // basicSelectionRequest
    // basicSelectionRequest
    export interface basicSelectionRequest extends abstractSelectionRequest {
        constructionTimeFrom?: number; // xs:int
        constructionTimeTo?: number; // xs:int
        restriction: releaseRestriction; // tns:releaseRestriction
    }

    // abstractSelectionRequest
    // abstractSelectionRequest
    export interface abstractSelectionRequest {
        locale: locale; // tns:locale
        sessionID: string; // xs:string
    }

    // locale
    // locale
    export interface locale {
        _attr_country: string;
        _attr_datCountryIndicator: string;
        _attr_language: string;
    }

    // valueCode2DescriptionResponse
    // valueCode2DescriptionResponse
    export interface valueCode2DescriptionResponse {
        valueCodeDescription?: valueCodeDescription; // tns:valueCodeDescription
    }

    // valueCodeDescription
    // valueCodeDescription
    export interface valueCodeDescription {
        ECodeEquipments?: equipment[]; // tns:equipment
        _attr_baseModelLabeling: string;
        _attr_containerLabeling: string;
        _attr_manufacturerLabeling: string;
        _attr_subModelLabeling: string;
        _attr_vehicleTypeLabeling: string;
    }

    // equipment
    // equipment
    export interface equipment {
        appraisalGroup?: string; // xs:string
        equipmentClassification?: number; // xs:int
        equipmentNumber?: number; // xs:int
        equipmentStandardOrOptional?: string; // xs:string
        manufacturerCode?: string; // xs:string
        price?: number; // xs:int
        _attr_equipmentLabeling: string;
    }

    // constructionTime2Date
    // constructionTime2Date
    export interface constructionTime2Date {
        request?: constructionTime2DateSelectionRequest; // tns:constructionTime2DateSelectionRequest
    }

    // constructionTime2DateSelectionRequest
    // constructionTime2DateSelectionRequest
    export interface constructionTime2DateSelectionRequest {
        constructionTime: number; // xs:int
        sessionID: string; // xs:string
    }

    // constructionTime2DateResponse
    // constructionTime2DateResponse
    export interface constructionTime2DateResponse {
        Date?: string; // xs:anySimpleType
    }

    // getPossibleEquipmentN
    // getPossibleEquipmentN
    export interface getPossibleEquipmentN {
        request?: vehicleSelectionRequest; // tns:vehicleSelectionRequest
    }

    // vehicleSelectionRequest
    // vehicleSelectionRequest
    export interface vehicleSelectionRequest extends constructionPeriodSelectionRequest {
        constructionTime: number; // xs:int
    }

    // getPossibleEquipmentNResponse
    // getPossibleEquipmentNResponse
    export interface getPossibleEquipmentNResponse {
        VXS?: ns1.VXS;
    }

    // getEquipmentGroups
    // getEquipmentGroups
    export interface getEquipmentGroups {
        request?: abstractSelectionRequest; // tns:abstractSelectionRequest
    }

    // getEquipmentGroupsResponse
    // getEquipmentGroupsResponse
    export interface getEquipmentGroupsResponse {
        equipmentGroup?: stringStringPair[]; // tns:stringStringPair
    }

    // stringStringPair
    // stringStringPair
    export interface stringStringPair {
        _attr_key: string;
        _attr_value: string;
    }

    // date2ConstructionTime
    // date2ConstructionTime
    export interface date2ConstructionTime {
        request?: date2ConstructionTimeSelectionRequest; // tns:date2ConstructionTimeSelectionRequest
    }

    // date2ConstructionTimeSelectionRequest
    // date2ConstructionTimeSelectionRequest
    export interface date2ConstructionTimeSelectionRequest {
        date: string; // xs:anySimpleType
        sessionID: string; // xs:string
    }

    // date2ConstructionTimeResponse
    // date2ConstructionTimeResponse
    export interface date2ConstructionTimeResponse {
        constructionTime: number; // xs:int
    }

    // getExistingEquipmentN
    // getExistingEquipmentN
    export interface getExistingEquipmentN {
        request?: vehicleSelectionRequest; // tns:vehicleSelectionRequest
    }

    // getExistingEquipmentNResponse
    // getExistingEquipmentNResponse
    export interface getExistingEquipmentNResponse {
        VXS?: ns1.VXS;
    }

    // getEquipmentFromManufacturerCodeN
    // getEquipmentFromManufacturerCodeN
    export interface getEquipmentFromManufacturerCodeN {
        request?: equipmentFromManufacturerCodeSelectionRequest; // tns:equipmentFromManufacturerCodeSelectionRequest
    }

    // equipmentFromManufacturerCodeSelectionRequest
    // equipmentFromManufacturerCodeSelectionRequest
    export interface equipmentFromManufacturerCodeSelectionRequest extends vehicleSelectionRequest {
        manufacturerCodes: string[]; // xs:string
    }

    // getEquipmentFromManufacturerCodeNResponse
    // getEquipmentFromManufacturerCodeNResponse
    export interface getEquipmentFromManufacturerCodeNResponse {
        VXS?: ns1.VXS;
    }

    // =============== generated ENUM ===============

    export type releaseRestriction =
        | 'ALL'
        | 'REPAIR'
        | 'APPRAISAL'
        | 'GLASS'
        | 'APPRAISALNEW'
        | 'APPRAISALSHORT'
        | 'TCO'
        | 'APPRAISAL2YNEW'
        | 'APPRAISAL_AND_REPAIR'
        | 'TCO_NEW'
        | 'CA_DATAENGINEERINGMODE'
        | 'VA_DATAENGINEERINGMODE'
        | 'COMPREHENSIVE'
        | 'APPRAISALNEWFORMER'
        | 'REPAIR_OR_REPAIR_INCOMPLETE'
        | 'COMPREHENSIVE_OR_REPAIR_INCOMPLETE'
        | 'ALL_OR_REPAIR_OR_APPRAISAL';
} // namespace tns close {
