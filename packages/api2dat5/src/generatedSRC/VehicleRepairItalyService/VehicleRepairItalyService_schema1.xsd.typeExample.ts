// Auto generated code. Don't change it manually (But some times it is allow :)
import { tns } from './VehicleRepairItalyService_schema1.xsd.type';
import { tns_Example as ns1 } from './VehicleRepairItalyService_schema2.xsd.typeExample';

export namespace tns_Example {
    // =============== generated example values for types ===============

    //  level: 0
    // workBenchRequest_positionsItaly
    export const workBenchRequest_positionsItaly_Example: tns.workBenchRequest_positionsItaly = {
        PositionItaly: [] // ns1.PositionItaly
    };

    //  level: 0
    // addManualPositionsRequest_positionsItaly
    export const addManualPositionsRequest_positionsItaly_Example: tns.addManualPositionsRequest_positionsItaly = {
        PositionItaly: [] // ns1.PositionItaly
    };

    //  level: 0
    // workLogicRequest_positionsItaly
    export const workLogicRequest_positionsItaly_Example: tns.workLogicRequest_positionsItaly = {
        PositionItaly: [] // ns1.PositionItaly
    };

    //  level: 0
    // deleteAccesoryPartsRequest_positionsItaly
    export const deleteAccesoryPartsRequest_positionsItaly_Example: tns.deleteAccesoryPartsRequest_positionsItaly = {
        PositionItaly: [] // ns1.PositionItaly
    };

    //  level: 1
    export const anyType_Example: tns.anyType = {
        _value: '', // string
        _attr_type: '' // string
    };

    // locale level: 1
    // locale
    export const locale_Example: tns.locale = {
        _attr_country: '', // string
        _attr_datCountryIndicator: '', // string
        _attr_language: '' // string
    };

    //  level: 1
    // addWorkBenchResponse_calculationResult
    export const addWorkBenchResponse_calculationResult_Example: tns.addWorkBenchResponse_calculationResult = {
        Dossier: [], // ns1.Dossier
        _attr_source: '', // string
        _attr_type: '' // string
    };

    //  level: 1
    // addManualPositionsResponse_calculationResult
    export const addManualPositionsResponse_calculationResult_Example: tns.addManualPositionsResponse_calculationResult =
        {
            Dossier: [], // ns1.Dossier
            _attr_source: '', // string
            _attr_type: '' // string
        };

    //  level: 1
    // addWorkLogicResponse_calculationResult
    export const addWorkLogicResponse_calculationResult_Example: tns.addWorkLogicResponse_calculationResult = {
        Dossier: [], // ns1.Dossier
        _attr_source: '', // string
        _attr_type: '' // string
    };

    // deleteAccesoryPartsResponse level: 1
    // deleteAccesoryPartsResponse
    export const deleteAccesoryPartsResponse_Example: tns.deleteAccesoryPartsResponse = {
        deletevSuccessful: true // boolean xs:boolean
    };

    // workBenchRequest level: 2
    // workBenchRequest
    export const workBenchRequest_Example: tns.workBenchRequest = {
        positionsItaly: workBenchRequest_positionsItaly_Example, // workBenchRequest_positionsItaly
        contractID: 0, // number xs:long
        locale: locale_Example // locale tns:locale
    };

    // postCalculationRequest level: 2
    // postCalculationRequest
    export const postCalculationRequest_Example: tns.postCalculationRequest = {
        contractID: 0, // number xs:long
        locale: locale_Example // locale tns:locale
    };

    // addWorkBenchResponse level: 2
    // addWorkBenchResponse
    export const addWorkBenchResponse_Example: tns.addWorkBenchResponse = {
        calculationResult: addWorkBenchResponse_calculationResult_Example // addWorkBenchResponse_calculationResult
    };

    // addManualPositionsRequest level: 2
    // addManualPositionsRequest
    export const addManualPositionsRequest_Example: tns.addManualPositionsRequest = {
        positionsItaly: addManualPositionsRequest_positionsItaly_Example, // addManualPositionsRequest_positionsItaly
        contractID: 0, // number xs:long
        locale: locale_Example // locale tns:locale
    };

    // addManualPositionsResponse level: 2
    // addManualPositionsResponse
    export const addManualPositionsResponse_Example: tns.addManualPositionsResponse = {
        calculationResult: addManualPositionsResponse_calculationResult_Example // addManualPositionsResponse_calculationResult
    };

    // workLogicRequest level: 2
    // workLogicRequest
    export const workLogicRequest_Example: tns.workLogicRequest = {
        positionsItaly: workLogicRequest_positionsItaly_Example, // workLogicRequest_positionsItaly
        contractID: 0, // number xs:long
        locale: locale_Example // locale tns:locale
    };

    // addWorkLogicResponse level: 2
    // addWorkLogicResponse
    export const addWorkLogicResponse_Example: tns.addWorkLogicResponse = {
        calculationResult: addWorkLogicResponse_calculationResult_Example // addWorkLogicResponse_calculationResult
    };

    // deleteAccesoryPartsRequest level: 2
    // deleteAccesoryPartsRequest
    export const deleteAccesoryPartsRequest_Example: tns.deleteAccesoryPartsRequest = {
        positionsItaly: deleteAccesoryPartsRequest_positionsItaly_Example, // deleteAccesoryPartsRequest_positionsItaly
        contractID: 0, // number xs:long
        locale: locale_Example // locale tns:locale
    };

    // addWorkBench level: 3
    // addWorkBench
    export const addWorkBench_Example: tns.addWorkBench = {
        request: workBenchRequest_Example // workBenchRequest tns:workBenchRequest
    };

    // addManualPositions level: 3
    // addManualPositions
    export const addManualPositions_Example: tns.addManualPositions = {
        request: addManualPositionsRequest_Example // addManualPositionsRequest tns:addManualPositionsRequest
    };

    // addWorkLogic level: 3
    // addWorkLogic
    export const addWorkLogic_Example: tns.addWorkLogic = {
        request: workLogicRequest_Example // workLogicRequest tns:workLogicRequest
    };

    // deleteAccesoryParts level: 3
    // deleteAccesoryParts
    export const deleteAccesoryParts_Example: tns.deleteAccesoryParts = {
        request: deleteAccesoryPartsRequest_Example // deleteAccesoryPartsRequest tns:deleteAccesoryPartsRequest
    };

    workBenchRequest_positionsItaly_Example.PositionItaly = [ns1.PositionItaly_Example]; // ns1.PositionItaly undefined
    addManualPositionsRequest_positionsItaly_Example.PositionItaly = [ns1.PositionItaly_Example]; // ns1.PositionItaly undefined
    workLogicRequest_positionsItaly_Example.PositionItaly = [ns1.PositionItaly_Example]; // ns1.PositionItaly undefined
    deleteAccesoryPartsRequest_positionsItaly_Example.PositionItaly = [ns1.PositionItaly_Example]; // ns1.PositionItaly undefined
    addWorkBenchResponse_calculationResult_Example.Dossier = [ns1.Dossier_Example]; // ns1.Dossier undefined
    addManualPositionsResponse_calculationResult_Example.Dossier = [ns1.Dossier_Example]; // ns1.Dossier undefined
    addWorkLogicResponse_calculationResult_Example.Dossier = [ns1.Dossier_Example]; // ns1.Dossier undefined
}
