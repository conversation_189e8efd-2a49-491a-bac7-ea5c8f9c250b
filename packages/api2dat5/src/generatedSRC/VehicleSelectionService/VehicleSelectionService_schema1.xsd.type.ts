// Auto generated code. Don't change it manually (But some times it is allow :)

export namespace tns {
    // =============== generated types ===============

    export interface anyType {
        _value?: string;
        _attr_type: string;
    }

    // ActualRepairCost
    export interface ActualRepairCost {
        InvoiceDate?: string; // tns:fieldDate
        InvoiceNumber?: string; // tns:fieldString
        Designation?: string; // tns:fieldString
        ValueNet?: number; // tns:fieldDecimal
        ValueGross?: number; // tns:fieldDecimal
    }

    // AdditionalCostsPosition
    export interface AdditionalCostsPosition {
        DATProcessId?: number; // tns:fieldInteger
        Description?: string; // tns:fieldString
        ValueTotal?: number; // tns:fieldDecimal
        ManualPosition?: boolean; // tns:fieldBoolean
        ManualAmount?: boolean; // tns:fieldBoolean
        ManualPrice?: boolean; // tns:fieldBoolean
        ValuePerUnit?: number; // tns:fieldDecimal
        Amount?: number; // tns:fieldDecimal
        QuantityUnit?: string; // tns:fieldString
        PartNumber?: string; // tns:fieldString
        PartNumberOrigin?: string; // tns:fieldString
        DATPartNumber?: string; // tns:fieldString
        RequiredByProcessId?: number; // tns:fieldInteger
        IsSpecific?: boolean; // tns:fieldBoolean
        IncludedPositions?: IncludedPositions;
        AdditionalCostsPositionPriceState?: string; // tns:fieldString
        ExternalId?: string; // tns:fieldString
    }

    // AdditionalData
    export interface AdditionalData {
        partner: string; // xs:string
        foreignBrand: boolean; // xs:boolean
        CalculationWages: CalculationWages;
        MetaPositions: MetaPositions;
    }

    // AdditionalInsuranceData
    export interface AdditionalInsuranceData {
        InsuranceReferenceNumber?: string; // tns:fieldString
        Email?: Email;
    }

    // AdditionalService
    export interface AdditionalService {
        Selected?: boolean; // tns:fieldBoolean
        ServiceType?: string; // tns:fieldString
        CostOwner?: string; // tns:fieldString
        Price?: number; // tns:fieldDecimal
        PricePerDay?: number; // tns:fieldDecimal
        NbDays?: number; // tns:fieldDecimal
        UsedSince?: string; // tns:fieldDateTime
        UsedUntil?: string; // tns:fieldDateTime
        RentalCarGroup?: number; // tns:fieldInteger
    }

    // AdditionalServices
    export interface AdditionalServices {
        AdditionalService?: AdditionalService[];
        TotalAdditionalServicePrice?: number; // tns:fieldDecimal
    }

    // AdditionalVehicle
    export interface AdditionalVehicle {
        Name?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        ExternalId?: string; // tns:fieldString
        IdSD2?: string; // tns:fieldString12
        IdSDo?: number; // tns:fieldInteger
        IdExtern?: string; // tns:fieldString40
        Country?: string; // tns:fieldString3
        Language?: string; // tns:fieldString5
        Currency?: string; // tns:fieldString3
        UsageType?: string; // tns:fieldString
        CDYear?: number; // tns:fieldInteger
        CDMonth?: number; // tns:fieldInteger
        Vehicle?: Vehicle;
        Images?: Images;
        VAT?: VAT;
        Management?: anyType; // xs:anyType
        TradingData?: TradingData;
        Valuation?: Valuation;
        RepairCalculation?: RepairCalculation;
        RepairOrder?: RepairOrder;
        SparePartPositions?: SparePartPositions;
        MaintenanceIntervals?: MaintenanceIntervals;
        Comments?: Comments;
        Attachments?: Attachments;
    }

    // AdditionalVehicles
    export interface AdditionalVehicles {
        AdditionalVehicle?: AdditionalVehicle[];
    }

    // Addon
    export interface Addon {
        _attr_name?: string;
        _value: string; // xs:string
    }

    // AddonList
    export interface AddonList {
        Addon?: Addon[];
    }

    // Admission
    export interface Admission {
        CustomerId?: string; // tns:fieldString10
        BuyerId?: string; // tns:fieldString10
        VehicleGroup?: string; // tns:fieldString
        AdmissionDate?: string; // tns:fieldDateTime
        AdmissionPriceNet?: number; // tns:fieldDecimal
        AdmissionPriceGross?: number; // tns:fieldDecimal
        PurchasePriceNet?: number; // tns:fieldDecimal
        PurchasePriceGross?: number; // tns:fieldDecimal
        TaxationType?: string; // tns:fieldString
        AcceptanceDate?: string; // tns:fieldDateTime
        AcceptanceDetails?: string; // tns:fieldString
        MileageVehicle?: number; // tns:fieldDecimal
        MileageOdometer?: number; // tns:fieldDecimal
        Location?: string; // tns:fieldString
        DeregistrationDate?: string; // tns:fieldDateTime
        ResidualWarrantyType?: string; // tns:fieldString
        ResidualWarrantyUntil?: string; // tns:fieldDateTime
        ResidualWarrantyValueNet?: number; // tns:fieldDecimal
        ResidualWarrantyValueGross?: number; // tns:fieldDecimal
        PaymentAgreements?: string; // tns:fieldString
        OtherAgreements?: string; // tns:fieldString
        BusinessType?: string; // tns:fieldString
        AcceptanceInfo?: string; // tns:fieldString
        Buyer?: string; // tns:fieldString
        ProvisionOn?: string; // tns:fieldDateTime
        FollowUpBusiness?: FollowUpBusiness;
        Labelling?: Labelling;
    }

    // AgreementOnDeviationItem
    export interface AgreementOnDeviationItem {
        AgreementPosition?: number; // tns:fieldInteger
        Feature?: string; // tns:fieldString
        State?: string; // tns:fieldString
    }

    // Attachment
    export interface Attachment {
        AttachmentId?: number; // tns:fieldInteger
        AttachmentType?: string; // tns:fieldString
        FolderId?: string; // tns:fieldString
        Source?: string; // tns:fieldString
        Suffix?: string; // tns:fieldString
        Version?: string; // tns:fieldString
        Filename?: string; // tns:fieldString
        FilenameShort?: string; // tns:fieldString
        Offset?: number; // tns:fieldInteger
        Length?: number; // tns:fieldInteger
        Description?: string; // tns:fieldString
        B64Data?: string; // tns:fieldString
        FileDate?: string; // tns:fieldDateTime
        Name?: string; // tns:fieldString20
        Number?: number; // tns:fieldInteger
        Thumbnail?: fieldBinary; // tns:fieldBinary
        Usage?: string; // tns:fieldString5
        DATProcessIdList?: DATProcessIdList;
    }

    // Attachments
    export interface Attachments {
        Attachment?: Attachment[];
    }

    // Axle
    export interface Axle {
        AxleNo?: number; // tns:fieldInteger
        TireId?: number; // tns:fieldInteger
        TireState?: string; // tns:fieldString
        NrOfTires?: number; // tns:fieldInteger
        TireType?: string; // tns:fieldString
        TireTypeTextId?: string; // tns:fieldString
        TireOriginalPrice?: number; // tns:fieldDecimal
        TireSpeedIndex?: string; // tns:fieldString
        TireSize?: string; // tns:fieldString
        TireSafetySystem?: string; // tns:fieldString
        TireManufacturer?: number; // tns:fieldInteger
        TireManufacturerName?: string; // tns:fieldString
        PrintLoadCapacityIdx?: boolean; // tns:fieldBoolean
        TireOriginalTreadDepth?: number; // tns:fieldInteger
        TireOriginalTreadDepthUser?: number; // tns:fieldInteger
        TireOriginalTreadDepthN?: number; // tns:fieldDecimal
        TireOriginalTreadDepthNUser?: number; // tns:fieldDecimal
        TireLoadCapacityIndex?: number; // tns:fieldInteger
        TireLoadCapacityIndex2?: number; // tns:fieldInteger
        TreadDepthLeftOuterPerc?: number; // tns:fieldDecimal
        TreadDepthLeftInnerPerc?: number; // tns:fieldDecimal
        TreadDepthRightInnerPerc?: number; // tns:fieldDecimal
        TreadDepthRightOuterPerc?: number; // tns:fieldDecimal
        TreadDepthLeftOuterMm?: number; // tns:fieldDecimal
        TreadDepthLeftInnerMm?: number; // tns:fieldDecimal
        TreadDepthRightInnerMm?: number; // tns:fieldDecimal
        TreadDepthRightOuterMm?: number; // tns:fieldDecimal
        ManualEntry?: boolean; // tns:fieldBoolean
        RetreadedLeftOuter?: boolean; // tns:fieldBoolean
        RetreadedLeftInner?: boolean; // tns:fieldBoolean
        RetreadedRightInner?: boolean; // tns:fieldBoolean
        RetreadedRightOuter?: boolean; // tns:fieldBoolean
        TireAveragePriceUser?: number; // tns:fieldDecimal
        TireBrandPrice?: number; // tns:fieldDecimal
        TireBrandPriceUser?: number; // tns:fieldDecimal
        TireManufacturerId?: number; // tns:fieldInteger
        TireManufacturerTextId?: number; // tns:fieldInteger
        TireBrandId?: number; // tns:fieldInteger
        TireBrandName?: string; // tns:fieldString
        TireBrandTextId?: number; // tns:fieldInteger
        TireBrandEanCode?: string; // tns:fieldString40
        ProductCodeNumber?: number; // tns:fieldInteger
    }

    // BaseSummaryItaly
    export interface BaseSummaryItaly {
        WorkTimeReplace?: number; // tns:fieldDecimal
        WorkTimeOverhaul?: number; // tns:fieldDecimal
        WorkTimeMechanic?: number; // tns:fieldDecimal
        WorkTimeLacquer?: number; // tns:fieldDecimal
        ValueParts?: number; // tns:fieldDecimal
    }

    // BlanketCalculation
    export interface BlanketCalculation {
        LacquerCosts?: number; // tns:fieldDecimal
        LabourCosts?: number; // tns:fieldDecimal
        SparePartsCosts?: number; // tns:fieldDecimal
        Total?: number; // tns:fieldDecimal
        TotalWithVAT?: number; // tns:fieldDecimal
    }

    // CO2Costs
    export interface CO2Costs {
        CO2PriceMiddleAverage?: number; // tns:fieldDecimal
        CO2CostsMiddleAverageAccumulated?: number; // tns:fieldDecimal
        CO2PriceLowAverage?: number; // tns:fieldDecimal
        CO2CostsLowAverageAccumulated?: number; // tns:fieldDecimal
        CO2PriceHighAverage?: number; // tns:fieldDecimal
        CO2CostsHighAverageAccumulated?: number; // tns:fieldDecimal
        CO2CostAccountingPeriodFrom?: number; // tns:fieldInteger
        CO2CostAccountingPeriodTo?: number; // tns:fieldInteger
    }

    // CalcResultsHistory
    export interface CalcResultsHistory {
        CalcResultHistory?: calcResult[]; // tns:calcResult
    }

    // CalculationWage
    export interface CalculationWage {
        _attr_type: string;
        _attr_value?: number;
    }

    // CalculationWages
    export interface CalculationWages {
        CalculationWage?: CalculationWage[];
    }

    // Capacity
    export interface Capacity {
        _attr_type?: number;
        _attr_desc?: string;
        _attr_min?: number;
        _attr_max?: number;
        _attr_unit?: string;
        _attr_condition?: string;
    }

    // ClientContactAddresses
    export interface ClientContactAddresses {
        ClientContactAddress?: address[]; // tns:address
    }

    // Comment
    export interface Comment {
        CommentType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        Text?: string; // tns:fieldString
        comment1?: string; // tns:fieldString
        comment2?: string; // tns:fieldString
        kz?: number; // tns:fieldInteger
    }

    // Comments
    export interface Comments {
        Comment?: Comment[];
    }

    // Compare_CompareFlatEquipments
    export interface Compare_CompareFlatEquipments {
        CompareFlatEquipment?: CompareFlatEquipment[];
    }

    // Compare_CompareParameters
    export interface Compare_CompareParameters {
        CompareParameter?: CompareParameter[];
    }

    // Compare
    export interface Compare {
        MandatorId?: string; // tns:fieldString
        International?: string; // tns:fieldString
        NameMandatory?: boolean; // tns:fieldBoolean
        ConfigurationHidden?: boolean; // tns:fieldBoolean
        BaseType?: string; // tns:fieldString
        IncludeVat?: boolean; // tns:fieldBoolean
        PriceType?: string; // tns:fieldString
        RasterType?: string; // tns:fieldString
        MilageType?: string; // tns:fieldString
        Tracking?: string; // tns:fieldString
        FlatEquipmentValueFlag?: boolean; // tns:fieldBoolean
        FlatEquipmentValueType?: string; // tns:fieldString
        FlatEquipmentValue?: number; // tns:fieldDecimal
        CountryCode?: string; // tns:fieldString
        CompareFlatEquipments: Compare_CompareFlatEquipments;
        CompareParameters: Compare_CompareParameters;
    }

    // CompareFlatEquipment
    export interface CompareFlatEquipment {
        OriginalPricePercentage?: number; // tns:fieldDecimal
        DecreaseType?: string; // tns:fieldString
        OriginalPrice?: number; // tns:fieldDecimal
        Value?: number; // tns:fieldDecimal
    }

    // CompareParameter
    export interface CompareParameter {
        Type?: string; // tns:fieldString
        Position?: number; // tns:fieldInteger
        Milage?: number; // tns:fieldInteger
        Age?: number; // tns:fieldInteger
    }

    // Condition
    export interface Condition {
        OwnerCorrectionPerc?: number; // tns:fieldDecimal
        DatOwnerCorrectionPerc?: number; // tns:fieldDecimal
        OwnerCorrectionAmount?: number; // tns:fieldDecimal
        OwnerCorrectionAmountGross?: number; // tns:fieldDecimal
        ConditionCorrectionFactorPerc?: number; // tns:fieldDecimal
        DatConditionCorrectionFactorPerc?: number; // tns:fieldDecimal
        ConditionCorrectionAmount?: number; // tns:fieldDecimal
        ConditionCorrectionAmountGross?: number; // tns:fieldDecimal
        ConditionCorrectionDescription?: string; // tns:fieldString40
        NumberOfOwners?: number; // tns:fieldInteger
        NumberOfOwnersN?: string; // tns:fieldString
        NumberOfProprietors?: string; // tns:fieldString
        AccidentDamage?: string; // tns:fieldString
        IncreaseInValue?: number; // tns:fieldDecimal
        DatIncreaseInValue?: number; // tns:fieldDecimal
        IncreaseInValueGross?: number; // tns:fieldDecimal
        DatIncreaseInValueGross?: number; // tns:fieldDecimal
        CommentIncreaseInValue?: string; // tns:fieldString
        DecreaseInValue?: number; // tns:fieldDecimal
        DatDecreaseInValue?: number; // tns:fieldDecimal
        DecreaseInValueGross?: number; // tns:fieldDecimal
        DatDecreaseInValueGross?: number; // tns:fieldDecimal
        CommentDecreaseInValue?: string; // tns:fieldString
        TiresMountedValue?: number; // tns:fieldDecimal
        DatTiresMountedValue?: number; // tns:fieldDecimal
        TiresMountedValueGross?: number; // tns:fieldDecimal
        DatTiresMountedValueGross?: number; // tns:fieldDecimal
        TiresUnmountedValue?: number; // tns:fieldDecimal
        DatTiresUnmountedValue?: number; // tns:fieldDecimal
        TiresUnmountedValueGross?: number; // tns:fieldDecimal
        DatTiresUnmountedValueGross?: number; // tns:fieldDecimal
        RepairCosts?: number; // tns:fieldDecimal
        DatRepairCosts?: number; // tns:fieldDecimal
        RepairCostsGross?: number; // tns:fieldDecimal
        DatRepairCostsGross?: number; // tns:fieldDecimal
        ConditionSubTotal1?: number; // tns:fieldDecimal
        ConditionSubTotal1Gross?: number; // tns:fieldDecimal
        ConditionSubTotal2?: number; // tns:fieldDecimal
        ConditionSubTotal2Gross?: number; // tns:fieldDecimal
        ConditionDataAvailable?: boolean; // tns:fieldBoolean
        NextGeneralInspection?: string; // tns:fieldDate
        NextExhaustInspection?: string; // tns:fieldDate
        NextServiceDate?: string; // tns:fieldDate
        LastServiceDate?: string; // tns:fieldDate
        NextServiceMileage?: number; // tns:fieldInteger
        LastServiceMileage?: number; // tns:fieldInteger
        RepairCostsInTradeMargin?: boolean; // tns:fieldBoolean
        ConditionComment?: string; // tns:fieldString
        DamageAmount?: number; // tns:fieldDecimal
        Damage?: string; // tns:fieldString
        DamageExtent?: number; // tns:fieldDecimal
        ConditionRegistrationDate?: string; // tns:fieldDate
        ConditionDeregistrationDate?: string; // tns:fieldDate
        IdentificationProcedureStateOfHealth?: string; // tns:fieldString
        DatBatteryStateOfHealth?: number; // tns:fieldDecimal
        BatteryStateOfHealth?: number; // tns:fieldDecimal
        DatBatteryCorr?: number; // tns:fieldDecimal
        DatBatteryCorrGross?: number; // tns:fieldDecimal
        BatteryCorr?: number; // tns:fieldDecimal
        BatteryCorrGross?: number; // tns:fieldDecimal
    }

    // ContractData
    export interface ContractData {
        ContractNumber?: string; // tns:fieldString
        CustomerNumber?: string; // tns:fieldString
        ContractStartingDate?: string; // tns:fieldDate
        MileageAtContractBegin?: number; // tns:fieldInteger
        ContractDurationInMonths?: number; // tns:fieldInteger
        ContractMileage?: number; // tns:fieldInteger
        ContractEndDate?: string; // tns:fieldDate
        MileageAtContractEnd?: number; // tns:fieldInteger
    }

    // CoolingUnit
    export interface CoolingUnit {
        Manufacturer?: number; // tns:fieldInteger
        ManufacturerName?: string; // tns:fieldString
        MainModel?: number; // tns:fieldInteger
        MainModelName?: string; // tns:fieldString
        InitialRegistration?: string; // tns:fieldDate
        OriginalPrice?: number; // tns:fieldDecimal
        DatOriginalPrice?: number; // tns:fieldDecimal
        OriginalPriceGross?: number; // tns:fieldDecimal
        DatOriginalPriceGross?: number; // tns:fieldDecimal
        GeneralCondition?: number; // tns:fieldDecimal
        DatGeneralCondition?: number; // tns:fieldDecimal
        SalesPrice?: number; // tns:fieldDecimal
        DatSalesPrice?: number; // tns:fieldDecimal
        SalesPriceGross?: number; // tns:fieldDecimal
        DatSalesPriceGross?: number; // tns:fieldDecimal
        DriveType?: string; // tns:fieldString
        Usage?: string; // tns:fieldString
        MinRecommendedBodyLength?: number; // tns:fieldInteger
        DatMinRecommendedBodyLength?: number; // tns:fieldInteger
        MaxRecommendedBodyLength?: number; // tns:fieldInteger
        DatMaxRecommendedBodyLength?: number; // tns:fieldInteger
    }

    // CustomData
    export interface CustomData {
        _attr_name?: string;
        _value: string; // xs:string
    }

    // CustomDataList
    export interface CustomDataList {
        CustomData?: CustomData[];
        _attr_source?: string;
    }

    // DATProcessIdComment
    export interface DATProcessIdComment {
        DATProcessId?: number; // tns:fieldInteger
        Description?: string; // tns:fieldString
        Comment: string; // xs:string
    }

    // DATProcessIdCommentList
    export interface DATProcessIdCommentList {
        DATProcessIdComment: DATProcessIdComment[];
    }

    // DATProcessIdList
    export interface DATProcessIdList {
        DATProcessIdRef?: DATProcessIdRef[];
    }

    // DATProcessIdRef
    export interface DATProcessIdRef {
        DATProcessId?: number; // tns:fieldInteger
    }

    // DamageDetail
    export interface DamageDetail {
        TotalLoss?: boolean; // tns:fieldBoolean
        TotalLossNet?: number; // tns:fieldDecimal
        FireDamage?: boolean; // tns:fieldBoolean
        RepairDuration?: number; // xs:int
        DamagedGlassType?: string; // tns:fieldString
        DamageFrontMiddle?: boolean; // tns:fieldBoolean
        DamageFrontRight?: boolean; // tns:fieldBoolean
        DamageMiddleRight?: boolean; // tns:fieldBoolean
        DamageBehindRight?: boolean; // tns:fieldBoolean
        DamageBehindMiddle?: boolean; // tns:fieldBoolean
        DamageBehindLeft?: boolean; // tns:fieldBoolean
        DamageMiddleLeft?: boolean; // tns:fieldBoolean
        DamageFrontLeft?: boolean; // tns:fieldBoolean
        DamageAllRound?: boolean; // tns:fieldBoolean
        DamageTopAndBottom?: boolean; // tns:fieldBoolean
        DamageOnlyTop?: boolean; // tns:fieldBoolean
        DamageOnlyBottom?: boolean; // tns:fieldBoolean
        EstimatedRepairPrice?: number; // tns:fieldDecimal
    }

    // DamageManagement
    export interface DamageManagement {
        DamageProcess?: DamageProcess;
        DamageDetail?: DamageDetail;
    }

    // DamageProcess
    export interface DamageProcess {
        RepairDeclarationAvailable?: boolean; // tns:fieldBoolean
        SecurityAssignmentAvailable?: boolean; // tns:fieldBoolean
        RepairIntentDeclared?: boolean; // tns:fieldBoolean
        RepairOrderIssued?: boolean; // tns:fieldBoolean
        RegulationAlreadyDone?: boolean; // tns:fieldBoolean
        RepairAlreadyDone?: boolean; // tns:fieldBoolean
        RegulatedByIntermediaries?: boolean; // tns:fieldBoolean
        ClaimsManagementDesired?: boolean; // tns:fieldBoolean
        FictitiousBillingDesired?: boolean; // tns:fieldBoolean
        AgreementsMade?: boolean; // tns:fieldBoolean
        AgreementDescription?: string; // tns:fieldString
        DamageAlreadyVisited?: boolean; // tns:fieldBoolean
        InspectionRequired?: boolean; // tns:fieldBoolean
        InspectionComment?: string; // tns:fieldString
        SubstituteVehicleKind?: string; // tns:fieldString
        VehicleRetrievalRequired?: boolean; // tns:fieldBoolean
        VehicleRetrievalDate?: string; // tns:fieldDateTime
        VehicleRetrievalComment?: string; // tns:fieldString
        ExpertRequired?: boolean; // tns:fieldBoolean
        TarifFeature?: string; // tns:fieldString
        GdvReferenceNumber?: string; // tns:fieldString
    }

    // DamageSegment
    export interface DamageSegment {
        DamageNumber: number; // tns:fieldInteger
        DamageName: string; // tns:fieldString
        calcResult?: calcResult; // tns:calcResult
    }

    // DamageSegmentation
    export interface DamageSegmentation {
        DamageSegment?: DamageSegment[];
    }

    // DeductiblePartsPosition
    export interface DeductiblePartsPosition {
        DATProcessId?: number; // tns:fieldInteger
        PartNumber?: string; // tns:fieldString
        RepairType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        DeductionType?: string; // tns:fieldString
        ValueTotal?: number; // tns:fieldDecimal
        CorrPerc?: number; // tns:fieldDecimal
        ValueTotalCorrected?: number; // tns:fieldDecimal
    }

    // DeductiblePartsPositions
    export interface DeductiblePartsPositions {
        DeductiblePartsPosition?: DeductiblePartsPosition[];
    }

    // DeductionsPositions
    export interface DeductionsPositions {
        DeductiblePartsPosition?: DeductiblePartsPosition[];
    }

    // DentPositionsProtocol
    export interface DentPositionsProtocol {
        DentPositionsProtocolEntry?: DentPositionsProtocolEntry[];
    }

    // DentPositionsProtocolEntry
    export interface DentPositionsProtocolEntry {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        CalculationMethod?: string; // tns:fieldString
        CalculationMethodDescription?: string; // tns:fieldString
        DentsCount?: string; // tns:fieldString
        DentsSize?: string; // tns:fieldString
    }

    // DiscountPosition
    export interface DiscountPosition {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        BaseValue?: number; // tns:fieldDecimal
        CorrectionPercentage?: number; // tns:fieldDecimal
        CorrectionValue?: number; // tns:fieldDecimal
        DiscountKind?: string; // tns:fieldString
        Note?: string; // tns:fieldString
    }

    // DiscountPositions
    export interface DiscountPositions {
        DiscountPosition?: DiscountPosition[];
    }

    // DomusAggregate
    export interface DomusAggregate {
        DomusProcessId?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        Location?: string; // tns:fieldString
        WorkTimeReplace?: number; // tns:fieldDecimal
        WorkTimeOverhaul?: number; // tns:fieldDecimal
        WorkTimeLacquer?: number; // tns:fieldDecimal
        WorkAmountReplace?: number; // tns:fieldDecimal
        WorkAmountOverhaul?: number; // tns:fieldDecimal
        WorkAmountLacquer?: number; // tns:fieldDecimal
        ValueTotal?: number; // tns:fieldDecimal
        DomusAggregateComponents?: DomusAggregateComponents;
    }

    // DomusAggregateComponent
    export interface DomusAggregateComponent {
        DATProcessId?: number; // tns:fieldInteger
        Amount?: number; // tns:fieldDecimal
        DomusProcessId?: string; // tns:fieldString
        Location?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        PartPrice?: number; // tns:fieldDecimal
        ValueTotal?: number; // tns:fieldDecimal
        Rc?: string; // tns:fieldString
        PartNo?: string; // tns:fieldString
    }

    // DomusAggregateComponents
    export interface DomusAggregateComponents {
        DomusAggregateComponent?: DomusAggregateComponent[];
    }

    // DomusAggregates
    export interface DomusAggregates {
        DomusAggregate?: DomusAggregate[];
    }

    // DomusExtraCharge
    export interface DomusExtraCharge {
        Description?: string; // tns:fieldString
        Duration?: number; // tns:fieldDecimal
        ValueTotal?: number; // tns:fieldDecimal
    }

    // DomusExtraCharges
    export interface DomusExtraCharges {
        DomusExtraCharge?: DomusExtraCharge[];
    }

    // DomusLacquerExtraCharge
    export interface DomusLacquerExtraCharge {
        Description?: string; // tns:fieldString
        Duration?: number; // tns:fieldDecimal
        ValueTotal?: number; // tns:fieldDecimal
    }

    // DomusLacquerExtraCharges
    export interface DomusLacquerExtraCharges {
        DomusLacquerExtraCharge?: DomusLacquerExtraCharge[];
    }

    // DomusVehicleData
    export interface DomusVehicleData {
        DomusVehicleType?: string; // tns:fieldString
        DomusManufacturer?: string; // tns:fieldString
        DomusBaseModel?: string; // tns:fieldString
        DomusSubModel?: string; // tns:fieldString
        DomusVehicleTypeName?: string; // tns:fieldString
        DomusManufacturerName?: string; // tns:fieldString
        DomusBaseModelName?: string; // tns:fieldString
        DomusSubModelName?: string; // tns:fieldString
    }

    // Dossier
    export interface Dossier {
        Name?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        UUID?: string; // tns:fieldString
        ExternalId?: string; // tns:fieldString
        IdSD2?: string; // tns:fieldString12
        IdSDo?: number; // tns:fieldInteger
        IdSD3Local?: number; // tns:fieldInteger
        DossierId?: number; // tns:fieldInteger
        IdSD3Network?: number; // tns:fieldInteger
        IdExtern?: string; // tns:fieldString40
        Country: string; // tns:fieldString3
        Language: string; // tns:fieldString5
        DataVersion?: number; // tns:fieldDecimal
        Currency?: string; // tns:fieldString3
        DatCustomerId?: string; // tns:fieldString10
        MerchandId?: number; // tns:fieldDecimal
        DossierType?: string; // tns:fieldString
        DossierOrigin?: string; // tns:fieldString
        CreateDate?: string; // tns:fieldDateTime
        CreateUser?: string; // tns:fieldString
        ChangeDate?: string; // tns:fieldDateTime
        ChangeUser?: string; // tns:fieldString
        CDYear?: number; // tns:fieldInteger
        CDMonth?: number; // tns:fieldInteger
        ProcedureType?: string; // tns:fieldString
        DatCustomerAddress?: address; // tns:address
        Vehicle?: Vehicle;
        Images?: Images;
        ImageList?: ImageList;
        VAT?: VAT;
        Management?: anyType; // xs:anyType
        TradingData?: TradingData;
        Labelling?: Labelling;
        ContractData?: ContractData;
        Valuation?: Valuation;
        Compare?: Compare;
        RepairCalculation?: RepairCalculation;
        RepairOrder?: RepairOrder;
        SparePartPositions?: SparePartPositions;
        MaintenanceIntervals?: MaintenanceIntervals;
        Comments?: Comments;
        Attachments?: Attachments;
        AdditionalVehicles?: AdditionalVehicles;
        ChangeUserId?: number; // tns:fieldInteger
        CreateUserId?: number; // tns:fieldInteger
        WearCalculation?: boolean; // tns:fieldBoolean
        InsuranceClaim?: boolean; // tns:fieldBoolean
        AdditionalServices?: AdditionalServices;
        ProcessManagement?: ProcessManagement;
        TradingActivity?: TradingActivity;
        TradingAdditional?: TradingAdditional;
        MetaPositions?: MetaPositions;
        TelematicData?: TelematicData;
        _attr_overwrite?: boolean;
    }

    // Dossiers
    export interface Dossiers {
        Dossier?: Dossier[];
        _attr_source?: string;
        _attr_type?: string;
    }

    // Email
    export interface Email {
        Subject: string; // tns:fieldString
        From: string; // tns:fieldString
        To?: string; // tns:fieldString
        Body: string; // tns:fieldString
        EmailAttachments?: EmailAttachments[];
    }

    // EmailAttachments
    export interface EmailAttachments {
        Filename: string; // tns:fieldString
        Data: string; // xs:base64Binary
        MimeType: string; // tns:fieldString
        inline: boolean; // xs:boolean
    }

    // EmissionClassItemN
    export interface EmissionClassItemN {
        _attr_type: string;
        _attr_description?: string;
        _attr_obd?: boolean;
    }

    // EmissionClassN
    export interface EmissionClassN {
        EmissionClassItemN?: EmissionClassItemN[];
    }

    // EnVKVAdditionals
    export interface EnVKVAdditionals {
        EnergyCosts?: EnergyCosts;
        CO2Costs?: CO2Costs;
        VehicleTax?: number; // tns:fieldDecimal
        DatVehicleTax?: number; // tns:fieldDecimal
    }

    // EnergyCosts
    export interface EnergyCosts {
        FuelPrice?: number; // tns:fieldDecimal
        NGPrice?: number; // tns:fieldDecimal
        HydrogenPrice?: number; // tns:fieldDecimal
        PowerPrice?: number; // tns:fieldDecimal
        EnergyCostsValue?: number; // tns:fieldDecimal
        ConsumptionPriceYear?: number; // tns:fieldInteger
    }

    // Engine
    export interface Engine {
        EngingeType?: string; // tns:fieldString30
        EngineType?: string; // tns:fieldString30
        CatalyticConverterType?: string; // tns:fieldString30
        GearType?: string; // tns:fieldString2
        FuelMethod?: string; // tns:fieldString
        DatFuelMethod?: string; // tns:fieldString
        EnginePowerKw?: number; // tns:fieldInteger
        DatEnginePowerKw?: number; // tns:fieldInteger
        EnginePowerHp?: number; // tns:fieldInteger
        DatEnginePowerHp?: number; // tns:fieldInteger
        Cylinders?: number; // tns:fieldInteger
        DatCylinders?: number; // tns:fieldInteger
        Capacity?: number; // tns:fieldInteger
        DatCapacity?: number; // tns:fieldInteger
        PollutionClass?: string; // tns:fieldString
        Consumption?: number; // tns:fieldDecimal
        ConsumptionInTown?: number; // tns:fieldDecimal
        ConsumptionOutOfTown?: number; // tns:fieldDecimal
        Co2Emission?: number; // tns:fieldDecimal
        DirectInjection?: string; // tns:fieldString
        EngineClass?: string; // tns:fieldString
        EnginePowerHpManufacturerInformation?: number; // tns:fieldDecimal
        PowerKwPsManual?: string; // tns:fieldString
    }

    // Equipment
    export interface Equipment {
        ColorType?: string; // tns:fieldString
        DatColorType?: string; // tns:fieldString
        Color?: string; // tns:fieldString
        DatColor?: string; // tns:fieldString
        ColorCodeFromVin?: string; // tns:fieldString
        ColorVariant?: string; // tns:fieldString
        DatColorVariant?: string; // tns:fieldString
        LacquerType?: string; // tns:fieldString
        DatLacquerType?: string; // tns:fieldString
        CushionType?: string; // tns:fieldString
        DatCushionType?: string; // tns:fieldString
        CushionTypeName?: string; // tns:fieldString
        DatCushionTypeName?: string; // tns:fieldString
        CushionColorType?: string; // tns:fieldString
        DatCushionColorType?: string; // tns:fieldString
        CushionColor?: string; // tns:fieldString
        DatCushionColor?: string; // tns:fieldString
        EquipmentValue?: number; // tns:fieldDecimal
        EquipmentValueGross?: number; // tns:fieldDecimal
        DatEquipmentValue?: number; // tns:fieldDecimal
        DatEquipmentValueGross?: number; // tns:fieldDecimal
        OriginalEquipmentValue?: number; // tns:fieldDecimal
        OriginalEquipmentValueGross?: number; // tns:fieldDecimal
        DatOriginalEquipmentValue?: number; // tns:fieldDecimal
        DatOriginalEquipmentValueGross?: number; // tns:fieldDecimal
        EquipmentValueType?: string; // tns:fieldString
        SpecialEditionPackageId?: number; // tns:fieldInteger
        SpecialEditionPackageName?: string; // tns:fieldString30
        SpecialEditionPackageNameN?: string; // tns:fieldString200
        SpecialEditionPackageDetails1?: string; // tns:fieldString4000
        SpecialEditionPackageDetails2?: string; // tns:fieldString4000
        SeriesEquipment?: equipSequence; // tns:equipSequence
        DeselectedSeriesEquipment?: equipSequence; // tns:equipSequence
        SpecialModelEquipment?: equipSequence; // tns:equipSequence
        SpecialEquipment?: equipSequence; // tns:equipSequence
        SeriesOrSpecialEquipment?: equipSequence; // tns:equipSequence
        FreeSpecialEquipment?: equipSequence; // tns:equipSequence
        AdditionalEquipment?: equipSequence; // tns:equipSequence
        FlatRateEquipment?: equipSequence; // tns:equipSequence
        DenialCaseEquipment?: equipSequence; // tns:equipSequence
    }

    // EquipmentGroupItem
    export interface EquipmentGroupItem {
        MasterDataGroupId?: number; // tns:fieldInteger
        GroupDescription?: string; // tns:fieldString
        EquipmentList?: EquipmentList;
    }

    // EquipmentItem
    export interface EquipmentItem {
        EquipmentId?: string; // tns:fieldString
        EquipmentDescription?: number; // tns:fieldInteger
        EquipmentOrderNumber?: number; // tns:fieldInteger
    }

    // EquipmentItemN
    export interface EquipmentItemN {
        EquipmentId?: number; // tns:fieldInteger
        EquipmentDescription?: string; // tns:fieldString
        EquipmentOrderNumber?: number; // tns:fieldInteger
    }

    // EquipmentList
    export interface EquipmentList {
        EquipmentItem?: EquipmentItem[];
        EquipmentItemN?: EquipmentItemN[];
    }

    // EquipmentPosition_ContainedEquipmentPositions
    export interface EquipmentPosition_ContainedEquipmentPositions {
        EquipmentPosition?: EquipmentPosition[];
    }

    // EquipmentPosition
    export interface EquipmentPosition {
        InstallDate?: string; // tns:fieldDate
        AgeInMonths?: number; // tns:fieldInteger
        DatAgeInMonths?: number; // tns:fieldInteger
        Deselected?: boolean; // tns:fieldBoolean
        DatEquipmentId?: number; // tns:fieldInteger
        ManufacturerEquipmentId?: string; // tns:fieldString
        ManufacturerDescription?: string; // tns:fieldString
        ValuationControlType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        LongDescription?: string; // tns:fieldString
        FootnoteType?: string; // tns:fieldString
        FootnotePerc?: number; // tns:fieldDecimal
        DatFootnotePerc?: number; // tns:fieldDecimal
        DecreaseType?: string; // tns:fieldString
        DatDecreaseType?: string; // tns:fieldString
        PercentageOfBasePrice?: number; // tns:fieldInteger
        OriginalPrice?: number; // tns:fieldDecimal
        OriginalPriceGross?: number; // tns:fieldDecimal
        OriginalPriceUser?: number; // tns:fieldDecimal
        OriginalPriceGrossUser?: number; // tns:fieldDecimal
        DatResidualValue?: number; // tns:fieldDecimal
        DatResidualValueGross?: number; // tns:fieldDecimal
        ResidualValue?: number; // tns:fieldDecimal
        ResidualValueGross?: number; // tns:fieldDecimal
        Amount?: number; // tns:fieldInteger
        EquipmentGroup?: string; // tns:fieldString
        EquipmentType?: string; // tns:fieldString
        Category?: string; // tns:fieldString
        ManualEntry?: boolean; // tns:fieldBoolean
        ManualAgeEntry?: boolean; // tns:fieldBoolean
        EquipmentClass?: number; // tns:fieldInteger
        ConstructionTimeFrom?: number; // tns:fieldInteger
        EquipmentOrigin?: string; // tns:fieldCharacter
        SeriesEquipmentMissing?: boolean; // tns:fieldBoolean
        PackageEquipmentId?: number; // tns:fieldInteger
        GearBoxType?: string; // tns:fieldString
        NrOfGears?: string; // tns:fieldString
        AddedByLogikCheck?: boolean; // tns:fieldBoolean
        ContainedEquipmentPositions?: EquipmentPosition_ContainedEquipmentPositions;
        DatEquipmentIdReason?: number; // tns:fieldInteger
        DatEquipmentIdReason2?: number; // tns:fieldInteger
        EquipmentClassification?: number; // tns:fieldInteger
        ManualDecreaseType?: string; // tns:fieldString
        VersionAccording1?: number; // tns:fieldInteger
        VersionAccording2?: number; // tns:fieldInteger
        VersionAccording3?: number; // tns:fieldInteger
        VersionAccording4?: number; // tns:fieldInteger
        VersionAccording5?: number; // tns:fieldInteger
    }

    // ExtensionPosition
    export interface ExtensionPosition {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        PartsSum?: number; // tns:fieldDecimal
        LabourTime?: number; // tns:fieldDecimal
        LabourSum?: number; // tns:fieldDecimal
        LacquerTime?: number; // tns:fieldDecimal
        LacquerSum?: number; // tns:fieldDecimal
        LacquerWageSum?: number; // tns:fieldDecimal
        LacquerMaterialSum?: number; // tns:fieldDecimal
        TotalSum?: number; // tns:fieldDecimal
    }

    // ExtensionPositions
    export interface ExtensionPositions {
        ExtensionPosition?: ExtensionPosition[];
    }

    // FeatureList
    export interface FeatureList {
        FeatureName?: string; // xs:string
        TargetMarketplaceFeatureItem?: TargetMarketplaceFeatureItem[];
    }

    // FillingQuantities
    export interface FillingQuantities {
        Fluid: Fluid[];
    }

    // Fluid
    export interface Fluid {
        Capacity: Capacity[];
        Recommendation: Recommendation[];
        _attr_type?: number;
        _attr_desc?: string;
        _attr_code?: string;
    }

    // FollowUpBusiness
    export interface FollowUpBusiness {
        FollowUpBusinessType?: string; // tns:fieldString
        BusinessType?: string; // tns:fieldString
        VehicleNo?: string; // tns:fieldString
        VehicleIdentNo?: string; // tns:fieldString
        KbaKey?: string; // tns:fieldString
        Details?: string; // tns:fieldString
    }

    // Forecast_ForecastItems
    export interface Forecast_ForecastItems {
        ForecastItem?: ForecastItem[];
    }

    // Forecast
    export interface Forecast {
        ForecastType?: string; // tns:fieldString
        PriceType?: string; // tns:fieldString
        IncludeVat?: boolean; // tns:fieldBoolean
        CurveType?: string; // tns:fieldString
        DecreaseType?: string; // tns:fieldString
        StartType?: string; // tns:fieldString
        ValueType?: string; // tns:fieldString
        MileageType?: string; // tns:fieldString
        ConsiderCurrentCondition?: boolean; // tns:fieldBoolean
        ForecastItems?: Forecast_ForecastItems;
    }

    // ForecastItem
    export interface ForecastItem {
        Row?: number; // tns:fieldInteger
        Months?: number; // tns:fieldInteger
        MileagePerYear?: number; // tns:fieldInteger
        MileageTotal?: number; // tns:fieldInteger
        Value?: number; // tns:fieldDecimal
        ValueGross?: number; // tns:fieldDecimal
        Percentage?: number; // tns:fieldDecimal
        PercentageNet?: number; // tns:fieldDecimal
        PercentageGross?: number; // tns:fieldDecimal
        Error?: string; // tns:fieldString
    }

    // Forecasts
    export interface Forecasts {
        Forecast?: Forecast[];
    }

    // Image
    export interface Image {
        Description?: string; // tns:fieldString
        DefaultImage?: boolean; // tns:fieldBoolean
        Image?: fieldBinary; // tns:fieldBinary
        ForValuation?: boolean; // tns:fieldBoolean
        ForRepairCalculation?: boolean; // tns:fieldBoolean
        ForMarketplace?: boolean; // tns:fieldBoolean
        ListLabelVariable?: string; // tns:fieldString
        ImageType?: string; // tns:fieldString
        Origin?: string; // tns:fieldString
        LastModification?: string; // tns:fieldDateTime
        AssignedApplication?: string; // tns:fieldString4
        BitIndicatorImageAlteration?: string; // tns:fieldString8
        ImageNumber?: number; // tns:fieldInteger
        Height?: number; // tns:fieldInteger
        Width?: number; // tns:fieldInteger
        RealFilename?: string; // tns:fieldString
        ImageId?: string; // tns:fieldString
        RelativePath?: string; // tns:fieldString
    }

    // ImageList
    export interface ImageList {
        Image?: Image[];
    }

    // ImageReference
    export interface ImageReference {
        ImageId?: string; // tns:fieldString
        OrderNumber?: number; // tns:fieldInteger
        Image?: string; // tns:fieldString
    }

    // Images
    export interface Images {
        Description?: string; // tns:fieldString
        DefaultImage?: boolean; // tns:fieldBoolean
        Image?: fieldBinary; // tns:fieldBinary
        ForValuation?: boolean; // tns:fieldBoolean
        ForRepairCalculation?: boolean; // tns:fieldBoolean
        ForMarketplace?: boolean; // tns:fieldBoolean
        ListLabelVariable?: string; // tns:fieldString
        ImageType?: string; // tns:fieldString
        Origin?: string; // tns:fieldString
        LastModification?: string; // tns:fieldDateTime
        AssignedApplication?: string; // tns:fieldString4
        BitIndicatorImageAlteration?: string; // tns:fieldString8
        ImageNumber?: number; // tns:fieldInteger
    }

    // IncludedPosition
    export interface IncludedPosition {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
    }

    // IncludedPositions
    export interface IncludedPositions {
        IncludedPosition?: IncludedPosition[];
    }

    // InspectionPosition
    export interface InspectionPosition {
        DATProcessId?: number; // tns:fieldInteger
        Description?: string; // tns:fieldString
        ValuePerUnit?: number; // tns:fieldDecimal
        Amount?: number; // tns:fieldDecimal
        QuantityUnit?: string; // tns:fieldString
        PartNumber?: string; // tns:fieldString
        TaxNeutral?: boolean; // tns:fieldBoolean
        Location?: string; // tns:fieldString
        WorkNumber?: string; // tns:fieldString
        RepairType?: string; // tns:fieldString
        WageType?: string; // tns:fieldString
        WageLevel?: number; // tns:fieldInteger
        Duration?: number; // tns:fieldDecimal
        ValueTotal?: number; // tns:fieldDecimal
    }

    // Interval
    export interface Interval {
        _attr_type?: number;
        _value: string; // xs:string
    }

    // InvoiceDetail
    export interface InvoiceDetail {
        SourceOfInvoice?: string; // tns:fieldString
    }

    // Labelling
    export interface Labelling {
        OriginalPricewithEquipmentNet?: number; // tns:fieldDecimal
        OriginalPricewithEquipmentGross?: number; // tns:fieldDecimal
        SalesDiscountPerc?: number; // tns:fieldDecimal
        SalesDiscountNet?: number; // tns:fieldDecimal
        SalesDiscountGross?: number; // tns:fieldDecimal
        SubtotalNet?: number; // tns:fieldDecimal
        SubtotalGross?: number; // tns:fieldDecimal
        AdditionalEquipmentValueNet?: number; // tns:fieldDecimal
        AdditionalEquipmentValueGross?: number; // tns:fieldDecimal
        TransferCostsNet?: number; // tns:fieldDecimal
        TransferCostsGross?: number; // tns:fieldDecimal
        IncludingTransferCosts?: boolean; // tns:fieldBoolean
        IncidentalCostsNet?: number; // tns:fieldDecimal
        IncidentalCostsGross?: number; // tns:fieldDecimal
        IncludingIncidentalCosts?: boolean; // tns:fieldBoolean
        SalesPriceNet?: number; // tns:fieldDecimal
        SalesPriceGross?: number; // tns:fieldDecimal
        DisplayPriceNet?: number; // tns:fieldDecimal
        DisplayPriceGross?: number; // tns:fieldDecimal
        DeviatingMarketPlacePrice?: boolean; // tns:fieldBoolean
        MinimumSalesPriceNet?: number; // tns:fieldDecimal
        MinimumSalesPriceGross?: number; // tns:fieldDecimal
        ResellerPriceNet?: number; // tns:fieldDecimal
        ResellerPriceGross?: number; // tns:fieldDecimal
        DisplayPriceMarketPlaceNet?: number; // tns:fieldDecimal
        DisplayPriceMarketPlaceGross?: number; // tns:fieldDecimal
    }

    // LacquerAdjustment
    export interface LacquerAdjustment {
        Identifier: string; // tns:fieldString
        Description?: string; // tns:fieldString
    }

    // LacquerAdjustments
    export interface LacquerAdjustments {
        LacquerAdjustment?: LacquerAdjustment[];
    }

    // LacquerConstant
    export interface LacquerConstant {
        Id?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        Price?: number; // tns:fieldDecimal
        Parameter?: string; // tns:fieldString
        Unit?: string; // tns:fieldString
        ConstantType?: number; // tns:fieldInteger
        ValueInPositions?: boolean; // tns:fieldBoolean
        ValueForInformation?: boolean; // tns:fieldBoolean
    }

    // LacquerMaterialGroupSummary
    export interface LacquerMaterialGroupSummary {
        Name?: string; // xs:string
        Units?: number; // tns:fieldDecimal
        PricePerUnit?: number; // tns:fieldDecimal
        Price?: number; // tns:fieldDecimal
    }

    // LacquerPositions
    export interface LacquerPositions {
        LacquerPosition?: LacquerPosition[]; // tns:LacquerPosition
        LacquerWithPrepressPosition?: LacquerPosition[]; // tns:LacquerPosition
        DentCivdPosition?: LacquerPosition[]; // tns:LacquerPosition
        SpotRepairPositions?: SpotRepairPositions;
        OpelGoodwillLacquerPosition?: LacquerPosition[]; // tns:LacquerPosition
    }

    // LacquerSummaryItaly
    export interface LacquerSummaryItaly {
        LacquerSummaryItemItaly?: LacquerSummaryItemItaly[];
    }

    // LacquerSummaryItemItaly
    export interface LacquerSummaryItemItaly {
        Identifier?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        Percentage?: number; // tns:fieldDecimal
        Hours?: number; // tns:fieldDecimal
    }

    // Maintenance
    export interface Maintenance {
        LastServiceKilometer?: number; // tns:fieldInteger
        LastServiceDate?: string; // tns:fieldDate
        NextServiceKilometer?: number; // tns:fieldInteger
        NextServiceDate?: string; // tns:fieldDate
        NextEmissionCheck?: string; // tns:fieldDate
        NextVehicleInspection?: string; // tns:fieldDate
        NextSecurityCheck?: string; // tns:fieldDate
    }

    // MaintenanceInterval
    export interface MaintenanceInterval {
        Description?: string; // tns:fieldString
        DATProcessId?: number; // tns:fieldInteger
        Hint?: string; // tns:fieldString
        MaintenancePositions?: MaintenancePositions[];
    }

    // MaintenanceIntervals
    export interface MaintenanceIntervals {
        MaintenanceInterval?: MaintenanceInterval[];
        _attr_dummy?: string;
    }

    // MaintenancePosition
    export interface MaintenancePosition {
        Order?: number; // tns:fieldDecimal
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        FootNote?: string; // tns:fieldString
        PositionType?: string; // tns:fieldString
        MaterialPositions?: MaterialPositions; // tns:MaterialPositions
    }

    // MaintenancePositions
    export interface MaintenancePositions {
        MaintenancePosition?: MaintenancePosition[];
        _attr_dummy?: string;
    }

    // MarketplaceEquipmentFlag
    export interface MarketplaceEquipmentFlag {
        _attr_mobile?: boolean;
        _attr_autoscout24?: boolean;
        _value: string; // xs:string
    }

    // MarketplaceImageList
    export interface MarketplaceImageList {
        ImageReference?: ImageReference[];
    }

    // MarketplacePreparation_MarketplaceAreaList
    export interface MarketplacePreparation_MarketplaceAreaList {
        AreaItem?: string[]; // xs:string
    }

    // MarketplacePreparation_MarketplaceManufacturerList
    export interface MarketplacePreparation_MarketplaceManufacturerList {
        ManufacturerItem?: string[]; // xs:string
    }

    // MarketplacePreparation_MarketplaceModelGroupList
    export interface MarketplacePreparation_MarketplaceModelGroupList {
        ModelGroupItem?: string[]; // xs:string
    }

    // MarketplacePreparation_MarketplaceModelList
    export interface MarketplacePreparation_MarketplaceModelList {
        ModelItem?: string[]; // xs:string
    }

    // MarketplacePreparation_MarketplaceVehicleSectionList
    export interface MarketplacePreparation_MarketplaceVehicleSectionList {
        VehicleSelectionItem?: string[]; // xs:string
    }

    // MarketplacePreparation_MarketplaceTypeLineList
    export interface MarketplacePreparation_MarketplaceTypeLineList {
        TypeLineItem?: string[]; // xs:string
    }

    // MarketplacePreparation_MarketplaceFuelConsumptionDataList
    export interface MarketplacePreparation_MarketplaceFuelConsumptionDataList {
        FuelConsumptionDataItem?: string[]; // xs:string
    }

    // MarketplacePreparation_MarketplaceFuelAccordingEnVkVList
    export interface MarketplacePreparation_MarketplaceFuelAccordingEnVkVList {
        FuelAccordingEnVkVItem?: string[]; // xs:string
    }

    // MarketplacePreparation_MarketplaceColorInteriorFittingsList
    export interface MarketplacePreparation_MarketplaceColorInteriorFittingsList {
        ColorInteriorFittingsItem?: string[]; // xs:string
    }

    // MarketplacePreparation_MarketplaceAccidentalDamageList
    export interface MarketplacePreparation_MarketplaceAccidentalDamageList {
        AccidentalDamageItem?: string[]; // xs:string
    }

    // MarketplacePreparation_PeriodOfDeliverySpecificList
    export interface MarketplacePreparation_PeriodOfDeliverySpecificList {
        PeriodOfDeliveryItem?: string[]; // xs:string
    }

    // MarketplacePreparation_MarketplaceWarrantyList
    export interface MarketplacePreparation_MarketplaceWarrantyList {
        WarrantyItem?: string[]; // xs:string
    }

    // MarketplacePreparation_MarketplaceEquipmentGroupList
    export interface MarketplacePreparation_MarketplaceEquipmentGroupList {
        EquipmentGroupItem?: EquipmentGroupItem;
    }

    // MarketplacePreparation_MarketplaceEquipmentGroupListN
    export interface MarketplacePreparation_MarketplaceEquipmentGroupListN {
        EquipmentGroupItem?: EquipmentGroupItem[];
    }

    // MarketplacePreparation_MPConfigIntroTextList
    export interface MarketplacePreparation_MPConfigIntroTextList {
        TextItem?: TextItem[];
    }

    // MarketplacePreparation_MPConfigFinalTextList
    export interface MarketplacePreparation_MPConfigFinalTextList {
        TextItem?: TextItem[];
    }

    // MarketplacePreparation_AdvertisementFeatures
    export interface MarketplacePreparation_AdvertisementFeatures {
        FeatureList?: FeatureList[];
    }

    // MarketplacePreparation_MarketplaceEquipmentFlagList
    export interface MarketplacePreparation_MarketplaceEquipmentFlagList {
        MarketplaceEquipmentFlag?: MarketplaceEquipmentFlag[];
    }

    // MarketplacePreparation_QualityLabelsList
    export interface MarketplacePreparation_QualityLabelsList {
        QualityLabel?: string[]; // xs:string
    }

    // MarketplacePreparation_MpEquipmentFlagAttributesData
    export interface MarketplacePreparation_MpEquipmentFlagAttributesData {
        MpEquipmentFlagAttributesEntries?: MpEquipmentFlagAttributesEntries[];
    }

    // MarketplacePreparation
    export interface MarketplacePreparation {
        VehicleNumber?: string; // tns:fieldString
        ExportFlag?: boolean; // tns:fieldBoolean
        MarketplaceVehicleType?: string; // tns:fieldString
        MarketplaceAreaList?: MarketplacePreparation_MarketplaceAreaList;
        MarketplaceManufacturerList?: MarketplacePreparation_MarketplaceManufacturerList;
        MarketplaceModelGroupList?: MarketplacePreparation_MarketplaceModelGroupList;
        MarketplaceModelList?: MarketplacePreparation_MarketplaceModelList;
        MarketplaceVehicleSectionList?: MarketplacePreparation_MarketplaceVehicleSectionList;
        MarketplaceTypeLineList?: MarketplacePreparation_MarketplaceTypeLineList;
        MarketplaceFuelConsumptionDataList?: MarketplacePreparation_MarketplaceFuelConsumptionDataList;
        MarketplaceFuelAccordingEnVkVList?: MarketplacePreparation_MarketplaceFuelAccordingEnVkVList;
        MarketplaceFuelAccordingEnVkVCust?: string; // tns:fieldString
        MarketplaceColorInteriorFittingsList?: MarketplacePreparation_MarketplaceColorInteriorFittingsList;
        GeneralInspection?: string; // tns:fieldString
        GeneralInspectionNewFlag?: boolean; // tns:fieldBoolean
        WithFullServiceHistoryFlag?: boolean; // tns:fieldBoolean
        MarketplaceAccidentalDamageList?: MarketplacePreparation_MarketplaceAccidentalDamageList;
        DescriptionAccidentalDamage?: string; // tns:fieldString
        DealerCarFlag?: boolean; // tns:fieldBoolean
        NonSmokerCarFlag?: boolean; // tns:fieldBoolean
        MarketplaceProductionCountryVersion?: string; // tns:fieldString
        PeriodOfDelivery?: string; // tns:fieldString
        PeriodOfDeliverySpecificList?: MarketplacePreparation_PeriodOfDeliverySpecificList;
        PlannedDeliveryDateCustomer?: string; // tns:fieldDate
        TaxiFlag?: boolean; // tns:fieldBoolean
        E10SuitableFlag?: boolean; // tns:fieldBoolean
        BiodieselSuitableFlag?: boolean; // tns:fieldBoolean
        VegetableOilSuitableFlag?: boolean; // tns:fieldBoolean
        AccessiblyDesignedFlag?: boolean; // tns:fieldBoolean
        MarketplaceWarrantyList?: MarketplacePreparation_MarketplaceWarrantyList;
        EquipmentFormat?: string; // tns:fieldString
        EquipmentSorting?: string; // tns:fieldString
        MarketplaceEquipmentGroupList?: MarketplacePreparation_MarketplaceEquipmentGroupList;
        MarketplaceEquipmentGroupListN?: MarketplacePreparation_MarketplaceEquipmentGroupListN;
        IntroductionText?: string; // tns:fieldString
        FinalText?: string; // tns:fieldString
        MPConfigIntroText?: string; // tns:fieldString
        MPConfigFinalText?: string; // tns:fieldString
        MPConfigIntroTextList?: MarketplacePreparation_MPConfigIntroTextList;
        MPConfigFinalTextList?: MarketplacePreparation_MPConfigFinalTextList;
        AdvertisementFeatures?: MarketplacePreparation_AdvertisementFeatures;
        MarketplaceEquipmentFlagList?: MarketplacePreparation_MarketplaceEquipmentFlagList;
        QualityLabelsList?: MarketplacePreparation_QualityLabelsList;
        MpEquipmentFlagAttributesData: MarketplacePreparation_MpEquipmentFlagAttributesData;
        EnVKVAdditionals?: EnVKVAdditionals;
    }

    // MaterialPosition_SparePartSubPositions
    export interface MaterialPosition_SparePartSubPositions {
        MaterialPosition?: MaterialPosition[];
    }

    // MaterialPosition
    export interface MaterialPosition {
        DATProcessId?: number; // tns:fieldInteger
        DomusProcessId?: string; // tns:fieldString
        Location?: string; // tns:fieldString
        PartNumber?: string; // tns:fieldString
        PartNumberOrigin?: string; // tns:fieldString
        DATPartNumber?: string; // tns:fieldString
        PartState?: string; // tns:fieldString
        PartPriceState?: string; // tns:fieldString
        PartUnitPriceState?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        Amount?: number; // tns:fieldDecimal
        AmountState?: string; // tns:fieldString
        ValuePerUnit?: number; // tns:fieldDecimal
        ValueTotal?: number; // tns:fieldDecimal
        ManualPosition?: boolean; // tns:fieldBoolean
        ManualAmount?: boolean; // tns:fieldBoolean
        ManualPrice?: boolean; // tns:fieldBoolean
        PartNoValidForOrder?: boolean; // tns:fieldBoolean
        PartValiditySuspect?: number; // tns:fieldDecimal
        PriceDerived?: boolean; // tns:fieldBoolean
        UsedPart?: boolean; // tns:fieldBoolean
        LastUPE?: boolean; // tns:fieldBoolean
        Includes?: string; // tns:fieldString
        AdditionalDesc?: string; // tns:fieldString
        AmountDesc?: string; // tns:fieldString
        CorrPerc?: number; // tns:fieldDecimal
        ValueTotalCorrected?: number; // tns:fieldDecimal
        FootNote?: string; // tns:fieldString
        ManualPriceRequired?: boolean; // tns:fieldBoolean
        IsRepairSet?: boolean; // tns:fieldBoolean
        RepairSetProcessId?: number; // tns:fieldInteger
        RequiredByProcessId?: number; // tns:fieldInteger
        IsSpecific?: boolean; // tns:fieldBoolean
        IncludedPositions?: IncludedPositions;
        Extended?: boolean; // tns:fieldBoolean
        IncludedInCalculation?: boolean; // tns:fieldBoolean
        MaterialUnit?: string; // tns:fieldString
        MaterialUnitAbbr?: string; // tns:fieldString
        MaterialUnitMultiplier?: number; // tns:fieldDecimal
        optionalPositionIncluded?: boolean; // tns:fieldBoolean
        PriceOrigin?: string; // tns:fieldString
        TimeOrigin?: string; // tns:fieldString
        SparePartHistoryPositions?: spHistPositionsSeq; // tns:spHistPositionsSeq
        PartOrigin?: string; // tns:fieldString
        FlagOrigin?: string; // tns:fieldString
        spoTmpDescription?: string; // tns:fieldString
        spoTmpItemNumber?: string; // tns:fieldString
        spoDatItemNumber?: string; // tns:fieldString
        spoTmpPrice?: number; // tns:fieldDecimal
        spoTmpManualPartNumber?: boolean; // tns:fieldBoolean
        Length?: number; // tns:fieldDecimal
        Width?: number; // tns:fieldDecimal
        SparePartSubPositions?: MaterialPosition_SparePartSubPositions;
        WearRus?: MTPLWearRus; // tns:MTPLWearRus
        Predefined?: boolean; // tns:fieldBoolean
        Manufacturer?: string; // tns:fieldString
        Dimension?: string; // tns:fieldString
        ExternalId?: string; // tns:fieldString
    }

    // MetaPosition
    export interface MetaPosition {
        _attr_key: string;
        _attr_value: string;
    }

    // MetaPositions
    export interface MetaPositions {
        MetaPosition?: MetaPosition[];
    }

    // MpEquipmentFlagAttributesEntries
    export interface MpEquipmentFlagAttributesEntries {
        MpEquipmentFlagAttributesEntry?: MpEquipmentFlagAttributesEntry[];
        _attr_type: string;
    }

    // MpEquipmentFlagAttributesEntry
    export interface MpEquipmentFlagAttributesEntry {
        _attr_key: string;
        _attr_value: string;
    }

    // OriginalPriceInfo
    export interface OriginalPriceInfo {
        OriginalPriceNet?: number; // tns:fieldDecimal
        OriginalPriceVATRate?: number; // tns:fieldDecimal
        OriginalPriceNoVA?: number; // tns:fieldDecimal
        OriginalPriceNoVARate?: number; // tns:fieldDecimal
        DatOriginalPriceNoVARate?: number; // tns:fieldDecimal
        OriginalPriceBonus?: number; // tns:fieldDecimal
        OriginalPriceMalus?: number; // tns:fieldDecimal
        RegistrationTaxRate?: number; // tns:fieldDecimal
        RegistrationTax?: number; // tns:fieldDecimal
        TransportationCosts?: number; // tns:fieldDecimal
        OriginalPriceGross?: number; // tns:fieldDecimal
    }

    // Parameters_ValueInfluencingFactors
    export interface Parameters_ValueInfluencingFactors {
        ValueInfluencingFactor?: ValueInfluencingFactor[];
    }

    // Parameters
    export interface Parameters {
        DaysOnLot?: number; // tns:fieldInteger
        DatDaysOnLot?: number; // tns:fieldInteger
        TonnageClass?: string; // tns:fieldString2
        DatTonnageClass?: string; // tns:fieldString2
        ValueInfluencingFactors?: Parameters_ValueInfluencingFactors;
    }

    // PlanData_ActualRepairCostList
    export interface PlanData_ActualRepairCostList {
        ActualRepairCost?: ActualRepairCost[];
    }

    // PlanData
    export interface PlanData {
        ProjectedAcceptanceDate?: string; // tns:fieldDate
        ProjectedSalesDate?: string; // tns:fieldDate
        WorkshopInDate?: string; // tns:fieldDate
        ActualAcceptanceDate?: string; // tns:fieldDate
        ActualSalesDate?: string; // tns:fieldDate
        WorkshopOutDate?: string; // tns:fieldDate
        RepairCostType?: string; // tns:fieldString
        SalesPreparationDate?: string; // tns:fieldDate
        Location?: string; // tns:fieldString
        OwnType?: string; // tns:fieldString
        ActualRepairCostList?: PlanData_ActualRepairCostList;
    }

    // PositionItaly
    export interface PositionItaly {
        Type?: string; // tns:fieldString
        Position?: number; // tns:fieldInteger
        DATProcessId?: number; // tns:fieldInteger
        PartCodeItaly?: string; // tns:fieldString
        PartNumber?: string; // tns:fieldString
        Amount?: number; // tns:fieldDecimal
        Description?: string; // tns:fieldString
        Location?: string; // tns:fieldString
        WorkTimeReplace?: number; // tns:fieldDecimal
        WorkTimeOverhaul?: number; // tns:fieldDecimal
        WorkTimeMechanic?: number; // tns:fieldDecimal
        WorkTimeLacquer?: number; // tns:fieldDecimal
        WorkLevelReplace?: string; // tns:fieldString
        WorkLevelOverhaul?: string; // tns:fieldString
        WorkLevelMechanic?: string; // tns:fieldString
        WorkLevelLacquer?: string; // tns:fieldString
        WorkLevelReplace2?: string; // tns:fieldString
        WorkLevelOverhaul2?: string; // tns:fieldString
        WorkLevelLacquer2?: string; // tns:fieldString
        FlagValueParts?: string; // tns:fieldString
        ValueParts?: number; // tns:fieldDecimal
        PartDiscountPerc?: number; // tns:fieldDecimal
        PartDiscountValue?: number; // tns:fieldDecimal
        PartDiscountType?: string; // tns:fieldCharacter
        PartDiscountAsterisk?: string; // tns:fieldCharacter
        ValuePartsCorrected?: number; // tns:fieldDecimal
        ParentPosition?: number; // tns:fieldInteger
    }

    // PositionsItaly
    export interface PositionsItaly {
        PositionItaly?: PositionItaly[];
    }

    // PositionsWithMeasuresProtocol
    export interface PositionsWithMeasuresProtocol {
        PositionsWithMeasuresProtocolEntry?: PositionsWithMeasuresProtocolEntry[];
    }

    // PositionsWithMeasuresProtocolEntry
    export interface PositionsWithMeasuresProtocolEntry {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        Length?: number; // tns:fieldDecimal
        With?: number; // tns:fieldDecimal
        MeasurementUnit?: string; // tns:fieldString
    }

    // PriceCalculation
    export interface PriceCalculation {
        SalesPriceNet?: number; // tns:fieldDecimal
        SalesPriceGross?: number; // tns:fieldDecimal
        PurchasePriceNet?: number; // tns:fieldDecimal
        PurchasePriceGross?: number; // tns:fieldDecimal
        VehicleNewPriceNet?: number; // tns:fieldDecimal
        VehicleNewPriceGross?: number; // tns:fieldDecimal
        ActualRepairCosts?: number; // tns:fieldDecimal
        DifferenceRepairCosts?: number; // tns:fieldDecimal
        LabelPriceNet?: number; // tns:fieldDecimal
        LabelPriceGross?: number; // tns:fieldDecimal
        SalesPriceMinimumNet?: number; // tns:fieldDecimal
        SalesPriceMinimumGross?: number; // tns:fieldDecimal
        ResellerSalesPriceNet?: number; // tns:fieldDecimal
        ResellerSalesPriceGross?: number; // tns:fieldDecimal
        NominalDaysOnLot?: number; // tns:fieldDecimal
        DatNominalDaysOnLot?: number; // tns:fieldDecimal
    }

    // PriceCorrectionsProtocol
    export interface PriceCorrectionsProtocol {
        PriceCorrectionsProtocolEntry?: PriceCorrectionsProtocolEntry[];
    }

    // PriceCorrectionsProtocolEntry
    export interface PriceCorrectionsProtocolEntry {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        PriceCorrection?: number; // tns:fieldDecimal
    }

    // PrivatePolicy
    export interface PrivatePolicy {
        Address?: string; // tns:fieldString
        PolicyDate?: string; // tns:fieldDate
        EMail?: string; // tns:fieldString
        PhoneBusiness?: string; // tns:fieldString
        PhoneMobile?: string; // tns:fieldString
        PhonePrivate?: string; // tns:fieldString
        Time?: string; // tns:fieldDateTime
        User?: string; // tns:fieldString
        UserNumber?: number; // tns:fieldInteger
        UserShort?: string; // tns:fieldString
    }

    // ProcedureRelatedParameter
    export interface ProcedureRelatedParameter {
        _attr_factor: string;
        _attr_attribute: string;
        _attr_type: string;
        _attr_mode?: string;
        _attr_default?: string;
        _attr_description?: string;
        _value: string; // xs:string
    }

    // ProcedureRelatedParameters
    export interface ProcedureRelatedParameters {
        ProcedureRelatedParameter?: ProcedureRelatedParameter[];
    }

    // ProcessManagement
    export interface ProcessManagement {
        CustomDataList?: CustomDataList;
        DamageManagement?: DamageManagement;
        InvoiceDetail?: InvoiceDetail;
        AdditionalInsuranceData?: AdditionalInsuranceData;
    }

    // Prospects
    export interface Prospects {
        Prospect?: address[]; // tns:address
    }

    // ProtocolEntry
    export interface ProtocolEntry {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
        LacquerLevel?: number; // tns:fieldInteger
        Description?: string; // tns:fieldString
        Logic?: string; // tns:fieldString
    }

    // ProtocolEntryWithRemoval
    export interface ProtocolEntryWithRemoval {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
        LacquerLevel?: number; // tns:fieldInteger
        Description?: string; // tns:fieldString
        ContainedInDATProcessId?: number; // tns:fieldInteger
        ContainedInRepairType?: string; // tns:fieldString
        ContainedInLacquerLevel?: number; // tns:fieldInteger
        Logic?: string; // tns:fieldString
    }

    // ProtocolHint
    export interface ProtocolHint {
        Identifier?: string; // tns:fieldString
        Description?: string; // tns:fieldString
    }

    // ProtocolHints
    export interface ProtocolHints {
        ProtocolHint?: ProtocolHint[];
    }

    // Purchase
    export interface Purchase {
        CustomerId?: string; // tns:fieldString10
        BuyerId?: string; // tns:fieldString10
        VehicleGroup?: string; // tns:fieldString
        PurchaseDate?: string; // tns:fieldDateTime
        PurchasePriceNet?: number; // tns:fieldDecimal
        PurchasePriceGross?: number; // tns:fieldDecimal
        TaxationType?: string; // tns:fieldString
        AcceptanceDate?: string; // tns:fieldDateTime
        AcceptanceDetails?: string; // tns:fieldString
        MileageExpected?: number; // tns:fieldInteger
        DeregistrationDate?: string; // tns:fieldDateTime
        ResidualWarrantyType?: string; // tns:fieldString
        ResidualWarrantyUntil?: string; // tns:fieldDateTime
        ResidualWarrantyValueNet?: number; // tns:fieldDecimal
        ResidualWarrantyValueGross?: number; // tns:fieldDecimal
        PaymentAgreements?: string; // tns:fieldString
        OtherAgreements?: string; // tns:fieldString
        ProvisionOn?: string; // tns:fieldDateTime
        UserId?: number; // tns:fieldInteger
        AcceptanceType?: string; // tns:fieldString
    }

    // PurchaseOffer
    export interface PurchaseOffer {
        PurchasePriceNet?: number; // tns:fieldDecimal
        PurchasePriceGross?: number; // tns:fieldDecimal
        PurchaseOfferNumber?: string; // tns:fieldString
        OfferDate?: string; // tns:fieldDate
        ResubmissionDate?: string; // tns:fieldDate
        OfferedBy?: string; // tns:fieldString
        Titles?: string; // tns:fieldString
        Comments?: string; // tns:fieldString
    }

    // Recommendation
    export interface Recommendation {
        Usage: Usage[];
        Interval: Interval[];
        Product: string[]; // xs:string
    }

    // RegistrationData
    export interface RegistrationData {
        KbaCode?: string; // tns:fieldString8
        KbaStructurePollution?: string; // tns:fieldString30
        KbaEngineType?: string; // tns:fieldString2
        LicenseNumber?: string; // tns:fieldString30
        BaseNumber?: string; // tns:fieldString9
        TypeNoteNumber?: string; // tns:fieldString6
        NationalCodeAustria?: string; // tns:fieldString6
        OpponentLicenseNumber?: string; // tns:fieldString30
        LicenseNumberSale?: string; // tns:fieldString
        RegistrationNumber?: string; // tns:fieldString30
        SuspensionDate?: string; // tns:fieldDate
        CNIT?: string; // tns:fieldString
        LicenseNumberAccident?: string; // tns:fieldString
        ManufacturerName?: string; // tns:fieldString30
        MotorIndicator?: string; // tns:fieldString6
        RegistrationDocumentNumber?: string; // tns:fieldString30
        LicenseNumberType?: string; // tns:fieldString80
        SeasonalLicensePlateMonthFrom?: number; // tns:fieldInteger
        SeasonalLicensePlateMonthTo?: number; // tns:fieldInteger
    }

    // RepairCalculation
    export interface RepairCalculation {
        Comment?: string; // tns:fieldString
        ExternalId?: string; // tns:fieldString
        Vehicle?: Vehicle;
        RepairWages?: RepairWages;
        RepairParameters?: RepairParameters;
        ProcedureRelatedParameters?: ProcedureRelatedParameters;
        RepairPositions?: RepairPositions;
        CalcResultToUse?: string; // tns:fieldString
        CalcEngine?: string; // tns:fieldString
        CalcSource?: string; // tns:fieldString
        CalcResultCommon?: calcResult; // tns:calcResult
        CalcResultGlass?: calcResult; // tns:calcResult
        CalcResultOptimized?: calcResult; // tns:calcResult
        CalcResultExtension?: calcResult; // tns:calcResult
        CalcResultItaly?: calcResultItaly; // tns:calcResultItaly
        CalcResultItalyOptimized?: calcResultItaly; // tns:calcResultItaly
        CalcResultItalyGlass?: calcResultItaly; // tns:calcResultItaly
        CalcResultDomus?: domusCalcResult; // tns:domusCalcResult
        CalcResultDomusOptimized?: domusCalcResult; // tns:domusCalcResult
        CalcResultDomusGlass?: domusCalcResult; // tns:domusCalcResult
        CalcResultCustomerRanking?: calcResult; // tns:calcResult
        CalcResultMaintenance?: calcResult; // tns:calcResult
        CalcResultsHistory?: CalcResultsHistory;
        CalcResultSPO?: calcResult; // tns:calcResult
        DiscountPositions?: DiscountPositions;
        CalculationSummary?: CalculationSummary; // tns:CalculationSummary
        SurchargeSettings?: SurchargeSettings;
        SettingsParameters?: SettingsParameters;
    }

    // RepairOrder
    export interface RepairOrder {
        DamageDate?: string; // tns:fieldDateTime
        RepairCoverage?: string; // xs:string
        OrderNumber?: string; // tns:fieldString
        InvoiceNumber?: string; // tns:fieldString
        JobNumber?: string; // tns:fieldString
        DamageNumber?: string; // tns:fieldString
        PolicyNumber?: string; // tns:fieldString
        Retention?: boolean; // tns:fieldBoolean
        RetentionAmount?: number; // tns:fieldDecimal
        BillingCategory?: number; // tns:fieldDecimal
        InsuranceId?: string; // tns:fieldString
        InsuranceGroupId?: string; // tns:fieldString
        ServiceProviderId?: string; // tns:fieldString
        InsuranceType?: string; // tns:fieldString
        LossLocation?: string; // tns:fieldString
        DamageType?: number; // tns:fieldInteger
        InspectionDate?: string; // tns:fieldDate
        DeclarationOfAssignment?: boolean; // tns:fieldBoolean
        InsuranceCase?: boolean; // tns:fieldBoolean
        CreationDateTime?: string; // tns:fieldDateTime
        AdditionalData?: AdditionalData;
        MetaPositions?: MetaPositions;
        InsuranceNumber?: string; // tns:fieldString
        InsuranceAgency?: string; // tns:fieldString
        TypeOfInsurance?: number; // tns:fieldInteger
        InvoiceDate?: string; // tns:fieldDateTime
        CountryFlagDamageEvent?: string; // tns:fieldString3
        InsuranceName?: string; // tns:fieldString40
        TokenContributionInInsuranceCase?: string; // tns:fieldString
        Deleted?: boolean; // tns:fieldBoolean
        GDVRoutingType?: string; // tns:fieldString
        Comment?: string; // tns:fieldString
        DATProcessIdCommentList?: DATProcessIdCommentList;
        EstimatedRepairTimeInDays?: number; // tns:fieldInteger
        ReplacementCar?: boolean; // tns:fieldBoolean
        ReplacementCarTimeInDays?: number; // tns:fieldInteger
        ReplacementCarCosts?: number; // tns:fieldDecimal
        OtherCostsDescription?: string; // tns:fieldString
        OtherCosts?: number; // tns:fieldDecimal
        TowingRecoveryCosts?: number; // tns:fieldDecimal
        OtherDeductionsDescription?: string; // tns:fieldString
        OtherDeductions?: number; // tns:fieldDecimal
        RepairAgreementExclVAT?: number; // tns:fieldDecimal
    }

    // RepairParameters
    export interface RepairParameters {
        SeriesSpecific?: boolean; // tns:fieldBoolean
        PhantomCalculation?: boolean; // tns:fieldBoolean
        PartsCorrection?: number; // tns:fieldDecimal
        PartsDiscountPercentage?: number; // tns:fieldDecimal
        PartsDiscountAmount?: number; // tns:fieldDecimal
        PartsDiscountPercentageForOpt?: number; // tns:fieldDecimal
        PartsDiscountAmountForOpt?: number; // tns:fieldDecimal
        PartsFlatrate?: number; // tns:fieldDecimal
        ConsumablesType?: number; // tns:fieldInteger
        ConsumablesFlatAmount?: number; // tns:fieldDecimal
        ConsumablesPercentage?: number; // tns:fieldDecimal
        ConsumablesPercentageForOpt?: number; // tns:fieldDecimal
        ProcurementCosts?: number; // tns:fieldDecimal
        ProcurementCostsMaterial?: number; // tns:fieldDecimal
        ProcurementCostsBodyInWhite?: number; // tns:fieldDecimal
        ProcurementCostsAlignmentBrackets?: number; // tns:fieldDecimal
        ProcurementCostsPercentage?: number; // tns:fieldDecimal
        ProcurementCostsPercentageBodyInWhite?: number; // tns:fieldDecimal
        ProcurementCostsMax?: number; // tns:fieldDecimal
        ProcurementCostsBodyInWhiteMax?: number; // tns:fieldDecimal
        PriceDate?: string; // tns:fieldDate
        LacquerFremdleistungGlobal?: string; // tns:fieldString
        LacquerMethod?: string; // tns:fieldString
        LacquerTypeId?: string; // tns:fieldString
        LacquerType?: string; // tns:fieldString
        LacquerTypeLayers?: number; // tns:fieldInteger
        LacquerDismountedPrelacquer?: boolean; // tns:fieldBoolean
        LacquerPlasticMounted?: boolean; // tns:fieldBoolean
        LacquerWithoutDisassembly?: boolean; // tns:fieldBoolean
        LacquerWithForcedDisassembly?: boolean; // tns:fieldBoolean
        LacquerReducedLeadTime?: boolean; // tns:fieldBoolean
        LacquerWithRack?: boolean; // tns:fieldBoolean
        LacquerAdditionTwoColor?: boolean; // tns:fieldBoolean
        LacquerAdditionMattpaint?: boolean; // tns:fieldBoolean
        LacquerAdjustmentMixingUnit?: boolean; // tns:fieldBoolean
        LacquerAdjustmentMixingColours?: number; // tns:fieldInteger
        LacquerAdjustmentExemplarySheets?: number; // tns:fieldInteger
        LacquerCompleteFlatrate?: number; // tns:fieldDecimal
        LacquerWorkFlatrate?: number; // tns:fieldDecimal
        LacquerMaterialFlatrate?: number; // tns:fieldDecimal
        SpecialLacquerAward?: number; // tns:fieldDecimal
        LacquerMaterialPercentage?: number; // tns:fieldDecimal
        LacquerMaterialIndex?: number; // tns:fieldInteger
        LacquerMaterialChargePerPoint?: number; // tns:fieldDecimal
        LacquerMaterialPriceCategory?: number; // tns:fieldInteger
        LacquerMaterialPrintDescr?: boolean; // tns:fieldBoolean
        LacquerLeadTimeMetal?: number; // tns:fieldDecimal
        LacquerLeadTimePlastic?: number; // tns:fieldDecimal
        LacquerLeadTimePercentage?: number; // tns:fieldDecimal
        LacquerDisposalCosts?: number; // tns:fieldDecimal
        LacquerCoveringPanes?: number; // tns:fieldInteger
        LacquerCoveringDismountedPlastic?: number; // tns:fieldInteger
        LacquerMattBlackWindowFrames?: number; // tns:fieldInteger
        LacquerWageDiscountAmount?: number; // tns:fieldDecimal
        LacquerWageDiscountPercentage?: number; // tns:fieldDecimal
        LacquerWageDiscountAmountForOpt?: number; // tns:fieldDecimal
        LacquerWageDiscountPercentageForOpt?: number; // tns:fieldDecimal
        LacquerMaterialDiscountAmount?: number; // tns:fieldDecimal
        LacquerMaterialDiscountPercentage?: number; // tns:fieldDecimal
        LacquerMaterialDiscountAmountForOpt?: number; // tns:fieldDecimal
        LacquerMaterialDiscountPercentageForOpt?: number; // tns:fieldDecimal
        LacquerCompleteDiscountAmount?: number; // tns:fieldDecimal
        LacquerCompleteDiscountPercentage?: number; // tns:fieldDecimal
        LacquerCompleteDiscountAmountForOpt?: number; // tns:fieldDecimal
        LacquerCompleteDiscountPercentageForOpt?: number; // tns:fieldDecimal
        LacquerPaintWorkDiscountPercentage?: number; // tns:fieldDecimal
        LacquerTimeUnitSystem?: string; // tns:fieldString
        LacquerTimeUnitsPerHour?: number; // tns:fieldInteger
        TimeUnitSystem?: string; // tns:fieldString
        TimeUnitsPerHour?: number; // tns:fieldInteger
        TimeUnitsOfManufacturer?: boolean; // tns:fieldBoolean
        RepairWageDiscountPercentage?: number; // tns:fieldDecimal
        RepairWageDiscountAmount?: number; // tns:fieldDecimal
        RepairWageDiscountPercentageForOpt?: number; // tns:fieldDecimal
        RepairWageDiscountAmountForOpt?: number; // tns:fieldDecimal
        RepairWageFlatrate?: number; // tns:fieldDecimal
        LongWorkDescriptions?: boolean; // tns:fieldBoolean
        AllIncludedWork?: boolean; // tns:fieldBoolean
        FeeAE?: number; // tns:fieldDecimal
        WithDomusCalculation?: boolean; // tns:fieldBoolean
        PriceLevel?: number; // tns:fieldInteger
        LacquerDiscountFactor?: number; // tns:fieldDecimal
        WearBodyAge?: number; // tns:fieldDecimal
        WearBodyCorrosion?: number; // tns:fieldDecimal
        WearTyreMinSize?: number; // tns:fieldDecimal
        WearTyreActualSize?: number; // tns:fieldDecimal
        WearTyreSize?: number; // tns:fieldDecimal
        WearTyreAge?: string; // tns:fieldString
        WearBatAge?: number; // tns:fieldDecimal
        WearBatTime?: number; // tns:fieldDecimal
        WearPlasticAge?: number; // tns:fieldDecimal
        WearCompAge?: number; // tns:fieldDecimal
        WearCompMileage?: number; // tns:fieldDecimal
        WearCompTypeId?: number; // tns:fieldInteger
        WearCompTypeDesc?: string; // tns:fieldString
        CalculationType?: string; // tns:fieldCharacter
        ConsumablesOfWagePerc?: number; // tns:fieldDecimal
        ConsumablesPercentageSmallPartsForOpt?: number; // tns:fieldDecimal
        IndicatorDataUsedAsPattern?: boolean; // tns:fieldBoolean
        IndicatorIsGlassCalculation?: boolean; // tns:fieldBoolean
        IndicatorGlassCalculationWithoutWork?: boolean; // tns:fieldBoolean
        IndicatorOptBodyShell?: boolean; // tns:fieldBoolean
        IndicatorTimeUnitsInHours?: boolean; // tns:fieldBoolean
        IndicatorWarrantyCalculation?: boolean; // tns:fieldBoolean
        IndicatorWithoutBodyCavityAndUnderseal?: boolean; // tns:fieldBoolean
        LevelOneE?: number; // tns:fieldDecimal
        LevelOneJ?: number; // tns:fieldDecimal
        LevelOneM?: number; // tns:fieldDecimal
        LevelThree?: number; // tns:fieldDecimal
        LevelTwo?: number; // tns:fieldDecimal
        MaterialConstantPartsIntegrated?: number; // tns:fieldDecimal
        MaterialConstantPartsRemoved?: number; // tns:fieldDecimal
        MaterialConstantByAmount?: number; // tns:fieldDecimal
        MaterialCostsPerUnit1CoatPainting?: number; // tns:fieldDecimal
        ProcurementCostsPercBodyShellForOpt?: number; // tns:fieldDecimal
        RentingCostsAlignmentBrackets?: number; // tns:fieldDecimal
        SparePartsDisposalCostsPerc?: number; // tns:fieldDecimal
        CalculationWithoutConstants?: boolean; // tns:fieldBoolean
        LacquerScratchResistant?: boolean; // tns:fieldBoolean
        PearlEffectSurcharge?: number; // tns:fieldDecimal
        ColorCount?: number; // tns:fieldInteger
        DMSCalculation?: boolean; // tns:fieldBoolean
        DMSPaintWPN?: string; // tns:fieldString
        ExternalProvider?: string; // tns:fieldString
        ExternalProviderStatus?: number; // tns:fieldInteger
        TotalPredamageAmount?: number; // tns:fieldDecimal
        TotalNFOAmount?: number; // tns:fieldDecimal
        TotalNFOPercent?: number; // tns:fieldDecimal
        TotalValueImprovementAmount?: number; // tns:fieldDecimal
        AdditionalCostsFlatAmount?: number; // tns:fieldDecimal
        AdditionalCostsPercent?: number; // tns:fieldDecimal
        DisposalCostsSparePartsPercentage?: number; // tns:fieldDecimal
        LacquerAdjustments?: LacquerAdjustments;
        PartsSurchargeInProtocol?: boolean; // tns:fieldBoolean
        PartsSurchargeSeparated?: boolean; // tns:fieldBoolean
        AztFourLayerLacquerMode?: string; // tns:fieldString
        AztThreeLayerLacquerMode?: string; // tns:fieldString
        LacquerWaterBasedPaintSurcharge?: boolean; // tns:fieldBoolean
    }

    // RepairPosition
    export interface RepairPosition {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        DescriptionId?: number; // tns:fieldInteger
        IsManualDescription?: boolean; // tns:fieldBoolean
        PartDescription?: string; // tns:fieldString
        ParentDATProcessId?: number; // tns:fieldInteger
        ParentRepairType?: string; // tns:fieldString
        ConstructionGroupId?: number; // tns:fieldInteger
        ConstructionGroup?: string; // tns:fieldString
        ConstructionGroupPolygon?: number; // tns:fieldInteger
        PositionEntryType?: string; // tns:fieldString
        AdditionalInformation?: string; // tns:fieldString
        CrossSeries?: boolean; // tns:fieldBoolean
        IsRepairExtension?: boolean; // tns:fieldBoolean
        IsForGlass?: boolean; // tns:fieldBoolean
        OptimizationHandling?: string; // tns:fieldString
        ConstructionType?: string; // tns:fieldString
        SparePartDiscount?: number; // tns:fieldDecimal
        SparePartPriceCorrection?: number; // tns:fieldDecimal
        SparePartNumber?: string; // tns:fieldString
        SparePartNumberOrigin?: string; // tns:fieldString
        SparePartNumberManufacturer?: string; // tns:fieldString
        SparePartPrice?: number; // tns:fieldDecimal
        SparePartAmount?: number; // tns:fieldDecimal
        SparePartUsed?: boolean; // tns:fieldBoolean
        SparePartExchange?: boolean; // tns:fieldBoolean
        SparePartSupplyDescription?: string; // tns:fieldString
        QuantityUnit?: string; // tns:fieldString
        WorkPositionNumber?: string; // tns:fieldString
        WorkPositionNumberOrigin?: string; // tns:fieldString
        WorkPositionNumberManufacturer?: string; // tns:fieldString
        WorkDifficultyLevel?: number; // tns:fieldInteger
        WorkType?: string; // tns:fieldString
        WorkTime?: number; // tns:fieldDecimal
        WorkLevel?: string; // tns:fieldString
        WorkPrice?: number; // tns:fieldDecimal
        WorkPriceOrigin?: string; // tns:fieldString
        WorkExtensionTime?: number; // tns:fieldDecimal
        WorkExtensionPrice?: number; // tns:fieldDecimal
        LacquerLevel?: string; // tns:fieldString
        LacquerLevelId?: number; // tns:fieldInteger
        LacquerLevelBaseId?: number; // tns:fieldInteger
        LacquerPercentage?: number; // tns:fieldDecimal
        LacquerCountSpotRepair?: number; // tns:fieldInteger
        LacquerScratchProofFinishTime?: number; // tns:fieldDecimal
        LacquerPrice?: number; // tns:fieldDecimal
        LacquerMaterialPrice?: number; // tns:fieldDecimal
        LacquerMaterialPriceOrigin?: string; // tns:fieldString
        LacquerWorkPrice?: number; // tns:fieldDecimal
        LacquerWorkPriceOrigin?: string; // tns:fieldString
        LacquerMaterialUnitsNumber?: number; // tns:fieldInteger
        LacquerBlending?: boolean; // tns:fieldBoolean
        PlasticDamageSpotsEasy?: number; // tns:fieldInteger
        PlasticDamageSpotsAverage?: number; // tns:fieldInteger
        PlasticDamageSpotsHard?: number; // tns:fieldInteger
        OverhaulLocation?: string; // tns:fieldString
        DentsCount?: number; // tns:fieldInteger
        DentsOver20mmCount?: number; // tns:fieldInteger
        DentsSize?: number; // tns:fieldInteger
        DentsPartOrientation?: string; // tns:fieldString
        DentsFlatrate?: number; // tns:fieldDecimal
        DentsCalculationMethod?: string; // tns:fieldString
        DentsCalculationMethodN?: string; // tns:fieldString
        DentsWithFinishing?: boolean; // tns:fieldBoolean
        DentsWithSetupTime?: boolean; // tns:fieldBoolean
        DentsWithAddLightMetals?: boolean; // tns:fieldBoolean
        DentsPrePressTime?: number; // tns:fieldDecimal
        DentsOutOfReach?: boolean; // tns:fieldBoolean
        IsAdditionalLM?: boolean; // tns:fieldBoolean
        RepairVariant?: string; // tns:fieldString
        PreDamage?: boolean; // tns:fieldBoolean
        GlassDamageSpots?: number; // tns:fieldInteger
        DentNumberLess?: number; // tns:fieldInteger
        DentNumberMore?: number; // tns:fieldInteger
        AlloyLM?: boolean; // tns:fieldBoolean
        LargeScale?: boolean; // tns:fieldBoolean
        AdhesiveTechnologyScale?: boolean; // tns:fieldBoolean
        SetupTime?: number; // tns:fieldInteger
        BlockWageFlatPrice?: number; // tns:fieldInteger
        AdditionLM?: boolean; // tns:fieldBoolean
        WorkCompleted?: boolean; // tns:fieldBoolean
        ContainMicroDents?: boolean; // tns:fieldBoolean
        NumDents20?: number; // tns:fieldInteger
        NumDents30?: number; // tns:fieldInteger
        NumDents45?: number; // tns:fieldInteger
        WorkTime20?: number; // tns:fieldInteger
        WorkTime30?: number; // tns:fieldInteger
        WorkTime45?: number; // tns:fieldInteger
        HasAluminium?: boolean; // tns:fieldBoolean
        IsDomusPart?: boolean; // tns:fieldBoolean
        Position?: string; // tns:fieldString
        Apos2?: string; // tns:fieldString
        Apos2MainWork?: string; // tns:fieldString
        ConstructionGroupMainType?: number; // tns:fieldInteger
        ConstructionGroupManufacturer?: number; // tns:fieldInteger
        DVNGroupPositionNumber?: number; // tns:fieldInteger
        Measure?: string; // tns:fieldString
        RepairVector?: string; // tns:fieldString
        SeqNo?: number; // tns:fieldDecimal
        SparePartDiscountIndicator?: string; // tns:fieldCharacter
        WearRus?: MTPLWearRus; // tns:MTPLWearRus
        Finishtype?: string; // tns:fieldString
        LacquerDifficulty?: number; // tns:fieldInteger
        GrupoPlastico?: string; // tns:fieldString
        InputSPD?: number; // tns:fieldInteger
        InputLP?: number; // tns:fieldInteger
        InputSDE?: number; // tns:fieldInteger
        InputSDI?: number; // tns:fieldInteger
        InputL?: number; // tns:fieldInteger
        PiezasPO?: string; // tns:fieldString
        PiezasOP?: string; // tns:fieldString
        Bulge_area?: number; // tns:fieldInteger
        Bulge_area_difficulty_factor?: number; // tns:fieldInteger
        DMSFlag?: string; // tns:fieldString
        PartCodeItaly?: string; // tns:fieldString
        Location?: string; // tns:fieldString
        WorkTimeItaly?: number; // tns:fieldDecimal
        WorkLevelItaly?: string; // tns:fieldString
        WorkManualInput?: string; // tns:fieldString
        NFOPercentage?: number; // tns:fieldDecimal
        PredamageAmount?: number; // tns:fieldDecimal
        PredamagePercentage?: number; // tns:fieldDecimal
        AdhesiveMethod?: boolean; // tns:fieldBoolean
        SparePartDiscountType?: string; // tns:fieldCharacter
        SparePartDiscountAsterisk?: string; // tns:fieldCharacter
        SortOrderNumber?: number; // tns:fieldInteger
        Length?: number; // tns:fieldDecimal
        Width?: number; // tns:fieldDecimal
        Predefined?: boolean; // tns:fieldBoolean
        Manufacturer?: string; // tns:fieldString
        Dimension?: string; // tns:fieldString
        PositionExecutionFlag?: string; // tns:fieldString
        PositionGoodwillPartDescription?: string; // tns:fieldString
        PositionEntryTypeKey?: number; // tns:fieldInteger
        WorkIndication?: number; // tns:fieldInteger
        WithSparePart?: boolean; // tns:fieldBoolean
        FastTrackOrigin?: string; // tns:fieldString
        ParkingDentPositions?: parkingDentPositions; // tns:parkingDentPositions
        IflPositionData?: iflPositionData; // tns:iflPositionData
        ReadError?: boolean; // tns:fieldBoolean
        ExternalId?: string; // tns:fieldString
        TaxNeutral?: boolean; // tns:fieldBoolean
        DamageSegment?: number; // tns:fieldInteger
        InspectionReview?: boolean; // tns:fieldBoolean
        EntryNumber?: number; // tns:fieldInteger
    }

    // RepairPositions
    export interface RepairPositions {
        RepairPosition?: RepairPosition[];
    }

    // RepairProcess
    export interface RepairProcess {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
    }

    // RepairWages
    export interface RepairWages {
        WageUnitsPerHour?: number; // tns:fieldInteger
        Mechanic1?: number; // tns:fieldDecimal
        Mechanic2?: number; // tns:fieldDecimal
        Mechanic3?: number; // tns:fieldDecimal
        Mechanic4?: number; // tns:fieldDecimal
        CarBody1?: number; // tns:fieldDecimal
        CarBody2?: number; // tns:fieldDecimal
        CarBody3?: number; // tns:fieldDecimal
        CarBody4?: number; // tns:fieldDecimal
        Electric1?: number; // tns:fieldDecimal
        Electric2?: number; // tns:fieldDecimal
        Electric3?: number; // tns:fieldDecimal
        Electric4?: number; // tns:fieldDecimal
        Interior1?: number; // tns:fieldDecimal
        Interior2?: number; // tns:fieldDecimal
        Interior3?: number; // tns:fieldDecimal
        Interior4?: number; // tns:fieldDecimal
        Lacquer?: number; // tns:fieldDecimal
        LacquerWithMaterial?: number; // tns:fieldDecimal
        LacquerFree?: number; // tns:fieldDecimal
        Dents?: number; // tns:fieldDecimal
        Comprehensive1?: number; // tns:fieldDecimal
        Comprehensive2?: number; // tns:fieldDecimal
        Comprehensive3?: number; // tns:fieldDecimal
        Comprehensive4?: number; // tns:fieldDecimal
    }

    // RunningExpenses
    export interface RunningExpenses {
        LiabilityInsuranceTypeClass?: string; // tns:fieldString2
        DatLiabilityInsuranceTypeClass?: string; // tns:fieldString2
        PartialCascoTypeClass?: string; // tns:fieldString2
        DatPartialCascoTypeClass?: string; // tns:fieldString2
        ComprCascoTypeClass?: string; // tns:fieldString2
        DatComprCascoTypeClass?: string; // tns:fieldString2
        TaxationDescription?: string; // tns:fieldString
        TaxPerYear?: number; // tns:fieldDecimal
        DatTaxPerYear?: number; // tns:fieldDecimal
        TaxPer100ccm?: number; // tns:fieldDecimal
        DatTaxPer100ccm?: number; // tns:fieldDecimal
    }

    // Sale_AgreementOnDeviationsList
    export interface Sale_AgreementOnDeviationsList {
        AgreementOnDeviationItem?: AgreementOnDeviationItem[];
    }

    // Sale
    export interface Sale {
        CustomerId?: string; // tns:fieldString10
        SellerId?: string; // tns:fieldString10
        OrderDate?: string; // tns:fieldDate
        BusinessType?: string; // tns:fieldString
        VehicleGroup?: string; // tns:fieldString
        SalesPriceNet?: number; // tns:fieldDecimal
        SalesPriceGross?: number; // tns:fieldDecimal
        PurchasingPriceNet?: number; // tns:fieldDecimal
        PurchasingPriceGross?: number; // tns:fieldDecimal
        DeliveryDate?: string; // tns:fieldDate
        MileageVehicle?: number; // tns:fieldInteger
        MileageOdometer?: number; // tns:fieldInteger
        LicenseNumber?: string; // tns:fieldString
        RegistrationNumber?: string; // tns:fieldString
        SalesDetails?: string; // tns:fieldString
        PaymentAgreements?: string; // tns:fieldString
        OtherAgreements?: string; // tns:fieldString
        RenewVehicleInspectionDate?: string; // tns:fieldDate
        ProcedureName?: string; // tns:fieldString
        Vin?: string; // tns:fieldString
        Purchaser?: string; // tns:fieldString
        LicenseNumberPurchase?: string; // tns:fieldString
        AcceptanceDate?: string; // tns:fieldDate
        Seller?: string; // tns:fieldString
        PaymentType?: string; // tns:fieldString
        SalesType?: string; // tns:fieldString
        PayementAgreements?: string; // tns:fieldString
        RenewMot?: string; // tns:fieldString
        ResidualWarrantyType?: string; // tns:fieldString
        ResidualWarrantyUntil?: string; // tns:fieldDate
        ResidualWarrantyValueNet?: number; // tns:fieldDecimal
        ResidualWarrantyValueGross?: number; // tns:fieldDecimal
        Provider?: string; // tns:fieldString
        RepaymentTerm?: number; // tns:fieldInteger
        StartDate?: string; // tns:fieldDate
        EndDate?: string; // tns:fieldDate
        WarrantyNumber?: string; // tns:fieldString
        WarrantyAmountNet?: number; // tns:fieldDecimal
        WarrantyAmountGross?: number; // tns:fieldDecimal
        NextEmissionsTestDate?: string; // tns:fieldDate
        NextVehicleInspectionDate?: string; // tns:fieldDate
        RenewEmissionVehicleInspectionCheck?: boolean; // tns:fieldBoolean
        AgreementOnDeviationsCheck?: boolean; // tns:fieldBoolean
        ShorteningLimitationPeriodCheck?: boolean; // tns:fieldBoolean
        ExclusionOfObligationToUpdateCheck?: boolean; // tns:fieldBoolean
        UpdateInformationCheck?: boolean; // tns:fieldBoolean
        InfoReqUpdatesAndInstallation?: string; // tns:fieldString
        Labelling?: Labelling;
        AgreementOnDeviationsList?: Sale_AgreementOnDeviationsList;
    }

    // SalesOffer
    export interface SalesOffer {
        SalesPriceNet?: number; // tns:fieldDecimal
        SalesPriceGross?: number; // tns:fieldDecimal
        SalesOfferNumber?: string; // tns:fieldString
        OfferDate?: string; // tns:fieldDate
        ResubmissionDate?: string; // tns:fieldDate
        GuaranteeDate?: string; // tns:fieldDate
        OfferedBy?: string; // tns:fieldString
        RenewMot?: string; // tns:fieldString
        Titles?: string; // tns:fieldString
        Comments?: string; // tns:fieldString
        Agreements?: string; // tns:fieldString
        NextEmissionsTestDate?: string; // tns:fieldDate
        NextVehicleInspectionDate?: string; // tns:fieldDate
        RenewEmissionVehicleInspectionCheck?: boolean; // tns:fieldBoolean
    }

    // SalesPreparation
    export interface SalesPreparation {
        MarketplaceImageList?: MarketplaceImageList;
    }

    // SettingsParameter
    export interface SettingsParameter {
        _attr_key: string;
        _attr_level: string;
        _attr_name: string;
        _attr_value: string;
    }

    // SettingsParameters
    export interface SettingsParameters {
        SettingsParameter?: SettingsParameter[];
    }

    // SparePartHistoryPosition
    export interface SparePartHistoryPosition {
        PartNumber?: string; // tns:fieldString
        LastUPE?: number; // xs:decimal
        LastUPEDate?: string; // tns:fieldDate
    }

    // SparePartPosition
    export interface SparePartPosition {
        DATProcessId?: number; // tns:fieldInteger
        DATProcessIdName?: string; // tns:fieldString
        PartNumber?: string; // tns:fieldString
        PartNumberManufacturer?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        LastUPE?: number; // xs:decimal
        LastUPEDate?: string; // tns:fieldDateTime
        SecondtoLastUPE?: number; // xs:decimal
        SecondtoLastUPEDate?: string; // tns:fieldDateTime
        ThirdtoLastUPE?: number; // xs:decimal
        ThirdtoLastUPEDate?: string; // tns:fieldDateTime
        FourthtoLastUPE?: number; // xs:decimal
        FourthtoLastUPEDate?: string; // tns:fieldDateTime
        EquipmentPositions?: equipSequence; // tns:equipSequence
        Orderable?: boolean; // tns:fieldBoolean
        WorkTimeMin?: number; // xs:decimal
        WorkTimeMax?: number; // xs:decimal
        SparePartHistoryPositions?: spHistPositionsSeq; // tns:spHistPositionsSeq
        RepairSetProcessId?: number; // xs:integer
    }

    // SparePartPositions
    export interface SparePartPositions {
        SparePartPosition?: SparePartPosition[];
    }

    // SpotRepairPositions
    export interface SpotRepairPositions {
        SpotRepairPosition?: LacquerPosition[]; // tns:LacquerPosition
    }

    // SummariesItaly
    export interface SummariesItaly {
        BaseSummaryItaly?: BaseSummaryItaly;
        LacquerSummaryItaly?: LacquerSummaryItaly;
        WorkSummaryItaly?: WorkSummaryItaly;
        TotalSummaryItaly?: TotalSummaryItaly;
    }

    // SuppressedPositionsProtocol
    export interface SuppressedPositionsProtocol {
        ProtocolEntry?: ProtocolEntry[];
    }

    // SurchargeSetting
    export interface SurchargeSetting {
        id: string; // tns:fieldString
        country: string; // tns:fieldString
        designation: string; // tns:fieldString
        workingType: number; // tns:fieldDecimal
        priceSTD: number; // tns:fieldDecimal
        priceAE?: number; // tns:fieldDecimal
    }

    // SurchargeSettings
    export interface SurchargeSettings {
        SurchargeSetting?: SurchargeSetting[];
    }

    // TargetMarketplaceFeatureItem
    export interface TargetMarketplaceFeatureItem {
        TMName?: string; // tns:fieldString
        TMType?: string; // xs:string
        TMCheckbox?: boolean; // xs:boolean
        TMFromDate?: string; // xs:date
        TMToDate?: string; // xs:date
    }

    // TechInfo
    export interface TechInfo {
        FillingQuantities?: FillingQuantities;
        StructureType?: string; // tns:fieldString
        DatStructureDescription?: string; // tns:fieldString
        StructureDescription?: string; // tns:fieldString
        CabineStructureType?: string; // tns:fieldString
        CabineStructureDescription?: string; // tns:fieldString1000
        UpperBodyStructureType?: string; // tns:fieldString
        UpperBodyStructureDescription?: string; // tns:fieldString
        UpperBodyStructureDescriptionUser?: string; // tns:fieldString1000
        UpperBodyStructureAndVersion?: string; // tns:fieldString
        CountOfAxles?: number; // tns:fieldInteger
        DatCountOfAxles?: number; // tns:fieldInteger
        CountOfDrivedAxles?: number; // tns:fieldInteger
        DatCountOfDrivedAxles?: number; // tns:fieldInteger
        WheelBase?: number; // tns:fieldInteger
        DatWheelBase?: number; // tns:fieldInteger
        WheelBase2?: number; // tns:fieldInteger
        AxleLoadFront?: number; // tns:fieldInteger
        AxleLoadMiddle?: number; // tns:fieldInteger
        AxleLoadBack?: number; // tns:fieldInteger
        TonnageClass?: string; // tns:fieldString2
        Length?: number; // tns:fieldInteger
        DatLength?: number; // tns:fieldInteger
        Width?: number; // tns:fieldInteger
        DatWidth?: number; // tns:fieldInteger
        Height?: number; // tns:fieldInteger
        DatHeight?: number; // tns:fieldInteger
        RoofLoad?: number; // tns:fieldInteger
        DatRoofLoad?: number; // tns:fieldInteger
        TrailerLoadBraked?: number; // tns:fieldInteger
        DatTrailerLoadBraked?: number; // tns:fieldInteger
        TrailerLoadUnbraked?: number; // tns:fieldInteger
        DatTrailerLoadUnbraked?: number; // tns:fieldInteger
        VehicleSeats?: number; // tns:fieldInteger
        DatVehicleSeats?: number; // tns:fieldInteger
        VehicleDoors?: number; // tns:fieldInteger
        DatVehicleDoors?: number; // tns:fieldInteger
        CountOfAirbags?: number; // tns:fieldInteger
        DatCountOfAirbags?: number; // tns:fieldInteger
        Acceleration?: number; // tns:fieldDecimal
        DatAcceleration?: number; // tns:fieldDecimal
        SpeedMax?: number; // tns:fieldInteger
        DatSpeedMax?: number; // tns:fieldInteger
        PowerHp?: number; // tns:fieldInteger
        DatPowerHp?: number; // tns:fieldInteger
        PowerKw?: number; // tns:fieldDecimal
        DatPowerKw?: number; // tns:fieldDecimal
        Capacity?: number; // tns:fieldInteger
        DatCapacity?: number; // tns:fieldInteger
        Cylinder?: number; // tns:fieldInteger
        DatCylinder?: number; // tns:fieldInteger
        CylinderArrangement?: string; // tns:fieldString30
        DatCylinderArrangement?: string; // tns:fieldString30
        RotationsOnMaxPower?: number; // tns:fieldInteger
        DatRotationsOnMaxPower?: number; // tns:fieldInteger
        RotationsOnMaxTorque?: number; // tns:fieldInteger
        DatRotationsOnMaxTorque?: number; // tns:fieldInteger
        Torque?: number; // tns:fieldInteger
        DatTorque?: number; // tns:fieldInteger
        GearboxType?: string; // tns:fieldString
        NrOfGears?: string; // tns:fieldString
        OriginalTireSizeAxle1?: string; // tns:fieldString
        OriginalTireSizeAxle2?: string; // tns:fieldString
        TankVolume?: number; // tns:fieldInteger
        DatTankVolume?: number; // tns:fieldInteger
        TankVolumeAlternative?: number; // tns:fieldInteger
        DatTankVolumeAlternative?: number; // tns:fieldInteger
        ConsumptionInTown?: number; // tns:fieldDecimal
        DatConsumptionInTown?: number; // tns:fieldDecimal
        ConsumptionOutOfTown?: number; // tns:fieldDecimal
        DatConsumptionOutOfTown?: number; // tns:fieldDecimal
        Consumption?: number; // tns:fieldDecimal
        DatConsumption?: number; // tns:fieldDecimal
        WltpConsumptionMixedMin?: number; // tns:fieldDecimal
        DatWltpConsumptionMixedMin?: number; // tns:fieldDecimal
        WltpConsumptionMixedMax?: number; // tns:fieldDecimal
        DatWltpConsumptionMixedMax?: number; // tns:fieldDecimal
        ConsumptionInnerCng?: number; // tns:fieldDecimal
        DatConsumptionInnerCng?: number; // tns:fieldDecimal
        ConsumptionOuterCng?: number; // tns:fieldDecimal
        DatConsumptionOuterCng?: number; // tns:fieldDecimal
        ConsumptionMixCng?: number; // tns:fieldDecimal
        DatConsumptionMixCng?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedCngMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedCngMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedCngMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedCngMax?: number; // tns:fieldDecimal
        ConsumptionInnerLpg?: number; // tns:fieldDecimal
        DatConsumptionInnerLpg?: number; // tns:fieldDecimal
        ConsumptionOuterLpg?: number; // tns:fieldDecimal
        DatConsumptionOuterLpg?: number; // tns:fieldDecimal
        ConsumptionMixLpg?: number; // tns:fieldDecimal
        DatConsumptionMixLpg?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedLpgMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedLpgMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedLpgMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedLpgMax?: number; // tns:fieldDecimal
        ConsumptionInnerH?: number; // tns:fieldDecimal
        DatConsumptionInnerH?: number; // tns:fieldDecimal
        ConsumptionOuterH?: number; // tns:fieldDecimal
        DatConsumptionOuterH?: number; // tns:fieldDecimal
        ConsumptionMixH?: number; // tns:fieldDecimal
        DatConsumptionMixH?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedHMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedHMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedHMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedHMax?: number; // tns:fieldDecimal
        Co2Emission?: number; // tns:fieldDecimal
        DatCo2Emission?: number; // tns:fieldDecimal
        WltpCo2EmissionMin?: number; // tns:fieldDecimal
        DatWltpCo2EmissionMin?: number; // tns:fieldDecimal
        WltpCo2EmissionMax?: number; // tns:fieldDecimal
        DatWltpCo2EmissionMax?: number; // tns:fieldDecimal
        EmissionClass?: string; // tns:fieldString5
        DatEmissionClass?: string; // tns:fieldString5
        EmissionClassN?: EmissionClassN;
        Drive?: string; // tns:fieldString1000
        DatDrive?: string; // tns:fieldString1000
        DriveN?: string; // tns:fieldString1000
        DatDriveN?: string; // tns:fieldString1000
        DriveCode?: string; // tns:fieldString10
        EngineCycle?: number; // tns:fieldInteger
        DatEngineCycle?: number; // tns:fieldInteger
        FuelMethod?: string; // tns:fieldString1000
        DatFuelMethod?: string; // tns:fieldString1000
        FuelMethodCode?: string; // tns:fieldString2
        FuelMethodType?: string; // tns:fieldString1000
        DatFuelMethodType?: string; // tns:fieldString1000
        UnloadedWeight?: number; // tns:fieldInteger
        DatUnloadedWeight?: number; // tns:fieldInteger
        PermissableTotalWeight?: number; // tns:fieldInteger
        DatPermissableTotalWeight?: number; // tns:fieldInteger
        Payload?: number; // tns:fieldInteger
        DatPayload?: number; // tns:fieldInteger
        LoadingLength?: number; // tns:fieldInteger
        DatLoadingLength?: number; // tns:fieldInteger
        LoadingWidth?: number; // tns:fieldInteger
        DatLoadingWidth?: number; // tns:fieldInteger
        LoadingHeight?: number; // tns:fieldInteger
        DatLoadingHeight?: number; // tns:fieldInteger
        LoadingSpace?: number; // tns:fieldInteger
        DatLoadingSpace?: number; // tns:fieldInteger
        LoadingSpaceMax?: number; // tns:fieldInteger
        DatLoadingSpaceMax?: number; // tns:fieldInteger
        UpperBodyMaterial?: string; // tns:fieldString30
        InsuranceTypeClassLiability?: string; // tns:fieldString2
        InsuranceTypeClassCascoPartial?: string; // tns:fieldString2
        InsuranceTypeClassCascoComplete?: string; // tns:fieldString2
        DustBadge?: string; // tns:fieldString
        ProductGroupName?: string; // tns:fieldString1000
        EmissionKey?: string; // xs:string
        Built?: string; // tns:fieldString30
        AllowedLoadCapacity?: number; // tns:fieldInteger
        CabinStructureAltDescription?: string; // tns:fieldString30
        CushionColorId?: string; // tns:fieldString30
        FuelmethodAbbr?: string; // tns:fieldString1000
        InsuranceTypeClassCascoCompleteNeu?: string; // tns:fieldString2
        InsuranceTypeClassCascoPartialNeu?: string; // tns:fieldString2
        InsuranceTypeClassLiabilityNew?: string; // tns:fieldString2
        PayloadAlternative?: number; // tns:fieldInteger
        PowerKwSae?: number; // tns:fieldDecimal
        SommerSmogBadge?: string; // tns:fieldString
        StowageMassFormat?: string; // tns:fieldString
        TokenChangedCapacity?: string; // tns:fieldString
        TokenTurboEngine?: string; // tns:fieldString
        TypeOfTaxation?: string; // tns:fieldString
        TypeSheetNumber?: string; // tns:fieldString10
        WhelBaseAlternative?: number; // tns:fieldInteger
        SuitableForE10?: boolean; // tns:fieldBoolean
        DatSuitableForE10?: boolean; // tns:fieldBoolean
        WeightTotalCombination?: number; // tns:fieldInteger
        DatWeightTotalCombination?: number; // tns:fieldInteger
        WidthForGarage?: number; // tns:fieldInteger
        DatWidthForGarage?: number; // tns:fieldInteger
        PowerKwSystem?: number; // tns:fieldDecimal
        DatPowerKwSystem?: number; // tns:fieldDecimal
        PowerHpSystem?: number; // tns:fieldDecimal
        DatPowerHpSystem?: number; // tns:fieldDecimal
        PowerKwPermanent?: number; // tns:fieldDecimal
        DatPowerKwPermanent?: number; // tns:fieldDecimal
        PowerHpPermanent?: number; // tns:fieldDecimal
        DatPowerHpPermanent?: number; // tns:fieldDecimal
        PowerKwMax?: number; // tns:fieldDecimal
        DatPowerKwMax?: number; // tns:fieldDecimal
        PowerHpMax?: number; // tns:fieldDecimal
        DatPowerHpMax?: number; // tns:fieldDecimal
        PowerKwPermanentSecondary?: number; // tns:fieldDecimal
        DatPowerKwPermanentSecondary?: number; // tns:fieldDecimal
        PowerHpPermanentSecondary?: number; // tns:fieldDecimal
        DatPowerHpPermanentSecondary?: number; // tns:fieldDecimal
        PowerKwMaxSecondary?: number; // tns:fieldDecimal
        DatPowerKwMaxSecondary?: number; // tns:fieldDecimal
        PowerHpMaxSecondary?: number; // tns:fieldDecimal
        DatPowerHpMaxSecondary?: number; // tns:fieldDecimal
        BatteryVoltage?: number; // tns:fieldDecimal
        DatBatteryVoltage?: number; // tns:fieldDecimal
        BatteryCapacity?: number; // tns:fieldDecimal
        DatBatteryCapacity?: number; // tns:fieldDecimal
        BatteryWeight?: number; // tns:fieldDecimal
        DatBatteryWeight?: number; // tns:fieldDecimal
        BatteryConstructionType?: string; // tns:fieldString
        DatBatteryConstructionType?: string; // tns:fieldString
        ChargingCurrentPlugType?: string; // tns:fieldString
        DatChargingCurrentPlugType?: string; // tns:fieldString
        PluginSystem?: boolean; // tns:fieldBoolean
        DatPluginSystem?: boolean; // tns:fieldBoolean
        QuickdropSystem?: boolean; // tns:fieldBoolean
        DatQuickdropSystem?: boolean; // tns:fieldBoolean
        NormalChargeVoltage?: number; // tns:fieldInteger
        DatNormalChargeVoltage?: number; // tns:fieldInteger
        NormalChargeDuration?: number; // tns:fieldDecimal
        DatNormalChargeDuration?: number; // tns:fieldDecimal
        QuickChargeVoltage?: number; // tns:fieldInteger
        DatQuickChargeVoltage?: number; // tns:fieldInteger
        QuickChargeDuration?: number; // tns:fieldDecimal
        DatQuickChargeDuration?: number; // tns:fieldDecimal
        ConsumptionElectricalCurrent?: number; // tns:fieldDecimal
        DatConsumptionElectricalCurrent?: number; // tns:fieldDecimal
        WltpConsumptionElectricalMin?: number; // tns:fieldDecimal
        DatWltpConsumptionElectricalMin?: number; // tns:fieldDecimal
        WltpConsumptionElectricalMax?: number; // tns:fieldDecimal
        DatWltpConsumptionElectricalMax?: number; // tns:fieldDecimal
        RangeOfElectricMotor?: number; // tns:fieldInteger
        DatRangeOfElectricMotor?: number; // tns:fieldInteger
        RangeTotal?: number; // tns:fieldInteger
        DatRangeTotal?: number; // tns:fieldInteger
        WltpRangeElectricalMin?: number; // tns:fieldInteger
        DatWltpRangeElectricalMin?: number; // tns:fieldInteger
        WltpRangeElectricalMax?: number; // tns:fieldInteger
        DatWltpRangeElectricalMax?: number; // tns:fieldInteger
        WltpRangeTotalMin?: number; // tns:fieldInteger
        DatWltpRangeTotalMin?: number; // tns:fieldInteger
        WltpRangeTotalMax?: number; // tns:fieldInteger
        DatWltpRangeTotalMax?: number; // tns:fieldInteger
        EnergyEfficiencyClass?: string; // tns:fieldString2
        DatEnergyEfficiencyClass?: string; // tns:fieldString2
        ModelTypecode?: string; // tns:fieldString
        ModelVariant?: string; // tns:fieldString
        Type?: string; // tns:fieldString
        TypeVariant?: string; // tns:fieldString
        EngineType?: string; // tns:fieldString
        SpecialModel?: string; // tns:fieldString
        TechInfoWltp?: TechInfoWltp;
    }

    // TechInfoWltp
    export interface TechInfoWltp {
        WltpConsumptionLowMin?: number; // tns:fieldDecimal
        DatWltpConsumptionLowMin?: number; // tns:fieldDecimal
        WltpConsumptionLowMax?: number; // tns:fieldDecimal
        DatWltpConsumptionLowMax?: number; // tns:fieldDecimal
        WltpConsumptionMediumMin?: number; // tns:fieldDecimal
        DatWltpConsumptionMediumMin?: number; // tns:fieldDecimal
        WltpConsumptionMediumMax?: number; // tns:fieldDecimal
        DatWltpConsumptionMediumMax?: number; // tns:fieldDecimal
        WltpConsumptionHighMin?: number; // tns:fieldDecimal
        DatWltpConsumptionHighMin?: number; // tns:fieldDecimal
        WltpConsumptionHighMax?: number; // tns:fieldDecimal
        DatWltpConsumptionHighMax?: number; // tns:fieldDecimal
        WltpConsumptionExtraHighMin?: number; // tns:fieldDecimal
        DatWltpConsumptionExtraHighMin?: number; // tns:fieldDecimal
        WltpConsumptionExtraHighMax?: number; // tns:fieldDecimal
        DatWltpConsumptionExtraHighMax?: number; // tns:fieldDecimal
        WltpConsumptionMixedMin?: number; // tns:fieldDecimal
        DatWltpConsumptionMixedMin?: number; // tns:fieldDecimal
        WltpConsumptionMixedMax?: number; // tns:fieldDecimal
        DatWltpConsumptionMixedMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentLowCngMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentLowCngMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentLowCngMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentLowCngMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMediumCngMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMediumCngMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMediumCngMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMediumCngMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentHighCngMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentHighCngMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentHighCngMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentHighCngMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentExtraHighCngMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentExtraHighCngMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentExtraHighCngMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentExtraHighCngMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedCngMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedCngMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedCngMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedCngMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentLowLpgMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentLowLpgMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentLowLpgMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentLowLpgMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMediumLpgMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMediumLpgMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMediumLpgMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMediumLpgMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentHighLpgMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentHighLpgMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentHighLpgMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentHighLpgMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentExtraHighLpgMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentExtraHighLpgMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentExtraHighLpgMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentExtraHighLpgMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedLpgMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedLpgMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedLpgMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedLpgMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentLowHMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentLowHMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentLowHMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentLowHMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMediumHMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMediumHMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMediumHMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMediumHMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentHighHMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentHighHMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentHighHMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentHighHMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentExtraHighHMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentExtraHighHMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentExtraHighHMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentExtraHighHMax?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedHMin?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedHMin?: number; // tns:fieldDecimal
        WltpConsumptionBivalentMixedHMax?: number; // tns:fieldDecimal
        DatWltpConsumptionBivalentMixedHMax?: number; // tns:fieldDecimal
        WltpCo2ClassMin?: string; // tns:fieldString4
        DatWltpCo2ClassMin?: string; // tns:fieldString4
        WltpCo2ClassMax?: string; // tns:fieldString4
        DatWltpCo2ClassMax?: string; // tns:fieldString4
        WltpCo2ClassWithDischargedBatteryMin?: string; // tns:fieldString4
        DatWltpCo2ClassWithDischargedBatteryMin?: string; // tns:fieldString4
        WltpCo2ClassWithDischargedBatteryMax?: string; // tns:fieldString4
        DatWltpCo2ClassWithDischargedBatteryMax?: string; // tns:fieldString4
        WltpCo2EmissionMin?: number; // tns:fieldDecimal
        DatWltpCo2EmissionMin?: number; // tns:fieldDecimal
        WltpCo2EmissionMax?: number; // tns:fieldDecimal
        DatWltpCo2EmissionMax?: number; // tns:fieldDecimal
        WltpCo2EmissionWithDischargedBatteryMin?: number; // tns:fieldDecimal
        DatWltpCo2EmissionWithDischargedBatteryMin?: number; // tns:fieldDecimal
        WltpCo2EmissionWithDischargedBatteryMax?: number; // tns:fieldDecimal
        DatWltpCo2EmissionWithDischargedBatteryMax?: number; // tns:fieldDecimal
        WltpConsumptionElectricalMin?: number; // tns:fieldDecimal
        DatWltpConsumptionElectricalMin?: number; // tns:fieldDecimal
        WltpConsumptionElectricalMax?: number; // tns:fieldDecimal
        DatWltpConsumptionElectricalMax?: number; // tns:fieldDecimal
        WltpConsumptionWithDischargedBatteryMin?: number; // tns:fieldDecimal
        DatWltpConsumptionWithDischargedBatteryMin?: number; // tns:fieldDecimal
        WltpConsumptionWithDischargedBatteryMax?: number; // tns:fieldDecimal
        DatWltpConsumptionWithDischargedBatteryMax?: number; // tns:fieldDecimal
        WltpRangeElectricalMin?: number; // tns:fieldInteger
        DatWltpRangeElectricalMin?: number; // tns:fieldInteger
        WltpRangeElectricalMax?: number; // tns:fieldInteger
        DatWltpRangeElectricalMax?: number; // tns:fieldInteger
        WltpRangeTotalMin?: number; // tns:fieldInteger
        DatWltpRangeTotalMin?: number; // tns:fieldInteger
        WltpRangeTotalMax?: number; // tns:fieldInteger
        DatWltpRangeTotalMax?: number; // tns:fieldInteger
    }

    // TelematicData
    export interface TelematicData {
        TelematicEntries?: TelematicEntries[];
    }

    // TelematicEntries
    export interface TelematicEntries {
        TelematicEntry?: TelematicEntry[];
        _attr_type: string;
    }

    // TelematicEntry
    export interface TelematicEntry {
        _attr_key: string;
        _attr_value: string;
    }

    // TextItem
    export interface TextItem {
        Key?: string; // tns:fieldString
        Text?: string; // tns:fieldString
    }

    // Tires_Axles
    export interface Tires_Axles {
        Axle?: Axle[];
    }

    // Tires
    export interface Tires {
        TireRepairType?: string; // tns:fieldString
        TireValuationType?: string; // tns:fieldString
        Axles?: Tires_Axles;
    }

    // TotalSummaryItaly
    export interface TotalSummaryItaly {
        TotalSummaryItemItaly?: TotalSummaryItemItaly[];
    }

    // TotalSummaryItemItaly
    export interface TotalSummaryItemItaly {
        Identifier?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        ValueNet?: number; // tns:fieldDecimal
        ValueNetDiscountPerc?: number; // tns:fieldDecimal
        ValueNetDiscountValue?: number; // tns:fieldDecimal
        ValueNetCorrected?: number; // tns:fieldDecimal
        ValueVat?: number; // tns:fieldDecimal
        ValueGross?: number; // tns:fieldDecimal
    }

    // TradingActivity
    export interface TradingActivity {
        State?: string; // tns:fieldString
        PlanData?: PlanData;
        Purchase?: Purchase;
        PriceCalculation?: PriceCalculation;
        Admission?: Admission;
        Sale?: Sale;
        SalesPreparation?: SalesPreparation;
    }

    // TradingAdditional_PurchaseOfferList
    export interface TradingAdditional_PurchaseOfferList {
        PurchaseOffer?: PurchaseOffer[];
    }

    // TradingAdditional_SalesOfferList
    export interface TradingAdditional_SalesOfferList {
        SalesOffer?: SalesOffer[];
    }

    // TradingAdditional
    export interface TradingAdditional {
        MarketplacePreparation?: MarketplacePreparation;
        PurchaseOfferList?: TradingAdditional_PurchaseOfferList;
        SalesOfferList?: TradingAdditional_SalesOfferList;
    }

    // TradingData
    export interface TradingData {
        PurchasePriceNet?: number; // tns:fieldDecimal
        PurchasePriceGross?: number; // tns:fieldDecimal
        SalesPriceNet?: number; // tns:fieldDecimal
        SalesPriceGross?: number; // tns:fieldDecimal
        ExportReportState?: string; // tns:fieldString
        OwnType?: string; // tns:fieldString
        ClaimFormId?: string; // tns:fieldString
        EuVehicle?: boolean; // tns:fieldBoolean
        Location?: string; // tns:fieldString
        Owner?: address; // tns:address
        PrevOwner?: address; // tns:address
        Buyer?: address; // tns:address
        Reservation?: address; // tns:address
        Insurance?: address; // tns:address
        Insurant?: address; // tns:address
        Driver?: address; // tns:address
        Garage?: address; // tns:address
        Opponent?: address; // tns:address
        Expert?: address; // tns:address
        Dealership?: address; // tns:address
        AccidentPlace?: address; // tns:address
        InspectionPlace?: address; // tns:address
        InsuranceBroker?: address; // tns:address
        ClientContactAddresses?: ClientContactAddresses;
        Prospects?: Prospects;
        AddonList?: AddonList;
    }

    // UpperBody
    export interface UpperBody {
        UpperBodyType?: number; // tns:fieldInteger
        UpperBodyTypeName?: string; // tns:fieldString
        DatUpperBodyTypeName?: string; // tns:fieldString
        Manufacturer?: number; // tns:fieldInteger
        ManufacturerName?: string; // tns:fieldString
        DatManufacturerName?: string; // tns:fieldString
        MainModel?: number; // tns:fieldInteger
        MainModelName?: string; // tns:fieldString
        DatMainModelName?: string; // tns:fieldString
        SubModel?: number; // tns:fieldInteger
        SubModelName?: string; // tns:fieldString
        DatSubModelName?: string; // tns:fieldString
        InitialRegistration?: string; // tns:fieldDate
        IdentificationNumber?: string; // tns:fieldString
        UpperBodyColor?: string; // tns:fieldString
        PaintLabelDescription?: string; // tns:fieldString
        OriginalPrice?: number; // tns:fieldDecimal
        DatOriginalPrice?: number; // tns:fieldDecimal
        OriginalPriceGross?: number; // tns:fieldDecimal
        DatOriginalPriceGross?: number; // tns:fieldDecimal
        GeneralCondition?: number; // tns:fieldDecimal
        DatGeneralCondition?: number; // tns:fieldDecimal
        BasePrice?: number; // tns:fieldDecimal
        DatBasePrice?: number; // tns:fieldDecimal
        BasePriceGross?: number; // tns:fieldDecimal
        DatBasePriceGross?: number; // tns:fieldDecimal
        SalesPrice?: number; // tns:fieldDecimal
        DatSalesPrice?: number; // tns:fieldDecimal
        SalesPriceGross?: number; // tns:fieldDecimal
        DatSalesPriceGross?: number; // tns:fieldDecimal
        IsDisengaged?: boolean; // tns:fieldBoolean
        TechInfo?: TechInfo;
        Equipment?: Equipment;
        CoolingUnit?: CoolingUnit;
        Condition?: Condition;
    }

    // Usage
    export interface Usage {
        _attr_type?: number;
        _value: string; // xs:string
    }

    // VAT
    export interface VAT {
        VatType?: string; // tns:fieldString
        VatAtConstructionTime?: number; // tns:fieldDecimal
        DatVatAtConstructionTime?: number; // tns:fieldDecimal
        BaseVatAtConstructionTime?: number; // tns:fieldDecimal
        DatBaseVatAtConstructionTime?: number; // tns:fieldDecimal
        AddOnTaxAtConstructionTime?: number; // tns:fieldDecimal
        AddOnTaxApplication?: string; // tns:fieldString
        PostTaxDifference?: number; // tns:fieldDecimal
        VatAtValuationTime?: number; // tns:fieldDecimal
        DatVatAtValuationTime?: number; // tns:fieldDecimal
        VatAtCalculationTime?: number; // tns:fieldDecimal
        VatAtSalesTime?: number; // tns:fieldDecimal
        DatVatAtSalesTime?: number; // tns:fieldDecimal
        VatAtPurchaseTime?: number; // tns:fieldDecimal
        DatVatAtPurchaseTime?: number; // tns:fieldDecimal
        VATReplacementPartAtCalculationTime?: number; // tns:fieldDecimal
    }

    // VINColor
    export interface VINColor {
        ColorID?: string; // tns:fieldString
        Code?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        StandardColor?: string; // tns:fieldString
        PaintType?: string; // tns:fieldString
        CountCoat?: string; // tns:fieldString
    }

    // VINColors
    export interface VINColors {
        VINColor: VINColor[];
    }

    // VINContainer
    export interface VINContainer {
        Container?: string; // tns:fieldString
        VehicleTypeKey?: number; // tns:fieldInteger
        ManufacturerKey?: number; // tns:fieldInteger
        VehicleMainTypeKey?: number; // tns:fieldInteger
        VehicleSubTypeKey?: number; // tns:fieldInteger
        VehicleConstructionTime?: number; // tns:fieldInteger
    }

    // VINContainers
    export interface VINContainers {
        VINContainer: VINContainer[];
    }

    // VINECode
    export interface VINECode {
        Sign?: number; // tns:fieldInteger
        Country?: string; // tns:fieldString3
        VehicleTypeKey?: number; // tns:fieldInteger
        ManufacturerKey?: number; // tns:fieldInteger
        VehicleMainTypeKey?: number; // tns:fieldInteger
        VehicleSubTypeKey?: number; // tns:fieldInteger
        VehicleSubTypeVariantKey?: number; // tns:fieldInteger
        ConstructionTimeMin?: number; // tns:fieldInteger
        ConstructionTime?: number; // tns:fieldInteger
        ConstructionTimeEdge?: number; // tns:fieldInteger
        ConstructionTimeProd?: number; // tns:fieldInteger
        ConstructionTimePriceList?: number; // tns:fieldInteger
        VINContainers?: VINContainers;
    }

    // VINECodes
    export interface VINECodes {
        VINECode: VINECode[];
    }

    // VINEquipment
    export interface VINEquipment {
        AvNumberDat?: number; // tns:fieldInteger
        ManufacturerCode?: string; // tns:fieldString
        ShortName?: string; // tns:fieldString
    }

    // VINEquipments
    export interface VINEquipments {
        VINEquipment: VINEquipment[];
    }

    // VINResult
    export interface VINResult {
        VinInterfaceVersion?: string; // tns:fieldString
        VinDatProcedure?: boolean; // tns:fieldBoolean
        CrossBorder?: boolean; // tns:fieldBoolean
        VINECodes?: VINECodes;
        VINEquipments?: VINEquipments;
        VinEquipmentsEncrypted?: string; // tns:fieldString
        VINColors?: VINColors;
        VINVehicle?: VINVehicle;
    }

    // VINVehicle
    export interface VINVehicle {
        VINumber: VINumber;
        ManufacturerCarCode?: string; // tns:fieldString
        ManufacturerEngineCode?: string; // tns:fieldString
        ManufacturerTransmissionCode?: string; // tns:fieldString
    }

    // VINumber
    export interface VINumber {
        VinCode?: string; // tns:fieldString
        OrderCode?: string; // tns:fieldString
        Manufacturer?: string; // tns:fieldString
    }

    // Valuation
    export interface Valuation {
        OriginalPrice?: number; // tns:fieldDecimal
        DatOriginalPrice?: number; // tns:fieldDecimal
        OriginalPriceGross?: number; // tns:fieldDecimal
        DatOriginalPriceGross?: number; // tns:fieldDecimal
        PrognosisDate?: string; // tns:fieldDate
        PricelistDate?: string; // tns:fieldDate
        BasePrice?: number; // tns:fieldDecimal
        DatBasePrice?: number; // tns:fieldDecimal
        Mileage?: number; // tns:fieldDecimal
        ReferenceMileage?: number; // tns:fieldDecimal
        MileageCorr?: number; // tns:fieldDecimal
        DatMileageCorr?: number; // tns:fieldDecimal
        LimitationMileageCorrPerc?: number; // tns:fieldDecimal
        ResidualValueModel?: string; // tns:fieldString
        AdditionalManualMileageCorr?: number; // tns:fieldDecimal
        MinValue4MileageCorr?: number; // tns:fieldInteger
        MaxValue4MileageCorr?: number; // tns:fieldInteger
        InitialRegistrationCorr?: number; // tns:fieldDecimal
        DatInitialRegistrationCorr?: number; // tns:fieldDecimal
        DevaluationFactorPerc?: number; // tns:fieldDecimal
        DatDevaluationFactorPerc?: number; // tns:fieldDecimal
        BasePrice2?: number; // tns:fieldDecimal
        DatBasePrice2?: number; // tns:fieldDecimal
        EquipmentSign?: string; // tns:fieldString
        ManualEquipmentOriginalPrice?: number; // tns:fieldDecimal
        EquipmentOriginalPrice?: number; // tns:fieldDecimal
        DatEquipmentOriginalPrice?: number; // tns:fieldDecimal
        EquipmentPercentage?: number; // tns:fieldInteger
        EquipmentDecreaseType?: string; // tns:fieldString
        EquipmentDecreaseTypeRemaining?: string; // tns:fieldString
        EquipmentPrice?: number; // tns:fieldDecimal
        DatEquipmentPrice?: number; // tns:fieldDecimal
        UpperBodiesPrice?: number; // tns:fieldDecimal
        DatUpperBodiesPrice?: number; // tns:fieldDecimal
        ValuationCorrection?: number; // tns:fieldDecimal
        DatValuationCorrection?: number; // tns:fieldDecimal
        BasePrice3?: number; // tns:fieldDecimal
        DatBasePrice3?: number; // tns:fieldDecimal
        ConditionCorrectionPerc?: number; // tns:fieldDecimal
        DatConditionCorrectionPerc?: number; // tns:fieldDecimal
        SalesPrice?: number; // tns:fieldDecimal
        SalesPriceRounded?: number; // tns:fieldDecimal
        SalesPricePrognosis?: number; // tns:fieldDecimal
        DatSalesPrice?: number; // tns:fieldDecimal
        DatSalesPriceRounded?: number; // tns:fieldDecimal
        SalesPriceGross?: number; // tns:fieldDecimal
        SalesPriceGrossRounded?: number; // tns:fieldDecimal
        SalesPricePrognosisGross?: number; // tns:fieldDecimal
        DatSalesPriceGross?: number; // tns:fieldDecimal
        DatSalesPriceGrossRounded?: number; // tns:fieldDecimal
        Margin?: number; // tns:fieldDecimal
        MarginRounded?: number; // tns:fieldDecimal
        DatMargin?: number; // tns:fieldDecimal
        DatMarginRounded?: number; // tns:fieldDecimal
        MarginGross?: number; // tns:fieldDecimal
        MarginGrossRounded?: number; // tns:fieldDecimal
        MarginPrognosis?: number; // tns:fieldDecimal
        MarginPrognosisGross?: number; // tns:fieldDecimal
        DatMarginGross?: number; // tns:fieldDecimal
        DatMarginGrossRounded?: number; // tns:fieldDecimal
        PurchasePrice?: number; // tns:fieldDecimal
        PurchasePriceRounded?: number; // tns:fieldDecimal
        PurchasePricePrognosis?: number; // tns:fieldDecimal
        DatPurchasePrice?: number; // tns:fieldDecimal
        DatPurchasePriceRounded?: number; // tns:fieldDecimal
        PurchasePriceGross?: number; // tns:fieldDecimal
        PurchasePriceGrossRounded?: number; // tns:fieldDecimal
        PurchasePricePrognosisGross?: number; // tns:fieldDecimal
        DatPurchasePriceGross?: number; // tns:fieldDecimal
        DatPurchasePriceGrossRounded?: number; // tns:fieldDecimal
        LastValuationDataYear?: number; // tns:fieldInteger
        LastValuationDataMonth?: number; // tns:fieldInteger
        LastValuationDataMonthSer?: number; // tns:fieldInteger
        PrognosisMileageDat?: number; // tns:fieldInteger
        PrognosisMileageUser?: number; // tns:fieldInteger
        LastValuationDate?: string; // tns:fieldDateTime
        ExpertsSurveyDate?: string; // tns:fieldDate
        SurveyorUserId?: string; // tns:fieldString
        Currency?: string; // tns:fieldString
        DefaultPlatformPresent?: string; // tns:fieldString
        SignMilageUnit?: string; // tns:fieldString
        Obsolete?: string; // tns:fieldString
        ObsoletePrognosis?: number; // tns:fieldInteger
        DisplayGross?: boolean; // tns:fieldBoolean
        DisplayRounded?: boolean; // tns:fieldBoolean
        ResultInformation?: string; // tns:fieldString
        DefaultTiresPrice?: number; // tns:fieldDecimal
        DatDefaultTiresPrice?: number; // tns:fieldDecimal
        SignDeterminatedDate?: boolean; // tns:fieldBoolean
        DeterminatedDate?: string; // tns:fieldDate
        Version?: string; // tns:fieldString4
        Approval?: string; // tns:fieldString
        ContractNo?: string; // tns:fieldString
        ValuationType?: string; // tns:fieldString
        ExtendedMileageCorrection?: boolean; // tns:fieldBoolean
        ModelYear?: number; // tns:fieldInteger
        RegionNo?: number; // tns:fieldInteger
        Region?: string; // tns:fieldString
        Condition: Condition;
        Parameters: Parameters;
        Forecasts: Forecasts;
        CountryFlagAfterSale?: string; // tns:fieldString3
        CountryFlagBeforeSale?: string; // tns:fieldString3
        Vehicle?: Vehicle;
    }

    // ValueInfluencingFactor
    export interface ValueInfluencingFactor {
        GroupKey?: string; // tns:fieldString
        Type?: string; // tns:fieldString
        CalculationType?: string; // tns:fieldString
        Fixed?: boolean; // tns:fieldBoolean
        CostCalculationType?: string; // tns:fieldString
        IntervalInMonths?: number; // tns:fieldInteger
        Name?: string; // tns:fieldString
        AmountFixed?: number; // tns:fieldDecimal
        AmountPerc?: number; // tns:fieldDecimal
        AmountPercMinValue?: number; // tns:fieldDecimal
        AmountPercMaxValue?: number; // tns:fieldDecimal
        ModificationType?: string; // tns:fieldString
    }

    // Vehicle_KbaNumbersN
    export interface Vehicle_KbaNumbersN {
        KbaNumber?: string[]; // xs:string
    }

    // Vehicle_NationalCodeAustria
    export interface Vehicle_NationalCodeAustria {
        NationalCodeAustria?: string[]; // tns:fieldString6
    }

    // Vehicle_PaintTypes
    export interface Vehicle_PaintTypes {
        PaintType: string[]; // tns:fieldString2
    }

    // Vehicle_UpperBodies
    export interface Vehicle_UpperBodies {
        UpperBody?: UpperBody[];
    }

    // Vehicle
    export interface Vehicle {
        VehicleIdentNumber?: string; // tns:fieldString17
        DatECode?: string; // tns:fieldString15
        Container?: string; // tns:fieldString5
        ConstructionYear?: number; // tns:fieldInteger
        DatConstructionYear?: number; // tns:fieldInteger
        ConstructionMonth?: number; // tns:fieldInteger
        ConstructionTime?: number; // tns:fieldInteger
        ConstructionTimeFrom?: number; // tns:fieldInteger
        ConstructionTimeTo?: number; // tns:fieldInteger
        ConstructionTimePriceList?: number; // tns:fieldInteger
        InitialRegistration?: string; // tns:fieldDate
        RecentRegistration?: string; // tns:fieldDate
        MileageEstimated?: number; // tns:fieldInteger
        MileageOdometer?: number; // tns:fieldInteger
        MileageAccordingUser?: number; // tns:fieldDecimal
        MileageType?: string; // tns:fieldString60
        MileageComment?: string; // tns:fieldString
        SalesDescription?: string; // tns:fieldString4000
        VehicleTypeName?: string; // tns:fieldString80
        VehicleTypeNameN?: string; // tns:fieldString80
        DatVehicleTypeNameN?: string; // tns:fieldString80
        ManufacturerName?: string; // tns:fieldString80
        DatManufacturerName?: string; // tns:fieldString80
        BaseModelName?: string; // tns:fieldString80
        DatBaseModelName?: string; // tns:fieldString80
        SubModelName?: string; // tns:fieldString80
        DatSubModelName?: string; // tns:fieldString80
        EngineNameManual?: string; // tns:fieldString80
        BodyNameManual?: string; // tns:fieldString80
        WheelbaseNameManual?: string; // tns:fieldString80
        PropulsionNameManual?: string; // tns:fieldString80
        DrivingCabNameManual?: string; // tns:fieldString80
        TonnageNameManual?: string; // tns:fieldString80
        ConstructionNameManual?: string; // tns:fieldString80
        SuspensionNameManual?: string; // tns:fieldString80
        AxleCountNameManual?: string; // tns:fieldString80
        EquipmentLineNameManual?: string; // tns:fieldString80
        GearboxNameManual?: string; // tns:fieldString80
        ContainerName?: string; // tns:fieldString
        ContainerNameN?: string; // tns:fieldString80
        DatContainerNameN?: string; // tns:fieldString80
        MainTypeGroupName?: string; // tns:fieldString
        VehicleType?: number; // tns:fieldInteger
        Manufacturer?: number; // tns:fieldInteger
        BaseModel?: number; // tns:fieldInteger
        AlternativeVehicleType?: number; // tns:fieldInteger
        AlternativeManufacturer?: number; // tns:fieldInteger
        AlternativeBaseModel?: number; // tns:fieldInteger
        SubModel?: number; // tns:fieldInteger
        AlternativeSubModel?: number; // tns:fieldInteger
        MainTypeGroup?: string; // tns:fieldString
        IdentificationSource?: string; // xs:string
        Country?: string; // xs:string
        CountryTarget?: string; // tns:fieldString3
        isDisengaged?: boolean; // xs:boolean
        withoutDistinctionEquStandardSpecial?: boolean; // xs:boolean
        IsWithManualTypeNames?: boolean; // tns:fieldBoolean
        IsDisengagedN?: boolean; // tns:fieldBoolean
        WithoutDistinctionEquStandardSpecialN?: boolean; // tns:fieldBoolean
        IsUniversalSubModel?: boolean; // tns:fieldBoolean
        VinAccuracy?: number; // tns:fieldInteger
        VinActive?: boolean; // tns:fieldBoolean
        VinEquipmentChanged?: boolean; // tns:fieldBoolean
        VinChecksum?: string; // tns:fieldCharacter
        IsConnectedCarSupported?: boolean; // tns:fieldBoolean
        IsConnectedCarEligible?: boolean; // tns:fieldBoolean
        IsConnectedCarDataDeliveryPush?: boolean; // tns:fieldBoolean
        IsConnectedCarDataDeliveryPull?: boolean; // tns:fieldBoolean
        ReleaseIndicator?: string; // xs:string
        KbaNumbersN?: Vehicle_KbaNumbersN;
        NationalCodeAustria?: Vehicle_NationalCodeAustria;
        TypeOfConstruction?: string; // tns:fieldString40
        ConstructionYearManual?: string; // tns:fieldString40
        ColorScheme?: string; // tns:fieldString3
        ColorSchemeManual?: string; // tns:fieldString30
        ColorVariant?: string; // tns:fieldString
        PaintTypes?: Vehicle_PaintTypes;
        GeneralInspectionDate?: string; // xs:string
        LastRegistration?: string; // tns:fieldDate
        ManufacturerOrderKey?: string; // tns:fieldString30
        MidTermReviewDate?: string; // tns:fieldDate
        SubModelVariant?: number; // tns:fieldInteger
        TokenColorScheme?: string; // tns:fieldString3
        VehicleTypeAUFromKba?: string; // tns:fieldString50
        VehicleTypeFromKba?: string; // tns:fieldString50
        VehicleTypeFromManufacturer?: string; // tns:fieldString50
        Colorcode?: number; // tns:fieldInteger
        MainTypeSubstitution?: number; // tns:fieldDecimal
        SubTypeSubstitution?: number; // tns:fieldInteger
        NextVehicleInspection?: string; // tns:fieldDate
        OriginalPrice?: number; // tns:fieldDecimal
        DatOriginalPrice?: number; // tns:fieldDecimal
        OriginalPriceGross?: number; // tns:fieldDecimal
        DatOriginalPriceGross?: number; // tns:fieldDecimal
        RentalCarClass?: number; // tns:fieldInteger
        RegistrationData?: RegistrationData;
        OriginalPriceInfo?: OriginalPriceInfo;
        Engine?: Engine;
        Maintenance?: Maintenance;
        RunningExpenses?: RunningExpenses;
        TechInfo?: TechInfo;
        Equipment?: Equipment;
        Tires?: Tires;
        DATECodeEquipment?: equipSequence; // tns:equipSequence
        VINResult?: VINResult;
        TokenOfVinResult?: string; // tns:fieldString
        UpperBodies?: Vehicle_UpperBodies;
        DomusVehicleData?: DomusVehicleData;
        VehicleDataItaly?: VehicleDataItaly;
        MetaPositions?: MetaPositions;
        BuildYear?: number; // tns:fieldInteger
        OperatingHours?: number; // tns:fieldInteger
        MileageInMiles?: number; // tns:fieldInteger
        LastVehicleInspection?: string; // tns:fieldDate
        VehicleCondition?: string; // tns:fieldString
    }

    // VehicleDataItaly
    export interface VehicleDataItaly {
        VehicleTypeItaly?: string; // tns:fieldString
        ManufacturerItaly?: string; // tns:fieldString
        BaseModelItaly?: string; // tns:fieldString
        SubModelItaly?: string; // tns:fieldString
        VehicleTypeNameItaly?: string; // tns:fieldString
        ManufacturerNameItaly?: string; // tns:fieldString
        BaseModelNameItaly?: string; // tns:fieldString
        SubModelNameItaly?: string; // tns:fieldString
    }

    // Work
    export interface Work {
        Type: string; // xs:string
        WageLevel?: number; // tns:fieldDecimal
        Units?: number; // tns:fieldDecimal
        PricePerUnit?: number; // tns:fieldDecimal
        Price?: number; // tns:fieldDecimal
    }

    // WorkSummaryItaly
    export interface WorkSummaryItaly {
        WorkSummaryItemItaly?: WorkSummaryItemItaly[];
    }

    // WorkSummaryItemItaly
    export interface WorkSummaryItemItaly {
        Identifier?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        Hours?: number; // tns:fieldDecimal
        WagePerHour?: number; // tns:fieldDecimal
        TotalWage?: number; // tns:fieldDecimal
        DiscountPerc?: number; // tns:fieldDecimal
        DiscountValue?: number; // tns:fieldDecimal
        TotalWageCorrected?: number; // tns:fieldDecimal
    }

    // Works
    export interface Works {
        Work?: Work[];
    }

    // fieldString
    // fieldString
    export interface fieldString {
        _attr_nil?: boolean;
        _attr_overwrite?: boolean;
        _attr_origin?: string;
        _value: string; // xs:string
    }

    // fieldString1000
    // fieldString1000
    export interface fieldString1000 {}

    // fieldString2
    // fieldString2
    export interface fieldString2 {}

    // fieldString30
    // fieldString30
    export interface fieldString30 {}

    // fieldString5
    // fieldString5
    export interface fieldString5 {}

    // fieldString10
    // fieldString10
    export interface fieldString10 {}

    // fieldString4
    // fieldString4
    export interface fieldString4 {}

    // fieldString8
    // fieldString8
    export interface fieldString8 {}

    // fieldString40
    // fieldString40
    export interface fieldString40 {}

    // fieldCharacter
    // fieldCharacter
    export interface fieldCharacter {}

    // fieldString9
    // fieldString9
    export interface fieldString9 {}

    // fieldString6
    // fieldString6
    export interface fieldString6 {}

    // fieldString80
    // fieldString80
    export interface fieldString80 {}

    // fieldString3
    // fieldString3
    export interface fieldString3 {}

    // fieldString17
    // fieldString17
    export interface fieldString17 {}

    // fieldString15
    // fieldString15
    export interface fieldString15 {}

    // fieldString60
    // fieldString60
    export interface fieldString60 {}

    // fieldString4000
    // fieldString4000
    export interface fieldString4000 {}

    // fieldString50
    // fieldString50
    export interface fieldString50 {}

    // fieldString200
    // fieldString200
    export interface fieldString200 {}

    // fieldString12
    // fieldString12
    export interface fieldString12 {}

    // fieldString20
    // fieldString20
    export interface fieldString20 {}

    // fieldString100
    // fieldString100
    export interface fieldString100 {}

    // fieldInteger
    // fieldInteger
    export interface fieldInteger {
        _attr_nil?: boolean;
        _attr_overwrite?: boolean;
        _attr_origin?: string;
        _attr_limit?: number;
        _value: string; // xs:string
    }

    // fieldDecimal
    // fieldDecimal
    export interface fieldDecimal {
        _attr_nil?: boolean;
        _attr_overwrite?: boolean;
        _attr_origin?: string;
        _attr_limit?: number;
        _value: number; // xs:decimal
    }

    // fieldDateTime
    // fieldDateTime
    export interface fieldDateTime {
        _attr_nil?: boolean;
        _attr_overwrite?: boolean;
        _attr_origin?: string;
        _value: string; // xs:string
    }

    // address
    // address
    export interface address {
        Title?: string; // tns:fieldString
        TitleEntry?: string; // tns:fieldString
        CompanyName?: string; // tns:fieldString
        LastName?: string; // tns:fieldString
        NameLong?: string; // tns:fieldString
        FirstName?: string; // tns:fieldString
        Birthday?: string; // tns:fieldDate
        Birthplace?: string; // tns:fieldString
        Country?: string; // tns:fieldString
        ExtCustomerNumber?: string; // tns:fieldString
        VatEntitled?: boolean; // tns:fieldBoolean
        TaxNumber?: string; // tns:fieldString
        CustomerNumber?: string; // tns:fieldString
        CustomerType?: string; // tns:fieldString
        CustomerTypeShort?: string; // tns:fieldString
        Addition?: string; // tns:fieldString
        Street?: string; // tns:fieldString
        StreetNumber?: string; // tns:fieldString
        StreetZipCode?: string; // tns:fieldString
        StreetCity?: string; // tns:fieldString
        PoBox?: string; // tns:fieldString
        PoBoxZipCode?: string; // tns:fieldString
        PoBoxCity?: string; // tns:fieldString
        EMail?: string; // tns:fieldString
        PhoneBusiness?: string; // tns:fieldString
        PhonePersonal?: string; // tns:fieldString
        PhoneMobile?: string; // tns:fieldString
        Fax?: string; // tns:fieldString
        Bank?: string; // tns:fieldString
        BIC?: string; // tns:fieldString
        BLZ?: string; // tns:fieldString
        AccountNo?: string; // tns:fieldString
        IBAN?: string; // tns:fieldString
        UsageFlag?: string; // tns:fieldString
        Language?: string; // tns:fieldString
        MaidenName?: string; // tns:fieldString
        Profession?: string; // tns:fieldString
        PrivatePolicy?: PrivatePolicy;
    }

    // fieldDate
    // fieldDate
    export interface fieldDate {
        _attr_nil?: boolean;
        _attr_overwrite?: boolean;
        _attr_origin?: string;
        _value: string; // xs:string
    }

    // fieldBoolean
    // fieldBoolean
    export interface fieldBoolean {
        _attr_nil?: boolean;
        _attr_overwrite?: boolean;
        _attr_origin?: string;
        _value: string; // xs:string
    }

    // equipSequence
    // equipSequence
    export interface equipSequence {
        EquipmentPosition: EquipmentPosition[];
    }

    // fieldBinary
    // fieldBinary
    export interface fieldBinary {
        _attr_nil?: boolean;
        _attr_overwrite?: boolean;
        _attr_origin?: string;
        _value: string; // xs:base64Binary
    }

    // MTPLWearRus
    // MTPLWearRus
    export interface MTPLWearRus {
        WearGroup?: string; // tns:fieldString
        WearAge?: number; // tns:fieldDecimal
        WearMileage?: number; // tns:fieldInteger
        WearAdditional?: number; // tns:fieldDecimal
        WearPercent?: number; // tns:fieldDecimal
        WearPrice?: number; // tns:fieldDecimal
    }

    // parkingDentPositions
    // parkingDentPositions
    export interface parkingDentPositions {
        ParkingDentPosition?: parkingDentPosition[]; // tns:parkingDentPosition
    }

    // parkingDentPosition
    // parkingDentPosition
    export interface parkingDentPosition {
        DentNumber?: number; // tns:fieldInteger
        DentSize?: number; // tns:fieldInteger
        HighStrengthOrAluminium?: boolean; // tns:fieldBoolean
        AdhesiveMaterial?: boolean; // tns:fieldBoolean
        OutOfReach?: boolean; // tns:fieldBoolean
        EdgeOrFold?: boolean; // tns:fieldBoolean
        PointyOrDeep?: boolean; // tns:fieldBoolean
        Overstretched?: boolean; // tns:fieldBoolean
        PolishedScratches?: boolean; // tns:fieldBoolean
    }

    // iflPositionData
    // iflPositionData
    export interface iflPositionData {
        IflWorkData?: iflWorkData[]; // tns:iflWorkData
        IflLacquerData?: iflWorkData[]; // tns:iflWorkData
        IflMaterialData?: iflMaterialData[]; // tns:iflMaterialData
    }

    // iflWorkData
    // iflWorkData
    export interface iflWorkData {
        Amount?: number; // tns:fieldInteger
        Time?: number; // tns:fieldDecimal
    }

    // iflMaterialData
    // iflMaterialData
    export interface iflMaterialData {
        Amount?: number; // tns:fieldInteger
        Price?: number; // tns:fieldDecimal
    }

    // calcResult
    // calcResult
    export interface calcResult {
        MaterialPositions?: MaterialPositions; // tns:MaterialPositions
        MaterialPositionsMaintenance?: MaterialPositions; // tns:MaterialPositions
        AdditionalCostsPositions?: AdditionalCostsPositions; // tns:AdditionalCostsPositions
        AdditionalCostsPositionsMaintenance?: AdditionalCostsPositions; // tns:AdditionalCostsPositions
        ExtensionPositions?: ExtensionPositions;
        LabourPositions?: LabourPositions; // tns:LabourPositions
        LabourPositionsMaintenance?: LabourPositions; // tns:LabourPositions
        LacquerPositions?: LacquerPositions;
        SurchargesDiscounts?: SurchargeDiscountPositions; // tns:SurchargeDiscountPositions
        DiscountPositions?: DiscountPositions;
        DeductionsPositions?: DeductionsPositions;
        DeductiblePartsPositions?: DeductiblePartsPositions;
        InspectionPositions?: InspectionPositions; // tns:InspectionPositions
        PriceDate?: string; // tns:fieldDate
        RepairCalculationSummary?: CalculationSummary; // tns:CalculationSummary
        MaterialProtocol?: repairProtocol; // tns:repairProtocol
        LabourProtocol?: repairProtocol; // tns:repairProtocol
        LacquerProtocol?: repairProtocol; // tns:repairProtocol
        CalcProtocol?: CalculationProtocol; // tns:CalculationProtocol
        Legends?: Legends; // tns:Legends
        CalcType?: string; // tns:fieldString
        BlanketCalculation?: BlanketCalculation;
        PrintData?: string; // tns:fieldString
        DamageSegmentation?: DamageSegmentation;
    }

    // MaterialPositions
    // MaterialPositions
    export interface MaterialPositions {
        MaterialPosition?: MaterialPosition[];
    }

    // spHistPositionsSeq
    // spHistPositionsSeq
    export interface spHistPositionsSeq {
        SparePartHistoryPosition: SparePartHistoryPosition[];
    }

    // AdditionalCostsPositions
    // AdditionalCostsPositions
    export interface AdditionalCostsPositions {
        AdditionalCostsPosition?: AdditionalCostsPosition[];
    }

    // LabourPositions
    // LabourPositions
    export interface LabourPositions {
        LabourPosition?: LabourPosition[]; // tns:LabourPosition
        DentPrepressPosition?: LabourPosition[]; // tns:LabourPosition
        DentDtCommPosition?: LabourPosition[]; // tns:LabourPosition
        DentDtCommSumPosition?: LabourPosition[]; // tns:LabourPosition
        DentBvatPosition?: LabourPosition[]; // tns:LabourPosition
        DentAXAWinterthurPosition?: LabourPosition[]; // tns:LabourPosition
        DentFCRPosition?: LabourPosition[]; // tns:LabourPosition
        DentVFFSPosition?: LabourPosition[]; // tns:LabourPosition
        DentVaudoisePosition?: LabourPosition[]; // tns:LabourPosition
        DentZurichPosition?: LabourPosition[]; // tns:LabourPosition
        DentATPosition?: LabourPosition[]; // tns:LabourPosition
        DentOWNPosition?: LabourPosition[]; // tns:LabourPosition
        DentHUKPosition?: LabourPosition[]; // tns:LabourPosition
        OpelGoodwillLabourPosition?: LabourPosition[]; // tns:LabourPosition
    }

    // LabourPosition
    // LabourPosition
    export interface LabourPosition {
        DATProcessId?: number; // tns:fieldInteger
        LabourPositionKind?: number; // tns:fieldInteger
        DomusProcessId?: string; // tns:fieldString
        Location?: string; // tns:fieldString
        RepairType?: string; // tns:fieldString
        LabourPosId?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        WageType?: string; // tns:fieldString
        ManualWageType?: boolean; // tns:fieldBoolean
        WageLevel?: number; // tns:fieldInteger
        Duration?: number; // tns:fieldDecimal
        ManualDuration?: boolean; // tns:fieldBoolean
        ValueTotal?: number; // tns:fieldDecimal
        ManualPosition?: boolean; // tns:fieldBoolean
        Includes?: string; // tns:fieldString
        AdditionalDesc?: string; // tns:fieldString
        AmountDesc?: string; // tns:fieldString
        CorrPerc?: number; // tns:fieldDecimal
        ValueTotalCorrected?: number; // tns:fieldDecimal
        Amount?: number; // tns:fieldInteger
        hasRequiredByPosition?: boolean; // tns:fieldBoolean
        RequiredByPosition?: calcPosReference; // tns:calcPosReference
        IncludedPositions?: IncludedPositions;
        Extended?: boolean; // tns:fieldBoolean
        IncludedInCalculation?: boolean; // tns:fieldBoolean
        Method?: string; // tns:fieldString
        HardshipAllowanceInd?: string; // tns:fieldCharacter
        optionalPositionIncluded?: boolean; // tns:fieldBoolean
        LabourPositionState?: string; // tns:fieldString
        LabourPositionPriceState?: string; // tns:fieldString
        LabourPositionTimeState?: string; // tns:fieldString
        size?: number; // tns:fieldInteger
        PriceOrigin?: string; // tns:fieldString
        TimeOrigin?: string; // tns:fieldString
        WorkNumber?: string; // tns:fieldString
        WorkNumberOrigin?: string; // tns:fieldString
        DATWorkNumber?: string; // tns:fieldString
        Length?: number; // tns:fieldDecimal
        Width?: number; // tns:fieldDecimal
        WorkIndication?: number; // tns:fieldInteger
        DentsCalculationMethod?: string; // tns:fieldString
        FlagOrigin?: string; // tns:fieldString
        SparePartNumber?: string; // tns:fieldString
        ExternalId?: string; // tns:fieldString
        ValueInPosition?: boolean; // tns:fieldBoolean
    }

    // calcPosReference
    // calcPosReference
    export interface calcPosReference {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
    }

    // LacquerPosition
    // LacquerPosition
    export interface LacquerPosition {
        DATProcessId?: number; // tns:fieldInteger
        DomusProcessId?: string; // tns:fieldString
        Location?: string; // tns:fieldString
        LabourPosId?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        Level?: string; // tns:fieldString
        LevelDescription?: string; // tns:fieldString
        LevelManufacturer?: string; // tns:fieldString
        Duration?: number; // tns:fieldDecimal
        ManualDuration?: boolean; // tns:fieldBoolean
        Material?: number; // tns:fieldDecimal
        MaterialPoints?: number; // tns:fieldDecimal
        ValueTotal?: number; // tns:fieldDecimal
        ManualPosition?: boolean; // tns:fieldBoolean
        Includes?: string; // tns:fieldString
        AdditionalDesc?: string; // tns:fieldString
        AmountDesc?: string; // tns:fieldString
        CorrPerc?: number; // tns:fieldDecimal
        ValueTotalCorrected?: number; // tns:fieldDecimal
        WageLevel1?: number; // tns:fieldDecimal
        WageLevel2?: number; // tns:fieldDecimal
        WageLevel3?: number; // tns:fieldDecimal
        WageLevel4?: number; // tns:fieldDecimal
        RequiredByProcessId?: number; // tns:fieldInteger
        IsSpecific?: boolean; // tns:fieldBoolean
        IncludedPositions?: IncludedPositions;
        AdditionalLacquerNumber?: number; // tns:fieldInteger
        EffortDeductionPerc?: number; // tns:fieldDecimal
        IndicatorAdditionalLacquer?: string; // tns:fieldString2
        Finish?: string; // tns:fieldString
        Surface?: number; // tns:fieldDecimal
        Scratches?: number; // tns:fieldDecimal
        PriceOrigin?: string; // tns:fieldString
        TimeOrigin?: string; // tns:fieldString
        WorkNumber?: string; // tns:fieldString
        WorkNumberOrigin?: string; // tns:fieldString
        DATWorkNumber?: string; // tns:fieldString
        LacquerPositionState?: string; // tns:fieldString
        LacquerPositionTimeState?: string; // tns:fieldString
        LacquerPositionMaterialState?: string; // tns:fieldString
        LacquerPositionPriceState?: string; // tns:fieldString
        MaterialType?: number; // tns:fieldInteger
        Length?: number; // tns:fieldDecimal
        Width?: number; // tns:fieldDecimal
        WorkIndication?: number; // tns:fieldInteger
        FlagOrigin?: string; // tns:fieldString
        DentsSize?: number; // tns:fieldInteger
        SparePartNumber?: string; // tns:fieldString
        ExternalId?: string; // tns:fieldString
        Extended?: boolean; // tns:fieldBoolean
        IncludedInCalculation?: boolean; // tns:fieldBoolean
    }

    // SurchargeDiscountPositions
    // SurchargeDiscountPositions
    export interface SurchargeDiscountPositions {
        SurchargeDiscountPosition?: SurchargeDiscountPosition[]; // tns:SurchargeDiscountPosition
    }

    // SurchargeDiscountPosition
    // SurchargeDiscountPosition
    export interface SurchargeDiscountPosition {
        DATProcessId?: number; // tns:fieldInteger
        RepairType?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        Value?: number; // tns:fieldDecimal
        Applied?: boolean; // tns:fieldBoolean
    }

    // InspectionPositions
    // InspectionPositions
    export interface InspectionPositions {
        InspectionPosition?: InspectionPosition[];
    }

    // CalculationSummary
    // CalculationSummary
    export interface CalculationSummary {
        SparePartsCosts?: SparePartsCostsSummary; // tns:SparePartsCostsSummary
        AuxiliaryCosts?: AuxiliaryCostsSummary; // tns:AuxiliaryCostsSummary
        InspectionCosts?: InspectionCostsSummary; // tns:InspectionCostsSummary
        LabourCosts?: LabourCostsSummary; // tns:LabourCostsSummary
        LacquerCosts?: LacquerCostsSummary; // tns:LacquerCostsSummary
        VATReplacementPart?: number; // tns:fieldDecimal
        SurchargesDiscountsCosts?: SurchargesDiscountsCostsSummary; // tns:SurchargesDiscountsCostsSummary
        TotalNetCosts?: number; // tns:fieldDecimal
        TotalVAT?: number; // tns:fieldDecimal
        TotalGrossCosts?: number; // tns:fieldDecimal
        TotalNetDiscount?: number; // tns:fieldDecimal
        TotalVATDiscount?: number; // tns:fieldDecimal
        TotalGrossDiscount?: number; // tns:fieldDecimal
        TotalNetDeductible?: number; // tns:fieldDecimal
        TotalVATDeductible?: number; // tns:fieldDecimal
        TotalGrossDeductible?: number; // tns:fieldDecimal
        TotalNetAppreciation?: number; // tns:fieldDecimal
        TotalVATAppreciation?: number; // tns:fieldDecimal
        TotalGrossAppreciation?: number; // tns:fieldDecimal
        TotalAppreciationExVAT?: number; // tns:fieldDecimal
        BaseNetForWasteDisposalCosts?: number; // tns:fieldDecimal
        BaseVatForWasteDisposalCosts?: number; // tns:fieldDecimal
        BaseGrossForWasteDisposalCosts?: number; // tns:fieldDecimal
        TotalNetWasteDisposalCosts?: number; // tns:fieldDecimal
        TotalVatWasteDisposalCosts?: number; // tns:fieldDecimal
        TotalGrossWasteDisposalCosts?: number; // tns:fieldDecimal
        TotalInspectionCosts?: number; // tns:fieldDecimal
        TotalTaxNeutralCosts?: number; // tns:fieldDecimal
        TotalNetCorrected?: number; // tns:fieldDecimal
        TotalVATCorrected?: number; // tns:fieldDecimal
        TotalGrossCorrected?: number; // tns:fieldDecimal
        RetentionTotalNet?: number; // tns:fieldDecimal
        RetentionTotalGross?: number; // tns:fieldDecimal
        DiffCommonToOptimizedAmount?: number; // tns:fieldDecimal
        DiffCommonToOptimizedPerc?: number; // tns:fieldDecimal
        DiffToUpe?: number; // tns:fieldDecimal
        UpeInProcent?: number; // tns:fieldDecimal
        AmountCustomer?: number; // tns:fieldDecimal
        AmountInsurance?: number; // tns:fieldDecimal
        SumNet?: number; // tns:fieldDecimal
        SumGross?: number; // tns:fieldDecimal
        SumSparePartCosts?: number; // tns:fieldDecimal
        SumSmallSparePartCosts?: number; // tns:fieldDecimal
        SumOtherMaterialCosts?: number; // tns:fieldDecimal
        SumMiscellaneousCosts?: number; // tns:fieldDecimal
        SumLabourCosts?: number; // tns:fieldDecimal
        CalculationDeductionsSummary?: DeductionsSummary; // tns:DeductionsSummary
        AdditionalCostsFlatAmount?: number; // tns:fieldDecimal
        TotalNetDiscountExpert?: number; // tns:fieldDecimal
        TotalVATDiscountExpert?: number; // tns:fieldDecimal
        TotalGrossDiscountExpert?: number; // tns:fieldDecimal
        TotalPriceWear?: number; // tns:fieldDecimal
        TotalPriceWearRound?: number; // tns:fieldDecimal
        MetaPositions?: MetaPositions;
    }

    // SparePartsCostsSummary
    // SparePartsCostsSummary
    export interface SparePartsCostsSummary {
        AllSum?: number; // tns:fieldDecimal
        ProcurementCosts?: number; // tns:fieldDecimal
        ProcurementCostsFromParts?: number; // tns:fieldDecimal
        ProcurementCostsFromPartsPercentage?: number; // tns:fieldDecimal
        ConsumablesSurcharge?: number; // tns:fieldDecimal
        ConsumablesSurchargePercentage?: number; // tns:fieldDecimal
        SurchargeDeduction?: number; // tns:fieldDecimal
        DisposalCostsSpareParts?: number; // tns:fieldDecimal
        DisposalCostsSparePartsPercentage?: number; // tns:fieldDecimal
        SmallMaterialsLumpSum?: number; // tns:fieldDecimal
        TotalSum?: number; // tns:fieldDecimal
        SparePartsTotalWear?: number; // tns:fieldDecimal
        SparePartsTotalPartsWear?: number; // tns:fieldDecimal
        SparePartsTotalTotalWear?: number; // tns:fieldDecimal
    }

    // AuxiliaryCostsSummary
    // AuxiliaryCostsSummary
    export interface AuxiliaryCostsSummary {
        AuxiliaryCosts?: number; // tns:fieldDecimal
        DentsAdhesiveMaterial?: number; // tns:fieldDecimal
        TotalSum?: number; // tns:fieldDecimal
    }

    // InspectionCostsSummary
    // InspectionCostsSummary
    export interface InspectionCostsSummary {
        InspectionCosts?: number; // tns:fieldDecimal
        TotalSum?: number; // tns:fieldDecimal
    }

    // LabourCostsSummary
    // LabourCostsSummary
    export interface LabourCostsSummary {
        AllSum?: number; // tns:fieldDecimal
        Bodywork?: detailBlockSummary; // tns:detailBlockSummary
        Electric?: detailBlockSummary; // tns:detailBlockSummary
        Mechanic?: detailBlockSummary; // tns:detailBlockSummary
        OpelGoodWill?: detailBlockSummary; // tns:detailBlockSummary
        DentsPress?: detailBlockSummary; // tns:detailBlockSummary
        DentsPrePress?: detailBlockSummary; // tns:detailBlockSummary
        DentsGlobal?: detailBlockSummary; // tns:detailBlockSummary
        DentsFlat?: detailBlockSummary; // tns:detailBlockSummary
        ParkingDents?: detailBlockSummary; // tns:detailBlockSummary
        ParkingDentsLacquerDeduction?: detailBlockSummary; // tns:detailBlockSummary
        TotalSum?: number; // tns:fieldDecimal
        Discount?: Discount; // tns:Discount
        Wages?: DetailBlockSummaryWages; // tns:DetailBlockSummaryWages
        Works?: Works;
    }

    // detailBlockSummary
    // detailBlockSummary
    export interface detailBlockSummary {
        Type: string; // xs:string
        Units?: number; // tns:fieldDecimal
        PricePerUnit?: number; // tns:fieldDecimal
        PricePerUnitState?: string; // tns:fieldString
        FlatPrice?: number; // tns:fieldDecimal
        Discount?: Discount; // tns:Discount
        Price?: number; // tns:fieldDecimal
        Metallic?: number; // tns:fieldDecimal
    }

    // Discount
    // Discount
    export interface Discount {
        PriceBeforeDiscount: number; // tns:fieldDecimal
        DiscountPercentage?: number; // tns:fieldDecimal
        Discount: number; // tns:fieldDecimal
    }

    // DetailBlockSummaryWages
    // DetailBlockSummaryWages
    export interface DetailBlockSummaryWages {
        Wages?: detailBlockSummary[]; // tns:detailBlockSummary
    }

    // LacquerCostsSummary
    // LacquerCostsSummary
    export interface LacquerCostsSummary {
        DiscountTotalFlat?: Discount; // tns:Discount
        TotalFlat?: number; // tns:fieldDecimal
        WageAndMaterial?: detailBlockSummary; // tns:detailBlockSummary
        Wage?: detailBlockSummary; // tns:detailBlockSummary
        SpotRepairWage?: detailBlockSummary; // tns:detailBlockSummary
        TotalWages?: number; // tns:fieldDecimal
        DentsCivdWage?: detailBlockSummary; // tns:detailBlockSummary
        DentsCivdMaterial?: detailBlockSummary; // tns:detailBlockSummary
        Material?: LacquerMaterialSummary; // tns:LacquerMaterialSummary
        TotalSum?: number; // tns:fieldDecimal
        TotalTimeUnits?: number; // tns:fieldDecimal
    }

    // LacquerMaterialSummary
    // LacquerMaterialSummary
    export interface LacquerMaterialSummary {
        FlatPercentage?: number; // tns:fieldDecimal
        FlatAmount?: number; // tns:fieldDecimal
        MaterialUnit?: string; // tns:fieldString
        Surface?: detailBlockSummary; // tns:detailBlockSummary
        Overhauling?: detailBlockSummary; // tns:detailBlockSummary
        Replacement?: detailBlockSummary; // tns:detailBlockSummary
        Preparation?: detailBlockSummary; // tns:detailBlockSummary
        SpotRepair?: detailBlockSummary; // tns:detailBlockSummary
        LacquerMaterialManual?: detailBlockSummary; // tns:detailBlockSummary
        SpotRepairPrice?: number; // tns:fieldDecimal
        SpecialLacquerAwardN?: number; // tns:fieldDecimal
        LacquerConstants?: LacquerConstantSequence; // tns:LacquerConstantSequence
        MaterialGroups?: LacquerMaterialGroupSummarySequence; // tns:LacquerMaterialGroupSummarySequence
        DiscountMaterial?: Discount; // tns:Discount
        TotalSum?: number; // tns:fieldDecimal
        DisposalCosts?: number; // tns:fieldDecimal
        SumMaterialCorrected?: number; // tns:fieldDecimal
    }

    // LacquerConstantSequence
    // LacquerConstantSequence
    export interface LacquerConstantSequence {
        LacquerConstant: LacquerConstant[];
    }

    // LacquerMaterialGroupSummarySequence
    // LacquerMaterialGroupSummarySequence
    export interface LacquerMaterialGroupSummarySequence {
        LacquerMaterialGroupSummary: LacquerMaterialGroupSummary[];
    }

    // SurchargesDiscountsCostsSummary
    // SurchargesDiscountsCostsSummary
    export interface SurchargesDiscountsCostsSummary {
        Total?: number; // tns:fieldDecimal
        TotalSumMax?: number; // tns:fieldDecimal
    }

    // DeductionsSummary
    // DeductionsSummary
    export interface DeductionsSummary {
        DeductionsTotalGross?: number; // tns:fieldDecimal
        DeductionsTotalNet?: number; // tns:fieldDecimal
        DeductionsTotalVAT?: number; // tns:fieldDecimal
        NfoGeneralSumGross?: number; // tns:fieldDecimal
        NfoGeneralSumNet?: number; // tns:fieldDecimal
        NfoMaterialSumGross?: number; // tns:fieldDecimal
        NfoMaterialSumNet?: number; // tns:fieldDecimal
        NfoLacquerSumGross?: number; // tns:fieldDecimal
        NfoLacquerSumNet?: number; // tns:fieldDecimal
        PreDamageSumGross?: number; // tns:fieldDecimal
        PreDamageSumNet?: number; // tns:fieldDecimal
    }

    // repairProtocol
    // repairProtocol
    export interface repairProtocol {
        InvalidProcesses?: repairProcessList; // tns:repairProcessList
        UnspecificProcesses?: repairProcessList; // tns:repairProcessList
        PartOfCombinationProcesses?: repairProcessList; // tns:repairProcessList
        PartOfOtherProcesses?: repairProcessList; // tns:repairProcessList
        RemovedByCompositeProcesses?: repairProcessList; // tns:repairProcessList
        MissingLacquerInformationProcesses?: repairProcessList; // tns:repairProcessList
    }

    // repairProcessList
    // repairProcessList
    export interface repairProcessList {
        RepairProcess?: RepairProcess[];
    }

    // CalculationProtocol
    // CalculationProtocol
    export interface CalculationProtocol {
        ProtocolHints?: ProtocolHints;
        RemovedRepairTypesProtocol?: ProtocolEntriesWithRemoval; // tns:ProtocolEntriesWithRemoval
        PartsWithoutWorkProtocol?: ProtocolEntries; // tns:ProtocolEntries
        InvalidPositionsProtocol?: ProtocolEntries; // tns:ProtocolEntries
        ImplausiblePositionsProtocol?: ProtocolEntries; // tns:ProtocolEntries
        MissingInputProtocol?: ProtocolEntries; // tns:ProtocolEntries
        UnspecificPositionsProtocol?: ProtocolEntries; // tns:ProtocolEntries
        PartsOfOtherPositionProtocol?: ProtocolEntriesWithRemoval; // tns:ProtocolEntriesWithRemoval
        PartsOfCombinationPositionProtocol?: ProtocolEntriesWithRemoval; // tns:ProtocolEntriesWithRemoval
        RemovedByCompositePositionProtocol?: ProtocolEntriesWithRemoval; // tns:ProtocolEntriesWithRemoval
        MissingLacquerInformationProtocol?: ProtocolEntries; // tns:ProtocolEntries
        PredamagePositionsProtocol?: ProtocolEntries; // tns:ProtocolEntries
        SetupTimeRelevantPositionsProtocol?: ProtocolEntries; // tns:ProtocolEntries
        WithoutUsedPriceProtocol?: ProtocolEntries; // tns:ProtocolEntries
        WithoutManufacturerPriceProtocol?: ProtocolEntries; // tns:ProtocolEntries
        WithLacquerBlendingProtocol?: ProtocolEntries; // tns:ProtocolEntries
        SuppressedPositionsProtocol?: SuppressedPositionsProtocol;
        DentPositionsProtocol?: DentPositionsProtocol;
        PositionsWithMeasuresProtocol?: PositionsWithMeasuresProtocol;
        PriceCorrectionsProtocol?: PriceCorrectionsProtocol;
        AllinDeductionFromTotalInPercent?: number; // tns:fieldDecimal
        CalculationStatus?: number; // tns:fieldInteger
        PrintDate?: string; // tns:fieldDate
        IndicatorAniaCalculation?: number; // tns:fieldDecimal
        IndicatorDoubleRequest?: number; // tns:fieldDecimal
        IndicatorLacquerWaterbased?: number; // tns:fieldDecimal
        LastCalcUserId?: number; // tns:fieldInteger
        LastCalculationDate?: string; // tns:fieldDateTime
        LastCalculationUser?: string; // tns:fieldString
        StatusRepairCostsTakeover?: number; // tns:fieldInteger
        TransmissionState?: number; // tns:fieldDecimal
        ProtocolData?: string; // tns:fieldString
    }

    // ProtocolEntriesWithRemoval
    // ProtocolEntriesWithRemoval
    export interface ProtocolEntriesWithRemoval {
        ProtocolEntryWithRemoval?: ProtocolEntryWithRemoval[];
    }

    // ProtocolEntries
    // ProtocolEntries
    export interface ProtocolEntries {
        ProtocolEntry?: ProtocolEntry[];
    }

    // Legends
    // Legends
    export interface Legends {
        legends?: legend[]; // tns:legend
    }

    // legend
    // legend
    export interface legend {
        Flag?: string; // tns:fieldString
        Description?: string; // tns:fieldString
        Key?: string; // tns:fieldString
    }

    // calcResultItaly
    // calcResultItaly
    export interface calcResultItaly {
        PositionsItaly?: PositionsItaly;
        SummariesItaly?: SummariesItaly;
    }

    // domusCalcResult
    // domusCalcResult
    export interface domusCalcResult {
        calcResult?: calcResult; // tns:calcResult
        DomusAggregates?: DomusAggregates;
        DomusLacquerExtraCharges?: DomusLacquerExtraCharges;
        DomusExtraCharges?: DomusExtraCharges;
    }
} // namespace tns close {
