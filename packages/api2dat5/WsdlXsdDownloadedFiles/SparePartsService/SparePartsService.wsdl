<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- Generated by JAX-WS RI (http://jax-ws.java.net). RI's version is JAX-WS RI 2.2.9-b130926.1035 svn-revision#5f6196f2b90e9460065a4c2f4e30e065b245e51e. -->
<definitions targetNamespace="http://sphinx.dat.de/services/SpareParts" name="SpareParts" xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsp1_2="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:tns="http://sphinx.dat.de/services/SpareParts" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata">
  <types>
    <xsd:schema>
      <xsd:import namespace="http://www.dat.de/vxs" schemaLocation="https://www.dat.de/PartsInfo/services/SpareParts/SpareParts_schema1.xsd"/>
    </xsd:schema>
    <xsd:schema>
      <xsd:import namespace="http://sphinx.dat.de/services/SpareParts" schemaLocation="https://www.dat.de/PartsInfo/services/SpareParts/SpareParts_schema2.xsd"/>
    </xsd:schema>
  </types>
  <message name="getExtPartNoInfoByVinAndIntPartNo">
    <part name="parameters" element="tns:getExtPartNoInfoByVinAndIntPartNo"/>
  </message>
  <message name="getExtPartNoInfoByVinAndIntPartNoResponse">
    <part name="parameters" element="tns:getExtPartNoInfoByVinAndIntPartNoResponse"/>
  </message>
  <message name="getExtPartNoInfoByMfrAndExtPartNo">
    <part name="parameters" element="tns:getExtPartNoInfoByMfrAndExtPartNo"/>
  </message>
  <message name="getExtPartNoInfoByMfrAndExtPartNoResponse">
    <part name="parameters" element="tns:getExtPartNoInfoByMfrAndExtPartNoResponse"/>
  </message>
  <message name="getExtPartNoInfoByModelAndExtPartNo">
    <part name="parameters" element="tns:getExtPartNoInfoByModelAndExtPartNo"/>
  </message>
  <message name="getExtPartNoInfoByModelAndExtPartNoResponse">
    <part name="parameters" element="tns:getExtPartNoInfoByModelAndExtPartNoResponse"/>
  </message>
  <message name="getExtPartNoInfoByFullVehicleAndIntPartNo">
    <part name="parameters" element="tns:getExtPartNoInfoByFullVehicleAndIntPartNo"/>
  </message>
  <message name="getExtPartNoInfoByFullVehicleAndIntPartNoResponse">
    <part name="parameters" element="tns:getExtPartNoInfoByFullVehicleAndIntPartNoResponse"/>
  </message>
  <message name="getModelInfoByMfrAndExtPartNo">
    <part name="parameters" element="tns:getModelInfoByMfrAndExtPartNo"/>
  </message>
  <message name="getModelInfoByMfrAndExtPartNoResponse">
    <part name="parameters" element="tns:getModelInfoByMfrAndExtPartNoResponse"/>
  </message>
  <portType name="SpareParts">
    <operation name="getExtPartNoInfoByVinAndIntPartNo">
      <input wsam:Action="getExtPartNoInfoByVinAndIntPartNo" message="tns:getExtPartNoInfoByVinAndIntPartNo"/>
      <output wsam:Action="http://sphinx.dat.de/services/SpareParts/SpareParts/getExtPartNoInfoByVinAndIntPartNoResponse" message="tns:getExtPartNoInfoByVinAndIntPartNoResponse"/>
    </operation>
    <operation name="getExtPartNoInfoByMfrAndExtPartNo">
      <input wsam:Action="getExtPartNoInfoByMfrAndExtPartNo" message="tns:getExtPartNoInfoByMfrAndExtPartNo"/>
      <output wsam:Action="http://sphinx.dat.de/services/SpareParts/SpareParts/getExtPartNoInfoByMfrAndExtPartNoResponse" message="tns:getExtPartNoInfoByMfrAndExtPartNoResponse"/>
    </operation>
    <operation name="getExtPartNoInfoByModelAndExtPartNo">
      <input wsam:Action="getExtPartNoInfoByModelAndExtPartNo" message="tns:getExtPartNoInfoByModelAndExtPartNo"/>
      <output wsam:Action="http://sphinx.dat.de/services/SpareParts/SpareParts/getExtPartNoInfoByModelAndExtPartNoResponse" message="tns:getExtPartNoInfoByModelAndExtPartNoResponse"/>
    </operation>
    <operation name="getExtPartNoInfoByFullVehicleAndIntPartNo">
      <input wsam:Action="getExtPartNoInfoByFullVehicleAndIntPartNo" message="tns:getExtPartNoInfoByFullVehicleAndIntPartNo"/>
      <output wsam:Action="http://sphinx.dat.de/services/SpareParts/SpareParts/getExtPartNoInfoByFullVehicleAndIntPartNoResponse" message="tns:getExtPartNoInfoByFullVehicleAndIntPartNoResponse"/>
    </operation>
    <operation name="getModelInfoByMfrAndExtPartNo">
      <input wsam:Action="getModelInfoByMfrAndExtPartNo" message="tns:getModelInfoByMfrAndExtPartNo"/>
      <output wsam:Action="http://sphinx.dat.de/services/SpareParts/SpareParts/getModelInfoByMfrAndExtPartNoResponse" message="tns:getModelInfoByMfrAndExtPartNoResponse"/>
    </operation>
  </portType>
  <binding name="SparePartsPortBindingBinding" type="tns:SpareParts">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document"/>
    <operation name="getExtPartNoInfoByVinAndIntPartNo">
      <soap:operation soapAction="getExtPartNoInfoByVinAndIntPartNo"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getExtPartNoInfoByMfrAndExtPartNo">
      <soap:operation soapAction="getExtPartNoInfoByMfrAndExtPartNo"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getExtPartNoInfoByModelAndExtPartNo">
      <soap:operation soapAction="getExtPartNoInfoByModelAndExtPartNo"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getExtPartNoInfoByFullVehicleAndIntPartNo">
      <soap:operation soapAction="getExtPartNoInfoByFullVehicleAndIntPartNo"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
    <operation name="getModelInfoByMfrAndExtPartNo">
      <soap:operation soapAction="getModelInfoByMfrAndExtPartNo"/>
      <input>
        <soap:body use="literal"/>
      </input>
      <output>
        <soap:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="SpareParts">
    <port name="SparePartsPortBinding" binding="tns:SparePartsPortBindingBinding">
      <soap:address location="https://www.dat.de/PartsInfo/services/SpareParts"/>
    </port>
  </service>
</definitions>

