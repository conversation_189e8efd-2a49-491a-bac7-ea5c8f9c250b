<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" targetNamespace="http://www.dat.eu/myClaim/soap/v2/MyClaimExternalAuthenticationService" xmlns:tns="http://www.dat.eu/myClaim/soap/v2/MyClaimExternalAuthenticationService" xmlns:xs="http://www.w3.org/2001/XMLSchema">

  <xs:element name="authenticateInterfacePartner" type="tns:authenticateInterfacePartner"/>

  <xs:element name="authenticateInterfacePartnerResponse" type="tns:authenticateInterfacePartnerResponse"/>

  <xs:element name="authenticateUser" type="tns:authenticateUser"/>

  <xs:element name="authenticateUserResponse" type="tns:authenticateUserResponse"/>

  <xs:element name="generateToken" type="tns:generateToken"/>

  <xs:element name="generateTokenResponse" type="tns:generateTokenResponse"/>

  <xs:element name="getProductVersion" type="tns:getProductVersion"/>

  <xs:element name="getProductVersionResponse" type="tns:getProductVersionResponse"/>

  <xs:complexType name="authenticateUser">
    <xs:sequence>
      <xs:element name="arg0" type="xs:string" minOccurs="0"/>
      <xs:element name="arg1" type="xs:string" minOccurs="0"/>
      <xs:element name="arg2" type="xs:string" minOccurs="0"/>
      <xs:element name="arg3" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="authenticateUserResponse">
    <xs:sequence>
      <xs:element name="return" type="tns:datCredentials" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="datCredentials">
    <xs:sequence>
      <xs:element name="authType" type="tns:authenticationMethod" minOccurs="0"/>
      <xs:element name="authorizationErrorMessage" type="xs:string" minOccurs="0"/>
      <xs:element name="authorizationErrorReason" type="tns:authorizationErrorCode" minOccurs="0"/>
      <xs:element name="customerNumber" type="xs:string" minOccurs="0"/>
      <xs:element name="datrcbkb" type="tns:datrcbkb" minOccurs="0"/>
      <xs:element name="freeProduct" type="xs:boolean"/>
      <xs:element name="httpMethod" type="tns:httpMethodEnum" minOccurs="0"/>
      <xs:element name="interfacePartnerNumber" type="xs:string" minOccurs="0"/>
      <xs:element name="interfacePartnerType" type="tns:interfaceType" minOccurs="0"/>
      <xs:element name="password" type="xs:string" minOccurs="0"/>
      <xs:element name="userAgent" type="xs:string" minOccurs="0"/>
      <xs:element name="username" type="xs:string" minOccurs="0"/>
      <xs:element name="wsFunctionName" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="datrcbkb">
    <xs:complexContent>
      <xs:extension base="tns:datrcpda">
        <xs:sequence>
          <xs:element name="benuBes" type="xs:string" minOccurs="0"/>
          <xs:element name="benuId" type="xs:string" minOccurs="0"/>
          <xs:element name="cbmdLnr" type="xs:long" minOccurs="0"/>
          <xs:element name="cugrLnr" type="xs:long" minOccurs="0"/>
          <xs:element name="cugrName" type="xs:string" minOccurs="0"/>
          <xs:element name="customerNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="fremdFr" type="xs:unsignedShort" minOccurs="0"/>
          <xs:element name="idExtern" type="xs:string" minOccurs="0"/>
          <xs:element name="kzAktiv" type="xs:unsignedShort" minOccurs="0"/>
          <xs:element name="lastFailedLogin" type="xs:dateTime" minOccurs="0"/>
          <xs:element name="lnr" type="xs:long" minOccurs="0"/>
          <xs:element name="nrFailedLogins" type="xs:int" minOccurs="0"/>
          <xs:element name="passwort" type="xs:string" minOccurs="0"/>
          <xs:element name="standard" type="xs:unsignedShort" minOccurs="0"/>
          <xs:element name="tempLocked" type="xs:unsignedShort" minOccurs="0"/>
          <xs:element name="userStatus" type="xs:boolean"/>
          <xs:element name="zpLae" type="xs:dateTime" minOccurs="0"/>
          <xs:element name="zpLlog" type="xs:dateTime" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="datrcpda">
    <xs:complexContent>
      <xs:extension base="tns:baseBusinessObject">
        <xs:sequence>
          <xs:element name="cpdaLnr" type="xs:long" minOccurs="0"/>
          <xs:element name="defaultDatrcpkk" type="tns:datrcpkk" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="baseBusinessObject" abstract="true">
    <xs:sequence/>
  </xs:complexType>

  <xs:complexType name="datrcpkk">
    <xs:complexContent>
      <xs:extension base="tns:baseBusinessObject">
        <xs:sequence>
          <xs:element name="agericht" type="xs:string" minOccurs="0"/>
          <xs:element name="anrede" type="xs:string" minOccurs="0"/>
          <xs:element name="anredeDru" type="xs:string" minOccurs="0"/>
          <xs:element name="cpkkLnr" type="xs:long" minOccurs="0"/>
          <xs:element name="defaultAddress" type="tns:datrcpan" minOccurs="0"/>
          <xs:element name="EMail" type="xs:string" minOccurs="0"/>
          <xs:element name="firma" type="xs:string" minOccurs="0"/>
          <xs:element name="geburt" type="xs:dateTime" minOccurs="0"/>
          <xs:element name="gfuehrer" type="xs:string" minOccurs="0"/>
          <xs:element name="kzAktiv" type="xs:unsignedShort" minOccurs="0"/>
          <xs:element name="lnr" type="xs:long" minOccurs="0"/>
          <xs:element name="name" type="xs:string" minOccurs="0"/>
          <xs:element name="name1" type="xs:string" minOccurs="0"/>
          <xs:element name="name2" type="xs:string" minOccurs="0"/>
          <xs:element name="name3" type="xs:string" minOccurs="0"/>
          <xs:element name="nameGeb" type="xs:string" minOccurs="0"/>
          <xs:element name="nameKurz" type="xs:string" minOccurs="0"/>
          <xs:element name="nameLang" type="xs:string" minOccurs="0"/>
          <xs:element name="ortGeb" type="xs:string" minOccurs="0"/>
          <xs:element name="telFax" type="xs:string" minOccurs="0"/>
          <xs:element name="telGesch" type="xs:string" minOccurs="0"/>
          <xs:element name="telMobil" type="xs:string" minOccurs="0"/>
          <xs:element name="telPrivat" type="xs:string" minOccurs="0"/>
          <xs:element name="typ" type="xs:int"/>
          <xs:element name="vorname" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="datrcpan">
    <xs:complexContent>
      <xs:extension base="tns:baseBusinessObject">
        <xs:sequence>
          <xs:element name="adrLkz" type="xs:string" minOccurs="0"/>
          <xs:element name="latitude" type="xs:double" minOccurs="0"/>
          <xs:element name="lnr" type="xs:long" minOccurs="0"/>
          <xs:element name="longitude" type="xs:double" minOccurs="0"/>
          <xs:element name="ort" type="xs:string" minOccurs="0"/>
          <xs:element name="plzPostf" type="xs:string" minOccurs="0"/>
          <xs:element name="plzStr" type="xs:string" minOccurs="0"/>
          <xs:element name="postfach" type="xs:string" minOccurs="0"/>
          <xs:element name="strNr" type="xs:string" minOccurs="0"/>
          <xs:element name="strasse" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

  <xs:complexType name="authenticateInterfacePartner">
    <xs:sequence>
      <xs:element name="arg0" type="xs:string" minOccurs="0"/>
      <xs:element name="arg1" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="authenticateInterfacePartnerResponse">
    <xs:sequence/>
  </xs:complexType>

  <xs:complexType name="generateToken">
    <xs:sequence>
      <xs:element name="request" type="tns:generateTokenRequest"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="generateTokenRequest">
    <xs:sequence>
      <xs:element name="customerLogin" type="xs:string"/>
      <xs:element name="customerNumber" type="xs:string"/>
      <xs:element name="customerPassword" type="xs:string"/>
      <xs:element name="includePermissionData" type="xs:boolean" minOccurs="0"/>
      <xs:element name="interfacePartnerNumber" type="xs:string"/>
      <xs:element name="interfacePartnerSignature" type="xs:string"/>
      <xs:element name="productVariant" type="xs:string" nillable="true" minOccurs="0"/>
      <xs:element name="restrictions" type="tns:stringStringPair" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="stringStringPair">
    <xs:sequence/>
    <xs:attribute name="key" type="xs:string" use="required"/>
    <xs:attribute name="value" type="xs:string" use="required"/>
  </xs:complexType>

  <xs:complexType name="generateTokenResponse">
    <xs:sequence>
      <xs:element name="token" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="getProductVersion">
    <xs:sequence/>
  </xs:complexType>

  <xs:complexType name="getProductVersionResponse">
    <xs:sequence>
      <xs:element name="productVersion" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <xs:simpleType name="authenticationMethod">
    <xs:restriction base="xs:string">
      <xs:enumeration value="JWT"/>
      <xs:enumeration value="RENOVATIO"/>
      <xs:enumeration value="SIGNATURE"/>
      <xs:enumeration value="PASSWORD"/>
      <xs:enumeration value="DO_LOGIN"/>
      <xs:enumeration value="NO_INFO"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="authorizationErrorCode">
    <xs:restriction base="xs:string">
      <xs:enumeration value="AUTHORIZATION_PARAMETERS_ARE_NOT_COMPLETE"/>
      <xs:enumeration value="SESSION_ALREADY_INVALIDATED"/>
      <xs:enumeration value="SIGNATURE_CHECK_FAILED"/>
      <xs:enumeration value="USER_MANAGEMENT_SERVER_AUTHORIZATION_FAILED"/>
      <xs:enumeration value="USER_HAS_NO_PERMISSIONS"/>
      <xs:enumeration value="UNKNOWN_ERROR"/>
      <xs:enumeration value="COUNCURRENT_USER_COUNT_EXHAUSTED"/>
      <xs:enumeration value="USER_HAS_NO_VALID_LOGIN"/>
      <xs:enumeration value="USER_LOCKED_TEMPORARILY"/>
      <xs:enumeration value="USER_LOCKED_PERMANENTLY"/>
      <xs:enumeration value="USER_NOT_ACTIVE"/>
      <xs:enumeration value="USER_DELETED"/>
      <xs:enumeration value="INTERFACE_PARTNER_SIGNATURE_CHECK_FAILED"/>
      <xs:enumeration value="INTERFACE_PARTNER_HAS_NO_PERMISSION"/>
      <xs:enumeration value="TOKEN_EXPIRED"/>
      <xs:enumeration value="TOKEN_CHECK_FAILED"/>
      <xs:enumeration value="UNEXPECTED_AUTHORIZATION_METHOD"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="httpMethodEnum">
    <xs:restriction base="xs:string">
      <xs:enumeration value="GET"/>
      <xs:enumeration value="POST"/>
      <xs:enumeration value="HEAD"/>
      <xs:enumeration value="PUT"/>
      <xs:enumeration value="OPTIONS"/>
      <xs:enumeration value="DELETE"/>
      <xs:enumeration value="TRACE"/>
      <xs:enumeration value="CONNECT"/>
      <xs:enumeration value="MOVE"/>
      <xs:enumeration value="PROXY"/>
      <xs:enumeration value="PRI"/>
      <xs:enumeration value="PATCH"/>
      <xs:enumeration value="UNKOWN"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="interfaceType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="SOAP"/>
      <xs:enumeration value="REST"/>
      <xs:enumeration value="GUI"/>
      <xs:enumeration value="NO_INFO"/>
    </xs:restriction>
  </xs:simpleType>
</xs:schema>

