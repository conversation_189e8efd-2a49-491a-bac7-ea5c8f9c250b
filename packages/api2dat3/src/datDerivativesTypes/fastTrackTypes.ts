import * as DAT5 from '@wedat/api';

//================================== common fast-track types ==================================

// this structure used mainly grapa to convert fastTrack datId damages to dvn repairPositions and vice vees
export type FtCommonDefaultConfigType = {
    ftRepair: FtRepairType;
    // ftDvnMap: FtDvnMapType; // this object convert to ftDvnMapItems
    ftDvnMapItems: FtDvnMapItem[];
    ftGroups: FtGroupsType;
    ftDamages: FtDamagesType;
};

export interface FtDvnMapType {
    [key: string]: number[];
}

export interface FtDvnMapItem {
    datId: string;
    datECodeMask: string;
    dvns: number[];
}

export interface FtRepairType {
    elemId: string;
    // corresponding class  FastTrackRepairPosition[]
    repair: ftRepairItemType[];
    vxsRepairPositions?: DAT5.MyClaimExternalService_schema2.RepairPosition[];
}

export interface ftRepairItemType {
    dvn?: number; // Replace element with forced DVN. Please don't use this feature, because dvn can be different for different DatECode
    repairType: string; // see package eu.dat.myclaim.model.dictionary enum RepairType
    repairProcessType?: string;

    dentingMethod?: string;
    dentNumberLess?: number;
    dentNumberMore?: number;

    kindOfRepair?: string; // seems tobe optional not setup by vxs
    flatRate?: boolean; // seems tobe optional not setup by vxs

    countSpotRepair?: number;
    lacquerLevel?: number;
    price?: number;
    worktime?: number; // the same as a time?
    time?: number;

    partNumber?: string;

    datrcadxs?: {
        cadxCemiLnr: number;
        cadxWert: string;
    }[];
}

export interface FtVxsRepairsType {
    damageId: string;
    elemOrGroupId: string;

    vxsRepairPositions: Partial<DAT5.MyClaimExternalService_schema2.RepairPosition>[];
}

// can be obtatain by https://www.dat.de//myClaim/json/test/GetRP?claim=claimID

// Exemple repair from documentation:

// Replace element
// [{"repairType":"E"}]
// Replace element with forced DVN
// [{"dvn":44910, "repairType":"E"}]
// Replace element and paint it Level 2
// [{"repairType":"E"},
// {"repairType":"L", "lacquerLevel":2}]
// Paint element, level 2
// [{"repairType":"L","lacquerLevel":2}]
// Repair element 2,5 hours
// [{"repairType":"I","time":2.5}]
// Force repair element 1 hour and paint it level 1
// [{"repairType":"/","time":1},
// {"repairType":"L", "lacquerLevel":1}]
// Force repair with flat rate price
// [{"repairType":"/","price":40, "kindOfRepair":"general", "flatRate" : true}]
// Force painting with flat rate price
// [{"repairType":"L","lacquerLevel":4, "price":100}]
// Force repair with flat rate price glass element
// [{"repairType":"/","price":120,"kindOfRepair":"glass", "flatRate" : true}]
// Replace parts with fixed labour cost of €200
// [{"repairType":"E"}, "datrcadxs": [{"cadxCemiLnr":994,"cadxWert":"200"},{"cadxCemiLnr":211,"cadxWert":"2"}]]
// [{"repairType": "E","datrcadxs": [{"cadxCemiLnr": 994,	"cadxWert": "200"},	{"cadxCemiLnr": 211,"cadxWert": "2"}]}]
// Repair for Hail/dents
// [{"repairType": "I", "repairProcessType": "DENTS",
// "dentingMethod": "VFFS_ASEAI",
// "dentNumberLess": 5.0,
// "dentNumberMore": 7.0,
// "worktime": 1.4}]
// Spot Repairs “C”
// [{"repairType": "C","countSpotRepair": 1}]

// de.dat.vxs.entity.RepairPositions =>

// importRepairPosition

// protected RepairPosition importRepairPosition( de.dat.vxs.entity.RepairPosition repairPosition, List<RepairPosition> myClaimRepairPositions, CalculationSettings calculationSettings )

export interface FtGroupsType {
    [key: string]: string[];
}

export interface FtDamagesType {
    [key: string]: (
        | string
        | {
              id: string;
              label: string | { [key: string]: string };
          }
    )[];
}
