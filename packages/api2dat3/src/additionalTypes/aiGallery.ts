export interface AiGallery {
    claimId: string;
    claimStatus: string;
    aiResults: AIResults;
}

interface AIResults {
    damageSummaries: DamageSummary[];
    aiPipelineImages: ImageResult[];
}

interface DamageSummary {
    datId: string;
    severity: string;
    damageList: Damage[];
}

export interface Damage {
    modelConfidence: number;
    damageType: string;
    panelPosition: number[];
    panelDamagePerc: number;
}

export interface ImageResult {
    fileName: string;
    imageId: string;
    panelSegmentation?: ResultItem[];
    damageSegmentation?: ResultItem[];
    carDetection?: CarDetection[];
    imageValidationStatus: ValidationStatus;
    isInScope: boolean;
    classification: string;
}

interface CarDetection {
    id: string;
    bbox: Bbox;
    confidence: number;
}

interface ValidationStatus {
    isValid: boolean;
    errors: aiError[];
}

interface aiError {}

interface Bbox {
    xMin: number;
    xMax: number;
    yMin: number;
    yMax: number;
}

export type Polygon = number[];

export interface ResultItem {
    id: string;
    className: string;
    polygons: Polygon[];
    bbox: number[];
    confidence: number;
}
