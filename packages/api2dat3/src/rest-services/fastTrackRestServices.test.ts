import {
    calculateFastTrack,
    getFastTrackConfiguration,
    listDamages,
    listRepairs,
    loadFTClaim,
    saveFTClaim
} from './fastTrackRestServices';
import { SaveFTClaimParams } from './fastTrackRestTypes';

const claimIdTest = 26057827;

describe.skip('fastTrackInterface', () => {
    it('listDamages', async () => {
        const result = await listDamages();

        expect(result.length).toBeGreaterThanOrEqual(0);
    });
    it('listRepairs', async () => {
        const result = await listRepairs();

        expect(result.length).toBeGreaterThanOrEqual(0);
    });
    it('getFastTrackConfiguration', async () => {
        const result = await getFastTrackConfiguration({ contractId: claimIdTest });

        expect(Object.keys(result.fastTrackGroups).length).toBeGreaterThanOrEqual(0);
    }, 100_000);
    it('calculateFastTrack', async () => {
        const result = await calculateFastTrack({ contractId: claimIdTest });

        expect(result).toEqual({});
    });
    it('loadFTClaim', async () => {
        const result = await loadFTClaim({ contractId: claimIdTest });

        expect(Object.keys(result).length).toBeGreaterThanOrEqual(1);
    });
    it('saveFTClaim', async () => {
        const params: SaveFTClaimParams = {
            contractId: claimIdTest,
            fastTrack: {
                elements: [{ elementId: 'DATID_0004', userComment: '', damageType: 'level3' }]
                // additionalElements: []
            }
        };

        const result = await saveFTClaim(params);

        expect(result).toEqual({});
    });
});
