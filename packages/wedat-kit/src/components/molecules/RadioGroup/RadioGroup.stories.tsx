import type { Meta, StoryObj } from '@storybook/react';
import { useState } from '@storybook/preview-api';
import { RadioGroup, type RadioItem } from './RadioGroup';
import { variants } from '../../atoms/Radio/types';
import { globalSharedStories } from '../../shared/stories';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta: Meta<typeof RadioGroup> = {
    title: 'Components/Molecules/RadioGroup',
    component: RadioGroup,
    tags: ['autodocs'],
    argTypes: {
        ...globalSharedStories,
        radioItems: {
            description: 'RadioGroup items'
        },
        value: {
            description: 'Current selected value'
        },
        variant: {
            options: variants,
            control: {
                type: 'radio'
            },
            description: 'Defines the style of the Radio'
        }
    }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {},
    render: args => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [selectedItem, setSelectedItem] = useState('first');

        const radioItems: RadioItem[] = [
            {
                inputId: 'first',
                label: 'Costo materiali per punto materiale',
                value: 'first',
                'aria-label': 'First Item'
            },
            { inputId: 'second', label: 'Manodopera forfettaria (%)', value: 'second', 'aria-label': 'Second Item' },
            { inputId: 'third', label: 'Indicazione forfettaria (EUR)', value: 'third', 'aria-label': 'Third Item' }
        ];

        return (
            <div
                style={{
                    width: '90%'
                }}
            >
                <RadioGroup
                    {...args}
                    radioItems={radioItems}
                    value={selectedItem}
                    onChange={e => setSelectedItem(e.value)}
                />
            </div>
        );
    }
};

export const DisabledItem: Story = {
    args: {},
    render: args => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [selectedItem, setSelectedItem] = useState('first');

        const radioItems: RadioItem[] = [
            { inputId: 'first', label: 'First', value: 'first', 'aria-label': 'First Item' },
            { inputId: 'second', label: 'Second', value: 'second', disabled: true, 'aria-label': 'Second Item' },
            { inputId: 'third', label: 'Third', value: 'third', 'aria-label': 'Third Item' }
        ];

        return (
            <div
                style={{
                    width: '90%'
                }}
            >
                <RadioGroup
                    {...args}
                    radioItems={radioItems}
                    value={selectedItem}
                    onChange={e => setSelectedItem(e.value)}
                />
            </div>
        );
    }
};
