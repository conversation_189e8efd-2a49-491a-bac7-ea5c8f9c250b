import { FC } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { useArgs } from '@storybook/preview-api';
import { within, userEvent, expect } from '@storybook/test';
import { BoxSwitcher } from './BoxSwitcher';
import type { BoxSwitcherProps } from './types';
import { variants } from '../../../types';

const meta: Meta<BoxSwitcherProps> = {
    title: 'Components/Molecules/BoxSwitcher',
    component: BoxSwitcher as FC<BoxSwitcherProps>,
    tags: ['autodocs'],
    argTypes: {
        checked: {
            control: {
                type: 'boolean'
            },
            description: 'If true, the Switcher will be activated.'
        },
        // Control for Switcher style types
        variant: {
            options: variants,
            control: {
                type: 'radio'
            },
            description: 'The style and color of the Switcher.'
        },
        disabled: {
            control: {
                type: 'boolean'
            },
            description: 'If true, the Switcher will be disabled.'
        },
        invalid: {
            control: {
                type: 'boolean'
            },
            description: 'If true, the Switcher will be invalid.'
        }
    }
};

export default meta;
type Story = StoryObj<typeof meta>;

const testFn = async (canvasElement: HTMLElement, testId: string, disabled?: boolean) => {
    const canvas = within(canvasElement);
    const switcher = canvas.getByTestId(`${testId}-switcher`);

    await expect(switcher).toHaveAttribute('data-testid', `${testId}-switcher`);
    await userEvent.keyboard('{Tab}');
    await userEvent.keyboard('{Space}');

    if (!disabled) {
        await userEvent.click(switcher);
    }
};

// Example stories with default args
export const Default: Story = {
    args: {
        variant: 'primary',
        checked: false,
        testId: 'default',
        inputId: 'default',
        'aria-label': 'Default BoxSwitcher'
    },
    play: async ({ canvasElement, args: { testId = '', disabled = false } }) => {
        await testFn(canvasElement, testId, disabled);
    },
    render: args => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [_, updateArgs] = useArgs();
        return (
            <div
                style={{
                    width: '300px'
                }}
            >
                <BoxSwitcher {...(args as BoxSwitcherProps)} onChange={() => updateArgs({ checked: !args.checked })} />
            </div>
        );
    }
};
