@import url('../shared/styles.css');

.password-input-icon-field.required-empty .required_icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-required);
}

.password-toggle-icon {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    z-index: 1;
    cursor: pointer;
}

.password-toggle-icon.disabled {
    color: var(--color-dust-blue-500);
    pointer-events: none;
    cursor: default;
}

.input-password-input-root {
    width: 100%;
    height: 100%;
    padding: 0 12px;
    box-sizing: border-box;
    background: none;
    color: var(--color-dust-blue-900);
    border: 1px solid var(--color-dust-blue-100);
    border-radius: var(--wedat-kit-password-input-border-radius, var(--border-radius));
    outline: none;
    z-index: 0;
}

.input-password-input-root.has-mask {
    padding-right: 2rem;
}

.input-password-input-root {
    font: var(--font-note);
}

.input-password-input-root::placeholder {
    color: var(--color-dust-blue-500);
    font: var(--font-14);
}

.input-password-input-root.uppercase {
    text-transform: uppercase;
}

.input-password-input-root.required-empty {
    border-color: var(--color-required);
}

.input-password-input-root.no-border {
    border: none;
}

.input-password-input-root.only-border-bottom {
    border: none;
    border-radius: 0;
    border-bottom: 1px solid var(--color-deep-blue-100);
}

.input-password-input-root.bold {
    font-weight: var(--font-weight-bold);
}

.input-password-input-root.disabled {
    color: inherit;
    border-color: var(--color-disabled);
    background-color: var(--color-gray-50);
}

.input-password-input-root.invalid {
    border-color: var(--color-invalid);
}

.input-password-input-root.disabled.invalid {
    border-color: var(--color-disabled);
}

.password-input-floating-label.has-mask {
    max-width: max-content;
    right: 2rem;
}

.password-input-floating-label.has-mask.required-empty {
    right: 2.5rem;
}
