import { SidebarProps as PrimeDrawerProps } from 'primereact/sidebar';

export interface DrawerProps
    extends Omit<PrimeDrawerProps, 'unstyled' | 'dismissable' | 'aria-label' | 'tooltip' | 'tooltipOptions'> {
    testId?: string;
    width?: string;
    height?: string;
    bodyNoPadding?: boolean;
    arrowPosition?: 'left' | 'right';
    clickable?: boolean;
    halfScreen?: boolean;
    'aria-label': string;
    headerBorderBottom?: boolean;
    contentOverflowDisabled?: boolean;
    offset?: number;
    showHeader?: boolean;
    isAutoZIndex?: boolean;
}
