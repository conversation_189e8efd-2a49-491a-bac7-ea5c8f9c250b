import { useMemo } from 'react';
import { AccordionContext } from 'primereact/accordion';
import type { AccordionProps } from '../types';

interface Params extends Partial<AccordionProps> {
    cx: (...args: classNames.ArgumentArray) => string;
}

export const useAccordionPassThroughs = ({ tabClassName = '', cx }: Params) => {
    const pt = useMemo(() => {
        return {
            root: {
                className: cx('accordion-root', 'accordion')
            },
            accordiontab: {
                root: {
                    className: cx('accordion-accordiontab-root', `${tabClassName || ''}`.trim())
                },
                header: ({ context: { disabled, selected } }: { context: AccordionContext }) => ({
                    className: cx('accordiontab-header', {
                        disabled,
                        selected
                    })
                }),
                headerAction: {
                    className: cx('accordiontab-header-action')
                },
                headerIcon: {
                    className: cx('accordiontab-header-icon')
                },
                headerTitle: {
                    className: cx('accordiontab-header-title')
                },
                toggleableContent: {
                    className: cx('accordiontab-toggleable-content')
                },
                content: {
                    className: cx('accordiontab-content')
                },
                transition: {
                    classNames: {
                        appear: cx('appear'),
                        appearActive: cx('appear-active'),
                        appearDone: cx('appear-done'),
                        enter: cx('enter'),
                        enterActive: cx('enter-active'),
                        enterDone: cx('enter-done'),
                        exit: cx('exit'),
                        exitActive: cx('exit-active'),
                        exitDone: cx('exit-done')
                    },
                    addEndListener: () => undefined
                }
            }
        };
    }, [cx, tabClassName]);

    return {
        pt
    };
};
