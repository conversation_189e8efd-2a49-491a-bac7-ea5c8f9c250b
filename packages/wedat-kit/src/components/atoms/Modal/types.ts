import { DialogProps } from 'primereact/dialog';

export interface ModalProps
    extends Omit<DialogProps, 'unstyled' | 'modal' | 'resizable' | 'onResize' | 'onResizeEnd' | 'onResizeStart'> {
    testId?: string;
    unsetOverflow?: boolean;
    bodyNoPadding?: boolean;
    minWidth?: string;
    maxWidth?: string;
    bodyHeight?: string;
    fullWidth?: boolean;
    fullScreen?: boolean;
    isAutoZIndex?: boolean;
}
