import { FC } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { useArgs } from '@storybook/preview-api';
import { Dropdown } from './Dropdown';
import { Button } from '../Button';
import type { DropdownProps } from './types';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta: Meta<DropdownProps> = {
    title: 'Components/Atoms/Dropdown',
    component: Dropdown as FC<DropdownProps>,
    tags: ['autodocs'],
    argTypes: {
        side: {
            options: ['left', 'right'],
            control: {
                type: 'radio'
            }
        },
        verticalSide: {
            options: ['top', 'bottom'],
            control: {
                type: 'radio'
            }
        },
        overflow: {
            control: {
                type: 'boolean'
            }
        },
        width: {
            control: {
                type: 'text'
            }
        },
        minWidth: {
            control: {
                type: 'text'
            }
        }
    }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        testId: 'default',
        width: '260px',
        verticalSide: 'bottom',
        side: 'right'
    },
    render: args => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [_, updateArgs] = useArgs();

        const id = 'popup_menu';

        return (
            <div
                style={{
                    width: 'max-content',
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    margin: '150px'
                }}
            >
                <Button
                    label="Toggle Dropdown"
                    aria-label="Toggle Dropdown Button"
                    fitContent
                    onClick={() => updateArgs({ isOpen: !args.isOpen })}
                    aria-controls={id}
                    aria-haspopup
                />

                <Dropdown {...args} testId={args.testId} id={id} isOpen={args.isOpen}>
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: 2
                        }}
                    >
                        <Button label="Item 1" aria-label="First Item" />
                        <Button label="Item 2" aria-label="Second Item" variant="success" />
                        <Button
                            label="Item 3"
                            aria-label="Third Item"
                            variant="palette"
                            paletteStyles={{
                                bgColor: 'purple',
                                borderColor: 'purple',
                                textColor: 'white'
                            }}
                        />
                    </div>
                </Dropdown>
            </div>
        );
    }
};
