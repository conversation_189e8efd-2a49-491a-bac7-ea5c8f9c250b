export type PluginOptions = DAT2.Plugins.PluginBaseOptions & {
    contractId?: number;
    settings?: {
        networkType?: DAT2.NetworkType;
    };
    isHeader?: boolean;
    isOpen?: boolean;
    callbackClose?: () => void;
    isWithoutApi?: boolean;
    isFromFormBuilder?: {
        id: string;
        label?: string;
        visible?: boolean;
        formState: boolean;
        toggleFormState: () => void;
    };
};
