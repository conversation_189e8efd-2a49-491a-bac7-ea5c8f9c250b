import { media } from '@wedat/ui-kit/mediaQueries';
import styled, { css } from 'styled-components/macro';
import { Text } from '@wedat/ui-kit';

type ContainerProps = {
    isOpen: boolean;
};

export const RelazioneSectionStyled = styled.div`
    background-color: ${({ theme }) => theme.colors.white};
    padding: 24px;
    margin-bottom: 24px;
    border-radius: 16px;
    box-shadow:
        0px 8px 32px rgba(174, 174, 192, 0.2),
        0px -8px 32px rgba(255, 255, 255, 0.5);

    ${media.phoneBig`
        padding: 24px 16px;
    `};
`;

export const RelazioneSectionContent = styled.div<ContainerProps>`
    margin-top: 24px;

    ${media.phoneBig`
        ${({ isOpen }) =>
            !isOpen &&
            css`
                display: none;
            `}
    `};
`;

export const RelazioneSectionHeader = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
`;

export const RelazioneSectionTitle = styled(Text)`
    ${({ theme }) => theme.typography.noteBold};
    font-size: 18px;

    ${media.phoneBig`
        font-size: 16px;
    `};
`;

export const RelazioneSectionHeaderControl = styled.div`
    display: none;
    padding-left: 10px;

    ${media.phoneBig`
        display: flex;
        align-items: center;
        gap: 20px;
    `};
`;

export const RelazioneSectionButtonWrapper = styled.div<ContainerProps>`
    transition: transform 0.3s;

    ${({ isOpen }) =>
        isOpen &&
        css`
            transform: rotate(180deg);
        `}
`;
