import { decode as msgpackdecode } from '@msgpack/msgpack';
import { decode as b64decode } from 'base64-arraybuffer';
import { memo, useEffect, useMemo, useState } from 'react';
import { useUnit } from 'effector-react';
import { I18nextProvider } from 'react-i18next';

import { AiGallery } from '@dat/api2dat3/src/additionalTypes/aiGallery';
import { AiGallery2PluginOptions } from '@dat/ai-gallery-2/src/types/plugin';
import { PANEL_CLASSNAMES } from '@dat/ai-gallery-2/src/constants/panelClassname';
import { AiGallery2 } from '@dat/ai-gallery-2';
import { Preloader } from '@wedat/ui-kit';

import { pluginOptionsModel } from '../../stores/pluginOptionsModel';
import { fastTrackElementModel } from '../../stores/fastTrackElementModel';
import { i18n } from '../../i18n';
import { useElementSize } from '../../utils/hooks/useResizer';
import { AiCheck } from './AiCheck';
import { PickUpReturn } from './PickUpReturn';
import { SwitchOnlyDamage } from './SwitchOnlyDamage';
import { commonModel } from '../../stores/commonModel';
import { Fraud } from './Fraud';

import './fastTrackAiMode.css';
import { AttachmentWithBlob } from '@dat/api2dat3/src/datDerivativesTypes/datDerivativesTypes';
import { CONTRACT_ENTRIES_KEYS } from '@dat/core/constants';

export const FastTrackAiMode = memo<AiGallery2PluginOptions>(({ configuration }) => {
    const slidesPerView = configuration?.slidesPerView;

    const pluginOptions = useUnit(pluginOptionsModel.stores.pluginOptions);
    const contract = useUnit(pluginOptionsModel.stores.contract);
    const aiGallerySelectedPanelDatId = useUnit(fastTrackElementModel.stores.aiGallerySelectedPanelDatId);
    const isMobile = useUnit(commonModel.stores.isMobile);
    const summaryPanelOpened = useUnit(commonModel.stores.summaryPanelOpened);

    const { rentalCar, showOnlyAiDamages, showFraudDetection } = pluginOptions?.configuration || {};
    const isReadOnly = pluginOptions?.isReadOnly;

    const attachments = pluginOptions?.data?.attachmentsData?.listAttachmentsOfContractWithBlob;
    const pickUpFolderId = rentalCar?.pickUpFolderId;
    const returnFolderId = rentalCar?.returnFolderId;
    const pickUpMemoField = rentalCar?.pickUpMemoField || 'AIPickUp';
    const returnMemoField = rentalCar?.returnMemoField || 'AIReturn';
    const aiResultMemoField = rentalCar?.aiResultMemoField || CONTRACT_ENTRIES_KEYS.MEMO.AIResult;
    const customTemplateData = contract?.customTemplateData.entry;

    const [folderId, setFolderId] = useState<number | undefined>(undefined);
    const [showOnlyDamage, setShowOnlyDamage] = useState(!!showOnlyAiDamages);

    const {
        targetRef: refContainer,
        windowSize: { width, height }
    } = useElementSize({ showFullscreen: false, width: 0, height: 0 });

    const isTabletPanelOpened =
        window.matchMedia('(min-width: 863px) and (max-width: 1281px)').matches && summaryPanelOpened;
    const isMobileView = isMobile || isTabletPanelOpened;

    const fraudWarning = customTemplateData?.find(data => data.key === 'fraudWarning')?.value?._value;

    const AIJsonResponseVal = customTemplateData?.find(data => data.key === aiResultMemoField)?.value?._value;
    const AIPickUpVal = customTemplateData?.find(data => data.key === pickUpMemoField)?.value?._value;
    const AIReturnVal = customTemplateData?.find(data => data.key === returnMemoField)?.value?._value;
    const AIJsonResponse = AIJsonResponseVal ? (JSON.parse(AIJsonResponseVal) as AiGallery) : undefined;
    const AIPickUp = AIPickUpVal ? (JSON.parse(AIPickUpVal) as AiGallery) : undefined;
    const AIReturn = AIReturnVal ? (JSON.parse(AIReturnVal) as AiGallery) : undefined;
    const showPickUpReturn = !!AIPickUp && !!AIReturn && !!pickUpFolderId && !!returnFolderId;
    const isPickUp = folderId === pickUpFolderId && showPickUpReturn;
    const isReturn = folderId === returnFolderId && showPickUpReturn;
    const activeView = isPickUp ? 'pickUp' : isReturn ? 'return' : 'aiResult';

    const AIData = useMemo(() => (isPickUp ? AIPickUp : isReturn ? AIReturn : AIJsonResponse), [folderId]);
    const AIDataDecoded = {
        ...AIData,
        aiResults: {
            ...AIData?.aiResults,
            aiPipelineImages: AIData?.aiResults?.aiPipelineImages.map(img => ({
                ...img,
                damageSegmentation: img.damageSegmentation?.map(dmgSegm => ({
                    ...dmgSegm,
                    polygons:
                        typeof dmgSegm.polygons === 'string'
                            ? msgpackdecode(b64decode(dmgSegm.polygons))
                            : dmgSegm.polygons
                })),
                panelSegmentation: img.panelSegmentation?.map(pnlSegm => ({
                    ...pnlSegm,
                    polygons:
                        typeof pnlSegm.polygons === 'string'
                            ? msgpackdecode(b64decode(pnlSegm.polygons))
                            : pnlSegm.polygons
                }))
            }))
        }
    } as AiGallery;
    const aiImages = AIDataDecoded?.aiResults?.aiPipelineImages;
    const damageSums = AIDataDecoded?.aiResults?.damageSummaries;
    const onlyDamageImgs = aiImages?.filter(
        img =>
            img.damageSegmentation?.length &&
            damageSums.some(dmgSum => {
                const damageClassName = PANEL_CLASSNAMES[dmgSum.datId];
                const hasDamagedPanel = img.panelSegmentation?.some(panel => panel.className === damageClassName);
                return (
                    hasDamagedPanel &&
                    img.damageSegmentation?.some(dmgSeg =>
                        dmgSum.damageList.some?.(dmgEl => dmgEl.damageType === dmgSeg.className)
                    )
                );
            })
    );
    const currentAiImages = showOnlyDamage && onlyDamageImgs?.length ? onlyDamageImgs : aiImages;

    const filteredAttachments = useMemo(() => {
        const result: AttachmentWithBlob[] = [];
        attachments?.forEach(at => {
            if (!currentAiImages?.some(img => img.fileName.includes(at.fileName))) return;

            if (isPickUp) {
                at.documentID === pickUpFolderId && result.push(at);
                return;
            } else if (isReturn) {
                at.documentID === returnFolderId && result.push(at);
                return;
            }
            // handling rare case, when pickUp and return have photos with the same name
            const foundSameNameIndex = result.findIndex(resAt => resAt.fileName === at.fileName);
            if (foundSameNameIndex === -1) {
                result.push(at);
            } else if (result[foundSameNameIndex].documentID !== returnFolderId) {
                result[foundSameNameIndex] = at;
            }
        });
        return result;
    }, [attachments, AIData, showOnlyDamage]);

    const aiGalleryData = {
        ...pluginOptions?.data,
        claimData: {
            ...pluginOptions?.data?.claimData,
            aiGallery: AIDataDecoded
        },
        attachmentsData: pluginOptions?.data?.attachmentsData
            ? {
                  ...pluginOptions.data.attachmentsData,
                  listAttachmentsOfContractWithBlob: filteredAttachments
              }
            : undefined
    };

    // Fix bottom minislider scrolling (incorrect count of visible slides)
    // Fix panels/damages polygons layout for Safari and Firefox
    // TODO: look for possible better solution
    useEffect(() => {
        setTimeout(() => {
            const slides = refContainer.current?.querySelectorAll('.IMG-AI');
            if (!!slides?.length) {
                const parentHeight = slides[0].parentElement?.offsetHeight;
                slides.forEach(slide => {
                    if (parentHeight) slide.setAttribute('style', `height: ${parentHeight - 0.5}px;`);
                });
                window.dispatchEvent(new Event('resize'));
            }
        }, 100);
    }, [filteredAttachments, width, height, summaryPanelOpened]);

    return (
        <>
            {!aiImages && (
                <div className="weDat-ft-preloaderWrapper">
                    <Preloader isLoading fullScreen={false} />
                </div>
            )}
            <div
                ref={refContainer}
                className={`${isMobileView ? 'weDat-ft-aiGallery-container-mobile' : 'weDat-ft-aiGallery-container'}${
                    !aiImages ? ' weDat-ft-hidden' : ''
                }`}
            >
                <span className="weDat-ft-aiTopBar">
                    {!isMobileView && (
                        <div style={{ position: 'absolute', display: 'flex', alignItems: 'center' }}>
                            <AiCheck aiGallery={AIDataDecoded} isMobileView={isMobileView} />
                            {showPickUpReturn && (
                                <PickUpReturn
                                    activeView={activeView}
                                    onPickUpSelect={() => {
                                        setFolderId(pickUpFolderId);
                                    }}
                                    onReturnSelect={() => {
                                        setFolderId(returnFolderId);
                                    }}
                                    onAiResultSelect={() => {
                                        setFolderId(undefined);
                                    }}
                                />
                            )}
                            <SwitchOnlyDamage
                                initChecked={!!showOnlyDamage}
                                onChange={checked => {
                                    setShowOnlyDamage(checked);
                                }}
                            />
                            {!!showFraudDetection && <Fraud isFraud={!!fraudWarning && !!parseInt(fraudWarning)} />}
                        </div>
                    )}
                    {isMobileView && <AiCheck aiGallery={AIDataDecoded} isMobileView={isMobileView} />}
                </span>
                {!!aiGalleryData.attachmentsData?.listAttachmentsOfContractWithBlob?.length && (
                    <I18nextProvider i18n={i18n} defaultNS="ai-gallery">
                        <AiGallery2
                            options={{
                                selectedPanelDatId: aiGallerySelectedPanelDatId,
                                onChangeSelectedPanelDatId: fastTrackElementModel.events.setAiGallerySelectedPanelDatId,
                                data: aiGalleryData,
                                configuration: {
                                    slidesPerView,
                                    isReadOnly,
                                    AttachmentFolderId: folderId
                                }
                            }}
                        />
                    </I18nextProvider>
                )}

                {isMobileView && (
                    <span className="weDat-ft-aiBottomBar">
                        {showPickUpReturn && (
                            <PickUpReturn
                                activeView={activeView}
                                onPickUpSelect={() => {
                                    setFolderId(pickUpFolderId);
                                }}
                                onReturnSelect={() => {
                                    setFolderId(returnFolderId);
                                }}
                                onAiResultSelect={() => {
                                    setFolderId(undefined);
                                }}
                            />
                        )}
                        <SwitchOnlyDamage
                            initChecked={!!showOnlyDamage}
                            onChange={checked => {
                                setShowOnlyDamage(checked);
                            }}
                        />
                    </span>
                )}
            </div>
        </>
    );
});
