import { AiGallery } from '@dat/api2dat3/src/additionalTypes/aiGallery';
import { FC } from 'react';
import { useUnit } from 'effector-react';
import AiCheckLightAuthIcon from '../CommonComponents/Icons/AiCheckLightAuthIcon.svg?react';
import AiCheckOkIcon from '../CommonComponents/Icons/AiCheckOkIcon.svg?react';
import AiCheckOnSiteAuthIcon from '../CommonComponents/Icons/AiCheckOnSiteAuthIcon.svg?react';
import { useTranslation } from 'react-i18next';
import { pluginOptionsModel } from '../../stores/pluginOptionsModel';
import { getOrchestratorAssessment } from '@dat/shared-models/ai-claim/utils/getOrchestratorAssessment';

interface Props {
    aiGallery?: AiGallery;
    isMobileView?: boolean;
}

export const AiCheck: FC<Props> = ({ aiGallery, isMobileView }) => {
    const { t } = useTranslation();
    const pluginOptions = useUnit(pluginOptionsModel.stores.pluginOptions);
    const orchestrator = pluginOptions?.configuration?.orchestrator;
    const assessment = getOrchestratorAssessment({ AIResult: aiGallery, orchestrator });
    if (!assessment) return null;

    return (
        <div className="weDat-ft-aiCheck">
            {assessment === 'green' ? (
                <>
                    <AiCheckOkIcon style={{ marginRight: '12px' }} />
                    {!isMobileView && t('AiResult.OkForAI')}
                </>
            ) : assessment === 'yellow' ? (
                <>
                    <AiCheckLightAuthIcon style={{ marginRight: '12px' }} />
                    {!isMobileView && t('AiResult.LightAuthority')}
                </>
            ) : (
                <>
                    <AiCheckOnSiteAuthIcon style={{ marginRight: '12px' }} />
                    {!isMobileView && t('AiResult.OnSiteAuthority')}
                </>
            )}
        </div>
    );
};
