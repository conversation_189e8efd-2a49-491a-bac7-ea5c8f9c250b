import { FC, useEffect, useMemo } from 'react';
import { useUnit } from 'effector-react';
import { pluginOptionsModel } from '../../stores/pluginOptionsModel';
import { elementsSVG_css } from './elementsSVG';
import { fastTrackElementModel } from '../../stores/fastTrackElementModel';
import { genericSVGGraphicModel } from '../../stores/genericSVGGraphicModel';
import { getFastTrackViewsToShow } from '../../utils/getFastTrackViewsToShow';
import { SvgLoader, SvgProxy } from '@wedat/react-svgmt';
import './fastTrackSVG.css';

export interface FastTrackSVGProps {
    isMobile: boolean;
    isTabletPanelOpened?: boolean;
    onElementClick: (id: string) => void;
}

export const FastTrackSVG: FC<FastTrackSVGProps> = ({ isMobile, isTabletPanelOpened, onElementClick }) => {
    const pluginOptions = useUnit(pluginOptionsModel.stores.pluginOptions);
    const fastTrackSVG = useUnit(fastTrackElementModel.stores.$fastTrackSVG);
    const genericSVGGraphics = useUnit(genericSVGGraphicModel.stores.genericSVGGraphics);
    const { selectedElements, markElementId } = useUnit(fastTrackElementModel.stores.$fastTrackElements);
    const maxWidthSVG = pluginOptions?.configuration?.maxWidthSVG;
    const mobileHeightSVG = window.screen.height - 420;

    const fastTrackViewsToShow = getFastTrackViewsToShow(pluginOptions);

    const damageColors = pluginOptions?.configuration?.damageColors?.ftDamages || {};
    const defaultDamageColor = pluginOptions?.configuration?.damageColors?.default || '#ffffff';
    const damageColorsOpacity = pluginOptions?.configuration?.damageColors?.opacity;
    const damageClassNames = useMemo(() => Object.keys(damageColors || {}), [damageColors]);

    useEffect(() => {
        if (
            (!!fastTrackSVG?.graphicId && !fastTrackViewsToShow.includes(fastTrackSVG.graphicId)) ||
            (!fastTrackSVG?.graphicId && fastTrackViewsToShow.length)
        ) {
            const firstFastTrackSVGToShow = genericSVGGraphics.find(grap =>
                fastTrackViewsToShow.includes(grap.graphicId)
            );
            fastTrackElementModel.events.setFastTrackSVG(firstFastTrackSVGToShow || null);
        }
    }, [fastTrackViewsToShow]);

    let activeClassName = isMobile ? 'weDat-ft-main-svg-mobile' : 'weDat-ft-main-svg';

    const showFastTrackElementsMenu = pluginOptions?.configuration?.showFastTrackElementsMenu;
    if (showFastTrackElementsMenu && !isMobile && !isTabletPanelOpened)
        activeClassName = activeClassName + ' weDat-ft-main-svg-with-ft-elements-menu';

    const readOnlyClassName = `${activeClassName} wedat-ft-read-only`;
    const className = pluginOptions?.isReadOnly ? readOnlyClassName : activeClassName;

    return (
        <div className={className} style={isMobile ? { height: `${mobileHeightSVG}px` } : undefined}>
            <SvgLoader
                svgXML={fastTrackSVG?.svg}
                style={
                    isMobile && fastTrackSVG?.graphicId === 'default'
                        ? {
                              transform: 'rotate(90deg)',
                              width: `${mobileHeightSVG}px`,
                              height: 'auto'
                          }
                        : {
                              maxWidth: maxWidthSVG || '40em',
                              width: '100%',
                              height: 'auto'
                          }
                }
            >
                <SvgProxy
                    selector={'[id^="DATID_"] > path'}
                    class=""
                    onClick={(e: any) => {
                        onElementClick(e.target.parentElement.id);
                    }}
                />
                {selectedElements.map(({ elementId, damageType }, index) => {
                    let matchedClass = damageClassNames.find(cls =>
                        damageType?.toLowerCase().includes(cls.toLowerCase())
                    );
                    if (!matchedClass && damageColors?.default) {
                        matchedClass = 'default';
                    }

                    return (
                        <SvgProxy
                            key={index}
                            selector={`[id^="${elementId}"] > path`}
                            class={matchedClass || 'selectedElements'}
                        />
                    );
                })}
                <SvgProxy selector={`[id^="${markElementId}"] > path`} class={'markElement'} />

                <style>{elementsSVG_css(damageColors, defaultDamageColor, damageColorsOpacity)}</style>
            </SvgLoader>
        </div>
    );
};
