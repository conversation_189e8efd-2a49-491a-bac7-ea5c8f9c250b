import { FC } from 'react';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';
import { Table, Tooltip } from 'antd';
import { ColumnsType, ColumnType } from 'antd/lib/table';
import DeleteIcon from '../CommonComponents/Icons/DeleteIcon.svg?react';
import { fastTrackElementModel } from '../../stores/fastTrackElementModel';
import { getTranslationFromFastTrackConfiguration } from '../../utils/getTranslationFromFastTrackConfiguration';
import i18next from 'i18next';
import { IconBtn } from '../CommonComponents/IconBtn';
import { DAT3 } from '@dat/api2dat3/dat3';
import { pluginOptionsModel } from '../../stores/pluginOptionsModel';
import { MAIN_POSITION_DATID_PREFIXES, WHAT_ELSE_POSITION_POSTFIX } from '../../constants/positionKind';
import { AdditionalElementsList } from '../ManualPositions/AdditionalElementsList';
import { UploadImageInputButton } from '../UploadImageInputButton';
import { isSelectedByAI } from '../../utils/isSelectedByAI';

export const ListDamagesFT: FC<{ isMobile?: boolean }> = ({ isMobile }) => {
    const { t } = useTranslation();

    const pluginOptions = useUnit(pluginOptionsModel.stores.pluginOptions);
    const contractId = useUnit(pluginOptionsModel.stores.contractId);
    const contract = useUnit(pluginOptionsModel.stores.contract);
    const attachmentsData = useUnit(pluginOptionsModel.stores.attachmentsData);
    const attachments = attachmentsData?.listAttachmentsOfContractWithBlob;
    const { selectedElements, additionalElements } = useUnit(fastTrackElementModel.stores.$fastTrackElements);
    const fastTrackConfiguration = useUnit(pluginOptionsModel.stores.fastTrackConfiguration);
    const aiGallery = useUnit(pluginOptionsModel.stores.aiGallery);

    if (!fastTrackConfiguration) return null;

    const repairPositionsFromContract = contract?.Dossier?.RepairCalculation?.RepairPositions?.RepairPosition;

    const { partsImagesFolderId, showAiOrManualIcons } = pluginOptions?.configuration || {};
    const translationFromFastTrackConfiguration = getTranslationFromFastTrackConfiguration({
        fastTrackConfiguration,
        language: i18next.language
    });

    const mainRepairPositions: DAT3.FastTrackRestTypes.FastTrackElement[] = [];
    const additionalRepairPositions: DAT3.FastTrackRestTypes.FastTrackElement[] = [];

    selectedElements.forEach(el => {
        const isWhatElsePosition =
            isSelectedByAI(el) &&
            (el.userComment.endsWith(WHAT_ELSE_POSITION_POSTFIX) ||
                !aiGallery?.aiResults.damageSummaries.some(dmgSum => dmgSum.datId === el.elementId));
        const isAdditionalPosition =
            isWhatElsePosition || !MAIN_POSITION_DATID_PREFIXES.some(pref => el.elementId.startsWith(pref));
        isAdditionalPosition ? additionalRepairPositions.push(el) : mainRepairPositions.push(el);
    });

    const columnsftClaimDamages: ColumnsType<DAT3.FastTrackRestTypes.FastTrackElement> = [
        {
            width: 20,
            dataIndex: 'aiSelected',
            render: (_, record) => {
                // AI result from Mariusz has no userComment (undefined), but elements from common AIJsonResponse have userComment === `{datId}___{repairType}{someKey}`.
                // Manual positions always have userComment as empty string since it's a required property of the FastTrackElement type
                const isSelectedByAi = isSelectedByAI(record);
                const color = isSelectedByAi ? '#42A4FF' : '#FFB444';
                const text = isSelectedByAi ? 'AI' : 'M';
                const tooltipText = isSelectedByAi ? t('fastTrack.AddedByAI') : t('fastTrack.ManuallyAdded');

                return (
                    !!showAiOrManualIcons && (
                        <Tooltip title={tooltipText}>
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                                style={{ marginTop: '5px' }}
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <circle cx="10" cy="10" r="10" fill={color} />
                                <text x="50%" y="14" text-anchor="middle" fill="white" font-size="10px">
                                    {text}
                                </text>
                            </svg>
                        </Tooltip>
                    )
                );
            }
        },
        {
            title: 'fastTrack.Damage',
            dataIndex: 'elemLabel'
        },
        {
            title: 'fastTrack.DamageDegree',
            dataIndex: 'damageLabel',
            align: 'left',
            width: '30%'
        },
        ...(partsImagesFolderId
            ? [
                  {
                      width: 48,
                      render: (_, record) => {
                          const datProcessId = repairPositionsFromContract?.find(repPos =>
                              repPos.FastTrackOrigin?.endsWith(record.elementId)
                          )?.DATProcessId;

                          return datProcessId ? (
                              <UploadImageInputButton
                                  attachments={attachments}
                                  DATProcessId={datProcessId}
                                  contractId={contractId || 0}
                              />
                          ) : (
                              <Tooltip title={t('fastTrack.PartNotCalculated')}>
                                  <div style={{ width: '26px' }} onClick={e => e.stopPropagation()}>
                                      <UploadImageInputButton
                                          attachments={attachments}
                                          DATProcessId={datProcessId}
                                          contractId={contractId || 0}
                                      />
                                  </div>
                              </Tooltip>
                          );
                      }
                  } as ColumnType<DAT3.FastTrackRestTypes.FastTrackElement>
              ]
            : []),
        {
            width: 48,
            align: 'right',
            render: (_, record) => (
                <IconBtn
                    icon={<DeleteIcon />}
                    onClick={e => {
                        fastTrackElementModel.events.delFastTrackDamage(record.elementId);
                        e.stopPropagation();
                    }}
                />
            )
        }
    ];

    const columnsftClaimDamagesMobile: ColumnsType<any> = [
        {
            dataIndex: 'elemLabel',
            render: (_, record) => (
                <>
                    <div>{record.elemLabel}</div>
                    <div style={{ color: '#687792' }}>{record.damageLabel}</div>
                </>
            )
        },
        ...(partsImagesFolderId
            ? [
                  {
                      width: 48,
                      render: (_, record) => {
                          const datProcessId = repairPositionsFromContract?.find(repPos =>
                              repPos.FastTrackOrigin?.endsWith(record.elementId)
                          )?.DATProcessId;

                          return datProcessId ? (
                              <UploadImageInputButton
                                  attachments={attachments}
                                  DATProcessId={datProcessId}
                                  contractId={contractId || 0}
                              />
                          ) : (
                              <Tooltip title={'Part has not been calculated'}>
                                  <div style={{ width: '26px' }} onClick={e => e.stopPropagation()}>
                                      <UploadImageInputButton
                                          attachments={attachments}
                                          DATProcessId={datProcessId}
                                          contractId={contractId || 0}
                                      />
                                  </div>
                              </Tooltip>
                          );
                      }
                  } as ColumnType<DAT3.FastTrackRestTypes.FastTrackElement>
              ]
            : []),
        {
            width: 48,
            align: 'right',
            render: (_, record) => (
                <IconBtn
                    icon={<DeleteIcon />}
                    onClick={e => {
                        fastTrackElementModel.events.delFastTrackDamage(record.elementId);
                        e.stopPropagation();
                    }}
                />
            )
        }
    ];

    const columns = isMobile ? columnsftClaimDamagesMobile : columnsftClaimDamages;

    const generateTable = (
        repairPositions: DAT3.FastTrackRestTypes.FastTrackElement[],
        title: string,
        rowKey?: string
    ) => (
        <Table
            rowKey={rowKey || 'rowKey'}
            className="weDat-ft-list-damage"
            dataSource={repairPositions.map((el, index) => ({
                rowKey: el.elementId + index,
                ...el,
                elemLabel: t(`fastLocalNameDATID.${el.elementId}`).toUpperCase(),
                damageLabel: t(
                    `fastLocalNameDamages.${el.damageType}`,
                    translationFromFastTrackConfiguration[el.damageType] || el.damageType
                )
            }))}
            title={() => title}
            columns={columns.map((col, index) => ({
                key: index,
                ...col,
                title: t(col.title?.toString() || '').toString()
            }))}
            pagination={false}
            size="small"
            locale={{ emptyText: t('fastTrack.NoData') }}
            onRow={record => ({
                onClick: _ => {
                    fastTrackElementModel.events.setMarkElementId(record.elementId);
                }
            })}
        />
    );

    return (
        <>
            {!!mainRepairPositions.length && generateTable(mainRepairPositions, t('fastTrack.MainRepairPositions'))}
            {!!additionalRepairPositions.length &&
                generateTable(additionalRepairPositions, t('fastTrack.AdditionalRepairPositions'))}
            {!!additionalElements.length && <AdditionalElementsList />}
        </>
    );
};
