import { NumberInput, NumberInputProps } from '@wedat/ui-kit';
import { InputProps } from '@wedat/ui-kit/components/Input/types';
import { FC } from 'react';

interface Props extends NumberInputProps, Pick<InputProps, 'status'> {
    hideZeroValue?: boolean;
}

export const NumberInputField: FC<Props> = ({ hideZeroValue, ...rest }) => (
    <NumberInput {...rest} value={hideZeroValue && !rest.value ? '' : rest.value} />
);
