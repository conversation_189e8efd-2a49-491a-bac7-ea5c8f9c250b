import { Card, List } from 'antd';
import { useUnit } from 'effector-react';
import { FC, useState } from 'react';
import { fastTrackElementsMenuModel } from '../../stores/fastTrackElementsMenuModel';
import { pluginOptionsModel } from '../../stores/pluginOptionsModel';
import { ElementsMenuModal } from './ElementsMenuModal';
import { ElementsMenuDrawer } from './ElementsMenuDrawer';
import ChevronDownIcon from '../CommonComponents/Icons/ChevronDownIcon.svg?react';
import ChevronUpIcon from '../CommonComponents/Icons/ChevronUpIcon.svg?react';
import SelectedElementIcon from '../CommonComponents/Icons/SelectedElementIcon.svg?react';
import { scrollElement, checkIfScrollEnd } from '../../utils/scrollElement';
import { fastTrackElementModel } from '../../stores/fastTrackElementModel';

import './fastTrackElementsMenu.css';

export const FastTrackElementsMenu: FC<{ elementsMenuType?: 'drawer' | 'modal' }> = ({ elementsMenuType }) => {
    const pluginOptions = useUnit(pluginOptionsModel.stores.pluginOptions);
    const fastTrackElementsMenuState = useUnit(fastTrackElementsMenuModel.stores.$fastTrackElementsMenu);
    const fastTrackSVG = useUnit(fastTrackElementModel.stores.$fastTrackSVG);
    const { selectedElements } = useUnit(fastTrackElementModel.stores.$fastTrackElements);
    const selectedElementsIds = selectedElements.map(elem => elem.elementId);

    const [showScrollDownBtn, setShowScrollDownBtn] = useState(!checkIfScrollEnd('.weDat-ft-elements-list', 0, 'down'));
    const [showScrollUpBtn, setShowScrollUpBtn] = useState(false);

    const customTemplateData = pluginOptions?.data?.claimData?.contract?.customTemplateData.entry;
    const { fastTrackElementsMenu, showSVGFastTrack } = pluginOptions?.configuration || {};
    const fastTrackElementsMenuFiltered = fastTrackElementsMenu?.filter(
        menuGroup =>
            (!menuGroup.includedViews && !menuGroup.excludedViews && !menuGroup.condition) ||
            (menuGroup.condition &&
                customTemplateData?.some(
                    entry =>
                        entry.key === menuGroup.condition?.field && entry.value?._value === menuGroup.condition?.value
                )) ||
            (menuGroup.includedViews &&
                fastTrackSVG?.graphicId &&
                menuGroup.includedViews.includes(fastTrackSVG.graphicId) &&
                !menuGroup.excludedViews?.includes(fastTrackSVG.graphicId)) ||
            (menuGroup.excludedViews &&
                fastTrackSVG?.graphicId &&
                !menuGroup.excludedViews.includes(fastTrackSVG.graphicId))
    );

    if (!fastTrackElementsMenuFiltered?.length) return null;

    return (
        <div className={showSVGFastTrack ? 'weDat-ft-elements-menu' : 'weDat-ft-elements-menu-noSVG'}>
            {/* {(!fastTrackElementsMenuState || !!fastTrackElementsMenuModal) && ( */}
            {showScrollUpBtn && !!showSVGFastTrack && (
                <div className="weDat-ft-scroll-up-btn-container">
                    <div
                        className="weDat-ft-scroll-up-btn"
                        onClick={() => {
                            const newScrollVal = scrollElement('.weDat-ft-elements-list', 300, 'up');
                            if (newScrollVal === -1000) return;
                            setShowScrollUpBtn(!checkIfScrollEnd('.weDat-ft-elements-list', newScrollVal, 'up'));
                            setShowScrollDownBtn(true);
                        }}
                    >
                        <ChevronUpIcon />
                    </div>
                </div>
            )}

            <List
                className={
                    pluginOptions?.isReadOnly ? 'weDat-ft-elements-list wedat-ft-read-only' : 'weDat-ft-elements-list'
                }
                itemLayout="vertical"
            >
                {fastTrackElementsMenuFiltered.map((menuGroup, index) => (
                    <List.Item
                        key={index}
                        onClick={() => {
                            fastTrackElementsMenuModel.events.setFastTrackElementsMenu(menuGroup);
                        }}
                    >
                        <Card hoverable style={{ width: '100%' }}>
                            <div className="weDat-ft-elements-list-img">
                                <svg height="32" width="58">
                                    <image height="32" viewBox="0 0 32 58" xlinkHref={menuGroup.menuGroupImgUrl} />
                                </svg>
                                {!!menuGroup.elementDatIDs?.some(elemId => selectedElementsIds.includes(elemId)) && (
                                    <SelectedElementIcon />
                                )}
                            </div>
                            <Card.Meta title={menuGroup.menuGroupLabel || menuGroup.menuGroupId} />
                        </Card>
                    </List.Item>
                ))}
            </List>
            {showScrollDownBtn && !!showSVGFastTrack && !checkIfScrollEnd('.weDat-ft-elements-list', 0, 'down') && (
                <div className="weDat-ft-scroll-down-btn-container">
                    <div
                        className="weDat-ft-scroll-down-btn"
                        onClick={() => {
                            const newScrollVal = scrollElement('.weDat-ft-elements-list', 300, 'down');
                            if (newScrollVal === -1000) return;
                            setShowScrollDownBtn(!checkIfScrollEnd('.weDat-ft-elements-list', newScrollVal, 'down'));
                            setShowScrollUpBtn(true);
                        }}
                    >
                        <ChevronDownIcon />
                    </div>
                </div>
            )}

            {fastTrackElementsMenuState &&
                (elementsMenuType === 'drawer' ? <ElementsMenuDrawer /> : <ElementsMenuModal />)}
        </div>
    );
};
