import { FC, useEffect, CSSProperties } from 'react';

import { useUnit } from 'effector-react';

import { Alert } from 'antd';
import { App } from './App';
import { PluginOptions } from './types/plugin';

import {
    pluginClaimDataModel,
    pluginAttachmentsDataModel,
    pluginVehicleDataModel
} from '@dat/dat-data-models/src/models/pluginsDataModel';
import { ftCommonDefaultConfigModel } from '@dat/dat-data-models/src/models/ftCommonDefaultConfigModel';
import { datServiceOptionsModel } from '@dat/dat-data-models/src/models/datServiceOptionsModel';
import { contractModel } from '@dat/dat-data-models/src/models/contractModel';
// import { i18nEvents } from '@dat/shared-models/i18n';

export interface ApplicationPluginOptions extends PluginOptions {
    selector?: string;
    token?: string;
    credentials?: {
        customerNumber: string;
        customerLogin: string;
        customerPassword: string;
        interfacePartnerNumber?: string;
        interfacePartnerSignature?: string;
    };
    contractId: number;
}

export interface SinglePluginApplicationInterface {
    options?: ApplicationPluginOptions;
    style?: CSSProperties;
}

export const SinglePluginApplication: FC<SinglePluginApplicationInterface> = ({ options, style }) => {
    // const locale = useUnit(i18nStores.locale);

    const claimData = useUnit(pluginClaimDataModel);
    const attachmentsData = useUnit(pluginAttachmentsDataModel);
    const vehicleData = useUnit(pluginVehicleDataModel);
    const ftCommonDefaultConfig = useUnit(ftCommonDefaultConfigModel.store.ftCommonDefaultConfig);

    useEffect(() => {
        // if (!!options) {
        //     i18nEvents.setLocale(options.locale as any);
        // }

        if (options?.token) {
            datServiceOptionsModel.event.setOptions({
                credentials: { 'dat-authorizationtoken': options?.token }
            });

            contractModel.effect.updateContractAndFtClaimFx(options.contractId);
        }
        if (options?.credentials) {
            datServiceOptionsModel.event.setOptions({
                credentials: options?.credentials
            });
            contractModel.effect.updateContractAndFtClaimFx(options.contractId);
        }
    }, [options]);

    if (!options?.token && !options?.credentials)
        return (
            <Alert
                style={{ width: '640px', maxWidth: '90%', margin: '16px auto 0px auto' }}
                message="No authority provided. 'token' or 'credentials' param must be provided."
                type="warning"
            />
        );

    return (
        <App
            options={{
                ...options,
                // locale,
                // settings: options.settings,
                data: {
                    claimData,
                    attachmentsData,
                    ftCommonDefaultConfig,
                    // TODO: check Andrey grapa
                    // @ts-ignore
                    vehicleData
                }
            }}
            style={style}
        />
    );
    // (
    //     <>
    //         {!token && !options?.credentials ? (
    //             <Auth options={{}} />
    //         ) : (
    //             // @ts-ignore
    //             <PluginProvider name="fast-track" options={options || {}} noAuth>
    //                 <Main options={options} />
    //             </PluginProvider>
    //         )}
    //     </>
    // );
};
