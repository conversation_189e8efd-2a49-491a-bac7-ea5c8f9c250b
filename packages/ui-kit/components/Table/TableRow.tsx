import { FC, MouseEvent, useMemo, useRef, useState } from 'react';
import { Row } from 'react-table';

import { BurgerIcon } from '../Icons';

import { DRAGGABLE_ROW_CLASS } from './constants';
import { Tr, Td, TdDndTrigger, TdProps } from './styles';
import { TableProps } from './types';

type Props = {
    row: Row<object>;
    rowIndex: number;
    childsIds?: string[];
    onMouseDown?: (
        e: MouseEvent<HTMLElement>,
        draggingEl: HTMLTableRowElement | null,
        setIsDragging: (s: boolean) => void
    ) => void;
} & Pick<
    TableProps,
    | 'dndData'
    | 'outlineLast'
    | 'onTableChange'
    | 'rowsSelectable'
    | 'highlightedIdxs'
    | 'hiddenIdxs'
    | 'horizontalTdPadding'
    | 'verticalTdPadding'
    | 'withoutPaddings'
    | 'withoutBorderBottom'
    | 'renderRowSubComponent'
>;

export const TableRow: FC<Props> = ({
    row,
    dndData,
    childsIds,
    rowIndex,
    outlineLast,
    rowsSelectable,
    highlightedIdxs,
    hiddenIdxs,
    horizontalTdPadding,
    verticalTdPadding,
    withoutPaddings,
    withoutBorderBottom,
    onMouseDown,
    renderRowSubComponent
}) => {
    const rowRef = useRef(null);
    const triggerRef = useRef(null);
    const [isDragging, setIsDragging] = useState(false);

    const toggleDragging = (state: boolean) => setIsDragging(state);

    const rowId = dndData?.[rowIndex].id || '';
    const isDndEnabled = !childsIds?.includes(rowId) && !dndData?.[rowIndex].disabled;

    const tdProps: Pick<
        TdProps,
        'horizontalTdPadding' | 'verticalTdPadding' | 'outlineLast' | 'withoutPaddings' | 'withoutBorderBottom'
    > = useMemo(
        () => ({
            horizontalTdPadding: horizontalTdPadding,
            verticalTdPadding: verticalTdPadding,
            outlineLast: outlineLast,
            withoutPaddings: withoutPaddings,
            withoutBorderBottom: withoutBorderBottom
        }),
        [horizontalTdPadding, verticalTdPadding, outlineLast, withoutPaddings, withoutBorderBottom]
    );

    return (
        <>
            <Tr
                className={DRAGGABLE_ROW_CLASS}
                outlineLast={outlineLast}
                {...row.getRowProps()}
                onClick={() => {
                    if (rowsSelectable && !isDragging) {
                        row.toggleRowSelected();
                    }
                }}
                selected={row.isSelected}
                highlighted={highlightedIdxs?.includes(rowIndex)}
                isHidden={hiddenIdxs?.includes(rowIndex)}
                ref={rowRef}
                id={dndData?.length ? rowId : ''}
                key={`row-${rowIndex}`}
            >
                {dndData && (
                    <TdDndTrigger
                        ref={triggerRef}
                        id="dnd-trigger"
                        {...tdProps}
                        isDragging={isDragging}
                        isDndEnabled={isDndEnabled}
                        onDragStart={() => false}
                        onMouseDown={(e: MouseEvent<HTMLElement>) =>
                            isDndEnabled ? onMouseDown?.(e, rowRef.current, toggleDragging) : () => {}
                        }
                    >
                        {isDndEnabled && <BurgerIcon />}
                    </TdDndTrigger>
                )}
                {row.cells.map((cell, cellIndex) => {
                    return (
                        <Td {...tdProps} {...cell.getCellProps()} key={`cell-${cellIndex}`}>
                            {cell.render('Cell')}
                        </Td>
                    );
                })}
            </Tr>
            {row.isExpanded && renderRowSubComponent && (
                <Tr {...row.getRowProps()} key={`row-${rowIndex}-expand`}>
                    {renderRowSubComponent({ row, tdProps })}
                </Tr>
            )}
        </>
    );
};
