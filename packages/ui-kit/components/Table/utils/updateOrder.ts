import { DndDataItem, DndOrderItem } from '../types';
import { getCoordinatesOfElement } from './getCoordinatesOfElement';

export const updateOrder = (mainElements: HTMLTableRowElement[], dndData: DndDataItem[]) => {
    const orders: DndOrderItem[] = [];

    mainElements.forEach(el => {
        const [top, bottom] = getCoordinatesOfElement(el, dndData);

        orders.push({ id: el.id, pos: (top + bottom) / 2 });
    });

    return orders.sort((a, b) => a.pos - b.pos);
};
