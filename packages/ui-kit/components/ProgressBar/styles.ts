import styled from 'styled-components/macro';
import { media } from '@wedat/ui-kit/mediaQueries';

interface ColorProps {
    color: string;
    percentage: number;
}

export const ComponentWraper = styled.div`
    flex: 0 0 180px;
    display: flex;
    gap: 4px;
    flex-direction: column;
    align-items: center;
`;

export const Label = styled.div<{ withoutMarginBottom?: boolean }>`
    ${({ theme }) => theme.typography.note};
    color: ${({ theme }) => theme.colors.deepBlue['800']};
    cursor: pointer;
    width: fit-content;
    background-color: ${({ theme }) => theme.colors.gray['100']};
    padding: 8px;
    border-radius: 8px;
    width: 100%;
    display: flex;
    justify-content: center;
    padding-bottom: 10px;
    ${media.tablet`
        margin: auto 0 ${({ withoutMarginBottom }) => (withoutMarginBottom ? '0' : '15px')} 0;
    `}
`;

// export const Percentage = styled.div`
//     ${({ theme }) => theme.typography.noteBold};
//     color: ${({ theme }) => theme.colors.deepBlue['800']};
// `;

export const ColorBackground = styled.div`
    height: 4px;
    width: 100%;
    background-color: ${({ theme }) => theme.colors.gray['300']};
    border-radius: 8px;
`;

export const ColorBackgroundAbsolute = styled(ColorBackground)`
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 27%;
`;

export const ColorForeground = styled.div<ColorProps>`
    ${(p: ColorProps) => `background-color: ${p.color}; width: ${p.percentage}%;`}
    height: 100%;
    border-radius: 8px;
`;
