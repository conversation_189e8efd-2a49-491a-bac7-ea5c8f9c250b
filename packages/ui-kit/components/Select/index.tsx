import { CSSProperties, memo, useEffect, useMemo, useRef, useState } from 'react';
import ReactSelect, { SelectComponentsConfig, StylesConfig, Theme } from 'react-select';
import { useTheme } from 'styled-components';

import { Option } from './Option';
import { ValueContainer } from './ValueContainer';

import { Label } from '../Label';
import { FieldsetStyled } from '../Fieldset';
import { Legend, LegendText } from '../Legend';
import { sizes } from '../../mediaQueries';
import { useMedia } from '../../../core/hooks/useMedia';

import { MultiSelectProps, SelectProps, SingleSelectProps, ValueLabelOption } from './types';

import { getValueForReactSelect } from './utils';

import * as styledComponents from './styles';
import { SelectRoot, IconWrapper, IconStyled } from './styles';
import { ExtendedOptionProps } from './Option/types';
import { EditIcon } from '../Icons';

const customComponents = ({
    defaultWhiteSpaceOption,
    withIcon,
    inputSize,
    disabled
}: ExtendedOptionProps): SelectComponentsConfig<ValueLabelOption, boolean> => ({
    ...styledComponents,
    Option: props => <Option {...props} defaultWhiteSpaceOption={defaultWhiteSpaceOption} inputSize={inputSize} />,
    ValueContainer: props => <ValueContainer {...props} withIcon={withIcon} inputSize={inputSize} disabled={disabled} />
});

type Props = SelectProps & {
    valueKey?: 'value' | 'key';
    // Need for typecast
    valueType?: 'string' | 'number';
    borderRadius?: number;
    defaultWhiteSpaceOption?: boolean;
    icon?: JSX.Element;
    status?: 'error' | 'success' | 'default';
    withMarginLeft?: boolean;
    bold?: boolean;
    style?: CSSProperties;
    required?: boolean;
    fixedLabel?: boolean;
    withBoxShadow?: boolean;
    noBorderLeft?: boolean;
    onlyBorderBottom?: boolean;
    noBorder?: boolean;
};

export const Select = memo<Props>(
    ({
        options,
        value,
        onChange,
        label,
        disabled,
        backgroundColor,
        isMulti,
        isGrouped,
        menuIsOpen,
        formatOptionLabel,
        formatValueLabel,
        onFocus,
        onBlur,
        placeholder = '',
        withPortal,
        valueKey = 'value',
        valueType,
        isSearchable,
        borderRadius,
        defaultWhiteSpaceOption,
        icon,
        maxMenuHeight,
        stopPropagation,
        status,
        inputSize,
        withMarginLeft,
        bold,
        style,
        required,
        fixedLabel = false,
        withBoxShadow = false,
        noBorderLeft,
        borderLeftColor,
        onlyBorderBottom,
        noBorder,
        ...rest
    }) => {
        const [isOpen, setIsOpen] = useState(!!menuIsOpen);
        const [focused, setFocused] = useState(false);
        const isMobile = useMedia(sizes.laptop);

        const {
            colors: { red }
        } = useTheme();

        const ref = useRef<ReactSelect<ValueLabelOption>>(null);
        const styledComponentsTheme = useTheme();

        const valueLabelOptions = options as ValueLabelOption[];

        const valueForReactSelect = useMemo(
            () =>
                getValueForReactSelect({ value, options: valueLabelOptions, isMulti, valueKey, valueType, isGrouped }),
            [value, valueLabelOptions, isMulti, valueKey, valueType, isGrouped]
        );

        useEffect(() => {
            setIsOpen(!!menuIsOpen);
        }, [menuIsOpen]);

        useEffect(() => {
            if (isOpen) ref.current?.focus();
            else ref.current?.blur();
        }, [isOpen]);

        const handleChangeSingleSelect = (option: ValueLabelOption | null) => {
            onChange = onChange as SingleSelectProps['onChange']; // just for type cast
            onChange(option);
        };

        const handleChangeMultiSelect = (options: ValueLabelOption[]) => {
            onChange = onChange as MultiSelectProps['onChange']; // just for type cast
            onChange(options);
        };

        const handleChange = isMulti ? handleChangeMultiSelect : handleChangeSingleSelect;

        const handleOpenMenu = () => setIsOpen(true);

        const handleCloseMenu = () => setIsOpen(false);

        /* Format option-label / value-label */
        const handleFormatOptionLabel: SelectProps['formatOptionLabel'] = (option, meta): any => {
            const isValueLabel = meta.context === 'value';

            option = { ...option };

            if (formatOptionLabel) option.label = formatOptionLabel(option, meta);

            if (isValueLabel && formatValueLabel) option.label = formatValueLabel(option, meta);

            return option.label;
        };

        const handleFocus: SelectProps['onFocus'] = e => {
            setFocused(true);

            onFocus?.(e);
        };

        const handleBlur: SelectProps['onBlur'] = e => {
            setFocused(false);

            onBlur?.(e);
        };

        const customStyles: StylesConfig<ValueLabelOption, boolean> = {
            container: styles => ({
                ...styles,
                minWidth: '60px',
                backgroundColor: backgroundColor || styledComponentsTheme.colors.white,
                borderRadius: '8px',
                marginLeft: withMarginLeft ? '50px' : '0',
                boxShadow: withBoxShadow ? '3px 7px 12px -12px rgba(0, 0, 0, 0.65)' : 'none'
            }),
            control: styles => {
                if (borderRadius) {
                    return { ...styles, borderRadius };
                }
                return { ...styles };
            },
            menu: styles => ({ ...styles, zIndex: 1000 }), // z-index footer plus 1
            menuPortal: styles => ({ ...styles, zIndex: 10002 }), // z-index setting more then in modals
            singleValue: styles => ({
                ...styles,
                fontWeight: bold ? styledComponentsTheme.weight.bold : undefined
            })
        };

        const memoComponents = useMemo(
            () =>
                customComponents({
                    defaultWhiteSpaceOption,
                    withIcon: !!icon,
                    inputSize: inputSize,
                    disabled: disabled
                }),
            [defaultWhiteSpaceOption, icon, inputSize, disabled]
        );

        return (
            <SelectRoot onClick={e => stopPropagation && e.stopPropagation()} inputSize={inputSize} style={style}>
                <ReactSelect<ValueLabelOption, boolean>
                    //@ts-ignore - strange compilation error on `ref`
                    ref={ref}
                    components={memoComponents}
                    isDisabled={disabled}
                    value={valueForReactSelect}
                    // using `as any` because @ts-ignore doesn't work here when compiling
                    onChange={handleChange as any}
                    options={valueLabelOptions}
                    isOptionDisabled={option => !!option.disabled}
                    isMulti={isMulti}
                    menuIsOpen={isOpen}
                    onMenuOpen={handleOpenMenu}
                    onMenuClose={handleCloseMenu}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    closeMenuOnSelect={!isMulti}
                    hideSelectedOptions={false}
                    isClearable={false}
                    isSearchable={isMobile && !isSearchable ? false : isSearchable}
                    tabSelectsValue={false}
                    backspaceRemovesValue={false}
                    menuPlacement="auto"
                    menuPosition="absolute"
                    styles={customStyles}
                    formatOptionLabel={handleFormatOptionLabel}
                    placeholder={placeholder}
                    aria-label={label}
                    menuPortalTarget={(withPortal ?? !isMobile) ? document.body : null}
                    theme={reactSelectTheme =>
                        ({
                            ...reactSelectTheme,
                            ...styledComponentsTheme
                        }) as unknown as Theme
                    }
                    {...rest}
                    autoFocus={false}
                    maxMenuHeight={maxMenuHeight}
                    inputSize={inputSize}
                />
                <Label
                    focused={focused}
                    status={status}
                    filled={!!valueForReactSelect}
                    withIcon
                    inputSize={inputSize}
                    disabled={disabled}
                    bold={bold}
                    required={required}
                    fixed={fixedLabel}
                >
                    {label}
                </Label>

                {required && !valueForReactSelect && (
                    <IconStyled>
                        <EditIcon color={red['800']} />
                    </IconStyled>
                )}

                <FieldsetStyled
                    borderRadius={borderRadius}
                    status={status}
                    disabled={disabled}
                    $borderLeftColor={borderLeftColor}
                    required={required}
                    hasValue={!!valueForReactSelect}
                    noBorderLeft={noBorderLeft}
                    onlyBorderBottom={onlyBorderBottom}
                    noBorder={noBorder}
                >
                    <Legend focused={focused} filled={!!valueForReactSelect} fixed={fixedLabel} disabled={disabled}>
                        <LegendText fontSize="12px" textOverflow="ellipsis" color="inherit" bold={bold}>
                            {label}
                        </LegendText>
                    </Legend>
                </FieldsetStyled>

                {icon && <IconWrapper>{icon}</IconWrapper>}
            </SelectRoot>
        );
    }
);

export {
    type ValueLabelOption,
    type SelectProps,
    type SingleSelectProps,
    type BaseProps,
    type MultiSelectProps,
    type KeyValueOption,
    type MultiSelectValue,
    type GroupedOption,
    type SingleSelectValue,
    type SelectOption,
    type SelectOptionBase
} from './types';
