import { useState, useCallback, useEffect, FC, useRef } from 'react';
import { FieldName, StyledReactQuillWrapper, ExpandButton } from './styles';

import { ArrowDownIconStyled } from '../TextArea/styles';
import { FORMATS } from './constants';
import ReactQuill, { Quill } from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { createQuillModules } from './utils/createQuillModules';

const Inline = Quill.import('blots/inline');
const Block = Quill.import('blots/block');

class Bold extends Inline {
    static blotName = 'bold';
    static tagName = 'b'; // Use <b> instead of <strong>
}
Quill.register(Bold, true);

class Italic extends Inline {
    static blotName = 'italic';
    static tagName = 'i'; // Use <i> instead of <em>
}
Quill.register(Italic, true);

class Strikethrough extends Inline {
    static blotName = 'strike';
    static tagName = 's'; // Use <s> instead of <del>
}
Quill.register(Strikethrough, true);

interface ReactQuilComponentProps {
    fieldValue: string;
    handleOnChange: (name: string, content: string) => void;
    name: string;
    label?: string;
    defaultHeight?: string;
    expandable?: boolean;
    richTextHiddenItems?: string[];
    fontSizeMapping?: Partial<Record<1 | 2 | 3 | 4 | 5 | 6, string>>;
    [key: string]: any;
    headerLabels?: Record<string, string>;
    disabled?: boolean;
}

const EXPANDABLE_DEFAULT_HEIGHT = 400;
export const ReactQuilComponent: FC<ReactQuilComponentProps> = ({
    fieldValue,
    handleOnChange,
    name,
    label,
    expandable = true,
    defaultHeight = 120,
    richTextHiddenItems = [],
    richTextFontSizes,
    disabled,
    ...props
}) => {
    const quillRef = useRef<ReactQuill | null>(null);
    const [value, setValue] = useState(fieldValue || '');
    const [isFocused, setIsFocused] = useState(false);
    const [toolbarHeight, setToolbarHeight] = useState<number>(disabled ? 0 : 40);
    const [expandedHeight, setExpandedHeight] = useState(defaultHeight);

    // Register dynamic Header on mount
    useEffect(() => {
        const defaultSizeMapping = {
            1: '3',
            2: '2',
            3: '1',
            4: '-1',
            5: '-2',
            6: '-3'
        };

        const appliedMapping: Record<1 | 2 | 3 | 4 | 5 | 6 | 'normal', string> = {
            1: richTextFontSizes?.[1] || defaultSizeMapping[1],
            2: richTextFontSizes?.[2] || defaultSizeMapping[2],
            3: richTextFontSizes?.[3] || defaultSizeMapping[3],
            4: richTextFontSizes?.[4] || defaultSizeMapping[4],
            5: richTextFontSizes?.[5] || defaultSizeMapping[5],
            6: richTextFontSizes?.[6] || defaultSizeMapping[6],
            normal: richTextFontSizes?.[0] || defaultSizeMapping[1]
        };

        // 1. Add <p> style for "normal" font size
        const styleId = 'dynamic-quill-normal-font-size';
        if (!document.getElementById(styleId)) {
            const style = document.createElement('style');
            style.id = styleId;
            style.innerHTML = `.ql-editor p { font-size: ${appliedMapping.normal}; display: block }`;
            document.head.appendChild(style);
        }

        // 2. Map sizes back to header levels
        const levelMapping = Object.entries(appliedMapping).reduce(
            (acc, [level, size]) => {
                if (level !== 'normal') acc[size] = Number(level);
                return acc;
            },
            {} as Record<string, number>
        );

        // 3. Custom header blot
        class Header extends Block {
            static blotName = 'header';
            static tagName = 'font';

            static create(value: number): HTMLElement {
                const size = appliedMapping[value as 1 | 2 | 3 | 4 | 5 | 6] ?? appliedMapping.normal;
                const node = document.createElement('font') as HTMLElement;
                node.setAttribute('size', size);
                node.style.display = 'block';
                node.style.whiteSpace = 'pre-wrap';
                node.style.marginBottom = '0.5em';

                return node;
            }

            static formats(domNode: HTMLElement): number | undefined {
                const size = domNode.getAttribute('size');
                return levelMapping[size ?? ''] || undefined;
            }

            format(name: string, value: number | boolean | null): void {
                if (name === 'header') {
                    if (!value) {
                        //Set to normal font size
                        const normalSize = appliedMapping.normal || '3';
                        this.domNode.setAttribute('size', normalSize);
                    } else {
                        const size = appliedMapping[value as 1 | 2 | 3 | 4 | 5 | 6] ?? '3';
                        this.domNode.setAttribute('size', size);
                    }
                } else {
                    super.format(name, value);
                }
            }
        }

        Quill.register(Header, true);
    }, [richTextFontSizes, name]);

    const handleChange = useCallback(
        (content: string) => {
            setValue(content);
            handleOnChange(name, content);
        },
        [handleOnChange, name]
    );

    const handleFocus = useCallback(() => setIsFocused(true), []);

    const handleBlur = useCallback(() => {
        props.onBlur?.(value);
        setIsFocused(false);
    }, [props, value]);

    // NOTE: Temporary fix call onBlur on unmaunt component
    // I don't know why, but onBlur from props ReactQuill don't trigger callback if current action unmount
    // (Example: user input some text in ReactQuill and without leaving focus, click button "to inbox" )
    // need refactor!
    useEffect(() => {
        const editor = quillRef.current?.editor;
        const root = editor?.root;
        if (!root) return;

        root.addEventListener('blur', handleBlur, true);

        return () => {
            root.removeEventListener('blur', handleBlur, true);
        };
    }, [handleBlur]);

    const updateToolbarHeight = useCallback(() => {
        const toolbarElementParent = document.getElementById(name);
        const toolbarElement = toolbarElementParent?.querySelector('.ql-toolbar')?.clientHeight;

        if (toolbarElement) {
            setToolbarHeight(toolbarElement);
        }
        return toolbarElement;
    }, [name]);

    const handleExpand = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        setExpandedHeight(prevState => (prevState > defaultHeight ? defaultHeight : EXPANDABLE_DEFAULT_HEIGHT));
    };

    const isFieldEmpty = value === '<p><br></p>' || value === '';

    useEffect(() => {
        const appliedFontSizes = richTextFontSizes ?? {
            1: '3',
            2: '2',
            3: '1',
            4: '-1',
            5: '-2',
            6: '-3',
            0: '3' // "normal"
        };

        const styleId = `quill-dynamic-heading-styles`;
        const css = Object.entries(appliedFontSizes)
            .map(([_, size]) => {
                return `font[size="${size}"] { font-size: ${size}; display: block; white-space: pre-wrap; margin-bottom: 0.5em; }`;
            })
            .join('\n');

        let styleEl = document.getElementById(styleId) as HTMLStyleElement;
        if (styleEl) {
            styleEl.innerHTML = css;
        } else {
            styleEl = document.createElement('style');
            styleEl.id = styleId;
            styleEl.innerHTML = css;
            document.head.appendChild(styleEl);
        }

        return () => {
            const el = document.getElementById(styleId);
            if (el) document.head.removeChild(el);
        };
    }, [richTextFontSizes]);

    useEffect(() => {
        setValue(fieldValue);
    }, [fieldValue]);

    useEffect(() => {
        updateToolbarHeight();
        window.addEventListener('resize', updateToolbarHeight);
        return () => {
            window.removeEventListener('resize', updateToolbarHeight);
        };
    }, [updateToolbarHeight, name, toolbarHeight]);

    return (
        <StyledReactQuillWrapper wrapperHeight={`${expandedHeight}px`} id={name} isDisabled={disabled}>
            <ReactQuill
                ref={quillRef}
                theme={props.theme || 'snow'}
                modules={disabled ? { toolbar: false } : createQuillModules([...richTextHiddenItems])}
                formats={props.formats || FORMATS}
                value={value}
                onChange={handleChange}
                onBlur={handleBlur}
                onFocus={handleFocus}
                data-gramm={false}
                readOnly={props.disabled}
            />
            <FieldName isShown={!isFieldEmpty || isFocused} id={name} toolbarHeight={toolbarHeight}>
                {label}
            </FieldName>

            {expandable && (
                <ExpandButton id="expandButton" onClick={e => handleExpand(e)}>
                    <ArrowDownIconStyled $isOpen={expandedHeight > defaultHeight} />
                </ExpandButton>
            )}
        </StyledReactQuillWrapper>
    );
};
