import { <PERSON><PERSON>, <PERSON> } from '@storybook/react';
import { Checkbox, CheckboxProps } from './index';

const Template: Story<CheckboxProps> = (args: CheckboxProps) => <Checkbox {...args} />;

export const CheckboxPrimary = Template.bind({});

export default {
    title: 'Design System/UI Kit/Checkbox',
    component: Checkbox,
    args: {
        label: 'pepega',
        checked: true,
        disabled: false
    }
} as Meta;
