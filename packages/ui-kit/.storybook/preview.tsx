import { withThemeFromJSXProvider } from '@storybook/addon-themes';
import { ThemeProvider } from 'styled-components';
import { getDark, getLight } from '../theme';

export const parameter = {
    actions: { argTypesRegex: '^on[A-Z].*' }
};

export const decorators = [
    withThemeFromJSXProvider({
        themes: {
            light: getLight(),
            dark: getDark()
        },
        defaultTheme: 'light',
        Provider: ThemeProvider
    })
];
