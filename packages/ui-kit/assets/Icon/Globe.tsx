import { SVGProps } from 'react';

const Globe = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 20.9C16.9153 20.9 20.9 16.9153 20.9 12C20.9 7.08467 16.9153 3.1 12 3.1C7.08467 3.1 3.1 7.08467 3.1 12C3.1 16.9153 7.08467 20.9 12 20.9ZM12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
            fill="currentColor"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M14.4602 18.0592C15.2643 16.5008 15.79 14.3209 15.79 12C15.79 9.67911 15.2643 7.49922 14.4602 5.94085C13.6163 4.30513 12.6787 3.71 12 3.71C11.3213 3.71 10.3837 4.30513 9.53977 5.94085C8.73572 7.49922 8.21 9.67911 8.21 12C8.21 14.3209 8.73572 16.5008 9.53977 18.0592C10.3837 19.6949 11.3213 20.29 12 20.29C12.6787 20.29 13.6163 19.6949 14.4602 18.0592ZM12 21.5C14.7614 21.5 17 16.9706 17 12C17 7.02944 14.7614 2.5 12 2.5C9.23858 2.5 7 7.02944 7 12C7 16.9706 9.23858 21.5 12 21.5Z"
            fill="currentColor"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M2.95 9C2.95 8.69624 3.19624 8.45 3.5 8.45H20.4473C20.7511 8.45 20.9973 8.69624 20.9973 9C20.9973 9.30376 20.7511 9.55 20.4473 9.55H3.5C3.19624 9.55 2.95 9.30376 2.95 9Z"
            fill="currentColor"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M2.95 15C2.95 14.6962 3.19624 14.45 3.5 14.45H20.6549C20.9587 14.45 21.2049 14.6962 21.2049 15C21.2049 15.3038 20.9587 15.55 20.6549 15.55H3.5C3.19624 15.55 2.95 15.3038 2.95 15Z"
            fill="currentColor"
        />
    </svg>
);

export default Globe;
