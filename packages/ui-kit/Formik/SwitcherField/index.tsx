import { useState, ChangeEvent, memo } from 'react';
import { FieldInputProps, useField, useFormikContext } from 'formik';

import { Switcher, SwitcherProps } from '../../components/SwitcherOld';

import { FieldEventCallback } from '../types';

export interface SwitcherFieldProps extends Omit<SwitcherProps, 'onChange' | 'value'> {
    onChange?: FieldEventCallback;
    onUserChange?: (field: FieldInputProps<any>) => void;
    name: string;
    checkBasedOnValue?: boolean;
}

export const SwitcherField = memo<SwitcherFieldProps>(
    ({ name, onChange, onUserChange, checkBasedOnValue, ...rest }) => {
        const formikContext = useFormikContext();
        const [{ ...field }, , helpers] = useField(name);

        const valueBasedChecked = field.value === rest.switchOptions[1].value;

        const [isChecked, setChecked] = useState(checkBasedOnValue ? valueBasedChecked : false);

        const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
            const valN = e.target.checked ? 1 : 0;

            const currentValue = rest.switchOptions[valN].value;

            helpers.setValue(currentValue);
            onChange?.(currentValue, formikContext);
            onUserChange?.({
                ...field,
                value: currentValue
            });
            setChecked(e.target.checked);
        };

        return <Switcher {...field} {...rest} checked={isChecked} onChange={handleChange} />;
    }
);
