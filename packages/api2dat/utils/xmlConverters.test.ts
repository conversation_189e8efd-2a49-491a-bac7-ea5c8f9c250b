import { xmlToObj, objToXml, xmlToObjWithArrayMode } from './xmlConverters';

describe('xmlConverters', () => {
    it('simble object', () => {
        const XML = '<a>12</a>';
        const obj = xmlToObj(XML);
        expect(obj).toEqual({ a: 12 });
        const backXML = objToXml(obj);
        expect(backXML).toMatch(XML);
    });
    // current version of fast-xml-parser need bugfix for using plugin inside myClaim
    // file hode2json.js in line 31
    // for (var tag in node.child[tagname]) {
    //     if (node.child[tagname].hasOwnProperty(tag)) {
    //         jObj[tagname].push(convertToJson(node.child[tagname][tag], options));
    //     }
    // }
    it('fast-xml-parser need bugfix for using plugin inside myClaim', () => {
        // @ts-ignore
        // eslint-disable-next-line no-extend-native
        Array.prototype.someExtentionOfArrayPrototype = 'someExtentionOfArrayPrototype';

        const XML = '<a><b>0</b><b>1</b></a>';
        const obj = xmlToObj(XML);
        expect(obj.a.b.length).toEqual(2);
    });
    it('object with attribute', () => {
        const XML = '<a b="test">11</a>';
        const obj = xmlToObj(XML);
        expect(obj).toEqual({
            a: {
                '#text': 11,
                attr: {
                    b: 'test'
                }
            }
        });
        const backXML = objToXml(obj);
        expect(backXML).toMatch(XML);
    });
});

describe('xmlToObjWithArrayMode', () => {
    const sampleXML = '<book><title>Over the cuckoos nest</title><author>Ken Kesey</author></book>';

    it('should parse XML to object with array mode disabled', () => {
        const result = xmlToObjWithArrayMode(sampleXML);
        expect(result).toEqual({
            book: {
                title: 'Over the cuckoos nest',
                author: 'Ken Kesey'
            }
        });
    });

    it('should parse XML to object with array mode enabled', () => {
        const tagNamesWithParent = ['book.author'];
        const result = xmlToObjWithArrayMode(sampleXML, tagNamesWithParent);
        expect(result).toEqual({
            book: {
                title: 'Over the cuckoos nest',
                author: ['Ken Kesey']
            }
        });
    });
});
