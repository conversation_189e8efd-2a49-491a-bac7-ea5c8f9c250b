import { isValid, format, parseISO } from 'date-fns';

export const isValidDateString = (value: string) => {
    if (!value || typeof value !== 'string') return false;

    // Reject numeric-only strings (e.g. "200934")
    if (/^\d+$/.test(value.trim())) return false;

    // Check if ISO works
    const parsedISO = parseISO(value);
    if (isValid(parsedISO)) return true;

    // Fallback parse
    const fallback = new Date(value);
    if (!isValid(fallback)) return false;

    // Reject absurd future dates (> year 3000 or < 1000)
    const year = fallback.getFullYear();
    if (year < 1000 || year > 3000) return false;

    return true;
};

export const formatDateString = (value: string | number): string => {
    if (value === null || value === undefined) return '-';

    const stringValue = String(value).trim();

    // Reject too-short or malformed numeric strings
    if (/^\d+$/.test(stringValue) && !/^(\d{8}|\d{13})$/.test(stringValue)) {
        return stringValue;
    }

    let date: Date | null = null;

    try {
        // Try ISO
        date = parseISO(stringValue);
        if (isValid(date)) return format(date, 'dd/MM/yyyy');

        // 13-digit Unix timestamp
        if (/^\d{13}$/.test(stringValue)) {
            date = new Date(Number(stringValue));
            if (isValid(date)) return format(date, 'dd/MM/yyyy');
        }

        // 8-digit numeric formats
        if (/^\d{8}$/.test(stringValue)) {
            const yyyy = Number(stringValue.slice(0, 4));
            const mm = Number(stringValue.slice(4, 6)) - 1;
            const dd = Number(stringValue.slice(6, 8));
            const isoTry = new Date(yyyy, mm, dd);
            if (isValid(isoTry)) return format(isoTry, 'dd/MM/yyyy');

            const dd2 = Number(stringValue.slice(0, 2));
            const mm2 = Number(stringValue.slice(2, 4)) - 1;
            const yyyy2 = Number(stringValue.slice(4, 8));
            const altTry = new Date(yyyy2, mm2, dd2);
            if (isValid(altTry)) return format(altTry, 'dd/MM/yyyy');
        }

        //  Native parser fallback
        date = new Date(stringValue);
        if (isValid(date)) {
            const year = date.getFullYear();
            if (year < 1000 || year > 3000) return stringValue;
            return format(date, 'dd/MM/yyyy');
        }
    } catch {
        return String(value);
    }

    return stringValue;
};

export const formatResolvedValue = (value: unknown, t: (key: string) => string): string => {
    if (typeof value === 'boolean') {
        return t(value ? 'spire.yes' : 'spire.no');
    }

    if (typeof value === 'string' && isValidDateString(value)) {
        return formatDateString(value);
    }

    if (value === undefined || value === null || value === '') {
        return '-';
    }
    return String(value);
};
