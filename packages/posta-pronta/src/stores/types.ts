import { Instances } from '@dat/address-book';
import { SelectOption } from '@wedat/ui-kit';
import { PPFormikValues } from '../types/plugin';
import { CustomersSubjectsData } from '@dat/shared-models/addressBook/type';

export type SubjectTypeOptions = SelectOption & {
    editorType?: string;
};

export type TokenAndSessionGUID = {
    token: string;
    sessionGUID: string;
};

export type SaveSubjectsFromPPFxParams = {
    formValues: PPFormikValues;
    instances: Instances;
    selectedSubjectType: string;
    customersSubjectsData: CustomersSubjectsData;
};

export type GetMailPriceFxParams = {
    data: { formValues: PPFormikValues; pdfBase64?: string };
    contractId: number;
    userProfile: DAT2.UserProfile | null;
};

export type SendPPMailFxParams = {
    data: { formValues: PPFormikValues; succsessCallback: () => void; pdfBase64?: string };
    isActiveSaveSelection: boolean;
    selectedSubjectType: string;
    contractId: number;
    userProfile: DAT2.UserProfile | null;
    tokenAndSessionGUID: TokenAndSessionGUID;
};
