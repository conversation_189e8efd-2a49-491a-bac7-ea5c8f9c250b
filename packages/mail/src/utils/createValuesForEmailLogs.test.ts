import { FormikMailValues } from '../constants/formik';
import { createValuesForEmailLogs } from './createValuesForEmailLogs';
import { format } from 'date-fns';

describe('createValuesForEmailLogs', () => {
    it('should return an array of modified email logs', () => {
        const values: FormikMailValues = {
            message: 'Hello world',
            from: '<EMAIL>',
            recipients: ['<EMAIL>', '<EMAIL>'],
            ccRecipients: ['<EMAIL>'],
            bccRecipients: ['<EMAIL>'],
            templates: 'template',
            subject: 'Test email',
            calculationReports: [],
            valuationReports: [],
            documents: [],
            reports: [],
            attachmentOptions: [],
            exportFiles: true,
            exportProfils: [],
            customerRoleExport: {},
            exportCombinedZip: true,
            exportCombinedPdf: true,
            reportConfig: [],
            dossier: true,
            adzFiles: true,
            szf: true,
            useSignature: false,
            claimId: null,
            body: 'This is the email body'
        };

        const emailLogs =
            '[{"from":"<EMAIL>","subject":"Subject 1","to":["<EMAIL>","<EMAIL>"],"ccRecipients":["<EMAIL>"],"bccRecipients":["<EMAIL>"],"predefinedText":"Test 1","body":"Email body 1","date":"01/01/2022 12:00, am"},{"from":"<EMAIL>","subject":"Subject 2","to":["<EMAIL>","<EMAIL>"],"ccRecipients":["<EMAIL>"],"bccRecipients":["<EMAIL>"],"predefinedText":"Test 2","body":"Email body 2","date":"02/02/2022 12:00, pm"}]';

        const predefinedText = 'Predefined text for the email body.';
        const expected = JSON.stringify([
            {
                from: '<EMAIL>',
                subject: 'Subject 1',
                to: ['<EMAIL>', '<EMAIL>'],
                ccRecipients: ['<EMAIL>'],
                bccRecipients: ['<EMAIL>'],
                predefinedText: 'Test 1',
                body: 'Email body 1',
                date: '01/01/2022 12:00, am'
            },
            {
                from: '<EMAIL>',
                subject: 'Subject 2',
                to: ['<EMAIL>', '<EMAIL>'],
                ccRecipients: ['<EMAIL>'],
                bccRecipients: ['<EMAIL>'],
                predefinedText: 'Test 2',
                body: 'Email body 2',
                date: '02/02/2022 12:00, pm'
            },
            {
                from: '<EMAIL>',
                subject: 'Test email',
                to: ['<EMAIL>', '<EMAIL>'],
                ccRecipients: ['<EMAIL>'],
                bccRecipients: ['<EMAIL>'],
                predefinedText: predefinedText,
                body: 'This is the email body',
                date: format(new Date(), 'dd/MM/yyyy h:mm, a')
            }
        ]);

        const result = createValuesForEmailLogs(values, emailLogs, predefinedText);

        expect(result).toEqual(expected);
    });
});
