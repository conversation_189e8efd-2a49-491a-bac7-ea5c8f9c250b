import { createDomain, createEffect } from 'effector';

import { modelFactory } from '@dat/core/effector/factory';
import { sharedLoginEvents } from '@dat/shared-models/auth/login';

import { PluginOptions } from '../../types/plugin';
import { instancesEvents, instancesStores } from '../instances';
import { createStores } from './createStores';
import { createEffects } from './createEffects';
import { createEvents } from './createEvents';
import { connectUpdateAllInstances } from './connectUpdateAllInstances';
import { connectUpdateStores } from './connectUpdateStores';
import { connectUpdateMainInstance } from './connectUpdateMainInstance';
import { connectUpdateInheritedInstance } from './connectUpdateInheritedInstance';
import { connectCommonUpdateInstance } from './connectCommonUpdateInstance';
import { connectUpdateContractAfterShowListChanged } from './connectUpdateContractAfterShowListChanged';
import { CONTACT_PERSON_INSTANCE, HISTORY_INSPECTION_CONTACT } from '../../constants/names';

const { instances } = instancesStores;

type Params = {
    options: PluginOptions;
};

export const abFactory = modelFactory(({ options }: Params) => {
    const domain = createDomain();

    const uuid = options.id || options.subjectType;

    const stores = createStores({ domain, instances, uuid });
    const events = createEvents({ domain });
    const effects = createEffects({ domain, stores, events, options });

    connectUpdateAllInstances({ stores, events });
    connectUpdateStores({ stores, events, effects });
    connectUpdateContractAfterShowListChanged({ stores, events, effects });
    connectCommonUpdateInstance({ stores, events, effects, options });

    if (options.inheritedList) {
        connectUpdateInheritedInstance({ stores, events, effects, uuid });
    } else {
        connectUpdateMainInstance({ stores, events, effects });
    }

    domain.onCreateStore(store => {
        store.reset([instancesEvents.unmountPlugin, sharedLoginEvents.loggedOut]);
    });

    return { stores, events, effects };
});

export const addContactPersonInstanceFx = createEffect(() => {
    const newModel = abFactory.createModel({ options: CONTACT_PERSON_INSTANCE });
    newModel.events.addToInstances(CONTACT_PERSON_INSTANCE);
    instancesEvents.addModel({ subjectType: CONTACT_PERSON_INSTANCE.subjectType, model: newModel });
    instancesEvents.setSelectedModel(HISTORY_INSPECTION_CONTACT);
});
