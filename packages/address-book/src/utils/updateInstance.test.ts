import { Instances } from '../types/plugin';
import { updateInstance } from './updateInstance';

describe('updateInstance', () => {
    const instances: Instances = {
        some_id: {
            instanceId: 'some_id',
            subjectType: 'owner',
            fields: [],
            currentSubjects: [],
            initialSubjects: [],
            groups: [],
            isModalOpen: true
        },
        some_id_3: {
            instanceId: 'some_id_3',
            subjectType: 'co_owner',
            fields: [],
            currentSubjects: [],
            initialSubjects: [],
            groups: [],
            isModalOpen: true
        }
    };

    it('main', () => {
        const currentInstanceId = 'some_id';
        const updatedFields = { isModalOpen: false };

        const data = updateInstance({
            instances,
            instanceId: currentInstanceId,
            updatedFields
        });

        const result = { ...instances, [currentInstanceId]: { ...instances[currentInstanceId], ...updatedFields } };

        expect(data).toEqual(result);
    });

    it('non-existent id', () => {
        const currentInstanceId = 'some_id_2';
        const updatedFields = { isModalOpen: false };

        const data = updateInstance({
            instances,
            instanceId: currentInstanceId,
            updatedFields
        });

        const result = { ...instances, [currentInstanceId]: { ...updatedFields } };

        expect(data).toEqual(result);
    });

    it('with array', () => {
        const updatedFields = { isModalOpen: false };

        const instanceId_1 = 'some_id';
        const instanceId_2 = 'some_id_3';

        const data = updateInstance({
            instances,
            update: [
                { instanceId: instanceId_1, updatedFields },
                { instanceId: instanceId_2, updatedFields }
            ]
        });

        const result = {
            ...instances,
            [instanceId_1]: { ...instances[instanceId_1], ...updatedFields },
            [instanceId_2]: { ...instances[instanceId_2], ...updatedFields }
        };

        expect(data).toEqual(result);
    });
});
