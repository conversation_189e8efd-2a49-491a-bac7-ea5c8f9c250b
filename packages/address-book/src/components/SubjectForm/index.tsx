import { FC, FocusEvent, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useUnit } from 'effector-react';

import { sizes } from '@wedat/ui-kit/mediaQueries';
import { useMedia } from '@dat/core/hooks/useMedia';
import { ArrowBack } from '@wedat/ui-kit/components';
import { Text, Switcher, Button } from '@wedat/kit';

import { apiStores } from '@dat/api2/stores';
import { FormBuilder, FormBuilderPluginOptions } from '@dat/form-builder';
import { zipCodeEvents } from '@dat/smart-components/AddressBookZip';
import { contractStores } from '@dat/shared-models/contract';
import { sharedConfigurationStores } from '@dat/shared-models/configuration';
import { sharedInboxStores } from '@dat/shared-models/inbox';
import { parseToFormBuilderData } from '../../utils/parseToFormBuilderData';
import { getSubjectFromContract } from '../../utils/getSubjectFromContract';
import { useSelectedModel } from '../../hooks/useSelectedModel';

import { EDITOR_TEMPLATES, defaultFields } from '../../constants/defaultFields';
import { NoDisplayedSubject } from './NoDisplayedSubject';
import { ApplyDataCheck } from './ApplyDataCheck';
import { Initials } from '../Initials';
import { Buttons } from '../Buttons';
import {
    Container,
    FormStyled,
    Content,
    FormBuilderWrapper,
    TitleWrapper,
    Header,
    SwitcherContainer,
    SwitcherWrapper,
    BackButtonWrapper,
    ButtonWrapper
} from './styles';
import { REPAIRER } from '../../constants/names';
import { addContactPersonInstanceFx } from '../../stores/factory';

export const SubjectForm: FC = () => {
    const { t } = useTranslation();
    const contract = useUnit(contractStores.contract);

    const isTablet = useMedia(sizes.tablet);
    const stdMailServiceActive = useUnit(sharedConfigurationStores.userSettings.stdMailServiceActive);

    const { stores, events } = useSelectedModel();
    const {
        showList,
        displayedSubject,
        assignedSubject,
        fields,
        groups,
        withMemoButton,
        applySameDataCheck,
        subjectType,
        isShowCompanySwitcher,
        withUserNameField,
        isRichTextEditor,
        readOnly
    } = useUnit(stores.instance);

    const formValues = useUnit(stores.formValues);
    const subjectForFormBuilder = useUnit(stores.subjectForFormBuilder);

    const subjectFromContract = getSubjectFromContract(contract, subjectType);
    const subject = useMemo(
        () => subjectForFormBuilder || displayedSubject || assignedSubject,
        [subjectForFormBuilder, displayedSubject, assignedSubject]
    );
    const isInbox = useUnit(sharedInboxStores.inboxOpened);
    const country = useUnit(apiStores.country);
    const isGroupChanged = useUnit(stores.isSubjectGroupChanged);
    const isEditor = EDITOR_TEMPLATES.includes(subjectType);
    const isContentReadOnly = (isEditor && withUserNameField && !subject?.userName) || readOnly;
    const shouldShowForm =
        !isGroupChanged &&
        (subject?._id ||
            subject?.name ||
            subject?.surname ||
            !showList ||
            (!isEditor && subjectFromContract?.name) ||
            (!isEditor && subjectFromContract?.surname));

    const shouldShowContactPersonButton = subjectType === REPAIRER && isInbox && country === 'AT';

    const resultFormBuilderData = useMemo(
        () =>
            parseToFormBuilderData({
                fields: fields?.length ? fields : defaultFields,
                displayedSubject: subject,
                fieldsWithGroup: groups,
                t,
                subjectType,
                showCompanySwitcher: subject?.type === 'company',
                isShowCompanySwitcher,
                stdMailServiceActive: !!stdMailServiceActive,
                isRichTextEditor,
                isContentReadOnly
            }),
        [
            fields,
            subject,
            groups,
            t,
            subjectType,
            isShowCompanySwitcher,
            stdMailServiceActive,
            isRichTextEditor,
            isContentReadOnly
        ]
    );

    const formBuilderOptions = useMemo<FormBuilderPluginOptions>(
        () => ({
            data: resultFormBuilderData,
            options: {
                isAddressBook: true
            },
            onComplete: () => {},
            onChangeForm: values => {
                events.setFormValues(values);
            },
            resetForm: events.resetForm,
            inputSize: 'medium',
            globalHandlers: {
                string: {
                    onBlur: (event: FocusEvent<HTMLInputElement>) => {
                        if (event.target.name === 'city' || event.target.name === 'address') {
                            zipCodeEvents.triggerGetZip();
                        }
                    }
                }
            },
            horizontalFields: true,
            isGroupContentReadOnly: isContentReadOnly
        }),
        [events, resultFormBuilderData, isContentReadOnly]
    );

    return (
        <FormStyled withMemoButton={withMemoButton} showList={showList}>
            <Container>
                {shouldShowForm ? (
                    <Content>
                        {isTablet && showList && (
                            <BackButtonWrapper>
                                <Button
                                    fitContent
                                    aria-label={t('buttons.backText')}
                                    onClick={() => events.toggleList()}
                                    variant="transparent"
                                    icon={<ArrowBack aria-label={t('arrowBackIcon')} />}
                                    label={t('buttons.backText')}
                                />
                            </BackButtonWrapper>
                        )}

                        <Header>
                            <Initials subject={subject} variant="big" withGlobe={false} />
                            <TitleWrapper>
                                <Text bold font="font-semi-heading">
                                    {subject?.name} {subject?.surname}
                                </Text>
                                {applySameDataCheck?.length && <ApplyDataCheck />}
                            </TitleWrapper>
                        </Header>

                        <FormBuilderWrapper>
                            {isShowCompanySwitcher &&
                                (() => {
                                    const typeInSubject =
                                        typeof formValues?.type === 'undefined' || !formValues?.type
                                            ? subject?.type
                                            : formValues?.type;

                                    return (
                                        <SwitcherContainer>
                                            <Text font="font-note">{t('address-book:fieldsLabels.Private')}</Text>
                                            <SwitcherWrapper>
                                                <Switcher
                                                    inputId="abook__subject-form_company"
                                                    aria-label={t('address-book:fieldsLabels.Company')}
                                                    checked={typeInSubject === 'company'}
                                                    onChange={() =>
                                                        events.setPrivateOrCompany(
                                                            typeInSubject === 'company' ? 'private' : 'company'
                                                        )
                                                    }
                                                />
                                            </SwitcherWrapper>
                                            <Text font="font-note">{t('address-book:fieldsLabels.Company')}</Text>
                                        </SwitcherContainer>
                                    );
                                })()}

                            <FormBuilder options={formBuilderOptions} />
                            {shouldShowContactPersonButton && (
                                <ButtonWrapper>
                                    <Button
                                        fitContent
                                        label={t('goToContactPerson')}
                                        aria-label={t('goToContactPerson')}
                                        onClick={() => addContactPersonInstanceFx()}
                                        variant="square"
                                        squareColor="blue"
                                    />
                                </ButtonWrapper>
                            )}
                        </FormBuilderWrapper>

                        {isTablet && <Buttons isForm={true} />}
                    </Content>
                ) : (
                    <NoDisplayedSubject />
                )}
            </Container>
        </FormStyled>
    );
};
