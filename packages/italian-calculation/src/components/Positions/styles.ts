import styled, { css } from 'styled-components/macro';
import { Button } from '@wedat/ui-kit/components/Button';
import { media, makeCustomScrollBar } from '@wedat/ui-kit/mediaQueries';
import { InputProps } from '@wedat/ui-kit/components/Input/types';
import { getInputSize } from '@wedat/ui-kit/utils/getInputSize';

export const Container = styled.div`
    width: 100%;

    position: relative;

    background-color: ${({ theme }) => theme.colors.white};
    border-radius: 16px;
    ${makeCustomScrollBar()}

    ${media.phoneBig`
        padding: 24px 0;
    `}
`;

export const SumRow = styled.div`
    width: 100%;
    display: flex;
    flex-direction: column;
    padding-bottom: 20px;
    justify-content: flex-end;
`;

export const ButtonWrapper = styled.div`
    width: 100%;
    display: flex;
    padding-bottom: 20px;
    justify-content: flex-end;
`;

export const ButtonStyled = styled(Button)`
    width: 98px;
    margin-left: auto;
`;

export const InfoTableWrapper = styled.div`
    ${media.phoneBig`
        width: 500px;
    `}

    ${media.phoneMedium`
        width: 100%;
    `}
`;

export const TableWrapper = styled.div<{ tableMaxHeight?: number; widthNotHundredPercent?: boolean }>`
    ${({ widthNotHundredPercent }) => !widthNotHundredPercent && 'width: 100%;'}
    overflow: auto;
    padding-bottom: 8px;

    ${({ tableMaxHeight }) => tableMaxHeight && `max-height: ${tableMaxHeight}px;`}

    ${makeCustomScrollBar()}
`;

// this is strictly for ui purposes for ASPIS optimized positions just to hide '#' in the beginning of the Description
export const WhiteSquare = styled.div<{
    $inputSize: InputProps['inputSize'];
    $aggregateComponent?: boolean;
    $isGray?: boolean;
    isDescriptionDisabled?: boolean;
}>`
    position: absolute;
    top: 1px;
    left: 1px;
    width: 12px;
    background-color: ${({ theme }) => theme.colors.white};
    ${({ $isGray, theme }) => $isGray && `background-color: ${theme.colors.deepBlue['50']};`}
    ${({ isDescriptionDisabled, theme }) => isDescriptionDisabled && `background-color: ${theme.colors.gray['50']};`}
    ${({ $aggregateComponent, theme }) =>
        $aggregateComponent &&
        css`
            background-color: ${theme.colors.blue['50']};
            color: ${theme.colors.dustBlue['900']};
        `}

    // 2 is a total border height
    height: ${({ $inputSize }) => `${+getInputSize($inputSize).substring(0, 2) - 2}px`};
    z-index: 2;
    border-bottom-left-radius: 8px;
    border-top-left-radius: 8px;
`;
