// eslint-disable-next-line react/no-deprecated
import { unmountComponentAtNode } from 'react-dom';
import { createRoot } from 'react-dom/client';

import App from './App';
import { ErrorBoundary } from '@wedat/ui-kit/components/ErrorBoundary';
import { PluginOptions } from './types/plugin';

if (import.meta.env.MODE === 'development') {
    createRoot(document.getElementById('root') as HTMLElement).render(<App options={{ contractId: 19915710 }} />);
} else {
    window.ITALIAN_CALCULATION_API = {
        init: (options: PluginOptions) => {
            const renderElement = document.querySelector(String(options.selector));

            if (renderElement) {
                unmountComponentAtNode(renderElement);
                createRoot(renderElement).render(
                    <ErrorBoundary>
                        <App options={options} />
                    </ErrorBoundary>
                );
            }
        }
    };
}
