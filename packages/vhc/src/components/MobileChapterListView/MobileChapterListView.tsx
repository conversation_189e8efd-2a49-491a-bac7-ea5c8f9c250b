// MobileChapterListView

import { FC, memo } from 'react';
import { useUnit } from 'effector-react';
import { Space } from 'antd';

import { scenarioStateModel } from '../../stores/scenarioStateModel';
import { ChapterElement } from '../ChapterElement/ChapterElement';

import { Icon123 } from '../Icon/Icon';
import LeftArrow from '../IconsSVG/LeftArrow.svg?react';
import { pluginOptionsModel } from '../../stores/pluginOptionsModel';
import { generateTemplateFieldsValue } from '../../utils/generateTemplateFieldsValue';

export const MobileChapterListView = memo(() => {
    const vhcConfig = useUnit(pluginOptionsModel.stores.vhcConfig);

    const pluginOptions = useUnit(pluginOptionsModel.stores.pluginOptions);

    const currentChapterList = useUnit(scenarioStateModel.stores.currentChapterList);

    const currentScenario = useUnit(scenarioStateModel.stores.currentScenario);

    const currentChapter = useUnit(scenarioStateModel.stores.currentChapter);

    const currentChapterListStatus = useUnit(scenarioStateModel.stores.currentChapterListStatus);

    const shouldShowBackIcon = (vhcConfig?.scenarios.length ?? 0) <= 1 && pluginOptions?.withExitCallBack;
    const { contract } = pluginOptions?.data?.claimData || {};
    const fieldsForExitCallBack = pluginOptions?.fieldsForExitCallBack || [];
    const templateDataValues = generateTemplateFieldsValue(fieldsForExitCallBack, contract);

    return (
        <>
            <div className="weDat-VHC-mobile-header">
                {((vhcConfig?.scenarios.length && vhcConfig.scenarios.length > 1) || shouldShowBackIcon) && (
                    <Icon123
                        icon={<LeftArrow />}
                        onClick={() => {
                            const mainPercent = currentChapterListStatus?.[0]?.percent;
                            const percentOfMandatoryFilledCards =
                                currentChapterListStatus?.[0]?.percentOfMandatoryFilledCards;

                            pluginOptions?.getScenarioPercent?.({ mainPercent, percentOfMandatoryFilledCards });

                            shouldShowBackIcon
                                ? pluginOptions?.withExitCallBack?.({
                                      contractId: pluginOptions?.data?.claimData?.contract?.Dossier?.DossierId,
                                      mainPercent,
                                      percentOfMandatoryFilledCards,
                                      templateDataValues
                                  })
                                : scenarioStateModel.events.setCurrentScenario(null);
                        }}
                    />
                )}
                <div>{currentScenario?.title}</div>
            </div>

            <div className="weDat-VHC-scenario-content">
                <Space direction="vertical" style={{ width: '100%', padding: '8px 8px 8px 8px' }}>
                    {currentChapterList?.map((chapter, index) => (
                        <ChapterElement
                            isShowProgress={chapter.chapterType !== 'FastTrack'}
                            key={chapter.id}
                            index={index + 1}
                            chapter={chapter}
                            active={currentChapter === chapter}
                            onClick={() => scenarioStateModel.events.setCurrentChapter(chapter)}
                        />
                    ))}
                </Space>
            </div>
        </>
    );
});
