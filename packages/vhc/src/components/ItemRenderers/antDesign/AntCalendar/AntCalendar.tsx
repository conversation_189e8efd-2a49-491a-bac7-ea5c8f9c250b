import { FC } from 'react';
import moment from 'moment';
import { Calendar } from 'antd';

import { CardItems } from '../../../../types/itemRenderType';

export const AntCalendar: FC<CardItems> = ({ vhcItemConfig, vhcItemValue, onChangeValue }) => (
    <>
        {vhcItemConfig.title && <div>{vhcItemConfig.title}</div>}
        <Calendar
            fullscreen={false}
            value={moment(vhcItemValue?.value)}
            onChange={momentV =>
                onChangeValue?.({
                    id: vhcItemConfig.id,
                    type: vhcItemConfig.type,
                    value: momentV?.toISOString(),
                    error: ''
                })
            }
        />
    </>
);
