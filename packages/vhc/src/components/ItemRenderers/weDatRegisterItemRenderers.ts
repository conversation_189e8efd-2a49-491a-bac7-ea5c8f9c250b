import { itemRendererModel } from '../../stores/ItemRendererModel';

import { WeDatInput } from './weDat/WeDatInput/WeDatInput';
import { WeDatDatePicker } from './weDat/WeDatDatePicker/WeDatDatePicker';
import { WeDatSelect } from './weDat/WeDatSelect/WeDatSelect';
import { WeDatCheckbox } from './weDat/WeDatCheckbox/WeDatCheckbox';
import { WeDatSwitcher } from './weDat/WeDatSwitcher/WeDatSwitcher';
import { WeDatToggle } from './weDat/WeDatToggle/WeDatToggle';
import { WeDatTextArea } from './weDat/WeDatTextArea/WeDatTextArea';
import { WeDatRadioGroup } from './weDat/WeDatRadioGroup/WeDatRadioGroup';
import { SparePartsForDATIDRenderer } from './SparePartsForDATIDRenderer/SparePartsForDATIDRenderer';
import { EquipmentForDATIDRenderer } from './EquipmentForDATIDRenderer/EquipmentForDATIDRenderer';
import { WeDatConfirmChapterButton } from './weDat/WeDatConfirmChapterButton/WeDatConfirmChapterButton';

itemRendererModel.events.addItemRenderers([
    {
        valueType: 'string',
        rendererId: 'WeDatInput',
        renderer: WeDatInput
    },
    {
        valueType: 'date',
        rendererId: 'WeDatDatePicker',
        renderer: WeDatDatePicker
    },
    {
        valueType: 'enum',
        rendererId: 'WeDatSelect',
        renderer: WeDatSelect
    },
    {
        valueType: 'boolean',
        rendererId: 'WeDatCheckbox',
        renderer: WeDatCheckbox
    },
    {
        valueType: 'boolean',
        rendererId: 'WeDatSwitcher',
        renderer: WeDatSwitcher
    },
    {
        valueType: 'boolean',
        rendererId: 'WeDatToggle',
        renderer: WeDatToggle
    },
    {
        valueType: 'string',
        rendererId: 'WeDatTextArea',
        renderer: WeDatTextArea
    },
    {
        valueType: 'enum',
        rendererId: 'WeDatRadioGroup',
        renderer: WeDatRadioGroup
    },
    {
        valueType: 'sparePartsForDATID',
        rendererId: 'SparePartsForDATIDRenderer',
        renderer: SparePartsForDATIDRenderer
    },
    {
        valueType: 'equipmentForDATID',
        rendererId: 'EquipmentForDATIDRenderer',
        renderer: EquipmentForDATIDRenderer
    },
    {
        valueType: 'confirmChapterButton',
        rendererId: 'WeDatConfirmChapterButton',
        renderer: WeDatConfirmChapterButton
    }
]);
