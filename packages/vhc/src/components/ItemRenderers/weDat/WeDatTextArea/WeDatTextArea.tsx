import { FC } from 'react';

import { TextArea } from '@wedat/ui-kit/components/TextArea';

import { CardItems } from '../../../../types/itemRenderType';

export const WeDatTextArea: FC<CardItems> = ({ vhcItemConfig, vhcItemValue, onChangeValue }) => (
    <>
        <TextArea
            // style={{ color: 'unset' }}
            value={vhcItemValue?.value}
            disabled={vhcItemConfig.readOnly}
            label={vhcItemConfig.title}
            onChange={e =>
                onChangeValue?.({
                    id: vhcItemConfig.id,
                    type: vhcItemConfig.type,
                    value: e.target.value,
                    error: ''
                })
            }
            {...vhcItemConfig.rendererProps}
        />
    </>
);
