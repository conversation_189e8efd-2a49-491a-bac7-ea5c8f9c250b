import { FC } from 'react';

import { Datepicker } from '@dat/smart-components/Datepicker';

import { CardItems } from '../../../../types/itemRenderType';

export const WeDatDatePicker: FC<CardItems> = ({ vhcItemConfig, vhcItemValue, onChangeValue }) => (
    <>
        <Datepicker
            // style={{ width: '100%' }}
            label={vhcItemConfig.title}
            value={vhcItemValue?.value}
            disabled={vhcItemConfig.readOnly}
            onChange={momentV =>
                onChangeValue?.({
                    id: vhcItemConfig.id,
                    type: vhcItemConfig.type,
                    value: momentV?.toISOString(),
                    error: ''
                })
            }
            {...vhcItemConfig.rendererProps}
        />
    </>
);
