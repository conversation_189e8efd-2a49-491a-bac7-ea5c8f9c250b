<!DOCTYPE html>
<html lang="en" translate="no">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VHC Vite</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="./src/main.tsx"></script>
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        window.VHC &&
          window.VHC.init({
            options: {
              selector: '#root',
              contractId: 43113906
            }
          });
      });
    </script>
  </body>
</html>
