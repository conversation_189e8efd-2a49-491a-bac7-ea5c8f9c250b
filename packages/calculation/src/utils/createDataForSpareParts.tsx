import { getParsedArraySafe } from '@dat/api2/utils';
import { TableFooterTypes } from '../types/tableFooter';
import { MobileTableHeading } from '../components/Layout/styles';
import { TFunction } from 'react-i18next';
import * as DAT5 from '@wedat/api';

export const createDataForSpareParts = (
    t: TFunction,
    materialPositions: DAT5.MyClaimExternalService_schema2.MaterialPosition[] | null,
    isMobile: boolean,
    formatPrice: (number: number | undefined) => string
) => {
    return getParsedArraySafe(materialPositions).map(
        ({ Amount, DATPartNumber, PartNumber, DATProcessId, Description, ValuePerUnit, ValueTotal }) =>
            isMobile
                ? {
                      col1: DATProcessId,
                      col2: PartNumber || DATPartNumber,
                      col3: Description,
                      children: [
                          {
                              col4: (
                                  <>
                                      <MobileTableHeading isTitle={true}>
                                          {t('sparePartsTable.headers.type')}:
                                      </MobileTableHeading>
                                      <MobileTableHeading>{t('default')}</MobileTableHeading>
                                  </>
                              ),
                              col5: (
                                  <>
                                      <MobileTableHeading isTitle={true}>
                                          {t('sparePartsTable.headers.discount')}:
                                      </MobileTableHeading>
                                      <MobileTableHeading>-</MobileTableHeading>
                                  </>
                              ),
                              col6: (
                                  <>
                                      <MobileTableHeading isTitle={true}>
                                          {t('sparePartsTable.headers.quantity')}:
                                      </MobileTableHeading>
                                      <MobileTableHeading>{Amount}</MobileTableHeading>
                                  </>
                              ),
                              col7: (
                                  <>
                                      <MobileTableHeading isTitle={true}>
                                          {t('sparePartsTable.headers.unitPrice')}:
                                      </MobileTableHeading>
                                      <MobileTableHeading>{formatPrice(ValuePerUnit)}</MobileTableHeading>
                                  </>
                              ),
                              col8: (
                                  <>
                                      <MobileTableHeading isTitle={true}>
                                          {t('sparePartsTable.headers.price')}:
                                      </MobileTableHeading>
                                      <MobileTableHeading>{formatPrice(ValueTotal)}</MobileTableHeading>
                                  </>
                              )
                          }
                      ]
                  }
                : {
                      col1: DATProcessId,
                      col2: PartNumber || DATPartNumber,
                      col3: Description,
                      col4: null,
                      col5: '-',
                      col6: Amount,
                      col7: formatPrice(ValuePerUnit),
                      col8: formatPrice(ValueTotal)
                  }
    );
};

export const createDataForSparePartsTableFooter = (
    t: TFunction,
    calculationSummary: DAT5.MyClaimExternalService_schema2.CalculationSummary | null
): TableFooterTypes => {
    const totalSparePartsCosts = calculationSummary?.SparePartsCosts?.TotalSum;
    const formatPrice = (data: number | undefined) => data?.toString() || '';

    return {
        items: [
            {
                price: formatPrice(totalSparePartsCosts),
                label: t('dataForCards.sparePartsTotal'),
                styles: {
                    fontWeight: 500,
                    fontSize: '17px'
                }
            },
            {
                price: formatPrice(totalSparePartsCosts),
                label: t('dataForCards.sparePartsSubtotal')
            },
            {
                price: '-',
                label: t('dataForCards.smallParts[PERCENT_OF_PARTS]')
            }
        ]
    };
};
