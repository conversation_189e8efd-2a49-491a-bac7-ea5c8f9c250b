import { getSPOSpainOptimization, SpoSpainResponse } from '@dat/api2/services/SPOSpain';
import { createEffect, createStore } from 'effector';

const getAlternativesSpainFx = createEffect(getSPOSpainOptimization);

const SPOSpainAlternatives = createStore<SpoSpainResponse | null>(null);

SPOSpainAlternatives.on(getAlternativesSpainFx.doneData, (_, payload) => payload);

export const SPOSpainStores = {
    SPOSpainAlternatives
};

export const SPOSpainEffects = {
    getAlternativesSpainFx
};
