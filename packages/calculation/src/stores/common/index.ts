import { combine } from 'effector';

import { subscribeEffectsToToast } from '@dat/shared-models/smart-components/Toast';
import { contractEffects } from '@dat/shared-models/contract';
import {
    calculateContractBaseFx,
    createOrUpdateContractBaseFx,
    updateContractBaseFx
} from '@dat/shared-models/baseEffects';
import { contractModel } from '@dat/dat-data-models/src/models/contractModel';

import { SPOEffects } from '../SPO';
import { microCalculationEffects } from '../microCalculation';
import { SPOSpainEffects } from '../SPOSpain';

const { calculateContractFx, calculateCurrentContractFx, calculateOptimizedCurrentContractFx, getContractFx } =
    contractEffects;
const { getSuppliersFx, optimizeWithSPOFx } = SPOEffects;
const { getAlternativesSpainFx } = SPOSpainEffects;
const { handleMicroCalculationFx } = microCalculationEffects;

const isLoading = combine([
    calculateContractFx.pending,
    calculateCurrentContractFx.pending,
    calculateOptimizedCurrentContractFx.pending,
    getSuppliersFx.pending,
    optimizeWithSPOFx.pending,
    getAlternativesSpainFx.pending,
    handleMicroCalculationFx.pending,
    createOrUpdateContractBaseFx.pending,
    updateContractBaseFx.pending,
    calculateContractBaseFx.pending,
    getContractFx.pending,
    contractModel.effect.saveAndUpdateContractFx.pending
]).map(stores => stores.reduce((a, b) => a || b, false));

subscribeEffectsToToast([
    contractEffects.getContractFx,
    contractEffects.updateContractFx,
    contractEffects.calculateContractFx
]);

export const commonStores = {
    isLoading
};
