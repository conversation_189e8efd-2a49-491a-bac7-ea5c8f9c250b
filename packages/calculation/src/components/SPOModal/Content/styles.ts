import styled from 'styled-components';

import { ButtonIcon } from '@wedat/ui-kit/components/ButtonIcon';
import { Checkbox, SearchIcon, CheckCircle, CrossCircle, SaveIcon, Text } from '@wedat/ui-kit';
import { media } from '@wedat/ui-kit/mediaQueries';

export const Container = styled.div`
    display: flex;
    flex-direction: column;
    padding-bottom: 20px;
    max-height: calc(100% - 10px);
`;

export const PositionsWrapper = styled.div``;

export const SearchRow = styled.div`
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 20px;
    margin-top: 20px;
    padding-left: 24px;
    padding-right: 24px;
`;

export const CellWrapper = styled.div`
    display: flex;
    height: 30px;
    align-items: center;
`;

export const CheckboxStyled = styled(Checkbox)`
    margin-left: 20px;
`;

export const SearchInputWrapper = styled.div`
    position: relative;
`;

export const IconWrapper = styled.div`
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 14px;
    display: flex;
    align-items: center;
`;

export const SearchIconStyled = styled(SearchIcon)`
    width: 20px;
    height: 20px;
`;

export const CheckCircleStyled = styled(CheckCircle)`
    margin-right: 10px;
`;

export const CrossCircleStyled = styled(CrossCircle)`
    margin-right: 10px;
`;

export const TextStyled = styled(Text)`
    margin-left: 10px;
    color: ${({ theme }) => theme.colors.white};
`;

export const SaveIconStyled = styled(SaveIcon)``;

export const SaveIconWrapper = styled.span`
    height: 18px;
    min-width: 18px;
    margin-bottom: 2px;
    color: ${({ theme }) => theme.colors.white};

    ${SaveIconStyled} {
        width: 100%;
        height: 100%;
    }
`;

export const ButtonIconStyled = styled(ButtonIcon)`
    min-width: 48px;
    width: fit-content;
    margin-left: auto;
    padding-left: 40px;
    padding-right: 40px;
    border-width: 1px;
    border-color: ${({ theme }) => theme.colors.gray_40};
    background-color: ${({ theme }) => theme.colors.deepBlue['800']};
    color: ${({ theme }) => theme.colors.white};

    &:disabled {
        background-color: ${({ theme }) => theme.colors.dustBlue['100']};
    }

    &:hover {
        border-width: 1px;
        background-color: ${({ theme }) => theme.colors.white};
        border-color: ${({ theme }) => theme.colors.deepBlue['800']};

        ${TextStyled} {
            color: ${({ theme }) => theme.colors.deepBlue['800']};
        }

        ${SaveIconWrapper} {
            ${SaveIconStyled} {
                color: ${({ theme }) => theme.colors.deepBlue['800']};
            }
        }
    }

    ${media.phoneBig`
        padding-left: 16px;
        padding-right: 16px;
        height: 100%;
        border: none;
        border-radius: 3px;
        background-color: ${({ theme }) => theme.colors.dustBlue['900']};
    `}
`;

export const DescAndCheck = styled.div`
    display: flex;
    align-items: center;
`;

export const PositionGroupWrapper = styled.div<{ hidden?: boolean }>`
    width: 100%;
    display: flex;
    flex-direction: column;
    ${({ hidden }) => hidden && `display: none;`}
`;

export const PartWrapper = styled.div<{ isSelected?: boolean }>`
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 20px;
    align-items: center;
    justify-content: space-between;
    padding: 1px 4px;

    background-color: ${({ isSelected, theme }) => (isSelected ? theme.colors.blue['50'] : 'transparent')};
`;

export const PartText = styled(Text)`
    margin-left: 10px;
`;

export const PartTextAligned = styled(Text)`
    text-align: end;
`;

export const ButtonsWrapper = styled.div`
    display: flex;
    width: 100%;
    margin-top: auto;
    align-items: center;
    margin-top: 20px;
`;
