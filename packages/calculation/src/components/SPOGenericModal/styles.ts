import { CheckIcon, PreloaderCircle, SearchIcon } from '@wedat/ui-kit';
import styled from 'styled-components';

export const Container = styled.div`
    display: flex;
    flex-direction: column;
    min-width: 50vw;
    min-height: 300px;
    padding-top: 4px;
    padding-bottom: 10px;
`;

export const TopContent = styled.div`
    display: flex;
    justify-content: space-between;
    padding-bottom: 6px;
    border-bottom: ${({ theme }) => `1px solid ${theme.colors.dustBlue['50']}`};
`;

export const SpoProfileWrapper = styled.div`
    width: 50%;
`;

export const FilterWrapper = styled.div`
    width: 40%;
`;

export const SearchIconStyled = styled(SearchIcon)`
    width: 20px;
    height: 20px;
`;

export const Dvn = styled.span``;

export const SpoTableWrapper = styled.div`
    max-height: 500px;
    flex: 1;
    overflow: auto;
    padding: 10px 0;
    ${PreloaderCircle} {
        width: 50px;
        height: 50px;
    }
    td {
        padding: 4px 5px;
        border-bottom: ${({ theme }) => `2px solid ${theme.colors.dustBlue['50']}`};
    }
    tr:has(${Dvn}) {
        & > td {
            font-weight: 700;
            background-color: ${({ theme }) => theme.colors.deepBlue['100']};
        }
    }
`;

export const CheckIconStyled = styled(CheckIcon)`
    width: 16px;
    color: ${({ theme }) => theme.colors.green['600']};
`;

export const FieldWrapper = styled.div`
    display: flex;
`;

export const Info = styled.div`
    font-size: 16px;
    font-weight: 600;
`;

export const Footer = styled.div`
    display: flex;
    padding-top: 6px;
    justify-content: space-between;
    align-items: end;
    border-top: ${({ theme }) => `1px solid ${theme.colors.dustBlue['50']}`};
`;

export const Summary = styled.div``;

export const SummaryItem = styled.div`
    display: flex;
    justify-content: space-between;
`;

export const SummaryField = styled.div<{ isBold?: boolean }>`
    font-size: 16px;
    margin: 0 10px;
    font-weight: ${({ isBold }) => (isBold ? '600' : undefined)};
`;

export const ButtonsWrapper = styled.div`
    width: 50%;
    display: flex;
    button {
        margin: 0 4px;
    }
`;
