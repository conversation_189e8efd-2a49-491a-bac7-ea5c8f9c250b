import { media } from '@wedat/ui-kit/mediaQueries';
import styled, { css } from 'styled-components/macro';

export const Container = styled.div`
    display: flex;
    width: 100%;
    padding: 6px;
    flex-direction: column;
    overflow: hidden;
    border-radius: 10px;
    background-color: ${({ theme }) => theme.colors.white};

    ${media.tablet`
        flex: 1;
        flex-basis: 48%;
    `}

    ${media.phoneBig`
        flex-basis: 100%;
    `}
`;

export const DataRow = styled.div<{ mtAuto?: boolean }>`
    display: flex;
    justify-content: space-between;
    padding: 0 8px;

    ${({ mtAuto }) =>
        mtAuto &&
        css`
            margin-top: auto;
        `}
`;
