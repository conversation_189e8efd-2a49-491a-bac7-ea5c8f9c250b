import styled, { css } from 'styled-components/macro';
import { makeCustomScrollBar, media } from '@wedat/ui-kit/mediaQueries';
import { WrapperStyled } from '@wedat/ui-kit/components/Tabs/styles';

interface TabsContainerProps {
    summaryNew?: boolean;
    isTableExpanded?: boolean;
}

export const TabsContainer = styled.div<TabsContainerProps>`
    width: 100%;

    ${({ summaryNew }) =>
        summaryNew &&
        css`
            height: calc(100vh - 182px);
            overflow: hidden;
            padding-bottom: 10px;

            ${media.tablet`
                height: fit-content;
                overflow: auto;
            `}
        `}

    ${({ isTableExpanded }) =>
        isTableExpanded &&
        css`
            overflow: auto;

            ${makeCustomScrollBar()}
        `}

    ${WrapperStyled} {
        overflow-x: auto;

        ${media.phone`
            ${makeCustomScrollBar()}
        `}
    }
`;

export const TabsWrapper = styled.div`
    padding-left: 30px;

    ${media.phoneBig`
        padding-left: 0px;
    `}
`;

export const GridContainer = styled.section`
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 24px;
    row-gap: 24px;
`;
