import styled from 'styled-components/macro';
import { InputField } from '@wedat/ui-kit/Formik';

export const TextStyled = styled.div`
    font-size: 16px;
    text-align: center;
`;

export const Container = styled.div<{ labelFontSize?: number }>`
    display: flex;
    flex-direction: column;
    label {
        z-index: 10;
    }

    ${({ labelFontSize }) =>
        labelFontSize
            ? `label span {
        font-size: ${labelFontSize}px;
    }`
            : ``}
`;

export const GroupWrapper = styled.div`
    width: 100%;
    display: flex;
    flex-direction: column;
    border-top: 1px solid ${({ theme }) => theme.colors.dustBlue['50']};
    margin-top: 8px;
    padding-top: 8px;
`;

export const InputFieldStyled = styled(InputField)<{ $isBold?: boolean }>`
    ${({ theme, $isBold }) => $isBold && theme.typography.noteBold}
`;

export const SubPositions = styled.div`
    display: flex;
    width: 100%;
    flex-direction: column;
    padding-top: 8px;
`;

export const SubPositionContainer = styled.div`
    padding: 6px;
    border: 1px solid ${({ theme }) => theme.colors.dustBlue['200']};
    border-radius: 8px;
    margin: 4px 0;
`;

export const ButtonDeleteContainer = styled.div`
    height: 40px;
    width: 100%;
    margin-top: 8px;
`;
