import { FC, useMemo } from 'react';
import { useStoreMap } from 'effector-react';
import { Column } from 'react-table';
import { useFormikContext } from 'formik';
import { noop } from 'lodash-es';

import { Portal, Table } from '@wedat/ui-kit';
import { Button } from '@wedat/kit';
import { useOrderPartsTranslation } from '@dat/smart-components/OrderParts';
import { contractStores } from '@dat/dat-data-models/src/models/fromSharedModel/contractStores';
import { contractModel } from '@dat/dat-data-models/src/models/contractModel';

import { PostEditingFormValues } from '../../../../../types/postEditingFormValues';
import { OrderingStepProps } from '../types';
import { OrderModalFooterId } from '../../constants';

import sharedStyles from '@dat/smart-components/OrderParts/order-parts.module.css';

export const SummaryStep: FC<OrderingStepProps> = ({ newRepairPositions, isOrdering, nextStep }) => {
    const {
        t,
        i18n: { language }
    } = useOrderPartsTranslation();
    const {
        values: { positions }
    } = useFormikContext<PostEditingFormValues>();

    const Dossier = useStoreMap(contractStores.contract, contract => contract?.Dossier || null);
    const currency = Dossier?.Currency;

    const column: Column<(typeof newRepairPositions)[0]>[] = useMemo(() => {
        const formatPrice = (price?: number) =>
            !price ? '' : price.toLocaleString(language, { style: 'currency', currency });

        return [
            {
                Header: t('steps.summary.table.DATProcessId'),
                id: 'DATProcessId',
                accessor: data =>
                    data.Description ||
                    positions.find(position => position.DATProcessId === data.DATProcessId)?.Description ||
                    data.DATProcessId
            },
            {
                Header: t('steps.summary.table.SparePartNumber'),
                id: 'SparePartNumber',
                accessor: data =>
                    data.SparePartNumber ||
                    positions.find(position => position.DATProcessId === data.DATProcessId)?.PartNumber
            },
            {
                Header: t('steps.summary.table.SparePartPrice'),
                id: 'SparePartPrice',
                accessor: data =>
                    formatPrice(
                        data.SparePartPrice ||
                            positions.find(position => position.DATProcessId === data.DATProcessId)?.ValueTotalCorrected
                    )
            }
        ];
    }, [currency, language, positions, t]);

    const callback = async () => {
        contractModel.event.updateWithMergeEditingContract({
            Dossier: {
                RepairCalculation: {
                    RepairPositions: {
                        RepairPosition: newRepairPositions
                    }
                }
            }
        });
        await contractModel.effect.saveAndUpdateContractFx();
    };

    return (
        <div>
            <Table columns={column as Column[]} data={newRepairPositions} />
            <Portal id={OrderModalFooterId}>
                <Button
                    className={sharedStyles.StyledButton}
                    loading={isOrdering}
                    variant={'transparent'}
                    onClick={() => nextStep(noop)}
                    aria-label={t('actions.keep-calculation')}
                >
                    {t('actions.keep-calculation')}
                </Button>
                <Button
                    className={sharedStyles.StyledButton}
                    loading={isOrdering}
                    onClick={() => nextStep(callback)}
                    aria-label={t('actions.calculate')}
                >
                    {t('actions.calculate')}
                </Button>
            </Portal>
        </div>
    );
};
