import { noop } from 'lodash-es';
import { Formik } from 'formik';
import { useMemo, FC } from 'react';
import { useUnit } from 'effector-react';

import { postEditingEffects, postEditingStores } from '../../stores/postEditing';

import { PostEditingFormValues, TotalSumsItems } from '../../types/postEditingFormValues';

import { Desktop } from './Desktop';
import { TotalSums } from '../TotalSums';
import { TotalSumsMobile } from '../TotalSums/mobile';
import { Container } from './styles';

import { Mobile } from './Mobile';
import { GlobalAmountPortal } from './GlobalAmount';
import { pluginStores } from '../../stores/plugin';

interface Props {
    totalSumsItems: TotalSumsItems[];
    isMobile: boolean;
}

export const PostEditing: FC<Props> = ({ totalSumsItems, isMobile }) => {
    const initialFormPositions = useUnit(postEditingStores.initialFormPositions);
    const selectedPositions = useUnit(postEditingStores.selectedPositions);
    const options = useUnit(pluginStores.pluginOptions);

    const { summaryNew, inlineGlobalAmount = false } = options || {};

    const initialValues = useMemo((): PostEditingFormValues => {
        return {
            positions: initialFormPositions,
            totalSumsItems
        };
    }, [initialFormPositions, totalSumsItems]);

    const handleDelete = (values: PostEditingFormValues) => {
        postEditingEffects.deletePositionsFx(values);
    };

    return (
        <Container>
            <Formik initialValues={initialValues} onSubmit={noop} enableReinitialize>
                {({ values }) => (
                    <>
                        {!isMobile ? (
                            <>
                                <Desktop
                                    handleDelete={() => handleDelete(values)}
                                    selectedPositions={selectedPositions}
                                />
                                {inlineGlobalAmount && <GlobalAmountPortal />}
                            </>
                        ) : (
                            <Mobile handleDelete={handleDelete} selectedPositions={selectedPositions} />
                        )}
                        {!summaryNew && (!isMobile ? <TotalSums /> : <TotalSumsMobile />)}
                    </>
                )}
            </Formik>
        </Container>
    );
};
