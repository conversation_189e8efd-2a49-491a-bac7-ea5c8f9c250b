import styled, { css } from 'styled-components';
import { Button } from '@wedat/ui-kit/components/Button';
import { ButtonIcon, Text, Tooltip } from '@wedat/ui-kit';

export const TextStyled = styled(Text)`
    margin-left: 12px;
`;

export const Header = styled.div`
    display: flex;
    align-items: center;
    margin-bottom: 24px;
`;
export const MobileContainer = styled.div`
    padding: 20px 0;
    background-color: ${({ theme }) => theme.colors.white};
    border-radius: 16px;
`;

export const MobileHeader = styled(Header)`
    padding-left: 24px;
    padding-right: 8px;
`;

export const ButtonsWrapperMobile = styled.div`
    display: flex;
    margin-left: auto;
`;

export const IconWrapperDelete = styled.span`
    height: 18px;
    width: 18px;
    margin-bottom: 2px;
    color: ${({ theme }) => theme.colors.dustBlue['900']};

    svg {
        width: 100%;
        height: 100%;
    }
`;

export const ButtonStyledDeleteMobile = styled(Button)`
    width: 40px;
    height: 40px;
    border-color: ${({ theme }) => theme.colors.red['400']};
    color: ${({ theme }) => theme.colors.red['400']};
    background-color: ${({ theme }) => theme.colors.white};

    ${IconWrapperDelete} {
        color: ${({ theme }) => theme.colors.red['400']};
    }

    &:hover {
        background-color: ${({ theme }) => theme.colors.red['400']};
        border-color: ${({ theme }) => theme.colors.red['400']};

        ${IconWrapperDelete} {
            color: ${({ theme }) => theme.colors.white};
        }
    }
`;

export const TooltipWrapper = styled(Tooltip)`
    width: 70px;
    height: 16px;
    position: absolute;
    bottom: -14px;
    left: calc(50% - 35px);
    z-index: 1;
    background-color: ${({ theme }) => theme.colors.white};
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
`;

export const ActionsContainer = styled.div`
    display: flex;
`;

const IconWrapperStyle = css`
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    width: 48px;
    border-radius: 0;
    color: ${({ theme }) => theme.colors.dustBlue['900']};
    border: 1px solid ${({ theme }) => theme.colors.dustBlue['300']};
    cursor: pointer;

    &:not(:last-child) {
        border-right-style: none;
    }
    &:first-child {
        border-radius: 8px 0 0 8px;
    }
    &:last-child {
        border-radius: 0 8px 8px 0;
    }
    &:only-child {
        border-radius: 8px;
    }
`;

export const IconWrapper = styled.span`
    ${IconWrapperStyle}
`;

export const TooltipStyled = styled(Tooltip)`
    margin-right: 10px;

    ${IconWrapper} {
        margin-left: 0;
    }
`;

export const ManualAdditionWrapper = styled.div`
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 8px;
`;

export const ButtonIconStyled = styled(ButtonIcon)`
    height: 40px;
    min-width: 30px;
    width: fit-content;
    padding-left: 15px;
    padding-right: 15px;
    border-width: 1px;
    border-color: ${({ theme }) => theme.colors.gray_40};
    background-color: ${({ theme }) => theme.colors.deepBlue['800']};
    color: ${({ theme }) => theme.colors.white};
    margin-left: 10px;

    ${Text} {
        color: ${({ theme }) => theme.colors.white};
    }

    &:disabled {
        background-color: ${({ theme }) => theme.colors.dustBlue['100']};
    }

    &:hover {
        border-width: 1px;
        background-color: ${({ theme }) => theme.colors.white};
        border-color: ${({ theme }) => theme.colors.deepBlue['800']};

        ${Text} {
            color: ${({ theme }) => theme.colors.deepBlue['800']};
        }

        ${IconWrapper} {
            color: ${({ theme }) => theme.colors.deepBlue['800']};
        }
    }
`;

export const ButtonsContainer = styled.div`
    display: flex;
    align-items: center;
    background-color: ${({ theme }) => theme.colors.white};
    padding: 6px 8px 8px 8px;
`;
