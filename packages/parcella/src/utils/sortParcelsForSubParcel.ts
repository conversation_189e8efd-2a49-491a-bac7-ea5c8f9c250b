import { ParcellaItem } from '../types/plugin';

export const sortParcels = (parcels: ParcellaItem[]): ParcellaItem[] => {
    const result: ParcellaItem[] = [];
    const unprocessed = [...parcels];

    const withoutParent = unprocessed.filter(parcel => !parcel.parentParcel);
    const withParent = unprocessed.filter(parcel => parcel.parentParcel);

    withoutParent.forEach(parcel => {
        result.push(parcel);
        withParent
            .filter(child => {
                if (!!child?.principal?._id) {
                    return (
                        child?.parentParcel === parcel?.id &&
                        child?.parentParcelData?.principal &&
                        child?.parentParcelData?.principal?._id === parcel?.principal?._id
                    );
                } else {
                    return child?.parentParcel === parcel?.id && !parcel?.principal?._id;
                }
            })
            .forEach(child => {
                result.push(child);
            });
    });
    const remainingWithParent = withParent.filter(child => !result.includes(child));
    result.push(...remainingWithParent);
    return result;
};
