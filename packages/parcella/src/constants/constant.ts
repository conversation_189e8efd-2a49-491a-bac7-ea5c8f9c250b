// TODO this link must be fetch from config then
import { ParcellaRateTabs } from '../types/plugin';
import { TFunction } from 'react-i18next';
import { andConditionsFields } from './fieldsDictionary';
import { v4 as uuidv4 } from 'uuid';

export const tabs = ({ t }: { t: TFunction }) => [
    { id: ParcellaRateTabs.General, label: t('parcella.general') },
    { id: ParcellaRateTabs.Rules, label: t('parcella.rules') }
];
export const getKindOfExpertiseTranslations = ({ t }: { t: TFunction }) => [
    { value: 'standard', label: t('complexRule.standard') },
    { value: 'checkListExpertiseWithEstimate', label: t('complexRule.checkListExpertiseWithEstimate') },
    { value: 'checkListExpertiseWithoutEstimate', label: t('complexRule.checkListExpertiseWithoutEstimate') },
    { value: 'negative', label: t('complexRule.negative') },
    { value: 'ctu', label: t('complexRule.ctu') },
    { value: 'canalization', label: t('complexRule.canalization') },
    { value: 'uneconomical', label: t('complexRule.uneconomical') },
    { value: 'videoExpertise', label: t('complexRule.videoExpertise') },
    { value: 'documentationRetrieval', label: t('complexRule.documentationRetrieval') },
    { value: 'cinematicReconstruction', label: t('complexRule.cinematicReconstruction') },
    { value: 'affiliatedBodyshop', label: t('complexRule.affiliatedBodyshop') }
];

export const KIND_OF_EXPERTISE = [
    { value: 'standard', label: 'standard' },
    { value: 'checkListExpertiseWithEstimate', label: 'checkListExpertiseWithEstimate' },
    { value: 'checkListExpertiseWithoutEstimate', label: 'checkListExpertiseWithoutEstimate' },
    { value: 'negative', label: 'negative' },
    { value: 'negativeCollaborator', label: 'negativeCollaborator' },
    { value: 'ctu', label: 'ctu' },
    { value: 'canalization', label: 'canalization' },
    { value: 'uneconomicalWithEstimation', label: 'uneconomicalWithEstimation' },
    { value: 'uneconomicalWithoutEstimation', label: 'uneconomicalWithoutEstimation' },
    { value: 'videoExpertise', label: 'videoExpertise' },
    { value: 'documentationRetrieval', label: 'documentationRetrieval' },
    { value: 'endOfJobExpertise', label: 'endOfJobExpertise' },
    { value: 'dynamicsCheck', label: 'dynamicsCheck' },
    { value: 'investigationsAndInformation', label: 'investigationsAndInformation' },
    { value: 'stateLocationAssessment', label: 'stateLocationAssessment' },
    { value: 'affiliatedBodyshop', label: 'affiliatedBodyshop' },
    { value: 'cinematicReconstruction', label: 'cinematicReconstruction' }
];

export const AND_CONDITION_SELECT = andConditionsFields.map(field => ({
    value: field.value,
    label: field.name
}));

export const CONDITION_OPERATORS = ['=', '<', '<=', '>', '>='];

export const CONDITION_FOR_THEN_PART = ['=', 'Cond'];

export const THEN_CONDITION_SUB_SELECT_VALUES = ['totalNet', 'totalVAT'];

export const CALCULATION_OPERATORS = ['+', '-', '/', 'x'];

export const THEN_CONDITION_SUB_SELECT_MAX_VALUES = ['totalNet', 'totalVAT', 'workingDays'];

export const THEN_CONDITION_MAX_VALUES = ['maxTotalNet', 'maxTotalVAT', 'maxWorkingDays'];

export const INITIAL_PARCELLA_VALUES = {
    basicPayment: {},
    otherValues: {
        CTURequired: null,
        agreedExpertise: null,
        ardcase: null,
        canalization: null,
        checkListAdditionalExpertise: null,
        collaboratorAmountPerKm: null,
        complaintRecovery: null,
        detailAnalysis: null,
        expertAmountPerKm: null,
        interimExpertise: null,
        investigation: null,
        kindOfSettlement0: null,
        kindOfSettlement1: null,
        kindOfSettlement2: null,
        kindOfSettlement3: null,
        negativeExpertise: null,
        registeredEmail: null,
        technicalConsultancy: null,
        telematicDataMgmt: null,
        testimonyAmount: null,
        uneconomical: null,
        visuraAlPra: null,
        complexData: {
            Kind_of_expertise: ''
        },
        and: [],
        then: {},
        extra: { additionalFee: {} }
    }
};

export const DEFAULT_AND_CONDITION_ITEM = {
    id: uuidv4(),
    value: '',
    name: '',
    operator: '='
};

export const DEFAULT_THEN_CONDITION_ITEM = {
    id: uuidv4(),
    value: 0,
    name: '',
    operator: '=',
    subCondition: {
        percent: 0,
        name: '',
        value: 0
    },
    basicPayment: false
};

export const DEFAULT_MAX_VALUES_ITEM = {
    id: uuidv4(),
    name: '',
    value: 0
};

export const DEFAULT_EXTRA_THEN_CONDITION_MAX_VALUES_ITEM = {
    id: uuidv4(),
    value: 0,
    name: '',
    operator: '=',
    subCondition: {
        percent: 0,
        name: '',
        value: 0,
        calculatedValue: '',
        calculationOperator: '-',
        maxValueName: '',
        operator: '-',
        manualValue: 0
    },
    basicPayment: false
};

export const DEFAULT_PARCELLA_ITEM = {
    id: uuidv4(),
    values: {},
    complexRule: []
};

export const DEFAULT_EXTRA_CONDITION_ITEM = {
    id: uuidv4(),
    name: 'workingDays',
    value: '',
    operator: '=',
    userType: 'expertAdditional',
    additionalFee: 0
};

export const DEFAULT_EXTRA_IF_CONDITION = {
    id: uuidv4(),
    name: 'workingDays',
    value: '',
    operator: '='
};

export const DEFAULT_EXTRA_THEN_CONDITION = {
    id: uuidv4(),
    name: '',
    value: 0,
    operator: '='
};

export const DEFAULT_EXTRA_CONDITION = {
    extraIfCondition: DEFAULT_EXTRA_IF_CONDITION,
    extraThenCondition: { ...DEFAULT_EXTRA_THEN_CONDITION, id: uuidv4() }
};

export const SMALL_INPUT_SIZE = 85;
export const BIG_INPUT_SIZE = 120;

export const SMALL_DROPDOWN_SIZE = '120px';
export const BIG_DROPDOWN_SIZE = '170px';

export const POSSIBLE_MAX_LENGTH_OF_ROLE = 4;

export const COST_PAYMENT_FIELDS_NAME = [
    'videoExpertise',
    'interimExpertise',
    'visuraAlPra',
    'ardcase',
    'agreedExpertise',
    'canalization',
    'ctu',
    'uneconomicalWithEstimation',
    'uneconomicalWithoutEstimation',
    'expertiseBodyshopNetwork',
    'photoWidgetExpertise',
    'technicalConsultancy',
    'deposit',
    'dealer',
    'testimonyAmount',
    'complaintRecovery',
    'registeredEmail',
    'kmAmount',
    'amountPerKm',
    'amountKmForfait',
    'photoNumber',
    'maxTotalPhotos',
    'photoCollection',
    'photoCollectionForfait',
    'PlTransfer',
    'PlCheck',
    'PlCheckLawyer',
    'detailAnalysis',
    'telematicDataMgmt',
    'varie',
    'endOfJobExpertise',
    'recommended',
    'simpleMail'
];

export const BASIC_PAYMENT_ACTIVITY_SELECT = [
    {
        label: 'external',
        value: 'external'
    },
    {
        label: 'internal',
        value: 'internal'
    }
];

export const GENERIC_AGREEMENT_CONDITION_SELECT = [
    {
        label: 'Forfait',
        value: 'forfait'
    },
    {
        label: 'Bonus',
        value: 'bonus'
    }
];

export const WORKING_DAYS_CONDITION_SELECT = [
    {
        label: 'workingDays',
        value: 'workingDays'
    },
    {
        label: 'agreedExpertise',
        value: 'agreedExpertise'
    },
    {
        label: 'damageInPL',
        value: 'damageInPL'
    }
];

export const PARCEL_SHOW_ALL_OPTION = 'showAll';

export const ASSIGNMENT_KINDS: ReadonlyArray<{ label: string; value: string }> = [
    {
        label: 'Auto - P.L. firma Singola',
        value: 'Auto - P.L. firma Singola'
    },
    {
        label: 'Auto - Normale',
        value: 'Auto - Normale'
    },
    {
        label: 'Auto - Riscontro',
        value: 'Auto - Riscontro'
    },
    {
        label: 'Auto - P.L. firma Doppia',
        value: 'Auto - P.L. firma Doppia'
    },
    {
        label: 'Auto - P.L. Riscontro',
        value: 'Auto - P.L. Riscontro'
    },
    {
        label: 'Auto - Carrozzeria',
        value: 'Auto - Carrozzeria'
    },
    {
        label: 'Auto - P.L.',
        value: 'Auto - P.L.'
    },
    {
        label: 'Cristalli - Authority',
        value: 'Cristalli - Authority'
    },
    {
        label: 'Auto - Integrazione di perizia documentale',
        value: 'Auto - Integrazione di perizia documentale'
    },
    {
        label: 'AUTO - Consulenza Tecnica di parte (CTP - Contenzioso)',
        value: 'AUTO - Consulenza Tecnica di parte (CTP - Contenzioso)'
    }
];

export const FIELDS_WITHOUT_CHECKBOX = [
    'amountPerKm',
    'amountKmForfait',
    'photoCollection',
    'photoCollectionForfait',
    'maxTotalPhotos'
];

export const ROLES = ['expert', 'collaborator'] as const;
export const GENERIC_RULES_CATEGORIES = [
    {
        value: 'internal',
        label: 'internal'
    },
    {
        value: 'external',
        label: 'external'
    }
];

export const CONDITION_CONNECTOR = ['and', 'or'];
export const GENERIC_RULE_CONDITION_OPERATORS = ['=', '!==', '<', '>', '<=', '>='];
export const GENERIC_RULE_CONDITION_OPERATORS_FOR_EXPERTISE = ['=', '!=='];

export const GENERIC_RULE_CONDITION = ['expertiseType', 'totalNet', 'totalGross', 'workingDays'];

export const COLLABORATOR = 'collaboratore';

export const EXPERTISE_TYPE = 'expertiseType';
