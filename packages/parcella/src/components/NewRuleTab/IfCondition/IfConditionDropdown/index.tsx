import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useUnit } from 'effector-react';

import { Dropdown } from '@wedat/ui-kit';

import { andConditionsFields } from '../../../../constants/fieldsDictionary';
import { ConditionDropdownProps } from '../../../../types/plugin';
import { parcellaStores } from '../../../../stores/parsella';

import { DisableWrapper, DropdownItem, DropdownTitle, DropdownWrapper, WhatPart } from '../../styles';

export const ConditionDropdown: FC<ConditionDropdownProps> = ({
    id,
    name,
    handleChangeValues,
    dropdownId,
    setDropdownId,
    options = andConditionsFields,
    storeName = 'andConditionStore'
}) => {
    const { t } = useTranslation();

    const parcellaItem = useUnit(parcellaStores.newParcellaItem);

    return (
        <WhatPart>
            <DropdownTitle isChecked={!!name} onClick={() => setDropdownId(id)}>
                {!!name ? t(`complexRule.${name}`) : t('complexRule.what')}
            </DropdownTitle>
            <DropdownWrapper width="170px">
                <Dropdown side="left" isOpen={id === dropdownId} width="auto" minWidth="170px">
                    {options?.map(({ name }, index) => (
                        <DropdownItem
                            id={id}
                            onClick={() => {
                                handleChangeValues(id, name, 'name', storeName);
                                setDropdownId('0');
                            }}
                            key={`${index}+${name}`}
                        >
                            {t(`complexRule.${name}`)}
                        </DropdownItem>
                    ))}
                </Dropdown>
            </DropdownWrapper>
            <DisableWrapper isVisible={!!parcellaItem?.parentParcel} />
        </WhatPart>
    );
};
