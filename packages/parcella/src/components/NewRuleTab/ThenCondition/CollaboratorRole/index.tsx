import { FC, useMemo } from 'react';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';

import { sharedConfigurationStores } from '@dat/shared-models/configuration';
import { Dropdown } from '@wedat/ui-kit';
import { CollaboratorRoleDropdownProps } from '../../../../types/plugin';
import { parcellaStores } from '../../../../stores/parsella';

import { ConditionOperators, DisableWrapper, DropdownItem, DropdownTitle } from '../../styles';

export const CollaboratorRole: FC<CollaboratorRoleDropdownProps> = ({
    id,
    role,
    dropdownId,
    setDropdownId,
    handleChangeValues,
    createdBySubParcel,
    storeName = 'thenConditionStore'
}) => {
    const { t } = useTranslation();

    const parcellaItem = useUnit(parcellaStores.newParcellaItem);
    const roles = useUnit(sharedConfigurationStores.templateRoles);
    const rolesKeys = useMemo(() => roles?.role?.map(obj => Object.keys(obj)[0]), [roles]);

    return (
        <ConditionOperators>
            <DropdownTitle isChecked={true} onClick={() => setDropdownId(id)}>
                {role ? `(${role})` : t(`complexRule.selectRole`)}
            </DropdownTitle>
            <Dropdown side="left" isOpen={dropdownId === id} width="auto" minWidth="50px">
                {rolesKeys?.map((role, index) => (
                    <DropdownItem
                        key={`${index}+${role}`}
                        id={id}
                        onClick={() => {
                            setDropdownId('');
                            handleChangeValues(id, role, 'role', storeName);
                        }}
                    >
                        {role}
                    </DropdownItem>
                ))}
            </Dropdown>
            <DisableWrapper isVisible={!createdBySubParcel && !!parcellaItem?.parentParcel} />
        </ConditionOperators>
    );
};
