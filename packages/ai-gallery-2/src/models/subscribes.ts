import { sample } from 'effector';
import { aiGalleryEvents } from './gallery';
import { pluginOptionsModel } from './pluginOptionsModel';

sample({
    clock: aiGalleryEvents.setSelectedPanelDatId,
    source: pluginOptionsModel.store.onChangeSelectedPanelDatId,
    fn: (onChangeSelectedPanelDatId, datId) => ({ datId, onChangeSelectedPanelDatId })
}).watch(({ datId, onChangeSelectedPanelDatId }) => {
    if (onChangeSelectedPanelDatId) {
        onChangeSelectedPanelDatId(datId);
    }
});

sample({
    source: pluginOptionsModel.store.selectedPanelDatId,
    target: aiGalleryEvents.initSelectedPanelDatId
});

sample({
    source: pluginOptionsModel.store.hasPanel,
    fn: hasPanel => !!hasPanel,
    target: aiGalleryEvents.setPanel
});
