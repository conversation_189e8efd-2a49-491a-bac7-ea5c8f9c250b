import { FC } from 'react';
import { useFormikContext } from 'formik';
import { useTranslation } from 'react-i18next';
import { Button } from '@wedat/kit';
import { FormikValuesProp } from '../../types/form';
import styles from './Footer.module.css';
import classnames from 'classnames/bind';

const cx = classnames.bind(styles);

interface Props {
    isInPortal?: boolean;
}

export const Footer: FC<Props> = ({ isInPortal }) => {
    const { t } = useTranslation();
    const { submitForm, values, errors } = useFormikContext<FormikValuesProp>();

    const isDisabledSubmitButton =
        !!Object.values(errors).length ||
        !values.columnsMonth.filter(col => col.Months !== '').length ||
        !values.rowsMileagePerYear.filter(row => row.MileagePerYear !== '').length;

    return (
        <div
            className={cx('footerStyled', {
                footerStyled_inPortal: isInPortal
            })}
        >
            <Button
                label={t('confirmButton')}
                onClick={submitForm}
                aria-label={t('confirmButton')}
                disabled={isDisabledSubmitButton}
            />
        </div>
    );
};
