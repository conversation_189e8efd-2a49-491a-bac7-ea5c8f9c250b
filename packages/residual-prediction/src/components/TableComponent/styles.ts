import styled, { css } from 'styled-components/macro';
import { Field } from 'formik';
import { media, makeCustomScrollBar } from '@wedat/ui-kit/mediaQueries';

export const TableComponentStyled = styled.div`
    width: 100%;
    padding: 30px 20px 20px;
    border-radius: 5px;
    background-color: ${({ theme }) => theme.colors.white};
    overflow-x: auto;

    ${makeCustomScrollBar()}

    ${media.laptop`
        margin-top: 20px;
        padding: 0;
        background-color: unset;
    `};
`;

export const Content = styled.div`
    margin-top: 30px;
`;

export const TitleWrapper = styled.div`
    display: block;
    margin-bottom: 40px;
    color: ${({ theme }) => theme.colors.gray_300};
    line-height: 36px;

    ${media.laptop`display: none;`}
`;

export const FieldStyled = styled.div`
    display: grid;
    grid-template-columns: 1fr 3fr;
    align-items: center;

    &:not(:last-child) {
        margin-bottom: 10px;
    }

    @media (max-width: 1000px) {
        grid-template-columns: 2fr 5fr;
    }

    ${media.laptop` 
        display: flex;
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        padding: 30px 10px;
        background-color: ${({ theme }) => theme.colors.white};
        border-radius: 5px;`};

    ${media.phoneBig` 
        [class*='radio-group-container'] {
            flex-direction:column;    
        };
    `};
`;

export const Label = styled.label`
    display: inline-block;
    white-space: nowrap;
    color: ${({ theme }) => theme.colors.gray_300};
    font-weight: 500;
    font-size: 18px;
    line-height: 27px;
    text-transform: uppercase;

    ${media.laptop` 
        color: ${({ theme }) => theme.colors.dustBlue[900]};
        padding-bottom: 12px;
        text-transform: capitalize;`}
`;

export const PeriodLabel = styled.label`
    display: inline-block;
    vertical-align: bottom;
    white-space: nowrap;
    color: ${({ theme }) => theme.colors.gray_300};
    font-weight: 500;
    font-size: 18px;
    line-height: 27px;
    text-transform: uppercase;
    text-overflow: ellipsis;
    overflow: hidden;

    ${media.laptop`
        color: ${({ theme }) => theme.colors.dustBlue[900]};
        padding-bottom: 12px;
        text-transform: capitalize;`}

    @media (max-width: 350px) {
        width: 120px;
    }
`;

export const FormikFieldStyled = styled(Field)`
    width: 100%;
    text-align: center;
    color: ${({ theme }) => theme.colors.gray_300};
    font-weight: 500;
    font-size: 18px;
    line-height: 27px;

    ${media.laptop`color: ${({ theme }) => theme.colors.deepBlue['800']};`}
`;

export const InputsRow = styled.div`
    display: flex;
    align-items: center;
`;

export const InputWrapper = styled.div`
    width: 50%;

    &:nth-child(2) {
        margin-left: 10px;

        @media (max-width: 360px) {
            margin-left: 4px;
        }
    }
`;

export const TableSubstitute = styled.div`
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
    padding: 10px;
    background-color: ${({ theme }) => theme.colors.white};
    border-radius: 5px;
    border-top: 2px solid ${({ theme }) => theme.colors.dustBlue['50']};
`;

export const ResidualValue = styled.div`
    display: flex;
    margin-top: 10px;
`;

//  Table styles

export const Table = styled.table`
    border-spacing: 0;
`;

export const THead = styled.thead`
    border-bottom: 2px solid ${({ theme }) => theme.colors.gray_10};
`;
export const TBody = styled.tbody``;

export const Th = styled.th`
    min-width: 120px;
    width: 100%;
    max-width: 200px;
    padding: 8px 10px;
    border-bottom: 2px solid ${({ theme }) => theme.colors.gray_10};
`;

export const Td = styled.td<{ disabled?: boolean }>`
    min-width: 120px;
    width: 100%;
    max-width: 200px;
    padding: 8px 10px;
    border-bottom: 2px solid ${({ theme }) => theme.colors.gray_10};

    ${({ disabled }) =>
        disabled &&
        css`
            opacity: 0.7;
            pointer-events: none;
        `}
`;

export const Tr = styled.tr`
    background-color: ${({ theme }) => theme.colors.white};
    border-bottom: 2px solid ${({ theme }) => theme.colors.gray_10};
`;

export const TableTitle = styled.div`
    color: ${({ theme }) => theme.colors.dustBlue[600]};
    text-align: center;
    font-weight: 500;
    font-size: 18px;
    line-height: 27px;
`;

export const ThCornerStyled = styled.th`
    min-width: 90px;
    width: 100%;
    max-width: 200px;
    padding: 8px 10px;
    color: ${({ theme }) => theme.colors.dustBlue[600]};
    font-weight: 500;
    font-size: 18px;
    line-height: 27px;
`;
