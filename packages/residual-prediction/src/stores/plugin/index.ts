import { createDomain, restore } from 'effector';

import { PluginOptions } from '../../types/plugin';

export const residualPredicationDomain = createDomain();
const { createEvent } = residualPredicationDomain;
//
/*** Init plugin ***/
//
const initPlugin = createEvent<PluginOptions>();
const unmountPlugin = createEvent<PluginOptions>();
const pluginOptions = restore(initPlugin, null);

//
/*** Export ***/
//
export const pluginEvents = {
    initPlugin,
    unmountPlugin
};

export const pluginStores = {
    pluginOptions
};
