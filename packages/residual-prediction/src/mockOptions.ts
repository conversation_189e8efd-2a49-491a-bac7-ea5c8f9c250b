export const requestData: DAT2.Request.GetUsedVehicleForecast = {
    datECode: '010601840970001',
    container: 'DE002',
    mileage: 40000,
    constructionTime: 5980,
    registrationDate: '2020-11-24',
    restriction: 'APPRAISAL',
    coverage: 'SIMPLE',
    save: 'TRUE',
    forecastItems: {},
    includeVat: 'true',
    curveType: 'Maximum',
    mileageType: 'Year',
    priceType: 'SalesPrice',
    valueType: 'Monetary',
    decreaseType: 'Table1'
};
