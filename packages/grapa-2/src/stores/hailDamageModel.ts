import * as DAT5 from '@wedat/api';
import { createToggle } from '@dat/core/utils/effector/createToggle';
import { createStore, sample, createEvent, combine, createEffect, attach } from 'effector';
import { RepairPositionsModel } from './repairPositionsModel';
import { DatLocalesParams, pluginOptionsModel } from './pluginOptionsModel';
import { getPDRFullName } from '../utils/getPDRFullName';
import { availableAssemblyGroupsModel } from './availableAssemblyGroupsModel';
import { RC } from '../types/svgGrapaTypes';
import { opaquePaintTypes } from '../constants/lacquer';
import { PANELS_OPTIONS } from '@dat/core/constants/hailDamage';
import { sharedHailDamagesEffects } from '@dat/shared-models/hail-damages';
import { sharedConfigurationStores } from '@dat/shared-models/configuration';
import { sharedLabourRatesEffects } from '@dat/shared-models/labour-rates';
import { sharedSubjectsDataStores } from '@dat/shared-models/addressBook';
import { CustomersSubjectsData } from '@dat/shared-models/addressBook/type';
import { sharedTemplateStores } from '@dat/shared-models/template';
import { labourRatesGenericStores } from '@dat/shared-models/labour-rates-generic';
import { getDependentRateDetailsForExpert } from '@dat/shared-models/labour-rates/utils/getDependantPrincipalRateDetails';
import { contractModel } from '@dat/dat-data-models/src/models/contractModel';
import { convertContractNToUpdates } from '@dat/dat-data-models/src/utils/convertContractToUpdate';
import { applyDiscountToPDR } from '../utils/applyDiscountToPDR';
import { FtDvnMapItem } from '@dat/api2dat3/src/datDerivativesTypes/fastTrackTypes';
import { getEmptyManualPosition, getManualRepairPositionForUpdate } from '@dat/smart-components/ManualRepairPosition2';
import { composeSubClaim } from '@dat/dat-data-models/src/models/copyClaim/utils/composeSubClaim';
import { sparePartsDetailsForDPNModels } from './sparePartsDetailsForDPNModels';
import { getPartNumberForVehicleByDVN } from '../utils/getPartNumberForVehicleByDVN';
import { GrapaPluginOptions } from '../types/plugin';
import { equipmentModel } from './equipmentModel';
import { getSubClaimDetails } from '@dat/shared-models/hail-damages/utils/getSubClaimDetails';
import { attachmentModel } from '@dat/dat-data-models/src/models/attachmentModel';
import { SUB_CLAIM_SEPARATOR } from '@dat/shared-models/hail-damages/constants';
import { CONTRACT_ENTRIES_KEYS } from '@dat/core/constants';
import { filterAdditionalPdrServices } from '../utils/filterAdditionalPdrServices';
import { API2 } from '@dat/api2';
import { RepairPositionWithRowKey } from '../types/repairTypes';

const {
    addReplaceEDamage,
    addOverhaulIDamage,
    addLacquerLDamage,
    addRepairPositionsWithExistenceCheck,
    delRepairPositionByKey,
    delRepairTypeDamage,
    delDamage
} = RepairPositionsModel.event;

const hailDamage = createStore<BFF.Customer.Response.HailDamagesContent | null>(null).reset(
    pluginOptionsModel.stores.principalId
);
const hailDamageConfigByRole = combine({
    hailDamage,
    contract: pluginOptionsModel.stores.contract,
    userRole: sharedConfigurationStores.customerSettings.role
}).map(({ hailDamage, contract, userRole }) => {
    if (!hailDamage) return null;
    const { subClaimRole } = getSubClaimDetails(contract?.customTemplateData.entry);

    return (!!subClaimRole && subClaimRole === 'bodyshop') || (!subClaimRole && userRole === 'EXPERT')
        ? hailDamage?.expert || null
        : hailDamage?.bodyShop || null;
});
const hailSmartRepair = combine({ hailDamageConfigByRole, vehicleClass: pluginOptionsModel.stores.vehicleClass }).map(
    ({ hailDamageConfigByRole, vehicleClass }) =>
        hailDamageConfigByRole?.smartRepair.find(smRep =>
            Object.entries(smRep.vehicleClasses).some(([key, val]) => key === vehicleClass && val)
        ) || null
);

const isAluminumPartPresent = createStore(false);
const isOpaquePaintType = createStore(false);

const PDRIsChanged = createEvent();

const shouldCheckHailDamageDiscount = sample({
    clock: [
        addReplaceEDamage,
        addOverhaulIDamage,
        addLacquerLDamage,
        addRepairPositionsWithExistenceCheck,
        delRepairPositionByKey,
        delRepairTypeDamage,
        delDamage,
        PDRIsChanged,
        isAluminumPartPresent,
        isOpaquePaintType
    ],
    source: {
        hailDamageConfigByRole,
        isAluminumPartPresent,
        isOpaquePaintType,
        repairPositions: RepairPositionsModel.store.repairPositions,
        ftDvnMapItems: pluginOptionsModel.stores.ftCommonDefaultConfig.map(conf => conf?.ftDvnMapItems || [])
    },
    filter: ({ hailDamageConfigByRole, repairPositions }) =>
        !!hailDamageConfigByRole &&
        repairPositions.some(
            repPos =>
                repPos.PositionEntryType === 'manual' &&
                hailDamageConfigByRole.pdr.pdrHails.some(pdr => getPDRFullName(pdr) === repPos.Description)
        )
});

const [isHailDamageOpen, toggleIsHailDamageOpen] = createToggle(true);

sample({
    clock: sharedHailDamagesEffects.getHailDamagesFx.doneData,
    source: pluginOptionsModel.stores.principalId,
    fn: (principalId, hailDamagesList) => hailDamagesList.hailDamages?.find(hd => hd.id === principalId) || null,
    target: hailDamage
});

sample({
    clock: hailDamageConfigByRole,
    source: {
        availableAssemblyGroupsStore: availableAssemblyGroupsModel.stores.availableAssemblyGroupsStore
    },
    filter: (_, hailDamageConfigByRole) => !!hailDamageConfigByRole?.pdr.aluminiumIncr,
    fn: ({ availableAssemblyGroupsStore }) =>
        !!availableAssemblyGroupsStore.availableAssemblyGroups.some(
            asGr =>
                !!asGr.objectsInfo?.some(
                    objInfo =>
                        (objInfo.dvnLeft &&
                            objInfo.dvnLeft.etBauart === '7' &&
                            objInfo.dvnLeft.rcs.rc.includes(RC.E) &&
                            objInfo.dvnLeft.rcs.rc.includes(RC.L) &&
                            objInfo.dvnLeft.rcs.rc.includes(RC.I)) ||
                        (objInfo.dvnRight &&
                            objInfo.dvnRight.etBauart === '7' &&
                            objInfo.dvnRight.rcs.rc.includes(RC.E) &&
                            objInfo.dvnRight.rcs.rc.includes(RC.L) &&
                            objInfo.dvnRight.rcs.rc.includes(RC.I))
                )
        ),
    target: isAluminumPartPresent
});

sample({
    clock: pluginOptionsModel.stores.selectedLacquerType,
    source: hailDamageConfigByRole,
    filter: hailDamageConfigByRole => !!hailDamageConfigByRole?.pdr.opaqueIncr,
    fn: (_, selectedLacquerType) => (selectedLacquerType ? opaquePaintTypes.includes(selectedLacquerType) : false),
    target: isOpaquePaintType
});

sample({
    clock: shouldCheckHailDamageDiscount,
    fn: applyDiscountParams => applyDiscountToPDR(applyDiscountParams) as RepairPositionWithRowKey,
    target: RepairPositionsModel.event.createOrUpdateRepairPosition
});

const getReplacePositionsWithNoTime = (
    repairPositions: DAT5.MyClaimExternalService_schema2.RepairPosition[],
    excludeLacquer?: boolean
) =>
    repairPositions.filter(
        repPos =>
            !!repPos.DATProcessId &&
            (repPos.RepairType === 'replace' || repPos.WorkLevelItaly === 'replace') &&
            repPos.WorkTime === undefined &&
            !repPos.WorkPrice &&
            (excludeLacquer ? repPos.RepairType !== 'lacquer' : true)
    );

sample({
    clock: RepairPositionsModel.event.addRepairPositionsWithExistenceCheck,
    source: {
        hailDamageConfigByRole,
        ftDvnMapItems: pluginOptionsModel.stores.ftCommonDefaultConfig.map(conf => conf?.ftDvnMapItems || [])
    },
    filter: ({ hailDamageConfigByRole }, addedRepairPositions) =>
        !hailDamageConfigByRole?.pdr.noWorkTimeForSubs && !hailDamageConfigByRole?.pdr.onlyLacquerTimeForSubs
            ? false
            : !!getReplacePositionsWithNoTime(addedRepairPositions, hailDamageConfigByRole?.pdr.onlyLacquerTimeForSubs)
                  .length,

    fn: ({ ftDvnMapItems, hailDamageConfigByRole }, addedRepairPositions) => {
        const panelsDatIds = PANELS_OPTIONS.map(opt => opt.value);
        const replacePositionsWithNoTime = getReplacePositionsWithNoTime(
            addedRepairPositions,
            hailDamageConfigByRole?.pdr.onlyLacquerTimeForSubs
        );

        const replacePositionsWithNoTimePanels = replacePositionsWithNoTime.filter(repPos => {
            const datId = ftDvnMapItems?.find(item => item.dvns.includes(repPos.DATProcessId || 0))?.datId;
            return datId ? panelsDatIds.includes(datId) : false;
        });

        return replacePositionsWithNoTimePanels.map(repPos => ({ ...repPos, WorkTime: 0 }));
    },
    target: RepairPositionsModel.event.addRepairPositionsWithExistenceCheck
});

sample({
    clock: addReplaceEDamage,
    source: {
        hailDamageConfigByRole,
        ftDvnMapItems: pluginOptionsModel.stores.ftCommonDefaultConfig.map(conf => conf?.ftDvnMapItems || [])
    },
    filter: ({ hailDamageConfigByRole }, addedReplace) =>
        (!!hailDamageConfigByRole?.pdr.noWorkTimeForSubs || !!hailDamageConfigByRole?.pdr.onlyLacquerTimeForSubs) &&
        !!addedReplace?.DATProcessId &&
        addedReplace.WorkTime === undefined &&
        !addedReplace.WorkPrice,
    fn: ({ ftDvnMapItems }, addedReplace) => {
        if (!addedReplace) return;
        const panelsDatIds = PANELS_OPTIONS.map(opt => opt.value);
        const dvn = addedReplace.DATProcessId;
        const datId = ftDvnMapItems?.find(item => item.dvns.includes(dvn))?.datId;

        return datId && panelsDatIds.includes(datId) ? { ...addedReplace, WorkTime: 0 } : undefined;
    },
    target: addReplaceEDamage
});

export type CreateOrUpdateSubContractParams = {
    pluginOptions: GrapaPluginOptions | null;
    userRole: DAT2.CustomerRole;
    hailDamage: BFF.Customer.Response.HailDamagesContent | null;
    localeParams: DatLocalesParams;
    config: DAT2.ProductsConfiguration;
    rates: DAT2.Internal.RateItem[];
    isAluminumPartPresent: boolean;
    isOpaquePaintType: boolean;
    repairPositions: RepairPositionWithRowKey[];
    ftDvnMapItems: FtDvnMapItem[];
    subjects: CustomersSubjectsData;
    vehicleClass?: string | null;
    sparePartsDetailsForDPN: DAT5.PartsService_schema1.sparePartsResultPerDPN[];
    datECode?: string | null;
    UT_KOPIE: number | null;
    currentVehicleEquipment: number[];
    labourRatesMetadata: DAT2.Internal.MetadataFactorAdress | null;
};

const createOrUpdateSubContractFx = attach({
    source: {
        pluginOptions: pluginOptionsModel.stores.pluginOptions,
        userRole: sharedConfigurationStores.customerSettings.role,
        hailDamage,
        localeParams: pluginOptionsModel.stores.localesParams,
        isAluminumPartPresent,
        isOpaquePaintType,
        repairPositions: RepairPositionsModel.store.repairPositions,
        ftDvnMapItems: pluginOptionsModel.stores.ftCommonDefaultConfig.map(conf => conf?.ftDvnMapItems || []),
        config: sharedTemplateStores.productsConfiguration,
        rates: labourRatesGenericStores.rates,
        subjects: sharedSubjectsDataStores.customersSubjectsData,
        vehicleClass: pluginOptionsModel.stores.vehicleClass,
        sparePartsDetailsForDPN: sparePartsDetailsForDPNModels.stores.sparePartsDetailsForDPN,
        datECode: pluginOptionsModel.stores.modelDatECodeWithAlternative,
        UT_KOPIE: pluginOptionsModel.stores.UT_KOPIE,
        currentVehicleEquipment: equipmentModel.stores.currentVehicleEquipment,
        labourRatesMetadata: pluginOptionsModel.stores.labourRatesMetadata
    },
    effect: createEffect(
        async ({
            userRole,
            pluginOptions,
            hailDamage,
            localeParams,
            isAluminumPartPresent,
            isOpaquePaintType,
            repairPositions,
            ftDvnMapItems,
            config,
            rates,
            subjects,
            vehicleClass,
            sparePartsDetailsForDPN,
            datECode,
            UT_KOPIE,
            currentVehicleEquipment,
            labourRatesMetadata
        }: CreateOrUpdateSubContractParams) => {
            vehicleClass = vehicleClass ?? undefined;
            // rate overwriting process for subClaim
            const contract = pluginOptions?.data?.claimData?.contract;
            const initSpecialRate = config?.['labour-rates']?.initSpecialRateForPrincipal;
            const dependantRateForSubClaim = getDependentRateDetailsForExpert({ contract, rates, subjects });
            const shouldSetSpecialRate = !!dependantRateForSubClaim && !!initSpecialRate;
            const dossier = contract?.Dossier;
            const contractId = dossier?.DossierId;

            if (
                !dossier ||
                !contractId ||
                (userRole !== 'EXPERT' && userRole !== 'REPAIRER' && userRole !== 'BODYSHOP') ||
                !hailDamage
            )
                return;

            const dossierVehicle = dossier.Vehicle;
            const isItalianCalculation = !!contract?.complexTemplateData?.some(
                templD => templD._attr_templateType === 'vro_domus_calculation'
            );
            const locale = localeParams.locale;
            const customTemplateDataEntries = contract.customTemplateData.entry;
            const { subClaimRole, subClaimId } = getSubClaimDetails(customTemplateDataEntries);

            if (userRole === 'EXPERT' && (!subClaimRole || subClaimRole === 'bodyshop')) return;

            const sparePartSingleDiscount = labourRatesMetadata?.sparePartSingleDiscount?.value || undefined;
            let subClaimSparePartSingleDiscount: number | undefined = undefined;
            // get sparePartSingleDiscount for the subClaim
            const subClaimRate = await API2.myClaimInternal.getRate({ id: dependantRateForSubClaim });
            const subClaimAdress = subClaimRate?.costRateFactors?.MetadataFactor?.address;
            if (subClaimAdress) {
                try {
                    const subClaimAdressParsed = JSON.parse(subClaimAdress);
                    subClaimSparePartSingleDiscount = subClaimAdressParsed?.sparePartSingleDiscount
                        ? subClaimAdressParsed.sparePartSingleDiscount.value || undefined
                        : undefined;
                } catch {}
            }

            const currentClaimRole = 'bodyshop';
            const currentHailDamage = hailDamage.bodyShop;
            const subHailDamage = hailDamage.expert;
            const subHailSmartRepair = subHailDamage.smartRepair.find(smRep =>
                Object.entries(smRep.vehicleClasses).some(([key, val]) => key === vehicleClass && val)
            );
            const attachments = pluginOptions?.data?.attachmentsData?.listAttachmentsOfContractWithBlob || [];
            const subHailDamageAdditional = filterAdditionalPdrServices(subHailDamage.pdr.additional, vehicleClass);
            const currentHailDamageAdditional = filterAdditionalPdrServices(
                currentHailDamage.pdr.additional,
                vehicleClass
            );

            const newSubRepairPositions = repairPositions.filter(
                repPos =>
                    !(
                        !!currentHailDamageAdditional.some(
                            curAdServ =>
                                repPos.PositionEntryType === 'manual' && curAdServ.description === repPos.Description
                        ) && !subHailDamageAdditional.some(subAdServ => subAdServ.description === repPos.Description)
                    )
            );
            const newSubTemplateDataEntries = [
                ...(customTemplateDataEntries?.filter(
                    ent =>
                        ent.value?._attr_type === 'xs:string' &&
                        ent.key !== 'claimNumber' &&
                        ent.key !== 'referenceNumber'
                ) || []),
                {
                    key: CONTRACT_ENTRIES_KEYS.MEMO.subClaim,
                    value: {
                        _attr_type: 'xs:string',
                        _value: `${currentClaimRole}${SUB_CLAIM_SEPARATOR}${contractId}`
                    }
                },
                ...(!subClaimId && shouldSetSpecialRate
                    ? [
                          {
                              key: 'jsonLabourRated',
                              value: {
                                  _attr_type: 'xs:string',
                                  _value: ''
                              }
                          }
                      ]
                    : [])
            ];

            const subClaimPDRRepPos = applyDiscountToPDR({
                hailDamageConfigByRole: subHailDamage,
                isAluminumPartPresent,
                isOpaquePaintType,
                repairPositions,
                ftDvnMapItems
            });
            const subClaimPDR = subHailDamage.pdr.pdrHails.find(
                pdr => getPDRFullName(pdr) === subClaimPDRRepPos?.Description
            );

            subHailDamageAdditional.forEach(adServ => {
                const adServRepPosIndex = newSubRepairPositions.findIndex(
                    repPos => repPos.PositionEntryType === 'manual' && repPos.Description === adServ.description
                );
                if (adServRepPosIndex !== -1) {
                    newSubRepairPositions[adServRepPosIndex].SparePartPrice = adServ.cost;
                    return;
                } else {
                    if (
                        subClaimPDR &&
                        (adServ.setByDefault || adServ.forDefaultHailsIds.split(',').includes(subClaimPDR.id))
                    ) {
                        newSubRepairPositions.push(
                            getManualRepairPositionForUpdate({
                                ...getEmptyManualPosition(),
                                Description: adServ.description,
                                SparePartPrice: adServ.cost,
                                RepairType: 'replace'
                            })
                        );
                    }
                }
            });

            const subClaimPDRRepPosIndex = repairPositions?.findIndex(
                repPos =>
                    repPos.PositionEntryType === 'manual' &&
                    subHailDamage.pdr.pdrHails.some(pdr => getPDRFullName(pdr) === subClaimPDRRepPos?.Description)
            );
            if (subClaimPDRRepPos && subClaimPDRRepPosIndex !== -1)
                newSubRepairPositions[subClaimPDRRepPosIndex] = subClaimPDRRepPos;

            const newSubRepairPositionsUpdated = newSubRepairPositions.map(repPos => {
                const newRepPos = { ...repPos };
                const {
                    DATProcessId,
                    RepairType,
                    PositionEntryType,
                    SparePartNumber,
                    SparePartPrice,
                    SparePartDiscount,
                    SparePartSupplyDescription
                } = newRepPos;

                // Update discount (replace) according to subRole's sparePartSingleDiscount
                if (
                    subClaimSparePartSingleDiscount !== sparePartSingleDiscount &&
                    RepairType === 'replace' &&
                    PositionEntryType === 'graphical' &&
                    SparePartDiscount === sparePartSingleDiscount
                ) {
                    newRepPos.SparePartDiscount = subClaimSparePartSingleDiscount;
                }

                const smartRepairRuleDescription = SparePartSupplyDescription;
                if (!smartRepairRuleDescription) return newRepPos;

                const dvn = DATProcessId;
                const datId = dvn ? ftDvnMapItems?.find(item => item.dvns.includes(dvn))?.datId : undefined;
                const smartRule = subHailSmartRepair?.smartRules.find(smartRule => {
                    const usedRule = smartRule.rules.find(rule =>
                        smartRepairRuleDescription.endsWith(rule.description)
                    );
                    return (smartRule.panel === datId || !smartRule.panel) && usedRule;
                });
                const selectedRule = smartRule?.rules?.find(
                    rule =>
                        (isItalianCalculation ? RepairType === 'replace' : rule.repairType === RepairType) &&
                        smartRepairRuleDescription.endsWith(rule.description)
                );

                let resultPrice = selectedRule?.valueType === '€' ? selectedRule.value : SparePartPrice;

                if (dvn && RepairType === 'replace' && selectedRule?.valueType === '€') {
                    const vehicle = {
                        equipment: currentVehicleEquipment,
                        manufacturerCodeEquipment: dossierVehicle?.VINResult?.VINEquipments?.VINEquipment,
                        constructionTime: dossierVehicle?.ConstructionTime,
                        datECode: datECode,
                        UT_KOPIE
                    };
                    const foundPart = getPartNumberForVehicleByDVN({ vehicle, DVN: dvn, sparePartsDetailsForDPN });
                    const partNumber = SparePartNumber || foundPart?.sparePartsVehicle?.partNumber;
                    const sparePartsDetailForDPN = sparePartsDetailsForDPN?.find(
                        sparePartsDetail => sparePartsDetail?.datProcessNumber?.[0] === dvn
                    );
                    const spInformation = sparePartsDetailForDPN?.sparePartsInformations?.sparePartsInformation?.find(
                        spInformation => spInformation.partNumber === partNumber
                    );
                    const foundPrice =
                        !SparePartNumber || spInformation?.partNumber === SparePartNumber
                            ? spInformation?.price
                            : undefined;
                    resultPrice = selectedRule.value + (foundPrice || 0);
                }

                return {
                    ...repPos,
                    SparePartPrice: resultPrice
                };
            });

            const newSubClaim = composeSubClaim({
                contract,
                locale,
                repairPositions: newSubRepairPositionsUpdated,
                templateDataEntries: subClaimId ? undefined : newSubTemplateDataEntries,
                subClaimId: subClaimId ? parseInt(subClaimId) : undefined,
                shouldSetSpecialRate
            });

            const newSubClaimId = (await DAT5.MyClaimExternalService.createOrUpdateContractN(newSubClaim)).responseObj
                .return;

            !subClaimId &&
                shouldSetSpecialRate &&
                (await sharedLabourRatesEffects.handleDataWithoutJsonLabourRatesFx({
                    rateId: dependantRateForSubClaim,
                    subClaimId: newSubClaimId
                }));

            if (!!newSubClaimId) {
                await DAT5.VehicleRepairService.calculateContract({
                    request: {
                        locale,
                        contractID: newSubClaimId
                    }
                });

                if (!subClaimId) {
                    attachmentModel.effect.uploadAttachmentsToSubClaimFx({
                        attachmentItems: attachments,
                        subClaimIdParam: newSubClaimId,
                        subClaimRoleParam: 'expert'
                    });

                    const newSubClaimRole = subClaimRole || 'expert';
                    const currentContractUpdated: DAT5.MyClaimExternalService_schema1.contractDetails = {
                        ...contract,
                        customTemplateData: {
                            entry: [
                                ...(customTemplateDataEntries || []),
                                {
                                    key: CONTRACT_ENTRIES_KEYS.MEMO.subClaim,
                                    value: {
                                        _attr_type: 'xs:string',
                                        _value: `${newSubClaimRole}${SUB_CLAIM_SEPARATOR}${newSubClaimId}`
                                    }
                                }
                            ]
                        }
                    };
                    const newRequestsForSave = convertContractNToUpdates(currentContractUpdated, contract);
                    if (newRequestsForSave.length) {
                        for (const newRequest of newRequestsForSave) {
                            await DAT5.MyClaimExternalService.createOrUpdateContractN(newRequest);
                        }
                        await contractModel.effect.updateContractAndFtClaimFx(contractId);
                    }
                }
            }
        }
    )
});

// update sub Expert/Bodyshop contract (Hail Damage)
sample({
    clock: contractModel.effect.saveAndUpdateContractFx.doneData,
    source: pluginOptionsModel.stores.pluginOptions,
    filter: (pluginOptions, _) => !!pluginOptions?.settings?.useHailDamages,
    target: createOrUpdateSubContractFx
});

export const hailDamageModel = {
    stores: {
        hailDamage,
        hailDamageConfigByRole,
        hailSmartRepair,
        isHailDamageOpen,
        isAluminumPartPresent,
        isOpaquePaintType
    },
    events: {
        toggleIsHailDamageOpen,
        PDRIsChanged
    },
    effects: {
        createOrUpdateSubContractFx
    }
};
