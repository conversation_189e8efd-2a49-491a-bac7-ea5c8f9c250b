{"packageGrapaName": "Графический каталог", "InputSearchTextPlaceHolder": "Введите текст поиска", "avaliableLaquer": {"none": "без окраски", "new": "Окраска новой детали", "surface": "Поверхностная", "major": ">50%", "minor": "<50%"}, "repairType": {"replace": "Замена", "overhaul": "Ремонт", "fixing": "Восстановление", "disAndMounting": "с/у", "lacquer": "Окраска"}, "RepairType": {"code": {"A": "A", "E": "E", "I": "I", "L": "L", "P": "P", "S": "S", "T": "T", "U": "U", "W": "W", "/": "/"}, "label": {"A": "Снятие-Установка", "E": "Замена", "I": "Ремонт", "L": "Покраска", "P": "P", "S": "S", "T": "T", "U": "U", "W": "W", "/": "Ремонт"}}, "ManualRepairPositionForm": {"Description": "Описание", "SparePartNumber": "Номер запчасти", "SparePartPrice": "Цена запчасти", "SparePartDiscount": "Скидка", "WorkTime": "Трудоемкость", "SparePartAmount": "Кол-во", "Save": "Сохранить", "Ok": "Ок", "Add": "Добавить", "Delete": "Удалить", "Close": "Закрыть"}, "fastTrack.noDamage": "Без повреждений", "fastLocalNameDATID": {"DATID_0001": "Передний бампер", "DATID_0002": "Решетка радиатора", "DATID_0003": "Капот двигателя", "DATID_0004": "Лобовое стекло", "DATID_0005": "Крыша", "DATID_0006": "Лю<PERSON>", "DATID_0007": "Задняя дверь", "DATID_0008": "<PERSON>а<PERSON><PERSON><PERSON> бампер", "DATID_0009": "Задний вспомогательный свет", "DATID_0010": "Заднее стекло", "DATID_0021": "Левая противотуманная фара", "DATID_0022": "Правая противотуманная фара", "DATID_0023": "Левая фара", "DATID_0024": "Правая фара", "DATID_0025": "Крыло левое", "DATID_0026": "Крыло правое", "DATID_0027": "Указатель поворота левый", "DATID_0028": "Указатель поворота правый", "DATID_0029": "Боковое окно переднее левое", "DATID_0030": "Боковое окно переднее правое", "DATID_0031": "Стойка левая с порогом", "DATID_0032": "Стойка правая с порогом", "DATID_0033": "Дверь передняя левая", "DATID_0034": "Дверь передняя правая", "DATID_0035": "Окно двери передний левое", "DATID_0036": "Окно двери передний правое", "DATID_0037": "Зеркало заднего вида левое", "DATID_0038": "Зеркало заднего вида правое", "DATID_0039": "Cтойка B левая с порогом", "DATID_0040": "Cтойка B правая с порогом", "DATID_0041": "Дверь задняя левая", "DATID_0042": "Дверь задняя правая", "DATID_0043": "Окно двери заднее левое", "DATID_0044": "Окно двери заднее правое", "DATID_0045": "Боковина левая задняя", "DATID_0046": "Боковина задняя правая", "DATID_0047": "Боковое стекло заднее левое", "DATID_0048": "Боковое стекло правое заднее", "DATID_0051": "Задний фонарь левый", "DATID_0052": "Задний фонарь правый", "DATID_0053": "Задний фонарь внутренний левый", "DATID_0054": "Задний фонарь внутренний правый", "DATID_0061": "<PERSON>ина передняя левая", "DATID_0062": "<PERSON>ина передняя правая", "DATID_0063": "Обод передний левый", "DATID_0064": "Обод передний правый", "DATID_0065": "Шины задняя левая", "DATID_0066": "Шины задняя правая", "DATID_0067": "Обод задний левый", "DATID_0068": "Обод задний правый", "DATID_0071": "Ручка двери левая передняя", "DATID_0072": "Ручка двери правая передняя", "DATID_0073": "Ручка двери задняя левая", "DATID_0074": "Ручка двери задняя правая", "DATID_0011": "Лючек топливного бака", "DATID_0081": "Порог левый", "DATID_0082": "Порог правый", "DATID_WC_FL": "Колпак передний левый", "DATID_WC_FR": "Колпак передний правый", "DATID_WC_RL": "Колпак задний левый", "DATID_WC_RR": "Колпак задний правый", "DATID_LCK_FL": "Замок левый", "DATID_LCK_FR": "Замок правый", "DATID_LCK_BK": "Замок задней двери"}, "fastLocalNameDamages": {"level0": "Замена", "level1": "Ремонтная покраска", "level2": "Покраска", "level3": "Замена", "level4": "Повреждено", "level5": "Скол", "level6": "Отсутствует", "level7": "Деформация", "chip": "Прокол", "crack": "Деформация"}, "fastMovingBar": {"GenericGroups": "Общие группы"}, "fastMovingBarMenu": {"windows": "Окна", "doors": "Двери"}, "genericGraphic": {"topView": "Сверху", "frontLeft": "Спереди слева", "backRight": "Сзади справа"}, "EquipmentList": {"Equipment": "Оборудование", "SearchPlaceHolder": "Введите слова для поиска", "BySelectedPart": "По выбранным деталям", "Changed": "Измененные", "All": "Все", "Close": "Закрыть", "ser": "<PERSON>ер", "vin": "вин", "Description": "Описание", "possible": "Возможное", "existing": "Существующее", "DVN": "DVN", "deselected": "снятое", "special": "специальное", "Category": "Категория", "EquipmentGroup": "Группа", "EquipmentClass": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AddedByLogikCheck": "Добавлено логикой", "ManufacturerCode": "Код производителя"}, "FilterMenu": {"Close": "Закрыть", "SelectionFilter": "Фильтр выбора", "Single": "Единичные", "Left": "Левые", "Right": "Правые"}, "DefaultDamageMenu": {"DamagesForFastSelection": "Повреждения для быстрого выбора", "availableDefaultDamages": {"EReplace": "Замена", "ADisAndMounting": "Снятие установка", "lacquerNew": "Покраска новой", "lacquerSurface": "Покраска поверхности"}}, "ElementDescriptionLeftRight": {"L": "лев.", "R": "прав."}, "LiquidsView": {"Header": "Жидкости", "FillingAmount": "Заправочный объем (полный) : ", "UseCondition": "Условия : ", "Intervals": "Интервалы : ", "Products": "Продукты : "}}