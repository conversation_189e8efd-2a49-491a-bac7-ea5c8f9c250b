import { repairTypeByRepairCode, convertRepairType } from './ftRepairToRepairPosition';

describe('repairTypeByRepairCode', () => {
    it('returns an empty object when given an invalid repair code', () => {
        expect(repairTypeByRepairCode('invalidCode')).toEqual({
            representation: '',
            code: '',
            key: '',
            repairType: ''
        });
    });

    it.skip('returns the correct result when given a valid repair code', () => {
        expect(repairTypeByRepairCode('RC1')).toEqual({
            representation: 'Representation1',
            code: 'RC1',
            key: 'repairTypeNameSDIIDatExchange1',
            repairType: 'repairTypeNameFioVXSImport1'
        });
    });
});

describe('convertRepairType', () => {
    it('returns an empty object when given an empty string', () => {
        expect(convertRepairType('')).toEqual({
            representation: '',
            code: '',
            key: '',
            repairType: ''
        });
    });

    it('returns an empty object when given an invalid repair type', () => {
        expect(convertRepairType('invalidType')).toEqual({
            representation: '',
            code: '',
            key: '',
            repairType: ''
        });
    });

    it.skip('returns the correct result when given a valid repair type', () => {
        expect(convertRepairType('repairTypeNameFioVXSImport1')).toEqual({
            code: 'RC1',
            key: 'repairTypeNameSDIIDatExchange1',
            repairType: 'repairTypeNameFioVXSImport1'
        });
    });
});
