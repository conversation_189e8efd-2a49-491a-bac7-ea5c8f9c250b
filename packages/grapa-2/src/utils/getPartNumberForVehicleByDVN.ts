import * as DAT5 from '@wedat/api';

export type GetPartNumberForVehicleByDVNParams = {
    sparePartsDetailsForDPN?: DAT5.PartsService_schema1.sparePartsResultPerDPN[];
    DVN?: number;
    vehicle: ShortVehicle;
};

export interface VINEquipment {
    AvNumberDat?: number; // tns:fieldInteger
    ManufacturerCode?: string; // tns:fieldString
    ShortName?: string; // tns:fieldString
}

export type ShortVehicle = {
    equipment?: number[];
    manufacturerCodeEquipment?: VINEquipment[]; // if vehicle by VIN identified and available
    constructionTime?: number;
    datECode?: string | null;
    UT_KOPIE?: number | null;
};

export function getPartNumberForVehicleByDVN({
    sparePartsDetailsForDPN,
    DVN,
    vehicle
}: GetPartNumberForVehicleByDVNParams) {
    if (!sparePartsDetailsForDPN || !DVN) return undefined;

    const foundSparePartsResultPerDPN = sparePartsDetailsForDPN.find(
        sparePart => sparePart?.datProcessNumber?.[0] === DVN
    );

    const constructionTime = vehicle.constructionTime || 9999;

    const sortedSparePartsVehicle = foundSparePartsResultPerDPN?.sparePartsVehicles?.sparePartsVehicle
        ?.filter(sp => !(sp.partNumber || '').startsWith('INT#')) //  some part numbers has not used value "INT#000001"
        .sort((a, b) => (b.constructionTimeFrom || 0) - (a.constructionTimeFrom || 0));

    let foundSparePartsVehicleResult: DAT5.PartsService_schema1.sparePartsVehicle | undefined = undefined;

    if (foundSparePartsResultPerDPN?.sparePartsVehicles?.sparePartsVehicle?.length === 1) {
        foundSparePartsVehicleResult = sortedSparePartsVehicle?.[0];
        const foundSparePartsInformation =
            foundSparePartsResultPerDPN?.sparePartsInformations?.sparePartsInformation?.find(
                spInfo => spInfo.partNumber === foundSparePartsVehicleResult?.partNumber
            );
        return {
            sparePartsDetailsForDPN: foundSparePartsResultPerDPN,
            sparePartsVehicle: foundSparePartsVehicleResult,
            sparePartsInformation: foundSparePartsInformation
        };
    }

    let foundSparePartsVehicle = sortedSparePartsVehicle?.filter(
        sparePartVeh =>
            (((sparePartVeh.constructionTimeFrom || 0) <= constructionTime &&
                constructionTime <= (sparePartVeh.constructionTimeTo || 0)) ||
                sparePartVeh.constructionTimeFrom === 8999) &&
            isConstructionCondition(
                sparePartVeh.constructionCondition,
                sparePartVeh.excludingCondition,
                vehicle.equipment || []
            ) &&
            vehicle.datECode &&
            isSparePartsSubModels(vehicle, sparePartVeh.sparePartsSubModels)
    );

    // if there is vin manufacture equipment
    if (vehicle.manufacturerCodeEquipment?.length) {
        const manEquipmentArray: string[] = vehicle.manufacturerCodeEquipment.map(eq => eq.ManufacturerCode || '');

        if (foundSparePartsVehicle?.length) {
            foundSparePartsVehicle = foundSparePartsVehicle.filter(part => {
                if (
                    part.manufacturerCodeCondition
                    // &&
                    // part.constructionTimeFrom === 8999
                    // &&
                    // isSparePartsSubModels(vehicle, part.sparePartsSubModels)
                    // part.descriptionIdentifier === '01'
                ) {
                    return checkCondition(manEquipmentArray, part.manufacturerCodeCondition);
                }
                return true;
            });
        }
    }

    // found records with maximum condition (most accurate conditions, best match conditions)
    const maxSizeCondition = foundSparePartsVehicle?.reduce(
        (max, sparePartVeh) =>
            (sparePartVeh.constructionCondition?.length || 0) > max
                ? sparePartVeh.constructionCondition?.length || 0
                : max,
        0
    );
    foundSparePartsVehicle = foundSparePartsVehicle?.filter(
        sparePartVeh => (sparePartVeh.constructionCondition?.length || 0) === maxSizeCondition
    );

    // may be sorting is not correct
    foundSparePartsVehicle = foundSparePartsVehicle?.sort((a, b) => {
        return (b.descriptionIdentifier || '') >= (a.descriptionIdentifier || '') ? 1 : -1;
    });

    foundSparePartsVehicleResult = foundSparePartsVehicle?.[0];

    if (!foundSparePartsVehicleResult) {
        // if not found specific find and use default spare part number
        foundSparePartsVehicleResult = foundSparePartsResultPerDPN?.sparePartsVehicles?.sparePartsVehicle?.find(
            sparePartVeh =>
                (sparePartVeh.constructionTimeFrom || 0) <= constructionTime &&
                constructionTime <= (sparePartVeh.constructionTimeTo || 0) &&
                !sparePartVeh.constructionCondition &&
                !sparePartVeh.excludingCondition &&
                vehicle.datECode &&
                isSparePartsSubModels(vehicle, sparePartVeh.sparePartsSubModels)
        );
    }

    const foundSparePartsInformation = foundSparePartsResultPerDPN?.sparePartsInformations?.sparePartsInformation?.find(
        spInfo => spInfo.partNumber === foundSparePartsVehicleResult?.partNumber
    );

    return {
        sparePartsDetailsForDPN: foundSparePartsResultPerDPN,
        sparePartsVehicle: foundSparePartsVehicleResult,
        sparePartsInformation: foundSparePartsInformation
    };
}
function isConstructionCondition(
    constructionCondition: string | undefined,
    excludingCondition: string | undefined,
    equipment: number[]
): boolean {
    if (!constructionCondition && !excludingCondition) return true;

    const constructionConditionEquipment = constructionCondition?.split('&').map(eq => Number(eq));
    const excludingConditionEquipment = excludingCondition?.split('/').map(eq => Number(eq));

    if (!!constructionConditionEquipment) {
        for (const eq of constructionConditionEquipment) {
            if (!equipment.includes(eq)) return false;
        }
    }

    if (!!excludingConditionEquipment) {
        for (const eq of excludingConditionEquipment) {
            if (equipment.includes(eq)) return false;
        }
    }

    return true;
}

// function
// as strCondition can be 'ab01/cd/ef' or 'ab02&cd' or 'cd'
// as listParams is array of string like ['ab01','ef']
export function checkCondition(listParams: string[], strCondition: string): boolean {
    if (strCondition.includes('&')) {
        // AND condition: all parts must be found in the list
        const conditions = strCondition.split('&').map(s => s.trim());
        return conditions.every(cond => listParams.includes(cond));
    } else if (strCondition.includes('/')) {
        // OR condition: at least one part must be found in the list
        const conditions = strCondition.split('/').map(s => s.trim());
        return conditions.some(cond => listParams.includes(cond));
    } else {
        // single condition
        return listParams.includes(strCondition.trim());
    }
}

function isSparePartsSubModels(
    vehicle: ShortVehicle,
    sparePartsSubModels: DAT5.PartsService_schema1.sparePartsSubModels | undefined
): boolean {
    // example 015900360160001 (16 is subModel)
    const subModel = Number(vehicle.datECode?.substring(8, 11));

    if (
        !sparePartsSubModels ||
        !sparePartsSubModels.sparePartsSubModel ||
        !sparePartsSubModels.sparePartsSubModel.length
    )
        return true;

    const foundSparePartsSubModel = sparePartsSubModels.sparePartsSubModel?.find(
        subM => subM.subModel === subModel || subM.subModel === vehicle.UT_KOPIE
    );

    return !!foundSparePartsSubModel;
}
