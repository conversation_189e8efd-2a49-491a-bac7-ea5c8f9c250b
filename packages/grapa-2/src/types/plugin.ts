/**
 * Describe types for plugin GraPa
 */
// import { DatServicesUrls } from '@dat/api2dat/types/api2datTypes';
import { PluginAPIweDat } from '@dat/vehicle-static-data-service/src/types/pluginDataTypes';

export interface GrapaPluginOptions {
    selector?: string;

    // // todo remove manualPositionsService service for manual operation from grapa
    // token?: string;
    // credentials?: {
    //     interfacePartnerNumber?: string;
    //     interfacePartnerSignature?: string;
    //     customerNumber: string;
    //     customerLogin: string;
    //     customerPassword: string;
    // };
    // datServicesUrls?: DatServicesUrls;

    locale?: string; // like 'en-US' 'ru-RU'

    // in most plugin this field named as "settings" in grapa it is "settings" as in grapa 1
    settings?: DAT2.GrapaConfiguration;
    data?: PluginAPIweDat;

    stickyFooterTargetId?: string;
}

export interface GrapaPluginOptionsFromClaimStepper {
    locale?: string; // like 'en-US' 'ru-RU'
    data?: PluginAPIweDat;
    stickyFooterTargetId?: string;
}

export interface ApplicationPluginOptions extends GrapaPluginOptions {
    selector?: string;
    token?: string;
    credentials?: {
        customerNumber: string;
        customerLogin: string;
        customerPassword: string;
        interfacePartnerNumber?: string;
        interfacePartnerSignature?: string;
    };
    contractId: number;
}

