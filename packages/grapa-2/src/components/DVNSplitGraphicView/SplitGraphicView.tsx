import { SvgLoader } from '@wedat/react-svgmt';
import { Col, Row, Tooltip } from 'antd';
import { FC } from 'react';
import { graphicDamageSelectionModel } from '../../stores/graphicDamageSelectionModel';
import { rightSideDrawerModel } from '../../stores/rightSideDrawerModel';
import { ButtonWithMark } from '../CommonComponents/ButtonWithMark/ButtonWithMark';

import './DVNSplitGraphicView.css';
import { GroupBySVGElement } from './groupsDVNType';
import { RightViewKeys } from '../../types/views';

export const SplitGraphicView: FC<{
    currentDVNs: number[] | undefined;
    dvnsWithDamages: Set<number | undefined>;
    groupsBySVGElement: GroupBySVGElement[];
}> = ({ groupsBySVGElement, currentDVNs, dvnsWithDamages }) => {
    return (
        <Row gutter={[8, 16]}>
            {true &&
                groupsBySVGElement.map(groupBySVGElement => {
                    const datIDs = groupBySVGElement.groupsDVN.map(gr => gr.datID);
                    return (
                        <Col
                            key={groupBySVGElement.groupBySVGElement + groupBySVGElement.assemblyGroupId}
                            span={6}
                            xs={24}
                            sm={24}
                            md={12}
                            lg={8}
                            xl={6}
                            className={
                                currentDVNs?.find(dvn =>
                                    groupBySVGElement.groupsDVN.find(
                                        grDVN => grDVN.dvn === dvn || grDVN.dvnRight === dvn
                                    )
                                )
                                    ? 'DVNSplitGraphicSelected'
                                    : 'DVNSplitGraphicNotSelected'
                            }
                        >
                            <Tooltip
                                title={
                                    Array.isArray(groupBySVGElement.hintDescription)
                                        ? groupBySVGElement.hintDescription?.map(hintLine => (
                                              // TODO: check Andrey grapa
                                              // @ts-ignore
                                              <div>{hintLine}</div>
                                          ))
                                        : groupBySVGElement.hintDescription
                                }
                            >
                                <div
                                    style={{ width: '100%' }}
                                    onClick={() => {
                                        graphicDamageSelectionModel.events.setCurrentObjectByDATIDandGroup({
                                            datIDs,
                                            assemblyGroupId: groupBySVGElement.assemblyGroupId
                                        });
                                        rightSideDrawerModel.events.setCurrentView(RightViewKeys.GraphicSetDamageForms);
                                    }}
                                >
                                    {!!groupBySVGElement.svgElement && (
                                        <SvgLoader
                                            style={{ maxWidth: '600px', maxHeight: '64px' }}
                                            svgXML={groupBySVGElement.svgElement.resultSvg}
                                        />
                                    )}
                                    {!!groupBySVGElement.symbolSVG && (
                                        <SvgLoader
                                            style={{ maxWidth: '600px', maxHeight: '64px' }}
                                            svgXML={groupBySVGElement.symbolSVG.DTSY_DATEN}
                                        />
                                    )}
                                </div>
                            </Tooltip>
                            <Row gutter={[8, 8]}>
                                {groupBySVGElement.groupsDVN.map((gr, index) => {
                                    return (
                                        <Col key={index} span={24}>
                                            <ButtonWithMark
                                                style={{ width: '100%', textAlign: 'center' }}
                                                leftMark={dvnsWithDamages.has(gr.dvn || 0)}
                                                rightMark={dvnsWithDamages.has(gr.dvnRight || 0)}
                                                className="weDat-grapa-preselectBtn"
                                                onClick={() => {
                                                    if (gr.dvn) {
                                                        // graphicDamageSelectionModel.events.setCurrentObjectByDATIDandGroup(
                                                        //     {
                                                        //         datIDs: [gr.objectInfoSVG.datid],
                                                        //         // datIDs,
                                                        //         assemblyGroupId:
                                                        //             currentAssemblyGroup?.assemblyGroup
                                                        //                 .assemblyGroupId
                                                        //     }
                                                        // );
                                                        graphicDamageSelectionModel.events.updateStore({
                                                            typeOfGraphic: 'FullGraphic' // SearchResultSplitGraphic
                                                        });

                                                        graphicDamageSelectionModel.events.setCurrentObjectsByDVN({
                                                            assemblyGroupId: groupBySVGElement.assemblyGroupId,
                                                            dvns: [gr.dvn]
                                                        });

                                                        rightSideDrawerModel.events.setCurrentView(
                                                            RightViewKeys.GraphicSetDamageForms
                                                        );
                                                    }
                                                }}
                                            >
                                                {gr.isShowDVN && gr.dvn}&nbsp;{gr.description}&nbsp;
                                                {gr.isShowDVN && gr.dvnRight}
                                            </ButtonWithMark>
                                            {gr.partsInfo?.map(part => (
                                                <div key={part.partNumber}>
                                                    {'' + part.partNumber + ' ' + part.partDescription}
                                                </div>
                                            ))}
                                        </Col>
                                    );
                                })}
                            </Row>
                        </Col>
                    );
                })}
        </Row>
    );
};
