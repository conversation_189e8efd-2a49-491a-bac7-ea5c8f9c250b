// SwitchDvnRepairView

import { FC, memo } from 'react';
import { useUnit } from 'effector-react';

import { AniaAggregates } from './AniaTimesView2';
// import { DefaultDvnRepairView2 } from './DefaultDvnRepairView2';
import { pluginOptionsModel } from '../../../stores/pluginOptionsModel';
import { italyAniaModel2 } from '../../../stores/italyAniaModel2';
import { AniaTimesFastSelectView } from './FastSelect/AniaTimesFastSelectView';
import { CommonRepairView } from './CommonRepairView/CommonRepairView';

interface Props {
    isFastSelect?: boolean;
    unfoldedKey?: string[];
}

export const SwitchDvnRepairView = memo<Props>(({ isFastSelect, unfoldedKey }) => {
    const pluginOptions = useUnit(pluginOptionsModel.stores.pluginOptions);
    const isItalianCalcualtion = !!pluginOptions?.data?.claimData?.contract?.complexTemplateData?.find(
        templD => templD._attr_templateType === 'vro_domus_calculation'
    );
    const currentAniaTimes = useUnit(italyAniaModel2.stores.currentAniaTimes);
    const isAniaRepairView = isItalianCalcualtion && !!currentAniaTimes;

    return isAniaRepairView ? (
        <AniaTimesFastSelectView />
    ) : isFastSelect ? (
        <>
            {/* <DefaultDvnRepairView2 unfoldedKey={unfoldedKey} /> */}
            <CommonRepairView />
            <AniaAggregates />
        </>
    ) : (
        <>
            {/* <DefaultDvnRepairView2 unfoldedKey={unfoldedKey} /> */}
            <CommonRepairView />
            <AniaAggregates />
        </>
    );
});
