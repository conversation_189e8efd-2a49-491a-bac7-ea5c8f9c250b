import { Col, Row, Tooltip } from 'antd';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';
import { pluginOptionsModel } from '../../stores/pluginOptionsModel';
import './intermediateCalculationSummary.css';
import { DamageElementGroup } from '../../types/graphicTypes';
import InfoIcon from '../CommonComponents/Icons/InfoIcon.svg?react';
import { RepairPositionsModel } from '../../stores/repairPositionsModel';
import { useFormattedPrice } from '@dat/shared-models/hooks/useFormattedPrice';
import { getFormatPriceOptions } from '../../utils/getFormatPriceOptions';

interface IntermediateCalculationSummaryProps {
    damageList: DamageElementGroup[];
}

export const IntermediateCalculationSummary = ({ damageList }: IntermediateCalculationSummaryProps) => {
    const pluginOptions = useUnit(pluginOptionsModel.stores.pluginOptions);
    const selectedLacquerMethod = useUnit(pluginOptionsModel.stores.selectedLacquerMethod);
    const isRepPosModified = useUnit(RepairPositionsModel.store.isModifiedAfterConfirm);
    // const sparePartPositionDossier = useUnit(sparePartSearchModel.stores.sparePartPositionDossier);
    // const sparePartPosition = sparePartPositionDossier?.SparePartPositions?.SparePartPosition;

    const { t } = useTranslation();

    const { showSparePartPrice, showIntermediateTotalPriceOnly } = pluginOptions?.settings || {};

    const options = pluginOptions ? getFormatPriceOptions(pluginOptions) : null;
    const formatPrice = useFormattedPrice(options);

    const contract = pluginOptions?.data?.claimData?.contract;
    const repairCalculation = contract?.Dossier?.RepairCalculation;
    const calcResultCommon = repairCalculation?.CalcResultCommon;
    const isItalianCalculation = !!contract?.complexTemplateData?.find(
        templD => templD._attr_templateType === 'vro_domus_calculation'
    );

    const labourPositions = calcResultCommon?.LabourPositions?.LabourPosition;
    const materialPositions = calcResultCommon?.MaterialPositions?.MaterialPosition;
    const lacquerPositions = calcResultCommon?.LacquerPositions?.LacquerPosition;

    let totalSR = 0,
        totalLA = 0,
        totalVE = 0,
        totalME = 0,
        totalLabourTime = 0,
        totalPaintingTime = 0,
        totalSparePartPrice = 0;

    labourPositions?.forEach(lab => {
        totalLabourTime += lab.Duration || 0;
    });
    lacquerPositions?.forEach(lacq => {
        totalPaintingTime += lacq.Duration || 0;
    });
    materialPositions?.forEach(mat => {
        totalSparePartPrice += mat.ValueTotalCorrected || mat.ValueTotal || 0;
    });

    damageList.forEach(dmg => {
        // const sparePartInfoDamageElement = sparePartPosition?.find(pos => pos.DATProcessId === dmg.DATProcessId);
        const spPrice = 0; // todo

        const sparePartPrice = dmg.SparePartPrice || (!!dmg.WorkTypeE && spPrice) || 0;

        if (isItalianCalculation) {
            const italianPosition = dmg.italianPosition;
            const isAggregate = italianPosition?.Type === 'aggregate';
            const isCorrections = italianPosition?.Type === 'corrections';
            totalSR +=
                isAggregate || isCorrections
                    ? parseFloat(italianPosition.WorkTimeReplace?.toString() || '0')
                    : dmg.WorkTypeA?.WorkTime || italianPosition?.WorkTimeReplace || 0;
            totalLA +=
                isAggregate || isCorrections
                    ? parseFloat(italianPosition.WorkTimeOverhaul?.toString() || '0')
                    : (dmg.WorkTypeI?.WorkType !== 'mechanic' && dmg.WorkTypeI?.WorkTime) ||
                      dmg.WorkTypeE?.WorkTime ||
                      italianPosition?.WorkTimeOverhaul ||
                      0;
            totalVE +=
                isAggregate || isCorrections
                    ? parseFloat(italianPosition.WorkTimeLacquer?.toString() || '0')
                    : dmg.WorkTypeL?.WorkTime || dmg.WorkTimeL || italianPosition?.WorkTimeLacquer || 0;
            totalME += parseFloat(
                (dmg.WorkTypeI?.WorkType === 'mechanic'
                    ? dmg.WorkTypeI?.WorkTime?.toString()
                    : dmg.repairPositions.find(repPos => repPos.WorkType === 'mechanic')?.WorkTime?.toString()) ||
                    italianPosition?.WorkTimeMechanic?.toString() ||
                    '0'
            );
        } else {
            if (!labourPositions?.length)
                totalLabourTime +=
                    (dmg.WorkTypeA?.WorkTime || 0) + (dmg.WorkTypeE?.WorkTime || 0) + (dmg.WorkTypeI?.WorkTime || 0);
            if (!lacquerPositions?.length) totalPaintingTime += dmg.WorkTypeL?.WorkTime || 0;
        }

        if (!materialPositions?.length) totalSparePartPrice += sparePartPrice;
    });

    if (selectedLacquerMethod === 'CZ') {
        totalPaintingTime =
            calcResultCommon?.RepairCalculationSummary?.LacquerCosts?.TotalTimeUnits || totalPaintingTime;
    }

    return (
        <Row gutter={[8, 8]} align="middle" className="weDat-grapa-calcSum-wrapper">
            <Col span={24}>
                {!showIntermediateTotalPriceOnly && (
                    <>
                        <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                            <span style={{ width: '15px', height: '22px', display: 'flex', alignItems: 'center' }}>
                                {isRepPosModified && (
                                    <Tooltip placement="top" title={t('CalculationSummary.info')} arrowPointAtCenter>
                                        <InfoIcon width={15} color="#FF6E42" />
                                    </Tooltip>
                                )}
                            </span>
                            {isItalianCalculation ? (
                                <>
                                    <span className="weDat-grapa-calcSum-resultTime">
                                        {`${t(`RepairType.code.A`)}: `}
                                        <b>{`${totalSR.toFixed(2)}`}</b>
                                    </span>
                                    <span className="weDat-grapa-calcSum-resultTime">
                                        {`${t(`RepairType.code.I`)}: `}
                                        <b>{`${totalLA.toFixed(2)}`}</b>
                                    </span>
                                    <span className="weDat-grapa-calcSum-resultTime">
                                        {`${t(`RepairType.code.L`)}: `}
                                        <b>{`${totalVE.toFixed(2)}`}</b>
                                    </span>
                                    <span className="weDat-grapa-calcSum-resultTime">
                                        {`${t(`ManualRepairPositionForm.WorkTimeMechanic`)}: `}
                                        <b>{`${totalME.toFixed(2)}`}</b>
                                    </span>
                                    <span className="weDat-grapa-calcSum-resultTime">
                                        {`${t(`CalculationSummary.totalTime`)}: `}
                                        <b>{`${(totalSR + totalLA + totalVE + totalME).toFixed(2)}${t(
                                            'ManualRepairPositionForm.HoursShort',
                                            'h'
                                        )}`}</b>
                                    </span>
                                </>
                            ) : (
                                <>
                                    <span className="weDat-grapa-calcSum-resultTime">
                                        {`${t(`CalculationSummary.labourTime`)}: `}
                                        <b>{`${totalLabourTime.toFixed(2)}${t('ManualRepairPositionForm.HoursShort', 'h')}`}</b>
                                    </span>
                                    <span className="weDat-grapa-calcSum-resultTime">
                                        {`${t(`CalculationSummary.paintingTime`)}: `}
                                        <b>{`${totalPaintingTime.toFixed(2)}${t(
                                            'ManualRepairPositionForm.HoursShort',
                                            'h'
                                        )}`}</b>
                                    </span>
                                </>
                            )}
                        </div>
                        {!!showSparePartPrice && (
                            <div className="weDat-grapa-calcSum-total-wrapper">
                                <span>{t('CalculationSummary.sparePartsCosts')}:&nbsp;</span>
                                <span className="weDat-grapa-calcSum-ResultPrice">
                                    {formatPrice(totalSparePartPrice)}
                                </span>
                            </div>
                        )}
                    </>
                )}

                {!!showIntermediateTotalPriceOnly && (
                    <div className="weDat-grapa-calcSum-total-wrapper">
                        <span>{t('CalculationSummary.totalCosts')}:&nbsp;</span>
                        <span className="weDat-grapa-calcSum-ResultPrice">
                            {formatPrice(repairCalculation?.CalculationSummary?.TotalNetCorrected || 0)}
                        </span>
                    </div>
                )}
            </Col>
        </Row>
    );
};
