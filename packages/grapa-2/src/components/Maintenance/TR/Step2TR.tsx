import { Checkbox, Row, Col, DatePicker, InputNumber } from 'antd';
import { useUnit } from 'effector-react';
import moment from 'moment';
import { FC, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { maintenanceModel } from '../../../stores/maintenanceModel';
import { pluginOptionsModel } from '../../../stores/pluginOptionsModel';
import { RepairPositionsModel } from '../../../stores/repairPositionsModel';
import { uniqIdString } from '../../../utils/uniqId';
import { NumberInputField } from '../../CommonComponents/Input/NumberInputField';
import { uniqBy } from 'lodash-es';

import { Icon123 } from '../../CommonComponents/Icon';
import LiquidsIcon from '../../CommonComponents/Icons/LiquidsIcon.svg?react';
import { getNextInspectionTerms } from '../../../utils/getMaintenanceData';
import { nextInspectionDateId, nextInspectionDelimiter, nextInspectionMileageId } from '../../../constants/maintenance';

export const Step2TR: FC<{ isMobile?: boolean }> = ({ isMobile }) => {
    const { t } = useTranslation();
    const maintenance = useUnit(maintenanceModel.stores.maintenanceTR);
    const pluginOptions = useUnit(pluginOptionsModel.stores.pluginOptions);
    const repairPositions = useUnit(RepairPositionsModel.store.repairPositions);
    const selectedInterval = useUnit(maintenanceModel.stores.selectedIntervalTR);
    const intervalPositions = useMemo(
        () =>
            uniqBy(
                maintenance?.filter(
                    maintPos =>
                        maintPos.km === selectedInterval?.Km &&
                        maintPos.description &&
                        maintPos.description.trim() !== selectedInterval.Description
                ),
                'description'
            ),
        [selectedInterval]
    );
    const intervalPositionsSelected = intervalPositions?.filter(intPos =>
        repairPositions.find(
            repPos =>
                repPos.PositionEntryType === 'manual' &&
                repPos.SparePartSupplyDescription === 'maintenance' &&
                repPos.Description === intPos.description?.trim()
        )
    );

    useEffect(() => {
        const intervalPositionWithoutPrice = repairPositions.find(repPos => {
            const sameIntPos = intervalPositionsSelected.find(
                intPos =>
                    repPos.PositionEntryType === 'manual' &&
                    repPos.SparePartSupplyDescription === 'maintenance' &&
                    repPos.Description === intPos.description?.trim()
            );
            return !!sameIntPos && repPos.SparePartPrice === undefined;
        });
        maintenanceModel.events.setIsValidated(!intervalPositionWithoutPrice);
    }, []);

    const intervalRepPos = repairPositions.find(
        repPos =>
            repPos.PositionEntryType === 'maintenance' && repPos.Description === selectedInterval?.Description.trim()
    );

    const dateFormat = 'DD.MM.YYYY';
    const nextInspection = intervalRepPos?.SparePartSupplyDescription;
    const { nextInspectionMileage, nextInspectionDate } = getNextInspectionTerms(nextInspection || '') || {};

    const dossierCurrency = pluginOptions?.data?.claimData?.contract?.Dossier?.Currency;
    const locale = pluginOptions?.locale;
    const resultFormat = pluginOptions?.settings?.priceFormat;

    let formatter: Intl.NumberFormat;
    try {
        formatter = new Intl.NumberFormat(
            resultFormat?.locales || locale || 'en-US',
            resultFormat?.numberFormatOptions
                ? {
                      currency: resultFormat.numberFormatOptions.currency || dossierCurrency || 'EUR',
                      ...resultFormat?.numberFormatOptions,
                      style: 'currency'
                  }
                : {
                      currency: dossierCurrency || 'EUR',
                      minimumFractionDigits: 2,
                      style: 'currency'
                  }
        );
    } catch {
        formatter = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'EUR' });
    }

    return (
        <>
            <div className="weDat-grapa-step2Header">
                <Row
                    gutter={[12, 0]}
                    style={{
                        borderBottom: '1px #d3dceb solid',
                        color: '#687792',
                        fontSize: '14px',
                        paddingBottom: '6px'
                    }}
                >
                    <Col xs={{ span: 9, offset: 6 }} sm={{ span: 6, offset: 4 }}>
                        {t('Maintenance.Quantity')}
                    </Col>
                    <Col xs={8} sm={7}>
                        {t('Maintenance.UnitPrice')}
                    </Col>
                    {!isMobile && (
                        <Col offset={2} sm={5}>
                            {t('Maintenance.Total')}
                        </Col>
                    )}
                </Row>
            </div>
            <div className="weDat-grapa-step2Content">
                {!!intervalPositionsSelected?.length &&
                    intervalPositionsSelected.map((matPos, index) => {
                        const repairPosition = repairPositions.find(
                            repPos =>
                                repPos.PositionEntryType === 'manual' &&
                                repPos.SparePartSupplyDescription === 'maintenance' &&
                                repPos.Description === matPos.description?.trim()
                        );
                        const defaultSparePartPrice = matPos.price;
                        const sparePartPrice = repairPosition?.SparePartPrice;
                        const totalPrice = (repairPosition?.SparePartAmount || 1) * (sparePartPrice || 0);

                        return (
                            <Row
                                key={index}
                                gutter={[12, 0]}
                                align="middle"
                                style={{ paddingBottom: '12px', borderBottom: '1px #d3dceb solid' }}
                            >
                                <Col span={24}>
                                    <Checkbox
                                        checked={!!repairPosition}
                                        onChange={e => {
                                            if (e.target.checked) {
                                                maintenanceModel.events.addMaintenanceRepairPosition({
                                                    key: uniqIdString(),
                                                    RepairType: 'replace',
                                                    PositionEntryType: 'manual',
                                                    Description: matPos?.description,
                                                    SparePartSupplyDescription: 'maintenance'
                                                });
                                            } else {
                                                RepairPositionsModel.event.setRepairPositions(
                                                    repairPositions.filter(
                                                        repPos =>
                                                            !(
                                                                repPos.PositionEntryType === 'manual' &&
                                                                repPos.SparePartSupplyDescription === 'maintenance' &&
                                                                repPos.Description === matPos.description?.trim()
                                                            )
                                                    )
                                                );
                                            }
                                        }}
                                    >
                                        {matPos?.description}
                                    </Checkbox>
                                </Col>
                                {!!repairPosition && (
                                    <>
                                        <Col offset={1} xs={4} sm={2}>
                                            {matPos.amount?.toString().toUpperCase().includes('LT') && (
                                                <Icon123 active={false} icon={<LiquidsIcon />} />
                                            )}
                                        </Col>

                                        <Col offset={1} xs={9} sm={5}>
                                            <InputNumber
                                                size="large"
                                                min={0}
                                                placeholder="1.00"
                                                value={repairPosition?.SparePartAmount || 1}
                                                onChange={value => {
                                                    RepairPositionsModel.event.createOrUpdateRepairPosition({
                                                        ...repairPosition,
                                                        SparePartAmount: value || 1
                                                    });
                                                }}
                                            />
                                        </Col>
                                        <Col xs={9} sm={7}>
                                            <InputNumber
                                                size="large"
                                                min={0}
                                                required={!defaultSparePartPrice}
                                                placeholder={
                                                    defaultSparePartPrice
                                                        ? formatter.format(defaultSparePartPrice)
                                                        : undefined
                                                }
                                                value={sparePartPrice}
                                                status={sparePartPrice === undefined ? 'error' : undefined}
                                                onChange={value => {
                                                    RepairPositionsModel.event.createOrUpdateRepairPosition({
                                                        ...repairPosition,
                                                        SparePartPrice: value === null ? undefined : value
                                                    });
                                                    if (value === null) {
                                                        maintenanceModel.events.setIsValidated(false);
                                                    } else {
                                                        maintenanceModel.events.setIsValidated(true);
                                                    }
                                                }}
                                            />
                                        </Col>
                                        {isMobile ? (
                                            <Col
                                                offset={3}
                                                span={21}
                                                style={{ color: '#687792', fontSize: '14px', marginTop: '8px' }}
                                            >
                                                {t('Maintenance.Total')}:
                                                <b style={{ marginLeft: '15px', color: '#000' }}>
                                                    {sparePartPrice !== undefined || defaultSparePartPrice !== undefined
                                                        ? formatter.format(totalPrice || 0)
                                                        : ''}
                                                </b>
                                            </Col>
                                        ) : (
                                            <Col span={7} style={{ textAlign: 'right' }}>
                                                <b style={{ marginRight: '15px' }}>
                                                    {sparePartPrice !== undefined || defaultSparePartPrice !== undefined
                                                        ? formatter.format(totalPrice || 0)
                                                        : ''}
                                                </b>
                                            </Col>
                                        )}
                                    </>
                                )}
                            </Row>
                        );
                    })}
            </div>
            {!isMobile && (
                <div className="weDat-grapa-maint-inspection">
                    <div style={{ margin: '16px 24px' }}>
                        <b>{t('Maintenance.NextMaintenanceText')}:</b>
                    </div>
                    <Row align="middle" gutter={[8, 8]}>
                        <Col span={13}>
                            <div style={{ paddingLeft: '8px' }}>
                                <NumberInputField
                                    inputSize="medium"
                                    min={0}
                                    label={t('Maintenance.Mileage')}
                                    value={nextInspectionMileage}
                                    format={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ' ')}
                                    onChange={value => {
                                        if (!intervalRepPos) return;
                                        maintenanceModel.events.addMaintenanceTRRepairPosition({
                                            ...intervalRepPos,
                                            SparePartSupplyDescription: nextInspection
                                                ? nextInspection.replace(
                                                      `${nextInspectionMileageId}${nextInspectionMileage || ''}`,
                                                      `${nextInspectionMileageId}${value || ''}`
                                                  )
                                                : `${nextInspectionMileageId}${value || ''}${nextInspectionDelimiter}${nextInspectionDateId}`
                                        });
                                    }}
                                />
                            </div>
                        </Col>
                        <Col span={11}>
                            <DatePicker
                                size="large"
                                placeholder={t('Maintenance.Date')}
                                inputReadOnly
                                format={dateFormat}
                                value={nextInspectionDate ? moment(nextInspectionDate, dateFormat) : undefined}
                                onChange={(_date, dateString) => {
                                    if (!intervalRepPos) return;
                                    maintenanceModel.events.addMaintenanceRepairPosition({
                                        ...intervalRepPos,
                                        SparePartSupplyDescription: nextInspection
                                            ? nextInspection.replace(
                                                  `${nextInspectionDateId}${nextInspectionDate || ''}`,
                                                  `${nextInspectionDateId}${dateString || ''}`
                                              )
                                            : `${nextInspectionMileageId}${nextInspectionDelimiter}${nextInspectionDateId}${dateString || ''}`
                                    });
                                }}
                            />
                        </Col>
                    </Row>
                </div>
            )}
        </>
    );
};
