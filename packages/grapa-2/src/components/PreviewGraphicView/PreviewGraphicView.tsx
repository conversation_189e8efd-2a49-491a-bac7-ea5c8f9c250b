import { FC } from 'react';
import { useUnit } from 'effector-react';
import { Row, Col } from 'antd';

import { graphicDamageSelectionModel } from '../../stores/graphicDamageSelectionModel';
import { subscribes } from '../../stores/subscribes';
import { pluginOptionsModel } from '../../stores/pluginOptionsModel';

import { useTranslation } from 'react-i18next';
import { AvailableAssemblyGroup } from '../../types/graphicTypes';
import { ColorizedSVGType } from '@dat/dat-data-models/src/models/colorizedSVGModel';
import preloaderImage from '@wedat/ui-kit/assets/images/preloader.webp';
import { commonModel } from '../../stores/commonModel';

export const PreviewGraphicView: FC<{}> = () => {
    const { t } = useTranslation();

    const filteredByZoneAndOptionsGraphicsList = useUnit(subscribes.stores.filteredByZoneAndOptionsGraphicsList);

    const filteredGraphicsList = filteredByZoneAndOptionsGraphicsList.filter(item => !!item.isFiltered);

    const notFilteredGraphicsList = filteredByZoneAndOptionsGraphicsList.filter(item => !item.isFiltered);

    const isMobile = useUnit(commonModel.stores.isMobile);

    return (
        <div
            style={{
                width: '100%',
                height: '100%',
                overflowY: 'auto',
                overflowX: 'hidden',
                // backgroundColor: 'white',
                background: 'var(--weDat-grapa-background)',
                padding: isMobile ? '8px 8px 80px 8px' : '16px 64px 80px 80px',
                scrollbarWidth: 'thin'
            }}
        >
            <PreviewGraphicViewList graphicsList={filteredGraphicsList} />

            {!!notFilteredGraphicsList.length && (
                <div
                    style={{
                        backgroundColor: '#e3f2ff',
                        width: '100%',
                        lineHeight: 'normal',
                        // margin: '16px',
                        padding: '16px',
                        fontWeight: 'bold'
                    }}
                >
                    {t(
                        'DeviatingVehiclesGrapaMenuDivider',
                        'Deviating vehicles (production period, equipment, subtype)'
                    )}
                </div>
            )}

            <PreviewGraphicViewList graphicsList={notFilteredGraphicsList} />
        </div>
    );
};

const ratio = 837 / 1373;
const previewImageWidth = 180;

export const PreviewGraphicViewList: FC<{ graphicsList: AvailableAssemblyGroup[] }> = ({ graphicsList }) => {
    const colorizedSVG = useUnit(pluginOptionsModel.stores.colorizedSVG);
    const vehicleData = useUnit(pluginOptionsModel.stores.vehicleData);
    const modelDatECode = useUnit(pluginOptionsModel.stores.modelDatECodeWithAlternative);

    return (
        // <Row style={{ margin: '16px' }} gutter={[16, 16]} justify="space-evenly">
        <Row style={{ margin: '0px', justifyContent: 'center' }} gutter={[0, 0]}>
            {graphicsList?.map(gr => {
                const colorizedSVGObj: ColorizedSVGType =
                    colorizedSVG?.getColorizedSVG({
                        basicDatECode: modelDatECode,
                        groupId: gr.assemblyGroup.assemblyGroupId || 0,
                        colorizedSVGCacheParam: colorizedSVG.colorizedSVGCache,
                        countryVehicleStaticData: vehicleData?.countryVehicleStaticDataD,
                        SVGGroup: vehicleData?.SVGGroup
                    }) || {};

                return (
                    // <Col key={gr.assemblyGroup.assemblyGroupId} xs={{ span: 12 }} sm={{ span: 12 }} md={{ span: 6 }}>
                    <div
                        key={gr.assemblyGroup.assemblyGroupId}
                        // style={{ display: 'grid', width: "210px" ,gap: '16px', columnGap: '16px' }}
                        // style={{ width: previewImageWidth, height: previewImageWidth / ratio }}
                        style={{ width: previewImageWidth, margin: 8 }}
                        // className={false ? 'rimsEquipmentGridSelected' : 'rimsEquipmentGridNotSelected'}
                        onClick={_ => {
                            graphicDamageSelectionModel.events.updateStore({
                                currentAssemblyGroup: gr,
                                currentAssemblyGroupObject: undefined,
                                currentAssemblyGroupObjects: undefined
                            });
                            // const numberOfPolygon = getNumberOfPolygon(element.assemblyGroup.assemblyGroupId, vehicleData);
                            graphicDamageSelectionModel.events.updateStore({
                                // typeOfGraphic: numberOfPolygon <= MAX_POLYGON_SPLITS ? 'DVNSplitGraphic' : 'FullGraphic'
                                typeOfGraphic: false ? 'DVNSplitGraphic' : 'FullGraphic'
                            });
                            // if (isMobile)
                            //     graphicDamageSelectionModel.events.updateStore({
                            //         showGraphicGroupsMenu: false
                            //     });
                        }}
                    >
                        {!!colorizedSVGObj?.previewSvg ? (
                            <img
                                width={previewImageWidth}
                                height={previewImageWidth / ratio}
                                src={colorizedSVGObj?.previewSvg}
                                alt=""
                            />
                        ) : (
                            <div
                                style={{
                                    width: previewImageWidth,
                                    height: previewImageWidth / ratio,
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}
                            >
                                <img width={80} src={preloaderImage} alt="" />
                            </div>
                        )}
                        <br />
                        {gr.assemblyGroup.name}
                    </div>

                    // </Col>
                );
            })}
        </Row>
    );
};
