import { attach, combine, sample, createDomain } from 'effector';

import { listSheets, getSheet } from '@dat/api2/services/fr/CESVI';
import { contractEvents, contractStores } from '@dat/shared-models/contract';
import { sharedTemplateStores } from '@dat/shared-models/template';

import { mapListSheets } from '../utils/mapListSheets';
import { MapCesviFile } from '../types';
import { getPdfFromIndexedDB, storePDFInIndexedDB } from '../utils/indexedDbFiles';
import { GrapaPluginOptions } from '../../../types/plugin';

const cesviDomain = createDomain('cesvi');
cesviDomain.onCreateStore(store => store.reset(contractEvents.resetContract));

const { createStore, createEvent, createEffect } = cesviDomain;

const getListSheets = createEvent<void>();
const getListSheetsFx = createEffect(listSheets);
const getListSheetsFromContractFx = attach({
    source: contractStores.initialContract,
    mapParams: (_: void, contract) =>
        ({
            vehicleIdentification:
                contract?.Dossier?.Vehicle?.VehicleIdentNumber ||
                contract?.Dossier?.Vehicle?.RegistrationData?.LicenseNumber ||
                '',
            options: {
                callerId: `${contract?.Dossier?.DatCustomerId}/${contract?.Dossier?.CreateUser}`,
                terminalId: `${contract?.Dossier?.DossierId}`
            }
        }) as Parameters<typeof listSheets>[0],
    effect: getListSheetsFx
});

const listSheetsRequested = createStore(false).on(getListSheetsFx.finally, () => true);

const getSheetBlobFx = createEffect(async (...props: Parameters<typeof getSheet>): Promise<Blob | undefined> => {
    const [{ sheet, options }] = props;
    const indexedFile = await getPdfFromIndexedDB(sheet, options?.language);
    if (indexedFile) return indexedFile;

    const file = await getSheet({ sheet, options }).catch(() => undefined);
    if (file) return storePDFInIndexedDB(file);
});

const fetchingSheet = getSheetBlobFx.pending;
const fetchingListSheets = getListSheetsFx.pending;

const cesviSheets = createStore(new Map() as MapCesviFile).on(getListSheetsFx.doneData, (_, response) =>
    mapListSheets(response)
);

const isCesviEnabled = combine(
    [sharedTemplateStores.productsConfiguration],
    ([config]) => !!(config?.grapa as GrapaPluginOptions['settings'])?.cesviEnabled || false
);

const isIdentifiable = combine([contractStores.initialContract], ([contract]) =>
    Boolean(
        contract &&
            (contract.Dossier?.Vehicle?.VehicleIdentNumber ||
                contract.Dossier?.Vehicle?.RegistrationData?.LicenseNumber)
    )
);

const generalSheets = cesviSheets.map<MapCesviFile>(
    sheets => new Map(Array.from(sheets.entries()).filter(([key]) => key === 'MECE'))
);

const miscSheets = cesviSheets.map<MapCesviFile>(
    sheets => new Map(Array.from(sheets.entries()).filter(([key]) => key !== 'MECE'))
);

sample({
    clock: getListSheets,
    source: { isIdentifiable, isCesviEnabled, listSheetsRequested },
    filter: ({ isIdentifiable, isCesviEnabled, listSheetsRequested }) =>
        isCesviEnabled && isIdentifiable && !listSheetsRequested,
    target: getListSheetsFromContractFx
});

export const cesviStores = {
    cesviSheets,
    generalSheets,
    miscSheets,
    fetchingSheet,
    fetchingListSheets,
    isCesviEnabled,
    isIdentifiable
};

export const cesviEffects = {
    getListSheetsFx,
    getSheetBlobFx,
    getListSheetsFromContractFx
};

export const cesviEvents = {
    getListSheets
};
