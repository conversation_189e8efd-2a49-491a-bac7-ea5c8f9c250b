server {
    listen 3000;  # Nginx listens on port 3000 as per Kubernetes setup
    listen [::]:3000;  # Also listen on IPv6 address on port 3000

    root /usr/share/nginx/html;
    index index.html index.htm index.nginx-debian.html;

    # Main location block handles all requests not caught by more specific location blocks
    location / {
      try_files $uri /index.html;
      add_header Access-Control-Allow-Origin "*";
    }

    # Handling for static files: wasm, fonts, css, js, etc.
    location ~* \.(wasm|eot|ttf|woff|woff2|css|js|webmanifest)$ {
        add_header Access-Control-Allow-Origin "*";  # CORS header for fonts and other assets
        expires 1M;  # Caching static files for 1 month
        access_log off;  # Disable logging for static files
        add_header Cache-Control "public";  # Public caching
    }

    # Specific settings for JavaScript and CSS files
    location ~* \.(js|css)$ {
        sendfile on;  # Ensure sendfile is on for better performance
        expires 1M;  # Cache JS and CSS for 1 month
        access_log off;  # Turn off logging to improve performance
        add_header Cache-Control "public, no-transform";  # Additional caching headers
    }

    # Disable unnecessary if block that could cause performance issues
    # Instead, you can handle redirects or host-specific rules here in a safer manner

    # Enable gzip compression
    gzip on;
    gzip_disable "msie6";  # Disable gzip for very old browsers
    gzip_vary on;  # Vary header for proxy caches
    gzip_proxied any;  # Enable gzip for all proxy requests
    gzip_comp_level 6;  # Compression level (1-9)
    gzip_buffers 16 8k;  # Buffer size for gzip
    gzip_http_version 1.1;  # Use HTTP 1.1 for gzip
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript application/wasm;  # MIME types to compress
}
